package config

import (
	"github.com/jinzhu/configor"
)

const (
	// Dev develop environment
	Dev string = "dev"
	// Test test environment
	Test string = "test"
	// Pre preview environment
	Pre string = "pre"
	// Prod production environment
	Prod string = "prod"
)

// Items Items
var Items = func() configuration {
	return config
}
var config configuration

type configuration struct {
	AppName    string `env:"APP_NAME" default:"pilot-api"`
	ProjectEnv string `env:"PROJECT_ENV" default:"dev"`
	APIVersion string `env:"API_VERSION" default:"Commit ID"`
	Mysql      struct {
		Host    string `default:"mysql-sz.makeblock.com"`
		Port    string `default:"3306"`
		DBName  string `default:"pilot-api-dev"`
		User    string `default:"root"`
		Pwd     string `default:"tEKIZtC0CXjg"`
		Charset string `default:"utf8mb4"`
	}
	Redis struct {
		Host   string `default:"127.0.0.1"`
		Port   string `default:"6379"`
		Pwd    string `default:""`
		Prefix string `default:"'pilot-api:'"`
	}
	RPC struct {
		Account  string `default:"account-service-dev.makeblock.com:8000"`
		Efficacy string `default:"efficacy-service-dev.makeblock.com:8000"`
	}

	Sentry struct {
		DSN string `env:"SENTRY_DSN" default:"https://<EMAIL>/91"`
	}

	SecretKey string `yaml:"secret_key" env:"SECRET_KEY" default:"auenzeunehjwadfe"` // AES only supports key sizes of 16, 24 or 32 bytes

	Efficacy struct {
		Endpoint string `env:"EFFICACY_API_ENDPOINT" default:"https://api-dev.makeblock.com"`
		Token    string `env:"EFFICACY_API_TOKEN" default:"8F5A3AB5-0D41-473A-947D-7CC3362F14B5"`
	}

	// 飞书通知相关
	Notify struct {
		// 飞书应用配置
		AppID     string `env:"FEISHU_APP_ID" default:"cli_a6d7a428f631500d"`
		AppSecret string `env:"FEISHU_APP_SECRET" default:"w4tDQ2pwkbCRe7WOOR7x8cVvXZll4FFg"`
		// 网关审批通知群
		GatewayChatID string `env:"GATEWAY_CHAT_ID" default:"oc_d967eaed581c58fc48847c58036da071"`
		// 前端域名地址
		FrontendHost string `env:"FRONTEND_HOST" default:"http://local.makeblock.com:3001/pilot/#/"`
		// 审批页面路由(用于拼接审批页面的链接)
		VirtualServiceRoute string `env:"VIRTUAL_SERVICE_ROUTE" default:"gateway/virtualservice"`
		DomainRecordRoute   string `env:"DOMAIN_RECORD_ROUTE" default:"gateway/domain"`
	}

	// 阿里云镜像仓库配置
	AliyunRegistry struct {
		AccessKeyId     string `env:"ALIYUN_ACCESS_KEY_ID" default:"LTAI5tSoaHdJk8X3rZcqetpy"`
		AccessKeySecret string `env:"ALIYUN_ACCESS_KEY_SECRET" default:"******************************"`
		RegionId        string `env:"ALIYUN_REGION_ID" default:"cn-hangzhou"`
		InstanceId      string `env:"ALIYUN_CR_INSTANCE_ID" default:"cri-prqb1i32g6fwa5zf"`
		Endpoint        string `env:"ALIYUN_CR_ENDPOINT" default:"cr.cn-hangzhou.aliyuncs.com"`
	}

	// Nexus镜像仓库配置
	NexusRegistry struct {
		Endpoint string `env:"NEXUS_ENDPOINT" default:"http://nexus.example.com"`
		Username string `env:"NEXUS_USERNAME" default:""`
		Password string `env:"NEXUS_PASSWORD" default:""`
	}
}

// Load load configurations
func Load() {
	conf := &configor.Config{ENVPrefix: "-"}
	if err := configor.New(conf).Load(&config, "/pilot-api/etc/config.yaml", "config.yaml", "../config.yaml"); err != nil {
		panic(err)
	}
}
