#!/bin/bash
set -e
shell_dir=$(dirname $0)
cd ${shell_dir}

# check params
if [[ ! $1 ]]; then
    echo "api version is null"; exit 1;
else
    echo "api version: $1"
fi

if [[ ! $2 ]]; then
    echo "image name is null"; exit 1;
else
    echo "image name: $2"
fi

if [[ ! $3 ]]; then
    echo "image tag is null"; exit 1;
else
    echo "image tag: $3"
fi

# build binary
go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags '-w -s' -o pilot-api ../../cmd/main.go

# build image
docker build --build-arg tmp_api_version="$1" -t "$2:$3" -f Dockerfile .

# docker push
docker push "$2:$3"

# for render k8s yaml image tag
if [ -n "${PIPELINE_ENV}" ]; then
    echo "IMAGE_TAG=$3" >> "${PIPELINE_ENV}"
fi
