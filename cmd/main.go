package main

import (
	"os"
	"os/signal"
	"pilot-api/config"
	"pilot-api/internal/app"
	"pilot-api/internal/app/workflow"
	"pilot-api/internal/pkg/migrate"
	"syscall"

	efficacy "git.makeblock.com/makeblock-proto/efficacy-service/client"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/tracing"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/opentracing/opentracing-go"
	"gorm.io/gorm/logger"

	mbsentry "git.makeblock.com/makeblock-go/sentry"
	"github.com/getsentry/sentry-go"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func main() {
	config.Load()

	log.SetUp(config.Items().ProjectEnv == config.Prod, log.Any("app_name", config.Items().AppName))
	defer log.Sync()

	// sentry core配置
	cfg := mbsentry.SentryCoreConfig{
		Level: zap.ErrorLevel,
		Tags: map[string]string{
			"source": config.Items().AppName,
		},
	}
	// 生成sentry客户端
	sentryClient, err := sentry.NewClient(sentry.ClientOptions{
		Dsn:              config.Items().Sentry.DSN,
		Debug:            false,
		AttachStacktrace: true,
		Environment:      config.Items().ProjectEnv,
	})
	if err != nil {
		log.ErrorE("init sentry client failed ", err)
	}

	// 生成 sentryCore
	sCore := mbsentry.NewSentryCore(cfg, sentryClient, config.Items().AppName)

	// 添加sentryCore到默认logger产生新的logger，使用该logger即可自动上报sentry
	log.WithOptions(zap.WrapCore(func(core zapcore.Core) zapcore.Core {
		return zapcore.NewTee(core, sCore)
	}))

	tracer, closer, err := tracing.NewDefaultJaeger()
	if err != nil {
		log.Println("new jaeger err:", err)
	}
	if closer != nil {
		defer closer.Close()
	}
	opentracing.SetGlobalTracer(tracer)

	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	defer mysql.Close()

	// 开启gorm日志颜色
	colorful := false
	if config.Items().ProjectEnv == config.Dev {
		colorful = true
	}
	gormCtxLogger := trace.NewGormCtxLogger(logger.Config{LogLevel: logger.Info, Colorful: colorful})
	mysql.SetLogger(mysql.GetDB(), gormCtxLogger)

	// Auto-migrate our new table 开发环境使用
	if config.Items().ProjectEnv == config.Dev {
		migrate.AutoMigrate()
	}

	// 初始化工作流管理器
	if err = workflow.InitWorkflowManager(mysql.GetDB()); err != nil {
		log.FatalE("Failed to initialize workflow manager", err)
	}

	// 验证注册是否完整
	if err = workflow.ValidateRegistration(); err != nil {
		log.FatalE("Workflow registration validation failed", err)
	}

	redis.SetUp(
		config.Items().Redis.Host,
		config.Items().Redis.Port,
		config.Items().Redis.Pwd)
	defer redis.Close()

	efficacy.SetupClient(config.Items().RPC.Efficacy)

	app.RunServer(":8080")

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	app.ShutdownServer()
	log.Println("Shutdown Server ...")
}
