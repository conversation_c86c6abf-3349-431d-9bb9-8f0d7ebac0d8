# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
# See https://pre-commit.com/all-hooks.json for hooks details
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.2.0
    hooks:
      # https://github.com/pre-commit/pre-commit-hooks#trailing-whitespace
      - id: trailing-whitespace
      # https://github.com/pre-commit/pre-commit-hooks#end-of-file-fixer
      - id: end-of-file-fixer
      # https://github.com/pre-commit/pre-commit-hooks#check-yaml
      - id: check-yaml
        args: [--allow-multiple-documents]
      # https://github.com/pre-commit/pre-commit-hooks#hooks-available
      - id: check-added-large-files
# Commitlint temporarily disabled - focus on Go code quality
# - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
#   rev: v9.3.0
#   hooks:
#       - id: commitlint
#         stages: [commit-msg]
#         additional_dependencies: ['@commitlint/config-conventional']
  - repo: https://github.com/golangci/golangci-lint
    rev: v1.64.8
    hooks:
      - id: golangci-lint
        entry: golangci-lint run -c .golangci.yml --fix
        args: [--timeout=5m]
  - repo: local
    hooks:
      - id: gomod
        name: gomod
        entry: make mod
        language: system
      - id: gofmt
        name: gofmt
        entry: make fmt
        language: system
      - id: goimports
        name: goimports
        entry: make imports
        language: system
      - id: test
        name: test
        entry: go test -short ./...
        language: system
        pass_filenames: false
