SHELL := /bin/bash
BASEDIR = $(shell pwd)

export GO111MODULE=on
export GOPROXY=https://goproxy.cn,direct
export GOPRIVATE=*.gitlab.com,git.makeblock.com
export GOSUMDB=off

# params stable
APP_NAME ?= pilot-api # app name
GIT_BRANCH ?= `git rev-parse --abbrev-ref HEAD` # git branch name
GIT_COMMIT_SHA ?= `git rev-parse HEAD` # git commit sha

# image repository
IMAGE_REGISTRY ?= mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com
IMAGE_NAMESPACE ?= makeblock
IMAGE_TAG ?= `git rev-parse --short HEAD` # image tag, default is git commit short hash

all: fmt imports mod lint test
install-pre-commit:
	brew install pre-commit
install-git-hooks:
	pre-commit install --hook-type commit-msg
	pre-commit install
run-pre-commit:
	pre-commit run --all-files
fmt:
	gofmt -w .
imports:
ifeq (, $(shell which goimports))
	go install golang.org/x/tools/cmd/goimports@latest
endif
	goimports -w .
mod:
	go mod tidy
lint: mod
ifeq (, $(shell which golangci-lint))
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.64.8
endif
	golangci-lint run -c .golangci.yml
.PHONY: test
test: mod
	go test -gcflags=-l -coverpkg=./... -coverprofile=coverage.data ./...
.PHONY: docs
docs: mod
ifeq (, $(shell which swag))
	go install github.com/swaggo/swag/cmd/swag@latest
endif
	swag init -g cmd/main.go
.PHONY: build
build:
	IMAGE_NAME="${IMAGE_REGISTRY}/${IMAGE_NAMESPACE}/${APP_NAME}"; \
	sh build/package/build.sh ${GIT_COMMIT_SHA} $$IMAGE_NAME ${IMAGE_TAG}
build-dev:
	make build IMAGE_TAG=main
build-test:
	make build IMAGE_TAG=$(shell echo "${GIT_BRANCH}" | tr '[:upper:]' '[:lower:]' | sed -E 's/[^a-z0-9._-]/-/g' | sed -E 's/^[-._]//;s/[-._]$$//;s/[-._]{2,}/-/g' | cut -c1-128) # 转换分支名为合法Docker标签
build-tag:
	make build IMAGE_TAG=${GIT_TAG} # 取tag
cleanup:
	sh scripts/cleanup.sh
dev:
ifeq (, $(shell which air))
	go install github.com/air-verse/air@latest
endif
	air
help:
	@echo "fmt - format the source code"
	@echo "imports - goimports"
	@echo "mod - go mod tidy"
	@echo "lint - run golangci-lint"
	@echo "test - unit test"
	@echo "build - build docker image"
	@echo "build-main - build docker image for main branch"
	@echo "build-release GIT_BRANCH=branch - build docker image for release branch"
	@echo "build-prod GIT_TAG=tag - build docker image for prod branch"
	@echo "cleanup - clean up the build binary"
