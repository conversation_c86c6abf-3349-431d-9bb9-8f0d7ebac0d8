<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>GrapesJS Demo - Free Open Source Website Page Builder</title>
  <meta content="Best Free Open Source Responsive Websites Builder" name="description">
  <!-- <link rel="stylesheet" href="https://grapesjs.com/stylesheets/toastr.min.css"> -->
  <link rel="stylesheet" href="https://grapesjs.com/stylesheets/grapes.min.css?v0.20.2">
  <link rel="stylesheet" href="https://grapesjs.com/stylesheets/grapesjs-preset-webpage.min.css">
  <!-- <link rel="stylesheet" href="https://grapesjs.com/stylesheets/tooltip.css"> -->
  <link rel="stylesheet" href="https://grapesjs.com/stylesheets/demos.css?v3">
  <!-- <link href="https://unpkg.com/grapick/dist/grapick.min.css" rel="stylesheet"> -->

  <!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script> -->
  <!-- <script src="https://grapesjs.com/js/toastr.min.js"></script> -->
  <script src="https://grapesjs.com/js/grapes.min.js?v0.20.2"></script>
  <script src="https://unpkg.com/grapesjs-preset-webpage@1.0.2"></script>
  <script src="https://unpkg.com/grapesjs-blocks-basic@1.0.1"></script>
  <script src="https://unpkg.com/grapesjs-plugin-forms@2.0.5"></script>
  <!-- <script src="https://unpkg.com/grapesjs-component-countdown@1.0.1"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-plugin-export@1.0.11"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-tabs@1.0.6"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-custom-code@1.0.1"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-touch@0.1.1"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-parser-postcss@1.0.1"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-tooltip@0.1.7"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-tui-image-editor@0.1.3"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-typed@1.0.5"></script> -->
  <!-- <script src="https://unpkg.com/grapesjs-style-bg@2.0.1"></script> -->

</head>

<body>
  <div id="gjs" style="height:100%; overflow:hidden">
  </div>
</body>

<script type="text/javascript">
  var lp = './img/';
  var plp = 'https://via.placeholder.com/350x250/';
  var images = [
    lp + 'team1.jpg', lp + 'team2.jpg'
  ];

  var editor = grapesjs.init({
    height: '100%',
    container: '#gjs',
    fromElement: true,
    showOffsets: true,
    assetManager: {
      embedAsBase64: true,
      assets: images
    },
    selectorManager: { componentFirst: true },
    styleManager: {
      sectors: [{
        name: 'General',
        properties: [
          {
            extend: 'float',
            type: 'radio',
            default: 'none',
            options: [
              { value: 'none', className: 'fa fa-times' },
              { value: 'left', className: 'fa fa-align-left' },
              { value: 'right', className: 'fa fa-align-right' }
            ],
          },
          'display',
          { extend: 'position', type: 'select' },
          'top',
          'right',
          'left',
          'bottom',
        ],
      }, {
        name: 'Dimension',
        open: false,
        properties: [
          'width',
          {
            id: 'flex-width',
            type: 'integer',
            name: 'Width',
            units: ['px', '%'],
            property: 'flex-basis',
            toRequire: 1,
          },
          'height',
          'max-width',
          'min-height',
          'margin',
          'padding'
        ],
      }, {
        name: 'Typography',
        open: false,
        properties: [
          'font-family',
          'font-size',
          'font-weight',
          'letter-spacing',
          'color',
          'line-height',
          {
            extend: 'text-align',
            options: [
              { id: 'left', label: 'Left', className: 'fa fa-align-left' },
              { id: 'center', label: 'Center', className: 'fa fa-align-center' },
              { id: 'right', label: 'Right', className: 'fa fa-align-right' },
              { id: 'justify', label: 'Justify', className: 'fa fa-align-justify' }
            ],
          },
          {
            property: 'text-decoration',
            type: 'radio',
            default: 'none',
            options: [
              { id: 'none', label: 'None', className: 'fa fa-times' },
              { id: 'underline', label: 'underline', className: 'fa fa-underline' },
              { id: 'line-through', label: 'Line-through', className: 'fa fa-strikethrough' }
            ],
          },
          'text-shadow'
        ],
      }, {
        name: 'Decorations',
        open: false,
        properties: [
          'opacity',
          'border-radius',
          'border',
          'box-shadow',
          'background', // { id: 'background-bg', property: 'background', type: 'bg' }
        ],
      }, {
        name: 'Extra',
        open: false,
        buildProps: [
          'transition',
          'perspective',
          'transform'
        ],
      }, {
        name: 'Flex',
        open: false,
        properties: [{
          name: 'Flex Container',
          property: 'display',
          type: 'select',
          defaults: 'block',
          list: [
            { value: 'block', name: 'Disable' },
            { value: 'flex', name: 'Enable' }
          ],
        }, {
          name: 'Flex Parent',
          property: 'label-parent-flex',
          type: 'integer',
        }, {
          name: 'Direction',
          property: 'flex-direction',
          type: 'radio',
          defaults: 'row',
          list: [{
            value: 'row',
            name: 'Row',
            className: 'icons-flex icon-dir-row',
            title: 'Row',
          }, {
            value: 'row-reverse',
            name: 'Row reverse',
            className: 'icons-flex icon-dir-row-rev',
            title: 'Row reverse',
          }, {
            value: 'column',
            name: 'Column',
            title: 'Column',
            className: 'icons-flex icon-dir-col',
          }, {
            value: 'column-reverse',
            name: 'Column reverse',
            title: 'Column reverse',
            className: 'icons-flex icon-dir-col-rev',
          }],
        }, {
          name: 'Justify',
          property: 'justify-content',
          type: 'radio',
          defaults: 'flex-start',
          list: [{
            value: 'flex-start',
            className: 'icons-flex icon-just-start',
            title: 'Start',
          }, {
            value: 'flex-end',
            title: 'End',
            className: 'icons-flex icon-just-end',
          }, {
            value: 'space-between',
            title: 'Space between',
            className: 'icons-flex icon-just-sp-bet',
          }, {
            value: 'space-around',
            title: 'Space around',
            className: 'icons-flex icon-just-sp-ar',
          }, {
            value: 'center',
            title: 'Center',
            className: 'icons-flex icon-just-sp-cent',
          }],
        }, {
          name: 'Align',
          property: 'align-items',
          type: 'radio',
          defaults: 'center',
          list: [{
            value: 'flex-start',
            title: 'Start',
            className: 'icons-flex icon-al-start',
          }, {
            value: 'flex-end',
            title: 'End',
            className: 'icons-flex icon-al-end',
          }, {
            value: 'stretch',
            title: 'Stretch',
            className: 'icons-flex icon-al-str',
          }, {
            value: 'center',
            title: 'Center',
            className: 'icons-flex icon-al-center',
          }],
        }, {
          name: 'Flex Children',
          property: 'label-parent-flex',
          type: 'integer',
        }, {
          name: 'Order',
          property: 'order',
          type: 'integer',
          defaults: 0,
          min: 0
        }, {
          name: 'Flex',
          property: 'flex',
          type: 'composite',
          properties: [{
            name: 'Grow',
            property: 'flex-grow',
            type: 'integer',
            defaults: 0,
            min: 0
          }, {
            name: 'Shrink',
            property: 'flex-shrink',
            type: 'integer',
            defaults: 0,
            min: 0
          }, {
            name: 'Basis',
            property: 'flex-basis',
            type: 'integer',
            units: ['px', '%', ''],
            unit: '',
            defaults: 'auto',
          }],
        }, {
          name: 'Align',
          property: 'align-self',
          type: 'radio',
          defaults: 'auto',
          list: [{
            value: 'auto',
            name: 'Auto',
          }, {
            value: 'flex-start',
            title: 'Start',
            className: 'icons-flex icon-al-start',
          }, {
            value: 'flex-end',
            title: 'End',
            className: 'icons-flex icon-al-end',
          }, {
            value: 'stretch',
            title: 'Stretch',
            className: 'icons-flex icon-al-str',
          }, {
            value: 'center',
            title: 'Center',
            className: 'icons-flex icon-al-center',
          }],
        }]
      }
      ],
    },
    plugins: [
      'gjs-blocks-basic',
      'grapesjs-plugin-forms',
      'grapesjs-component-countdown',
      'grapesjs-plugin-export',
      'grapesjs-tabs',
      'grapesjs-custom-code',
      'grapesjs-touch',
      'grapesjs-parser-postcss',
      'grapesjs-tooltip',
      'grapesjs-tui-image-editor',
      'grapesjs-typed',
      'grapesjs-style-bg',
      'grapesjs-preset-webpage',
    ],
    pluginsOpts: {
      'gjs-blocks-basic': { flexGrid: true },
      'grapesjs-tui-image-editor': {
        script: [
          // 'https://cdnjs.cloudflare.com/ajax/libs/fabric.js/1.6.7/fabric.min.js',
          'https://uicdn.toast.com/tui.code-snippet/v1.5.2/tui-code-snippet.min.js',
          'https://uicdn.toast.com/tui-color-picker/v2.2.7/tui-color-picker.min.js',
          'https://uicdn.toast.com/tui-image-editor/v3.15.2/tui-image-editor.min.js'
        ],
        style: [
          'https://uicdn.toast.com/tui-color-picker/v2.2.7/tui-color-picker.min.css',
          'https://uicdn.toast.com/tui-image-editor/v3.15.2/tui-image-editor.min.css',
        ],
      },
      'grapesjs-tabs': {
        tabsBlock: { category: 'Extra' }
      },
      'grapesjs-typed': {
        block: {
          category: 'Extra',
          content: {
            type: 'typed',
            'type-speed': 40,
            strings: [
              'Text row one',
              'Text row two',
              'Text row three',
            ],
          }
        }
      },
      'grapesjs-preset-webpage': {
        modalImportTitle: 'Import Template',
        modalImportLabel: '<div style="margin-bottom: 10px; font-size: 13px;">Paste here your HTML/CSS and click Import</div>',
        modalImportContent: function (editor) {
          return editor.getHtml() + '<style>' + editor.getCss() + '</style>'
        },
      },
    },
  });

  window.editor = editor;
</script>

</html>
