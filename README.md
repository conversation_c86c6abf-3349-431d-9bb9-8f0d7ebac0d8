# Pilot Project

这是一个包含前后端的完整项目，使用Git Submodules管理前后端代码。

## 项目结构

```
pilot/
├── pilot-api/          # 后端API (Git Submodule)
├── pilot-web/          # 前端Web (Git Submodule)
├── Makefile           # 项目构建和运行脚本
└── README.md          # 项目说明文档
```

## 快速开始

### 1. 克隆项目（包含submodules）

```bash
# 克隆主仓库并初始化submodules
git clone --recursive <主仓库URL>

# 或者先克隆主仓库，再初始化submodules
git clone <主仓库URL>
cd pilot
git submodule update --init --recursive
```

### 2. 运行项目

```bash
# 同时启动前后端
make run
```

## Git Submodules 管理

### 更新submodules到最新版本

```bash
# 更新所有submodules到最新commit
git submodule update --remote

# 更新特定submodule
git submodule update --remote pilot-api
git submodule update --remote pilot-web
```

### 在submodule中进行开发

```bash
# 进入submodule目录
cd pilot-api

# 切换到开发分支
git checkout main

# 进行开发...
git add .
git commit -m "your changes"
git push

# 回到主仓库，提交submodule的更新
cd ..
git add pilot-api
git commit -m "Update pilot-api to latest version"
```

### 查看submodules状态

```bash
git submodule status
```

## 开发指南

- **后端开发**: 进入 `pilot-api/` 目录进行开发
- **前端开发**: 进入 `pilot-web/` 目录进行开发
- **主仓库**: 用于管理整体项目配置、部署脚本等

## 注意事项

1. 每次拉取主仓库代码后，记得更新submodules：`git submodule update --init --recursive`
2. 在submodule中开发时，确保在正确的分支上工作
3. 提交submodule更改后，需要在主仓库中也提交submodule的引用更新
