# Pilot

服务治理前端应用

## 开发规范

详见[前端项目开发模式](https://makeblock.feishu.cn/docs/doccn0229h8Y0NufVTd3sKuvEff)

## 调试流程

因为本项目使用 passport 做鉴权，需要使用 makeblock.com 域名来传递 token，所以本地调试的时候需要绑定一个域名来访问。

比如：绑定 `127.0.0.1 local.makeblock.com`，那么调试的时候就用 `http://local.makeblock.com:3001` 访问。

1. 下载基座项目运行

```bash
<NAME_EMAIL>:makeblock-devops/devops-web/main.git && cd main && npm i && pnpm run localhost
```

2. 下载本项目运行

```bash
<NAME_EMAIL>:makeblock-devops/devops-web/pilot.git && cd main && npm i && pnpm run localhost
```

# 技术栈

-   框架: Vue 3
-   语言: TypeScript
-   构建工具: Vite
-   **UI 库: Ant Design Vue**
-   状态管理: Vuex
-   代码风格: Composition API (`<script setup>`)