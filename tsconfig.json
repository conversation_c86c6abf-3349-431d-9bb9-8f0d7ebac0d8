{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": false, "suppressImplicitAnyIndexErrors": true, "ignoreDeprecations": "5.0", "noUnusedLocals": true, "outDir": "./dist", "baseUrl": "./", "allowJs": true, "paths": {"@/*": ["./src/*"], "@c/*": ["./src/components/*"]}, "types": ["vite/client", "jest", "node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts"], "exclude": ["node_modules/**"]}