{"extends": ["stylelint-config-standard", "stylelint-config-recommended-vue", "stylelint-config-recess-order"], "plugins": ["stylelint-order", "stylelint-declaration-block-no-ignored-properties"], "ignoreFiles": ["node_modules/**/*", "build/**/*"], "rules": {"order/order": ["declarations", "custom-properties", "dollar-variables", "rules", "at-rules"], "no-empty-source": null, "property-no-vendor-prefix": [true, {"ignoreProperties": ["background-clip"]}], "number-leading-zero": null, "number-no-trailing-zeros": true, "length-zero-no-unit": true, "value-list-comma-newline-after": null, "value-list-max-empty-lines": 0, "shorthand-property-no-redundant-values": true, "declaration-block-no-duplicate-properties": true, "declaration-block-no-redundant-longhand-properties": true, "block-closing-brace-newline-after": "always", "media-feature-colon-space-after": "always", "media-feature-range-operator-space-after": "always", "at-rule-name-space-after": "always", "indentation": 2, "no-eol-whitespace": true, "string-no-newline": null, "declaration-block-trailing-semicolon": null, "comment-empty-line-before": null, "declaration-empty-line-before": null, "function-name-case": "lower", "no-descending-specificity": null, "no-invalid-double-slash-comments": null, "rule-empty-line-before": ["always", {"except": ["first-nested"]}], "declaration-colon-newline-after": null, "declaration-colon-space-after": "always-single-line", "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}]}}