import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

import { errorHandler, requestInterceptor } from '@/utils/axios';
import { appUtil } from '@/utils/app';
import { ResponseMessage } from '@/types/response';
import { message } from 'ant-design-vue';
import NProgress from 'nprogress';

const createpilotRequest = (config: AxiosRequestConfig): AxiosInstance => {
  const request = axios.create(config);
  request.interceptors.request.use(requestInterceptor, errorHandler);
  request.interceptors.response.use(pilotResponseInterceptor, errorHandler);
  return request;
};

function pilotResponseInterceptor(
  response: AxiosResponse,
): AxiosResponse | Promise<AxiosResponse> {
  NProgress.done();
  if (response.config.responseType === 'blob') {
    // 下载文件
    return response.data;
  } else if (0 === response.data.code) {
    return response.data;
  } else if (20101 === response.data.code) {
    appUtil.goToLogin();
    return Promise.reject({ message: '登录失效' });
  } else if (response.data.code === 20102) {
    return Promise.reject({ message: '没有该权限' });
  } else {
    if (ResponseMessage[response.data.code as keyof typeof ResponseMessage]) {
      message
        .info(
          ResponseMessage[response.data.code as keyof typeof ResponseMessage],
        )
        .then((r) => r);
      // pilot, 规定90534状态码以上的错误可以直接返回错误原因给用户
    } else if (response?.data?.code >= 90534) {
      message.error(response.data.message).then((r) => r);
    }
    return Promise.reject(response);
  }
}

export const pilotRequest = createpilotRequest({
  baseURL: import.meta.env.VITE_PILOT_BASE_URL as string,
  timeout: 60000,
});
