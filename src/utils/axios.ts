import { ResponseMessage } from '@/types/response';
import { appUtil } from '@/utils/app';
import { message } from 'ant-design-vue';
import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import axios from 'axios';
import NProgress from 'nprogress';

const requests: Record<string, AxiosInstance> = {};

export const createRequest = (
  key: string,
  config: AxiosRequestConfig,
): AxiosInstance => {
  const request = axios.create(config);
  request.interceptors.request.use(requestInterceptor, errorHandler);
  request.interceptors.response.use(responseInterceptor, errorHandler);
  requests[key] = request;
  return request;
};

export const request = createRequest('default', {
  baseURL: import.meta.env.VITE_REQUEST_BASE_URL as string,
  timeout: 20000,
});

export function getRequest(key: string): AxiosInstance {
  if (requests && requests[key]) {
    return requests[key];
  }
  return request;
}

/**
 * @desc: 异常拦截处理器
 * @param { Object } error 错误信息
 */
export function errorHandler(
  error: AxiosError,
): AxiosError | Promise<AxiosError> {
  message.error(error.message);
  return Promise.reject(error);
}

export function requestInterceptor(
  config: InternalAxiosRequestConfig,
): InternalAxiosRequestConfig {
  // 是否忽略进度条
  if (!config.headers['X-Ignore-Progress']) {
    NProgress.start();
  }
  delete config.headers['X-Ignore-Progress'];
  const utoken = appUtil.getUToken();
  if (utoken) {
    config.headers['utoken'] = utoken;
  }
  return config;
}

function responseInterceptor(
  response: AxiosResponse,
): AxiosResponse | Promise<AxiosResponse> {
  NProgress.done();
  if (response.data.code === 0) {
    return response.data;
  } else if (response.data.code === 20101) {
    // 登录失效
    appUtil.goToLogin();
    return Promise.reject({ message: 'token 失效' });
  } else if (response.data.code === 20102) {
    // 用户无权限
    // appUtil.goTo403(location.href);
    return Promise.reject({ message: '没有该权限' });
  } else {
    if (response.data.code === 10000) {
      message.error(response.data.message);
    } else if (
      ResponseMessage[response.data.code as keyof typeof ResponseMessage]
    ) {
      message.info(
        ResponseMessage[response.data.code as keyof typeof ResponseMessage],
      );
    }
    return response.data;
  }
}
