import { getPermissions } from '@/api/system/permission';
import router from '@/router/index';
import { PermissionKey } from '@/types/permissionKey';
import PassportClient, {
  AREA,
  CLIENT_ENV,
  DOMAIN_SPACE,
  PAGE_MODE,
  XTOOL_PLATFORM,
} from '@makeblock/passport-client';
import type { RouteLocationRaw } from 'vue-router';
import { setCookie } from '@/utils/cookie';
import store from '@/store';

interface AppUtil {
  getUToken: () => string;
  getUrl: (path: string) => string;
  getUserInfo: () => Promise<any>;
  getPermissions: () => Promise<any>;
  logout: () => Promise<any>;
  goToLogin: () => void;
  goToLoginOrigin: () => void;

  goToHome: () => void;
  goTo403: (redirect: string) => void;
  goToPage: (name: string) => void;
  goToRoute: (to: RouteLocationRaw) => void;
  openLoginModal: () => void;
}

const client = new PassportClient({
  domainSpace: DOMAIN_SPACE.XTOOL,
  platform: XTOOL_PLATFORM.PASSPORT,
  mode: PAGE_MODE.WIN,
  lang: 'en',
  area: AREA.US,
});

if (import.meta.env.VITE_PASSPORT_ENV === 'prod') {
  client.env = CLIENT_ENV.PROD;
  client.lang = 'zh';
  client.area = AREA.CN;
  client.domainSpace = DOMAIN_SPACE.MAKEBLOCK;
} else if (import.meta.env.VITE_PASSPORT_ENV === 'prod-us') {
  client.env = CLIENT_ENV.PROD;
  client.lang = 'en';
  client.area = AREA.US;
  client.domainSpace = DOMAIN_SPACE.XTOOL;
} else if (import.meta.env.VITE_PASSPORT_ENV === 'prod-eu') {
  client.env = CLIENT_ENV.PROD;
  client.lang = 'en';
  client.area = AREA.EU;
  client.domainSpace = DOMAIN_SPACE.MAKEBLOCK;
} else if (import.meta.env.VITE_PASSPORT_ENV === 'test') {
  client.env = CLIENT_ENV.TEST;
  client.lang = 'zh';
  client.area = AREA.CN;
  client.domainSpace = DOMAIN_SPACE.MAKEBLOCK;
} else if (import.meta.env.VITE_PASSPORT_ENV === 'testus') {
  client.env = CLIENT_ENV.TEST;
  client.lang = 'en';
  client.area = AREA.US;
  client.domainSpace = DOMAIN_SPACE.XTOOL;
} else {
  client.env = CLIENT_ENV.DEV;
  client.lang = 'zh';
  client.area = AREA.CN;
  client.domainSpace = DOMAIN_SPACE.MAKEBLOCK;
}

export const appUtil: AppUtil = {
  getUToken: () => {
    return client.token;
  },
  getUrl: (path: string) => {
    return client.getUrl(path);
  },
  getUserInfo: () => {
    return client.userInfo();
  },
  getPermissions: () => {
    return getPermissions();
  },
  logout: () => {
    return client.logout();
  },
  goToLogin: () => {
    client.token = '';
    client.redirect = location.href;
    // window.location.href = client.url;
    window.parent.location.href = '/login?redirect=' + location.href;
    // router
    //   .push({
    //     path: '/login',
    //     query: { redirect: location.href },
    //   })
    //   .then(() => {
    //     window.location.reload();
    //   });
  },
  goToLoginOrigin: () => {
    client.token = '';
    client.redirect = window.location.origin; // 设置重定向URL为首页
    window.parent.location.href = '/login';
    // window.location.href = client.url;
    // router.push({ name: PermissionKey.LOGIN }).then(() => {
    //   window.location.reload();
    // });
  },
  goToHome: () => {
    router.push({ name: PermissionKey.HOME });
  },
  goTo403: (redirect: string) => {
    // router.push({ name: PermissionKey.ERROR403, query: { redirect } });
    window.parent.location.href = '/403?redirect=' + redirect || location.href;
  },
  goToPage: (name: string) => {
    // 添加防抖处理，避免快速点击导致的路由冲突
    try {
      router.push({ name: name }).catch((err) => {
        // 捕获路由导航错误，避免未处理的Promise rejection
        if (err.name !== 'NavigationDuplicated') {
          console.warn('Route navigation error:', err);
        }
      });
    } catch (error) {
      console.error('goToPage error:', error);
    }
  },
  goToRoute: (to: RouteLocationRaw) => {
    router.push(to);
  },
  openLoginModal: () => {
    client.openModal(function (data) {
      setCookie('utoken', data.token || '', { expires: 7 });
      // 如果跳转地址为空则跳转到首页
      const params: any = new Proxy(
        new URLSearchParams(window.location.hash.replace(/^#.*?\?/, '')),
        {
          get: (searchParams, prop: string) => searchParams.get(prop),
        },
      );
      // 刷新权限
      store.dispatch('app/permissions').then(() => {
        window.location.href = params.redirect || window.location.origin;
      });
    });
  },
};
