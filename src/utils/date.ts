import dayjs from 'dayjs';
import moment from 'moment';

export function dateRange(days: number = 7) {
  const gte = new Date();
  gte.setDate(gte.getDate() - days);
  const gteMoment = dayjs(gte);
  const ltMoment = dayjs(new Date());
  return [gteMoment, ltMoment];
}

export function toTimestamp(date: string): number {
  const dt = new Date(date).getTime();
  return Math.floor(dt / 1000);
}

export function daysBeforeTimestamp(days: number): number {
  const date = new Date();
  date.setDate(date.getDate() - days);
  const dt = date.getTime();
  return Math.floor(dt / 1000);
}

export function daysBeforeDateString(days: number): string {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date.toLocaleDateString();
}

export function nowTimestamp(): number {
  const date = new Date();
  const dt = date.getTime();
  return Math.floor(dt / 1000);
}

export function nowDateString(): string {
  const date = new Date();
  return date.toLocaleDateString();
}

export function formatContainMilliseconds(value: string | number | Date) {
  const d = new Date(value);
  return formatContainSeconds(value) + ':' + d.getMilliseconds();
}

export function formatContainSeconds(value: string | number | Date) {
  const d = new Date(value);
  return d.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
  });
}

export const timeFormat = (timeStamp: number, format?: string) => {
  if (!timeStamp) {
    return '';
  }
  if (!format) {
    format = 'YYYY-MM-DD HH:mm:ss';
  }
  const timeStampMs =
    timeStamp.toString().length === 10 ? timeStamp * 1000 : timeStamp;
  return moment(timeStampMs).format(format);
};

export const formatTimestamp = (
  t: number,
  format: string = 'YYYY-MM-DD HH:mm:ss',
) => {
  return dayjs(t).format(format);
};
