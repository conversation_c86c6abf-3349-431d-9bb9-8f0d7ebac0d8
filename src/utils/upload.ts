import { appUtil } from '@/utils/app';
import type { UploadConfig, UploadOptions } from '@makeblock/upload';
import { Uploader } from '@makeblock/upload';
import SparkMD5 from 'spark-md5';

const BASE_URL = import.meta.env.VITE_REQUEST_BASE_URL as string;

const uploader = new Uploader(BASE_URL);

export function getToken(configName: string) {
  return uploader.getToken(BASE_URL, appUtil.getUToken(), configName);
}

export function uploadWithConfig(
  config: UploadConfig,
  file: any,
  toPath: string,
  option: UploadOptions,
) {
  return uploader.uploadWithConfig(config, file, toPath, option);
}

export function upload(
  key: string,
  file: any,
  toPath: string,
  option: UploadOptions,
) {
  return uploader.upload(appUtil.getUToken(), key, file, toPath, option);
}

export function fileMd5(
  file: any,
  onSuccess?: (md5: string) => any,
  onError?: (error: any) => any,
) {
  const blobSlice = File.prototype.slice;
  const chunkSize = 2097152; // Read in chunks of 2MB
  const chunks = Math.ceil(file.size / chunkSize);
  let currentChunk = 0;
  const spark = new SparkMD5.ArrayBuffer();
  const fileReader = new FileReader();

  fileReader.onload = function (e: ProgressEvent<FileReader>) {
    console.log('read chunk nr', currentChunk + 1, 'of', chunks);
    spark.append(e.target.result as ArrayBuffer); // Append array buffer
    currentChunk++;

    if (currentChunk < chunks) {
      loadNext();
    } else {
      const md5 = spark.end();
      console.log('computed hash', md5); // Compute hash
      onSuccess && onSuccess(md5);
    }
  };

  fileReader.onerror = function (e: ProgressEvent<FileReader>) {
    console.warn('oops, something went wrong.');
    onError && onError(e.target.error);
  };

  function loadNext() {
    const start = currentChunk * chunkSize;
    const end = start + chunkSize >= file.size ? file.size : start + chunkSize;
    fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
  }

  loadNext();
}
