import { createVNode, render } from 'vue';
import { Button } from 'ant-design-vue';
import { FileTextOutlined } from '@ant-design/icons-vue';

export function renderNavbar() {
  const navbarContainer = document.getElementById('main-header-ext');
  if (!navbarContainer) {
    console.error('navbar container not found');
    return;
  }
  // 渲染空的 VNode 到容器中，卸载之前的组件
  navbarContainer.innerHTML = '';
  render(createVNode({}), navbarContainer);

  // 创建带有文档图标的按钮
  const DocButtonVNode = createVNode(
    Button,
    {
      size: 'small',
      type: 'link',
      style: { color: '#71777c' },
      icon: createVNode(FileTextOutlined),
      onClick: () => {
        window.open(
          'https://makeblock.feishu.cn/docx/CB9QdADlmozgGbx4i2Gc0A3AnJc',
          '_blank',
        );
      },
    },
    { default: () => '使用文档' },
  );

  render(DocButtonVNode, navbarContainer);
}
