export function openURL(url: string) {
  const a = document.createElement('a');
  a.setAttribute('href', url);
  a.setAttribute('target', '_blank');
  document.body.appendChild(a);
  a.click();
}

export function copyToClipboard(value: string | string[]): void {
  let data = '';
  if (value instanceof Array) {
    for (let index = 0; index < value.length; index++) {
      const element = value[index];
      if (index === value.length - 1) {
        data += element;
      } else {
        data += element + '\n';
      }
    }
  } else {
    data = value;
  }

  const listener = (e: ClipboardEvent) => {
    e.clipboardData.setData('text/plain', data);
    e.preventDefault();
    document.removeEventListener('copy', listener);
  };
  document.addEventListener('copy', listener);
  document.execCommand('copy');
}

export function downloadFile(data: string, name: string) {
  const a = document.createElement('a');

  a.download = name;
  a.style.display = 'none';

  const blob = new Blob([data]);

  a.href = URL.createObjectURL(blob);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

export function getFileExt(fileName: string): string {
  return fileName.substring(fileName.lastIndexOf('.'));
}

export const fileSizeFormat = (fileSize: number) => {
  if (!fileSize) {
    return '';
  }
  if (fileSize >= 1000 * 1000) {
    return (fileSize / 1000 / 1000).toFixed(1) + ' MB';
  } else if (fileSize >= 1000) {
    return (fileSize / 1000).toFixed(1) + ' KB';
  } else {
    return fileSize.toString() + ' B';
  }
};

export const isInIframe = window.self !== window.parent;
// 获取当前年份
export const getCurrentYear = () => {
  return new Date().getFullYear();
};
