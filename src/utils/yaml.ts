// @ts-ignore
import * as yaml from 'js-yaml';

// 解析yaml成json数据格式
export function parseYamlToJSON(yamlStr: string): any {
  try {
    return yaml.load(yamlStr);
  } catch (e) {
    console.error(e);
    return null;
  }
}

// 将JSON数据转换为YAML格式
export function stringifyYaml(data: any): string {
  try {
    return yaml.dump(data, {
      indent: 2,
      lineWidth: -1,
      noRefs: true,
      quotingType: '"',
    });
  } catch (e) {
    console.error(e);
    return '';
  }
}

// 将安全规则转换为Istio AuthorizationPolicy YAML
export function generateAuthorizationPolicyYaml(rule: any): string {
  const policy: any = {
    apiVersion: 'security.istio.io/v1beta1',
    kind: 'AuthorizationPolicy',
    metadata: {
      name: rule.name,
      namespace: rule.namespace || 'default',
    },
    spec: {},
  };

  // 根据规则类型设置action
  if (rule.type === 'blacklist') {
    policy.spec.action = 'DENY';
  } else if (rule.type === 'whitelist') {
    policy.spec.action = 'ALLOW';
  }

  // 构建规则
  const ruleSpec: any = {};

  // 来源配置
  if (rule.rules.ipBlocks?.length > 0 || rule.rules.principals?.length > 0) {
    ruleSpec.from = [
      {
        source: {},
      },
    ];

    if (rule.rules.ipBlocks?.length > 0) {
      ruleSpec.from[0].source.ipBlocks = rule.rules.ipBlocks;
    }

    if (rule.rules.principals?.length > 0) {
      ruleSpec.from[0].source.principals = rule.rules.principals;
    }
  }

  // 目标配置
  if (rule.rules.methods?.length > 0 || rule.rules.paths?.length > 0) {
    ruleSpec.to = [
      {
        operation: {},
      },
    ];

    if (rule.rules.methods?.length > 0) {
      ruleSpec.to[0].operation.methods = rule.rules.methods;
    }

    if (rule.rules.paths?.length > 0) {
      ruleSpec.to[0].operation.paths = rule.rules.paths;
    }
  }

  if (Object.keys(ruleSpec).length > 0) {
    policy.spec.rules = [ruleSpec];
  }

  return stringifyYaml(policy);
}

// 将限流规则转换为Istio EnvoyFilter YAML
export function generateRateLimitYaml(rule: any): string {
  if (!rule.rules.rateLimit) {
    throw new Error('限流配置不存在');
  }

  const rateLimit = rule.rules.rateLimit;

  const envoyFilter: any = {
    apiVersion: 'networking.istio.io/v1alpha3',
    kind: 'EnvoyFilter',
    metadata: {
      name: `${rule.name}-ratelimit`,
      namespace: rule.namespace || 'default',
    },
    spec: {
      configPatches: [
        {
          applyTo: 'HTTP_FILTER',
          match: {
            context: 'SIDECAR_INBOUND',
            listener: {
              filterChain: {
                filter: {
                  name: 'envoy.filters.network.http_connection_manager',
                },
              },
            },
          },
          patch: {
            operation: 'INSERT_BEFORE',
            value: {
              name: 'envoy.filters.http.local_ratelimit',
              typed_config: {
                '@type': 'type.googleapis.com/udpa.type.v1.TypedStruct',
                type_url:
                  'type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit',
                value: {
                  stat_prefix: 'local_rate_limiter',
                  token_bucket: {
                    max_tokens:
                      rateLimit.descriptors[0]?.rate_limit?.requests_per_unit ||
                      100,
                    tokens_per_fill:
                      rateLimit.descriptors[0]?.rate_limit?.requests_per_unit ||
                      100,
                    fill_interval: getTimeInterval(
                      rateLimit.descriptors[0]?.rate_limit?.unit || 'MINUTE',
                    ),
                  },
                  filter_enabled: {
                    runtime_key: 'local_rate_limit_enabled',
                    default_value: {
                      numerator: 100,
                      denominator: 'HUNDRED',
                    },
                  },
                  filter_enforced: {
                    runtime_key: 'local_rate_limit_enforced',
                    default_value: {
                      numerator: 100,
                      denominator: 'HUNDRED',
                    },
                  },
                },
              },
            },
          },
        },
      ],
    },
  };

  return stringifyYaml(envoyFilter);
}

// 将时间单位转换为秒数
function getTimeInterval(unit: string): string {
  switch (unit) {
    case 'SECOND':
      return '1s';
    case 'MINUTE':
      return '60s';
    case 'HOUR':
      return '3600s';
    case 'DAY':
      return '86400s';
    default:
      return '60s';
  }
}

export function dump(obj: any): string {
  return yaml.dump(obj);
}
export function clearDump(obj: any): string {
  removeEmpty(obj);
  return yaml.dump(obj, { lineWidth: -1 });
}

// 移除一些没有值的字段或者对象或者数组，在进行转为yaml时
function removeEmpty(obj: any) {
  Object.keys(obj).forEach((key) => {
    if (obj[key] && typeof obj[key] === 'object') {
      removeEmpty(obj[key]);
      if (
        (Array.isArray(obj[key]) && obj[key].length === 0) ||
        (typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0)
      ) {
        delete obj[key];
      }
    } else if (
      obj[key] === null ||
      obj[key] === '' ||
      obj[key] === false || // boolean类型的false也要删除
      (Array.isArray(obj[key]) && obj[key].length === 0)
    ) {
      delete obj[key];
    }
  });
}

/**
 * 清理 Kubernetes YAML 中的 metadata 信息
 * 移除系统自动生成的字段，保留用户关心的配置
 */

export function cleanKubernetesMetadata(yamlContent: string): string {
  console.log('开始清理 YAML metadata，原始内容长度:', yamlContent.length);

  try {
    // 解析 YAML
    const doc: any = yaml.load(yamlContent);

    if (!doc || typeof doc !== 'object') {
      console.log('YAML 解析失败或不是对象');
      return yamlContent;
    }

    // 清理 metadata 中的系统生成字段
    if (doc.metadata) {
      console.log('找到 metadata，开始清理系统字段');
      // 移除系统生成的字段
      delete doc.metadata.creationTimestamp;
      delete doc.metadata.generation;
      delete doc.metadata.managedFields;
      delete doc.metadata.resourceVersion;
      delete doc.metadata.uid;
      delete doc.metadata.labels;
      delete doc.metadata.annotations;

      console.log('清理后的 metadata:', doc.metadata);
    } else {
      console.log('未找到 metadata');
    }

    // 清理 status
    if (doc.status) {
      console.log('找到 status，开始清理');
      delete doc.status;
    }

    // 重新序列化为 YAML
    const cleanedYaml = yaml.dump(doc, {
      indent: 2,
      lineWidth: -1, // 不限制行宽
      noRefs: true, // 不处理引用
    });

    return cleanedYaml;
  } catch (error) {
    // 如果清理失败，返回原始内容
    return yamlContent;
  }
}

/**
 * 检查 YAML 是否包含系统生成的 metadata
 */
export function hasSystemMetadata(yamlContent: string): boolean {
  try {
    const doc: any = yaml.load(yamlContent);

    if (!doc || typeof doc !== 'object' || !doc.metadata) {
      return false;
    }

    const metadata = doc.metadata;
    return !!(
      metadata.creationTimestamp ||
      metadata.generation ||
      metadata.managedFields ||
      metadata.resourceVersion ||
      metadata.uid
    );
  } catch (error) {
    console.error('检查 YAML metadata 失败:', error);
    return false;
  }
}

/**
 * 格式化 YAML 内容
 */
export function formatYAML(yamlContent: string): string {
  try {
    const doc = yaml.load(yamlContent);

    return yaml.dump(doc, {
      indent: 2,
      lineWidth: -1,
      noRefs: true,
    });
  } catch (error) {
    console.error('格式化 YAML 失败:', error);
    return yamlContent;
  }
}
