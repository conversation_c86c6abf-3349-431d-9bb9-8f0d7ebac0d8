/**
 * 存储localStorage
 */
export const setStore = (name: string, content: any) => {
  if (!name) {
    return;
  }
  if (typeof content !== 'string') {
    content = JSON.stringify(content);
  }
  window.localStorage.setItem(name, content);
};

/**
 * 获取localStorage
 */
export const getStore = (name: string) => {
  if (!name) {
    return;
  }
  const content = window.localStorage.getItem(name);
  return JSON.parse(content);
};

/**
 * 删除localStorage
 */
export const removeStore = (name: string) => {
  if (!name) {
    return;
  }
  window.localStorage.removeItem(name);
};
