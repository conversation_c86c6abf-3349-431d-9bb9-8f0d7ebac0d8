<template>
  <div>
    <div
      id="monacoEditorDiff"
      ref="editorDiffRef"
      class="monaco-editor"
      :style="{ height: height }"
    ></div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount } from 'vue';
import debounce from 'lodash/debounce';
import * as monaco from 'monaco-editor';
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';

// 定义从父组件接收的属性
const props = defineProps({
  options: {
    type: Object,
    default: () => ({
      theme: 'dark',
    }),
  },
  oldValue: {
    type: String,
    default: '',
  },
  newValue: {
    type: String,
    default: '',
  },
  // 需要高亮的关键词
  highLightWords: {
    type: Array,
    default: () => [],
  },
  // 自定义高度
  height: {
    type: String,
    default: 'calc(100vh - 185px)',
  },
});
const emits = defineEmits(['newYamlCode']);
// 解决 Monaco Editor 无法正确加载其所需的 Web Worker
self.MonacoEnvironment = {
  getWorker(workerId, label) {
    console.log(workerId, label);
    return new editorWorker();
  },
};
let editor;
let originalModel;
let modifiedModel;
const init = () => {
  editor = monaco.editor.createDiffEditor(
    document.getElementById('monacoEditorDiff'),
    {
      value: '', // 值
      autoIndex: true, // 控制是否开启自动索引。当开启时，编辑器会自动创建索引以加速搜索和语义高亮。
      fontSize: 12, // 字体大小
      language: 'yaml', // 语言
      theme: props.options.theme, // 主题
      readOnly: true, // 是否只读
      overviewRulerBorder: false, // 滚动是否有边框
      cursorSmoothCaretAnimation: true, // 控制光标平滑动画的开启与关闭。当开启时，光标移动会有平滑的动画效果。
      mouseWheelZoom: true, // 设置是否开启鼠标滚轮缩放功能
      folding: true, // 控制是否开启代码折叠功能
      automaticLayout: true, // 控制编辑器是否自动调整布局以适应容器大小的变化
      minimap: {
        // 关闭代码缩略图
        enabled: true, // 是否启用预览图
      },
      scrollbar: {
        verticalScrollbarSize: 2, // 垂直滚动条宽度，默认px
        horizontalScrollbarSize: 2, // 水平滚动条高度
      },
      wordWrap: 'on', // 开启自动换行
      roundedSelection: true, // 右侧不显示编辑器预览框
      originalEditable: false, // 是否允许修改原始文本
      ...props.options,
    },
  );

  // 创建左右编辑器
  originalModel = monaco.editor.createModel(props.oldValue, 'yaml');
  modifiedModel = monaco.editor.createModel(props.newValue, 'yaml');
  editor.setModel({
    original: originalModel,
    modified: modifiedModel,
  });

  // 原始模型的内容发生变化时触发的操作
  originalModel.onDidChangeContent(() => {
    // 获取修改后的文本
    // const modifiedText = modifiedModel.getValue();

    // 获取原始文本
    const OriginalText = originalModel.getValue();
    // 内容发生变化 需要重新设置高亮
    setHighlight();
    emits('newYamlCode', OriginalText);
  });

  // 修改后模型的内容发生变化时触发的操作
  modifiedModel.onDidChangeContent(() => {
    // 获取修改后的文本
    const modifiedText = modifiedModel.getValue();
    // 发出修改后的内容
    emits('newYamlCode', modifiedText);
  });
  removeArrow();
};
/**
 * 移除 revert change（还原更改）箭头
 */
const removeArrow = () => {
  setTimeout(() => {
    const revertChangeButtons = document.querySelectorAll(
      '.codicon-arrow-right',
    );
    revertChangeButtons.forEach((button) => {
      button.style.display = 'none';
    });
  }, 500);
};
/**
 * 设置自定义高亮
 */
let observer = null;
let originalEditorContainer = null;

// 挂载高亮观察器
const setLightObserver = () => {
  // 有关键字的情况下 才需要高亮
  if (props.highLightWords.length > 0) {
    // 获取 Diff Editor 中原始编辑器的容器节点
    originalEditorContainer = editor.getOriginalEditor().getDomNode();
    // 获取第一个子节点
    const originalSonElement =
      originalEditorContainer && originalEditorContainer.firstChild;
    // 获取第一个子节点的第一个子节点
    const originalGrandsonElement =
      originalSonElement && originalSonElement.firstChild;

    // 创建一个 MutationObserver 实例
    observer = new MutationObserver(() => {
      setHighlight();
    });

    // 配置观察器以监视特定类型的变化
    if (originalSonElement) {
      // 监视 originalSonElement 的样式变化，如果发生变化重新设置高亮(用于页面放大缩小时)
      observer.observe(originalSonElement, {
        attributes: true, // 监视属性的变化
        attributeFilter: ['style'], // 监视特定属性
      });
      // 监视 originalGrandsonElement 的样式变化，如果发生变化重新设置高亮(用于编辑器放大缩小滚动时)
      observer.observe(originalGrandsonElement, {
        attributes: true, // 监视属性的变化
        attributeFilter: ['style'], // 监视特定属性
      });
    }
  }
};

// 使用防抖，避免多次渲染黄色背景
const setHighlight = debounce(() => {
  const linesContainer = originalEditorContainer.querySelector('.view-lines');
  // 获取所有行的容器节点
  const divLines = linesContainer.childNodes;
  // 遍历每一行的容器节点
  divLines.forEach((lineNode) => {
    // 拿到每一行的总span节点
    const spanNodes = lineNode.childNodes;
    // 拿到每一行的总span节点内容
    let originalSpanHtml = spanNodes[0].innerHTML;
    // 存储匹配到的关键字
    let successKeyWord = [];
    // 检查 span 元素的文本内容是否包含了 props.highLightWords 数组中的一个或多个关键字
    props.highLightWords.forEach((highlightWord) => {
      // 创建正则表达式进行全局、忽略大小写匹配
      const regex = new RegExp(highlightWord, 'gi');
      // 如果匹配到，则将匹配成功的关键字存储起来
      if (regex.test(originalSpanHtml)) {
        successKeyWord.push(highlightWord);
      }
    });
    // 对 successKeyWord 数组进行去重操作
    successKeyWord = Array.from(new Set(successKeyWord));

    // 如果有任意一个关键字包含在 span 元素的文本内容中，则修改对应关键字的样式，而不是修改整个span
    if (successKeyWord.length > 0) {
      // 创建一个新的字符串来保存修改后的结果
      let modifiedHtml = originalSpanHtml;
      // 遍历匹配成功关键字数组中的每个字符串
      successKeyWord.forEach((str) => {
        // 创建带有黄色背景色的 <span> 元素
        const highlightedText = `<span style="background-color: yellow;">${str}</span>`;
        // 使用正则表达式进行全局、忽略大小写的替换
        modifiedHtml = modifiedHtml.replace(
          new RegExp(str, 'gi'),
          highlightedText,
        );
      });

      // 将修改后的 HTML 内容设置回原来的 span 元素中
      spanNodes[0].innerHTML = modifiedHtml;
    }
  });
}, 500);

// 组件挂载后创建编辑器实例
onMounted(() => {
  // 初始化编辑器
  init();
  // 挂载高亮观察器
  setLightObserver();
});

onBeforeUnmount(() => {
  if (editor) {
    // 组件卸载前销毁编辑器实例
    editor.dispose();
    // 组件卸载时断开 MutationObserver
    if (observer) {
      observer.disconnect();
    }
  }
});
</script>

<style scoped>
.monaco-editor {
  //height: calc(100vh - 185px);
  width: 100%;
}
</style>
