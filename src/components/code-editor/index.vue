<template>
  <div class="code-editor-wrapper">
    <div class="code-editor-header">
      <div class="left-container">
        <a-select
          v-if="shells?.length > 0"
          v-model:value="shellType"
          size="small"
          class="editor-shell-type-select"
          :dropdown-class-name="'dark-dropdown'"
          :bordered="false"
          :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
          :options="shells"
          @change="handleShellTypeChange"
        >
          <template #suffixIcon></template>
        </a-select>
      </div>
      <a-button
        v-if="showExpandBtn"
        type="text"
        class="editor-expand-btn"
        @click="handleExpandClick"
      >
        <expand-outlined />
      </a-button>
    </div>
    <div
      ref="editorRef"
      class="monaco-editor-container"
      :style="mainEditorStyle"
    ></div>

    <!-- 内置抽屉编辑器 -->
    <a-drawer
      :title="label"
      :visible="drawerVisible"
      width="80%"
      placement="right"
      @close="handleDrawerClose"
      @after-visible-change="handleDrawerVisibleChange"
    >
      <template #extra>
        <a-space>
          <a-select
            v-if="shells?.length > 0"
            v-model:value="drawerShellType"
            :dropdown-class-name="'dark-dropdown'"
            :options="shells"
            @change="handleDrawerShellTypeChange"
          />
          <a-button @click="handleDrawerClose">取消</a-button>
          <a-button type="primary" @click="handleDrawerSave">保存</a-button>
        </a-space>
      </template>
      <div
        ref="drawerEditorRef"
        class="monaco-editor-container"
        :style="drawerEditorStyle"
      ></div>
    </a-drawer>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  computed,
  nextTick,
  onMounted,
  onBeforeUnmount,
} from 'vue';
import { ExpandOutlined } from '@ant-design/icons-vue';
import * as monaco from 'monaco-editor';

const shellTypeToMonacoLang: Record<string, string> = {
  sh: 'shell',
  cmd: 'shell',
  bash: 'shell',
  pwsh: 'powershell',
  powershell: 'powershell',
  python: 'python',
  yaml: 'yaml',
  javascript: 'javascript',
  js: 'javascript',
  json: 'json',
};
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';

export default defineComponent({
  name: 'CodeEditor',
  components: { ExpandOutlined },
  props: {
    modelValue: { type: String, default: '' },
    initialShellType: { type: String, default: 'bash' },
    height: { type: String, default: '150px' },
    minHeight: { type: String, default: '150px' },
    maxHeight: { type: String, default: '650px' },
    label: { type: String, default: '代码编辑' },
    readonly: { type: Boolean, default: false },
    placeholder: { type: String, default: 'write you code in here' },
    customMode: { type: String, default: '' },
    shells: {
      type: Array,
      default: () => [] as { value: string; label: string }[],
    },
    showExpandBtn: { type: Boolean, default: true },
    minimap: { type: Boolean, default: false },
  },
  emits: ['update:modelValue', 'update:initialShellType'],
  setup(props, { emit }) {
    // 主编辑器
    const editorRef = ref<HTMLElement | null>(null);
    let editorInstance: monaco.editor.IStandaloneCodeEditor | null = null;
    const shellType = ref(props.initialShellType);
    const editorContent = ref(props.modelValue);

    // 抽屉编辑器
    const drawerVisible = ref(false);
    const drawerEditorRef = ref<HTMLElement | null>(null);
    let drawerEditorInstance: monaco.editor.IStandaloneCodeEditor | null = null;
    const drawerContent = ref('');
    const drawerShellType = ref('');

    // 样式
    const mainEditorStyle = computed(() => ({
      minHeight: props.minHeight,
      maxHeight: props.maxHeight,
      height: props.height,
      border: '1px solid #44475a',
      borderTopLeftRadius: '0',
      borderTopRightRadius: '0',
    }));
    const drawerEditorStyle = {
      height: 'calc(100vh - 182px)',
      minHeight: '400px',
      width: 'calc(100% - 2px)', // -2px是为了避免默认出现滚动条
      border: '1px solid #44475a',
    };

    // 语言类型
    const getMonacoLang = (type: string) => {
      if (props.customMode) {
        return props.customMode;
      }
      return shellTypeToMonacoLang[type] || type || 'shell';
    };

    // 初始化主编辑器
    const initEditor = () => {
      if (editorInstance) {
        editorInstance.dispose();
      }
      if (!editorRef.value) {
        return;
      }
      editorInstance = monaco.editor.create(editorRef.value as HTMLElement, {
        value: editorContent.value,
        language: getMonacoLang(shellType.value),
        readOnly: props.readonly,
        theme: 'vs-dark',
        fontSize: 13,
        minimap: { enabled: props.minimap },
        automaticLayout: true,
        lineNumbers: 'on',
        wordWrap: 'on',
        scrollBeyondLastLine: false,
        overviewRulerBorder: false,
      });
      editorInstance.onDidChangeModelContent(() => {
        const val = editorInstance!.getValue();
        if (val !== editorContent.value) {
          editorContent.value = val;
        }
      });
    };

    // 初始化抽屉编辑器
    const initDrawerEditor = () => {
      if (drawerEditorInstance) {
        drawerEditorInstance.dispose();
      }
      if (!drawerEditorRef.value) {
        return;
      }
      drawerEditorInstance = monaco.editor.create(
        drawerEditorRef.value as HTMLElement,
        {
          value: drawerContent.value,
          language: getMonacoLang(drawerShellType.value),
          readOnly: props.readonly,
          theme: 'vs-dark',
          fontSize: 13,
          minimap: { enabled: false },
          automaticLayout: true,
          lineNumbers: 'on',
          wordWrap: 'on',
          scrollBeyondLastLine: false,
          overviewRulerBorder: false,
        },
      );
      drawerEditorInstance.onDidChangeModelContent(() => {
        const val = drawerEditorInstance!.getValue();
        if (val !== drawerContent.value) {
          drawerContent.value = val;
        }
      });
    };

    // 监听主内容变化，向外同步
    watch(editorContent, (val) => {
      emit('update:modelValue', val);
    });
    // 监听外部modelValue变化，更新编辑器
    watch(
      () => props.modelValue,
      (val) => {
        if (val !== editorContent.value) {
          editorContent.value = val;
          if (editorInstance && editorInstance.getValue() !== val) {
            editorInstance.setValue(val);
          }
        }
      },
    );
    // 监听shellType变化，切换语言
    watch(shellType, (val) => {
      emit('update:initialShellType', val);
      if (editorInstance) {
        const model = editorInstance.getModel();
        if (model) {
          monaco.editor.setModelLanguage(model, getMonacoLang(val));
        }
      }
    });
    // 监听readonly变化
    watch(
      () => props.readonly,
      (val) => {
        if (editorInstance) {
          editorInstance.updateOptions({ readOnly: val });
        }
        if (drawerEditorInstance) {
          drawerEditorInstance.updateOptions({ readOnly: val });
        }
      },
    );

    // 头部shell选择
    const handleShellTypeChange = (val: string) => {
      shellType.value = val;
    };

    // 展开抽屉
    const handleExpandClick = () => {
      drawerContent.value = editorContent.value;
      drawerShellType.value = shellType.value;
      drawerVisible.value = true;
      nextTick(() => {
        setTimeout(() => {
          initDrawerEditor();
        }, 0);
      });
    };
    // 抽屉关闭
    const handleDrawerClose = () => {
      drawerVisible.value = false;
      setTimeout(() => {
        if (drawerEditorInstance) {
          drawerEditorInstance.dispose();
          drawerEditorInstance = null;
        }
      }, 300);
    };
    // 抽屉保存
    const handleDrawerSave = () => {
      let latest = drawerContent.value;
      if (drawerEditorInstance) {
        latest = drawerEditorInstance.getValue();
        drawerContent.value = latest;
      }
      emit('update:modelValue', latest);
      emit('update:initialShellType', drawerShellType.value);
      editorContent.value = latest;
      shellType.value = drawerShellType.value;
      // 强制刷新主编辑器内容和语言
      if (editorInstance) {
        if (editorInstance.getValue() !== latest) {
          editorInstance.setValue(latest);
        }
        const model = editorInstance.getModel();
        if (model) {
          monaco.editor.setModelLanguage(model, getMonacoLang(shellType.value));
        }
      }
      handleDrawerClose();
    };
    // 抽屉可见性变化
    const handleDrawerVisibleChange = (visible: boolean) => {
      if (visible) {
        nextTick(() => {
          setTimeout(() => {
            initDrawerEditor();
            if (drawerEditorInstance) {
              drawerEditorInstance.focus();
            }
          }, 0);
        });
      }
    };
    // 抽屉shell选择
    const handleDrawerShellTypeChange = (val: string) => {
      drawerShellType.value = val;
      if (drawerEditorInstance) {
        const model = drawerEditorInstance.getModel();
        if (model) {
          monaco.editor.setModelLanguage(model, getMonacoLang(val));
        }
      }
    };

    onMounted(() => {
      // 解决 Monaco Editor Web Worker 问题
      self.MonacoEnvironment = {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        getWorker: function (_: string, label: string) {
          // return new Worker(
          //
          //   new URL(
          //     'monaco-editor/esm/vs/editor/editor.worker?worker',
          //     import.meta.url,
          //   ),
          //   { type: 'module' },
          // );
          return new editorWorker();
        },
      };
      nextTick(() => {
        initEditor();
      });
    });
    onBeforeUnmount(() => {
      if (editorInstance) {
        editorInstance.dispose();
      }
      if (drawerEditorInstance) {
        drawerEditorInstance.dispose();
      }
    });

    return {
      editorRef,
      mainEditorStyle,
      shellType,
      // eslint-disable-next-line vue/no-dupe-keys
      shells: props.shells,
      handleShellTypeChange,
      handleExpandClick,
      drawerVisible,
      drawerEditorRef,
      drawerEditorStyle,
      drawerContent,
      drawerShellType,
      handleDrawerClose,
      handleDrawerSave,
      handleDrawerVisibleChange,
      handleDrawerShellTypeChange,
      // eslint-disable-next-line vue/no-dupe-keys
      label: props.label,
    };
  },
});
</script>

<style scoped>
.code-editor-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.code-editor-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
  background-color: #282a36;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border: 1px solid #44475a;
  border-bottom: none;
  height: 24px;
}

.left-container {
  flex: 1;
}

.monaco-editor-container {
  /* width: 100%; */
  background: #282a36;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* 让 monaco 编辑器内容区和 header 颜色一致 */
:deep(.monaco-editor),
:deep(.monaco-editor-background),
:deep(.monaco-editor .margin),
:deep(.monaco-editor .monaco-editor-background) {
  background-color: #282a36 !important;
  border-top-left-radius: 6px !important;
  border-top-right-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}

/* 去除 monaco 默认边框阴影 */
:deep(.monaco-editor),
:deep(.monaco-editor .overflow-guard) {
  box-shadow: none !important;
  border: none !important;
}

.editor-expand-btn {
  color: #7581a5 !important;
  transition: color 0.3s ease;
  border: none !important;
  font-size: 10px;
  height: 12px;
}

.editor-expand-btn:hover {
  color: #ff79c6 !important;
}

:deep(.dark-dropdown) {
  background-color: #282a36 !important;
  color: #f8f8f2 !important;
}

:deep(.pilot-select-selection-item) {
  color: #7581a5 !important;
}

.editor-shell-type-select {
  width: 120px;
  background-color: #282a36;
  border-radius: 6px;
  font-size: 12px;
}

:deep(.pilot-select-selector) {
  height: 10px;
  padding: 0 14px !important;
  font-size: 12px !important;
}

:deep(.pilot-select-dropdown .pilot-select-item) {
  background-color: #282a36 !important;
  font-size: 12px !important;
  color: #7581a5 !important;
  padding: 2px 12px !important;
  min-height: 12px !important;
}
</style>
