<template>
  <div class>
    <div class="logo">
      <img class="img" src="/src/assets/logo.png" alt="logo" />
      <!--     <span class="logo-title">持续集成 / 持续交付</span> -->
    </div>
    <!--    <div class="box-wrap">-->
    <!--      <a-input-->
    <!--        v-model:value="keywords"-->
    <!--        placeholder="搜索应用或文档"-->
    <!--        allow-clear-->
    <!--        class="searchBox"-->
    <!--        @change="keywordsChange"-->
    <!--      >-->
    <!--        <template #prefix>-->
    <!--          <search-outlined />-->
    <!--        </template>-->
    <!--      </a-input>-->
    <!--    </div>-->
    <!-- <div class="indexTabWrap"> -->
    <!-- <a-tabs> -->
    <!-- <a-tab-pane -->
    <!-- v-for="(cates, key) in filteredEntries" -->
    <!-- :key="key" -->
    <!-- class="tabPane flexTabPane" -->
    <!-- > -->
    <!-- <ItemBlobk v-for="item in cates" :key="item.name" v-bind="item" /> -->
    <!-- </a-tab-pane> -->
    <!-- </a-tabs> -->
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts">
// import { SearchOutlined } from '@ant-design/icons-vue';
// import { ref, computed } from 'vue';
// import { entries, type Entry, type EntryMap } from './entry';
// import ItemBlobk from './item.vue';

// const keywords = ref<string>('');
// const filteredEntries = computed<EntryMap>(() => {
//   const obj = {};
//   Object.keys(entries).forEach((cate) => {
//     obj[cate] = entries[cate].filter((item: Entry) => {
//       const reg = new RegExp(keywords.value, 'gi');
//       if (reg.test(item.name) || reg.test(item.desc)) {
//         return true;
//       }
//       return false;
//     });
//   });
//   return <EntryMap>obj;
// });

// const keywordsChange = (e: Event) => {
//   keywords.value = (e.target as HTMLInputElement).value;
// };
</script>

<style lang="scss" scoped>
.logo {
  text-align: center;
  padding: 38px 0 28px;
}

.logo-title {
  font-size: 24px;
  vertical-align: middle;
  font-weight: 700;
  //display: inline-block;
  color: #fff;
  background: #001529;
  border-radius: 12px;
  //width: 120px;
  height: 38px;
  //margin-left: 5px;
  padding: 5px 10px;
}

.box-wrap,
.tabPane {
  padding: 0 80px;
}

.searchBox {
  height: 48px !important;
  font-size: 24px !important;
  border-radius: 12px !important;
  border: 2px solid #0074ff !important;
}

.flexTabPane {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.cateBlock {
  margin-bottom: 12px;
}

.cateTitle {
  margin-bottom: 12px;
}
</style>

<style lang="scss">
$primayColor: #0074ff;

.indexTabWrap {
  margin-top: 22px;

  .ant-tabs-nav-wrap {
    justify-content: center;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: $primayColor;
  }

  .ant-tabs-ink-bar {
    background: $primayColor;
  }

  .ant-tabs-tab {
    width: 100px;
    justify-content: center;
    font-size: 16px !important;
  }

  .ant-tabs > .ant-tabs-nav {
    margin-bottom: 22px;
  }
}
</style>
