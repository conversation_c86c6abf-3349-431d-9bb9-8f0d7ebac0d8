export class Entry {
  name: string;
  image: string;
  link: string;
  desc: string;
}

export class EntryMap {
  '持续集成': Entry[];
}

export const entries: EntryMap = {
  '持续集成': [
    {
      name: 'GitLab',
      image:
        'https://ithelp.ithome.com.tw/upload/images/20190914/20120986nOfEu5a3rp.jpg',
      link: 'https://git.makeblock.com',
      desc: '代码仓库管理系统，需要双因素认证',
    },
    {
      name: 'Rancher',
      image:
        'https://dist-res-cn.oss-cn-shenzhen.aliyuncs.com/common/rancher.svg',
      link: 'https://rancher.makeblock.com/',
      desc: '管理和部署容器化应用程序',
    },
    {
      name: 'SonarQube',
      image:
        'https://dev-res-cn.oss-cn-shenzhen.aliyuncs.com/efficacy/public/image/sonar-logo-horizontal.svg',
      link: 'http://sonarqube.makeblock.com',
      desc: '代码审查工具，用于检测代码中的错误',
    },
    {
      name: 'Nexus',
      image:
        'https://repository.makeblock.com/static/rapture/resources/icons/x32/sonatype-repository-icon-reverse.svg',
      link: 'https://repository.makeblock.com',
      desc: 'Nexus仓库管理工具，用于存储二方库',
    },
    {
      name: 'Minio',
      image: 'https://efficacy.makeblock.com/assets/minio.png',
      link: 'https://minio.makeblock.com',
      desc: '对象存储服务，用于存储构建产物',
    },
    {
      name: 'Archery',
      image:
        'https://dist-res-cn.oss-cn-shenzhen.aliyuncs.com/common/%E6%88%AA%E5%B1%8F2024-10-02%2010.41.01.png',
      link: 'https://sql.makeblock.com',
      desc: 'SQL 审计平台',
    },
  ],
};
