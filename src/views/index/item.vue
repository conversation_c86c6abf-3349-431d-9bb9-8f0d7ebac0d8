<template>
  <a
    :href="item.link || 'javascript:viod(0)'"
    class="itemBlock"
    target="_blank"
  >
    <div
      class="itemLogo"
      :style="{ backgroundImage: 'url(' + item.image + ')' }"
    ></div>
    <div>
      <div class="itemName">{{ item.name }}</div>
      <div class="itemDesc">{{ item.desc }}</div>
    </div>
  </a>
</template>

<script setup lang="ts">
// import type { Entry } from './entry';
// type Exactly<T> = T;
const item = defineProps<{
  name: string;
  image: string;
  link: string;
  desc: string;
}>();
</script>

<style lang="scss" scoped>
.itemBlock {
  cursor: pointer;
  flex-basis: calc(33% - 6px);
  display: flex;
  align-items: center;
  background: #deefff;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 12px;

  &:hover {
    background: #cde7ff;
  }

  .itemLogo {
    flex: none;
    width: 60px;
    height: 60px;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 12px;
    border-radius: 50%;
  }

  .itemName {
    font-size: 16px;
    font-weight: 700;
    color: #000;
  }

  .itemDesc {
    margin-top: 4px;
    color: #666;
  }
}
</style>
