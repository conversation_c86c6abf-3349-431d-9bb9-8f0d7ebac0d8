<template>
  <a-dropdown>
    <section class="lang-wrap text-gray-40" :class="{ ['bg-gray-5']: false }">
      <GlobalOutlined
        name="language"
        style="font-size: 18px; margin-right: 4px"
      />
      <span class="text-xs mx-1">
        {{ langeList.find((l: any) => locale === l.value)?.label }}
      </span>
      <CaretDownOutlined class="text-xs" />
    </section>
    <template #overlay>
      <a-menu>
        <a-menu-item
          v-for="item in langeList"
          :key="item.value"
          :value="item.value"
          @click="handleSelect(item.value)"
        >
          {{ item.label }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
import { GlobalOutlined, CaretDownOutlined } from '@ant-design/icons-vue';

const locale = 'zh-CN';

const langeList = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
];

const handleSelect = async (value: string) => {
  console.log(value);
};
</script>

<style scoped>
.lang-wrap {
  @apply flex justify-center text-xl items-center cursor-pointer h-8 p-2 rounded-full;
}
.lang-wrap {
  display: flex;
  height: 2rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  line-height: 2rem;
}
</style>
