<template>
  <a-space
    direction="vertical"
    :style="{ width: '100%', height: '100%' }"
    :size="[0, 48]"
  >
    <a-layout style="height: 100vh">
      <a-layout-header
        style="background-color: rgb(247 249 251 / 1); padding-left: 2.5rem"
      >
        <a-space
          direction="horizontal"
          style="
            margin-top: 0.43rem;
            width: 100%;
            justify-content: space-between;
          "
        >
          <img :src="ImgLogo" alt="" style="height: 1rem" />
          <a-space style="width: 100%">
            <a
              :href="domain.privacy"
              class="mr-6 hover:text-blue hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              隐私政策
            </a>
            <a
              :href="domain.terms"
              class="mr-6 hover:text-blue hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              用户协议
            </a>
            <a-divider
              type="vertical"
              style="background: black; margin-right: 1.5rem"
            />
            <Lang />
          </a-space>
        </a-space>
      </a-layout-header>
      <a-layout-content
        style="background-color: rgb(247 249 251 / 1); display: flex"
      >
        <section class="element-center w-full">
          <section class="login-wrapper">
            <section class="login-image">
              <img :src="BgImg" alt="background-image" />
            </section>
            <section class="login-right">
              <section class="w-full flex justify-center items-center">
                <section ref="el" class="relative flex justify-center">
                  <section
                    class="flex flex-col w-25 min-h-600 bg-white rounded-1.25 divide-y max-md:divide-none"
                  >
                    <div class="flex-1 px-6 pt-6">
                      <section class="h-16" style="margin-bottom: 30px">
                        <div class="flex justify-between mb-4">
                          <img :src="ImgLogo" alt="" />
                        </div>
                      </section>
                      <div id="feiShuQRCode"></div>
                      <div
                        class="mb-44px mt-44px"
                        style="display: flex; justify-content: center"
                      >
                        <a-button
                          type="primary"
                          style="border-radius: 6px"
                          @click="gotoLogin"
                        >
                          点击打开飞书认证
                        </a-button>
                      </div>
                      <a-divider>
                        <span
                          class="text-gray-40 font-normal text-xs"
                          style="margin-top: 6px; font-size: 12px"
                        >
                          或
                        </span>
                      </a-divider>
                      <div class="element-center">
                        <a-button
                          type="normal"
                          style="border-radius: 8px; border: 4px"
                          @click="gotoPassportLogin"
                        >
                          邮箱或手机号登录
                        </a-button>
                      </div>
                    </div>
                  </section>
                </section>
              </section>
            </section>
          </section>
        </section>
      </a-layout-content>
      <a-layout-footer
        style="
          background-color: rgb(247 249 251 / 1);
          padding-top: 2px;
          padding-bottom: 2px;
        "
      >
        <div class="copyright">
          <div>
            {{ `Copyright © ${getCurrentYear()} Makeblock 童心制物 版权所有 ` }}
          </div>
          <div>粤ICP备19047421号-1 深圳市创客工场科技有限公司</div>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-space>
</template>

<script setup lang="ts">
import feiShuLogin from '@/views/login/FeiShuLogin';
import { computed, onMounted } from 'vue';
import { appUtil } from '@/utils/app';
import LogoZh from '@/assets/logo_zh.svg';
import BgImg from '@/assets/bg.png';
import Lang from '@/views/login/Lang.vue';
import { getCurrentYear } from '@/utils';

const ImgLogo = computed(() => LogoZh);
const domain = {
  privacy: 'https://efficacy.makeblock.com',
  terms: 'https://efficacy.makeblock.com',
};

onMounted(() => {
  feiShuLogin.paintQRCode('feiShuQRCode');
});

const gotoLogin = () => {
  window.location.href = feiShuLogin.mobileLoginUrl();
};

function gotoPassportLogin() {
  appUtil.openLoginModal();
}
</script>

<style scoped>
.items-center {
  align-items: center;
}

.mr-6 {
  margin-right: 1.5rem;
}

a {
  color: inherit;
  text-decoration: inherit;
}

.w-full {
  width: 100%;
}

.element-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-wrapper {
  display: flex;
  min-height: 600px;
  width: 904px;
  justify-content: center;
  border-radius: 1.5rem;
  background-color: rgb(255 255 255 / 1);
}

.login-wrapper .login-image {
  display: block;
}

.login-wrapper .login-image {
  flex: 1 1 0%;
  border-radius: 1.5rem;
  background-size: cover;
  background-position: center;
  padding: 0.5rem;
}

.login-wrapper .login-right {
  min-height: 600px;
  width: 400px;
}

.login-wrapper .login-image img {
  display: block;
  height: 100%;
  width: 100%;
  border-radius: 1.5rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.copyright {
  width: 100%;
  padding-bottom: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.375rem;
  --tw-text-opacity: 1;
  color: rgb(216 215 215 / 1);
}

.mb-4 {
  margin-bottom: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

::v-deep(#feiShuQRCode iframe) {
  border: 0 !important;
}

.mb-44px {
  margin-bottom: 44px;
}

.mt-44px {
  margin-top: 24px;
}
</style>
