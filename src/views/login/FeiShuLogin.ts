import { setCookie } from '@/utils/cookie';

export interface AuthResponse {
  code: number;
  data: AuthResponseData;
  message: string;
}

export interface AuthResponseData {
  token: string;
  utoken: string;
  user: AuthResponseUser;
}

export interface AuthResponseUser {
  createTime: number;
  email: string;
  headpic: string;
  oauthUser: {
    feishu: string;
  };
  phoneNumber: string;
  phoneZone: string;
  signature: string;
  stuAccount: string;
  stuOrgCode: string;
  type: number;
  uid: number;
  userName: string;
  verifyEmail: boolean;
}

// import { useUserStoreWithOut } from '/@/store/modules/user';

interface QRLoginConfig {
  clientId: string;
  redirectUrl: string;
}

export class FeiShuLogin {
  /**
   * 保存登录信息的 cookie key
   */
  static cookieKey = 'utoken';
  /**
   * 保存用户访问的地址, 用于最终重定向的 session key
   */
  private userPathSessionKey = 'feiShuBackPath';
  /**
   * 放二维码的 dom id
   */
  containerId = '';
  /**
   * 飞书 sdk 对象
   */
  private QRLoginObj: any;
  /**
   * 根据环境最终应用的配置
   */
  private config: QRLoginConfig = {
    clientId: import.meta.env.VITE_ENV_FEI_SHU_CLIENT_ID,
    redirectUrl: encodeURIComponent(
      `${import.meta.env.VITE_ENV_ORIGIN}${
        import.meta.env.VITE_ENV_BASE_PATH
      }#/feishu-redirect`,
    ),
  };

  constructor() {}

  /**
   * 监听二维码扫描后的事件
   */
  addEventListener() {
    window.addEventListener('message', this.handleMessage.bind(this), false);
  }

  mobileLoginUrl() {
    return `https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${this.config.clientId}&redirect_uri=${this.config.redirectUrl}&response_type=code&state=state`;
  }

  /**
   * 处理二维码扫描后的事件
   */
  handleMessage(event: { origin: any; data: any }) {
    const origin = event.origin;
    // 使用 matchOrigin 方法来判断 message 来自页面的url是否合法
    if (this.QRLoginObj.matchOrigin(origin)) {
      const loginTmpCode = event.data;
      window.location.href = `${this.getGoToUrl()}&tmp_code=${loginTmpCode}`;
    }
  }

  /**
   * 拼接 goto url
   */
  getGoToUrl() {
    return `https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${this.config.clientId}&redirect_uri=${this.config.redirectUrl}&response_type=code&state=STATE`;
  }

  /**
   * 渲染二维码
   */
  paintQRCode(containerId: string) {
    if (this.containerId) {
      return;
    }
    this.containerId = containerId;
    // @ts-ignore
    this.QRLoginObj = QRLogin({
      id: this.containerId,
      goto: this.getGoToUrl(),
      width: '100%',
      height: '100%',
      style: 'min-height:300px', // 可选的，二维码html标签的style属性
    });

    document.getElementById(this.containerId)?.addEventListener('click', () => {
      alert(this.mobileLoginUrl());
    });
    this.addEventListener();
    this.setUserPath();
  }

  /**
   * 保存用户访问的页面
   */
  setUserPath() {
    // 从 url 里面获取参数
    const params: any = new Proxy(
      new URLSearchParams(window.location.hash.replace(/^#.*?\?/, '')),
      {
        get: (searchParams, prop: string) => searchParams.get(prop),
      },
    );
    // 需要处理没有 redirect 参数的情况，如果没有则设置为首页
    const redirect =
      params.redirect || window.location.origin + window.location.pathname;
    window.sessionStorage.setItem(this.userPathSessionKey, redirect);
  }

  /**
   * 获取用户访问的页面
   */
  getUserPath(): string {
    const userPath = window.sessionStorage.getItem(this.userPathSessionKey);
    return userPath === 'null' ? '/' : userPath || '/';
  }

  /**
   * 用于 /feishu-redirect 页面的跳转
   */
  redirect() {
    // 从 url 里面获取参数
    const params: any = new Proxy(new URLSearchParams(window.location.search), {
      get: (searchParams, prop: string) => searchParams.get(prop),
    });
    // 发送给后端
    fetch(
      `${
        import.meta.env.VITE_PASSPORT_API_BASE_URL
      }/v1/oauth/feishu/login-with-code`,
      {
        method: 'POST',
        headers: {
          'Content-type': 'application/json',
        },
        body: JSON.stringify({
          code: params.code,
          cid: this.config.clientId,
        }),
      },
    )
      .then((response) => response.json())
      .then((res: AuthResponse) => {
        if (res.code === 0) {
          setCookie(FeiShuLogin.cookieKey, res.data.utoken, {
            expires: 7,
          });
        }
        location.href = this.getUserPath();
      });
  }
}

export default new FeiShuLogin();
