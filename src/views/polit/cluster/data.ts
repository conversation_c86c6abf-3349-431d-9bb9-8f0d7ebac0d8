export const columns = [
  {
    title: '序号',
    width: 40,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    width: 150,
    title: '名称',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 150,
    title: '描述',
    dataIndex: 'description',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否启用',
    dataIndex: 'enable',
    width: 50,
    align: 'center',
    ellipsis: true,
    key: 'enable',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 50,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: 130,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    align: 'center',
    width: 80,
  },
];

export enum Mode {
  Add = 'add',
  Edit = 'edit',
  View = 'view',
}
