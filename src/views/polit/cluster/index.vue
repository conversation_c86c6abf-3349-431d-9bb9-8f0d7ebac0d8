<template>
  <a-card :body-style="{ paddingBottom: '0!important' }">
    <a-space :size="20">
      <a-form
        ref="searchFormRef"
        layout="inline"
        :label-col="{ span: 25 }"
        :wrapper-col="{ span: 45 }"
        :model="searchFormState"
      >
        <a-form-item label="集群名称" name="name">
          <a-input-search
            v-model:value="searchFormState.name"
            style="width: 250px"
            allow-clear
            enter-button
            @search="doSearch"
          />
        </a-form-item>
      </a-form>

      <a-button type="primary" @click="handleAdd">
        <template #icon>
          <plus-circle-outlined />
        </template>
        添加
      </a-button>
      <a-button
        danger
        type="primary"
        :disabled="!hasSelected"
        :loading="state.loading"
        @click="deleteBySelect(state.selectedRowKeys)"
      >
        <template #icon>
          <delete-outlined />
        </template>
        删除
      </a-button>
    </a-space>
    <a-divider />
    <a-table
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        columnWidth: 35,
      }"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :columns="columns()"
      :show-index-column="true"
      :bordered="true"
      :scroll="{ x: 150, y: tableConfig.windowHeight - 240 }"
      row-key="uuid"
      class="ant-table-striped"
      :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
      @change="handlerTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'enable'">
          <a-switch
            v-model:checked="record.enable"
            @click="enableHandle(record)"
          />
        </template>
        <template v-if="column.key === 'operation'">
          <a style="color: rgba(0, 0, 0, 0.63)" @click="handleEdit(record)">
            编辑
          </a>
          <a-divider type="vertical" />
          <span>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1">
                    <a-button type="text" @click="handleCopy(record)">
                      详情
                    </a-button>
                  </a-menu-item>
                  <a-menu-item key="2">
                    <a-popconfirm
                      title="确认删除 ? "
                      @confirm="handleDelete([record.uuid])"
                    >
                      <a-button style="color: red" type="text">删除</a-button>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </template>
              <a style="color: rgba(0, 0, 0, 0.63)" class="ant-dropdown-link">
                更多
                <down-outlined />
              </a>
            </a-dropdown>
          </span>
        </template>
      </template>
    </a-table>
    <edit-drawer ref="editDrawer" :title="drawerTitle" @ok="handleSuccess" />
  </a-card>
</template>

<script lang="ts">
import {
  DeleteOutlined,
  DownOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { columns, Mode } from './data';
import EditDrawer from './drawer.vue';
import { deleteCluster, listCluster, updateCluster } from '@/api/pilot/cluster';

export default {
  components: {
    EditDrawer,
    PlusCircleOutlined,
    DownOutlined,
    DeleteOutlined,
  },
  setup() {
    const tableConfig = ref({
      windowHeight: document.documentElement.clientHeight - 40,
    });
    const editDrawer = ref();
    const drawerTitle = ref('');
    const dataSource = ref();
    const loading = ref();

    const pagination = ref({
      page: 1,
      pageSize: 10,
      showSizeChanger: true,
      total: 0,
      showTotal: (total: any) => `共 ${total} 条数据`,
    });

    async function doSearch() {
      try {
        loading.value = true;
        const response = await listCluster({
          ...searchFormState,
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
        });
        dataSource.value = response.data.list;
        pagination.value.total = response.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }

    const handleAdd = () => {
      drawerTitle.value = '新增';
      editDrawer.value.open(Mode.Add, '');
    };

    function handleEdit(item: any) {
      drawerTitle.value = '编辑';
      editDrawer.value.open(Mode.Edit, item.uuid);
    }

    async function handleDelete(ids: any[]) {
      try {
        loading.value = true;
        await deleteCluster({ uuids: ids });
        message.success('删除成功');
        handleSuccess();
        // }
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }

    onMounted(function () {
      window.onresize = () => {
        return (() => {
          tableConfig.value.windowHeight =
            document.documentElement.clientHeight;
        })();
      };
    });

    // 多选
    type Key = string | number;
    const state = reactive<{
      selectedRowKeys: Key[];
      loading: boolean;
    }>({
      selectedRowKeys: [], // Check here to configure the default column
      loading: false,
    });
    const hasSelected = computed(() => state.selectedRowKeys.length > 0);
    const onSelectChange = (selectedRowKeys: Key[]) => {
      console.log('selectedRowKeys changed: ', selectedRowKeys);
      state.selectedRowKeys = selectedRowKeys;
    };

    // 多选删除
    function deleteBySelect(ids: any[]) {
      Modal.confirm({
        type: 'warn',
        title: '确认要删除吗?',
        content: '确认要删除吗?',
        centered: true,
        onOk: () => {
          handleDelete(ids);
        },
      });
    }

    function handleSuccess() {
      doSearch();
    }

    const searchFormRef = ref();
    const searchFormState = reactive({ name: '' });

    doSearch();

    const handlerTableChange = (page: {
      pageSize: number;
      current: number;
    }) => {
      pagination.value.page = page.current;
      pagination.value.pageSize = page.pageSize;
      doSearch();
    };

    async function enableHandle(record: any) {
      try {
        loading.value = true;
        await updateCluster(record);
        message.success('更新成功');
      } finally {
        await doSearch();
      }
    }

    return {
      searchFormState,
      searchFormRef,
      dataSource,
      pagination,
      loading,
      editDrawer,
      drawerTitle,
      handleAdd,
      handleEdit,
      handleDelete,
      tableConfig,
      hasSelected,
      onSelectChange,
      state,
      deleteBySelect,
      handleSuccess,
      doSearch,
      handlerTableChange,
      enableHandle,
    };
  },
  methods: {
    columns() {
      return columns;
    },
  },
};
</script>

<style scoped>
.ant-card {
  height: 100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
</style>
