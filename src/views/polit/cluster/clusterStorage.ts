/**
 * 集群选择状态管理工具
 * 用于在不同 tab 之间同步集群选择状态
 */

const CLUSTER_STORAGE_KEY = 'pilot_selected_cluster';

/**
 * 获取当前选中的集群
 * @returns 选中的集群值，如果没有则返回空字符串
 */
export function getSelectedCluster(): string {
  try {
    return localStorage.getItem(CLUSTER_STORAGE_KEY) || '';
  } catch (error) {
    console.warn('Failed to get selected cluster from localStorage:', error);
    return '';
  }
}

/**
 * 设置当前选中的集群
 * @param cluster 集群值
 */
export function setSelectedCluster(cluster: string): void {
  try {
    if (cluster) {
      localStorage.setItem(CLUSTER_STORAGE_KEY, cluster);
    } else {
      localStorage.removeItem(CLUSTER_STORAGE_KEY);
    }
  } catch (error) {
    console.warn('Failed to set selected cluster to localStorage:', error);
  }
}

/**
 * 清除选中的集群
 */
export function clearSelectedCluster(): void {
  try {
    localStorage.removeItem(CLUSTER_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear selected cluster from localStorage:', error);
  }
}
