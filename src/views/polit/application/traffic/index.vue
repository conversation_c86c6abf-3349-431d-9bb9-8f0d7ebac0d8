<template>
  <div class="traffic-management">
    <a-tabs
      v-model:activeKey="activeKey"
      tab-position="left"
      size="small"
      :tab-bar-style="{ marginLeft: '-24px' }"
    >
      <a-tab-pane key="1">
        <template #tab>
          <span style="font-size: 12px">安全防护</span>
        </template>
        <div class="tab-content-wrapper">
          <!-- 安全防护的控制栏 -->
          <div class="control-bar">
            <div class="filter-controls">
              <a-space :size="12">
                <a-select
                  v-model:value="securityFilterType"
                  size="small"
                  placeholder="规则类型"
                  style="width: 120px"
                  allow-clear
                  @change="handleSecurityFilterChange"
                >
                  <a-select-option value="blacklist">黑名单</a-select-option>
                  <a-select-option value="whitelist">白名单</a-select-option>
                  <a-select-option value="ratelimit">限流</a-select-option>
                </a-select>
                <a-input
                  v-model:value="securitySearchText"
                  size="small"
                  placeholder="搜索规则名称、IP、用户等"
                  style="width: 220px"
                  allow-clear
                  @change="handleSecuritySearchChange"
                >
                  <template #prefix>
                    <search-outlined />
                  </template>
                </a-input>
              </a-space>
            </div>
            <div class="action-controls">
              <a-space :size="8">
                <a-button
                  size="small"
                  :loading="loading"
                  @click="handleRefresh"
                >
                  <template #icon>
                    <reload-outlined />
                  </template>
                  刷新策略
                </a-button>
                <a-button size="small" type="primary" @click="handleAddPolicy">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  添加策略
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 滚动内容区域 -->
          <div ref="securityScrollRef" class="scrollable-content">
            <div class="security-content">
              <SecurityRuleList ref="securityRuleListRef" />
            </div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="2">
        <template #tab>
          <span style="font-size: 12px">网络拓扑</span>
        </template>
        <div class="tab-content-wrapper">
          <!-- 网络拓扑的控制栏 -->
          <div class="control-bar">
            <div class="action-controls">
              <a-space>
                <a-button
                  size="small"
                  :loading="loading"
                  @click="handleRefresh"
                >
                  <template #icon>
                    <reload-outlined />
                  </template>
                  刷新拓扑
                </a-button>
                <a-button
                  size="small"
                  type="dashed"
                  @click="handleExportTopology"
                >
                  <template #icon>
                    <download-outlined />
                  </template>
                  导出拓扑
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 滚动内容区域 -->
          <div ref="topologyScrollRef" class="scrollable-content">
            <div class="topology-content">
              <a-card title="网络拓扑图" size="small" class="feature-card">
                <div class="feature-content">
                  <a-empty description="网络拓扑功能开发中" />
                  <p class="feature-description">
                    将展示应用间的网络拓扑关系，包括服务依赖、流量流向等信息
                  </p>
                </div>
              </a-card>
            </div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="3">
        <template #tab>
          <span style="font-size: 12px">流量管理</span>
        </template>
        <div class="tab-content-wrapper">
          <!-- 流量管理的控制栏 -->
          <div class="control-bar">
            <div class="action-controls">
              <a-space>
                <a-button
                  size="small"
                  :loading="loading"
                  @click="handleRefresh"
                >
                  <template #icon>
                    <reload-outlined />
                  </template>
                  刷新配置
                </a-button>
                <a-button size="small" type="primary" @click="handleAddRule">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  添加规则
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 滚动内容区域 -->
          <div ref="trafficScrollRef" class="scrollable-content">
            <div class="traffic-content">
              <a-row :gutter="[16, 16]">
                <a-col :span="12">
                  <a-card title="负载均衡" size="small" class="feature-card">
                    <div class="feature-content">
                      <a-empty description="负载均衡功能开发中" />
                      <p class="feature-description">
                        将提供负载均衡策略配置，支持轮询、加权轮询、最少连接等算法
                      </p>
                    </div>
                  </a-card>
                </a-col>

                <a-col :span="12">
                  <a-card title="流量镜像" size="small" class="feature-card">
                    <div class="feature-content">
                      <a-empty description="流量镜像功能开发中" />
                      <p class="feature-description">
                        将支持流量镜像配置，用于测试环境数据同步和问题排查
                      </p>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, onMounted, nextTick } from 'vue';
import {
  ReloadOutlined,
  DownloadOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import SecurityRuleList from './SecurityRuleList.vue';

// 从父组件注入集群相关状态
const selectedCluster = inject('selectedCluster', ref(''));
const clusterOptionsLoaded = inject('clusterOptionsLoaded', ref(false));

const activeKey = ref('1');
const loading = ref(false);

// 安全防护筛选条件
const securityFilterType = ref<string | undefined>(undefined);
const securitySearchText = ref<string>('');

// 滚动容器引用
const topologyScrollRef = ref<HTMLDivElement>();
const trafficScrollRef = ref<HTMLDivElement>();
const securityScrollRef = ref<HTMLDivElement>();

// 组件引用
const securityRuleListRef = ref();

// 监听集群变化
watch(
  selectedCluster,
  (newCluster) => {
    if (newCluster && clusterOptionsLoaded.value) {
      console.log('流量治理 - 集群切换到:', newCluster);
      // 这里可以添加集群切换时的数据加载逻辑
    }
  },
  { immediate: false },
);

// 监听安全防护筛选变化
watch(securityFilterType, (newType) => {
  if (securityRuleListRef.value) {
    securityRuleListRef.value.filterType = newType;
  }
});

// 刷新数据
const handleRefresh = () => {
  if (!selectedCluster.value) {
    message.warning('请先选择集群');
    return;
  }

  loading.value = true;

  // 根据当前标签页刷新对应数据
  if (activeKey.value === '1') {
    // 安全防护 - 刷新安全规则
    securityRuleListRef.value?.refresh();
  }

  // 模拟数据加载
  setTimeout(() => {
    loading.value = false;
    message.success('数据刷新完成');
  }, 1000);
};

// 导出拓扑
const handleExportTopology = () => {
  message.info('导出拓扑功能开发中');
};

// 添加规则
const handleAddRule = () => {
  message.info('添加流量规则功能开发中');
};

// 添加策略
const handleAddPolicy = () => {
  if (activeKey.value === '1') {
    // 安全防护 - 添加安全规则
    securityRuleListRef.value?.addRule();
  } else {
    message.info('添加安全策略功能开发中');
  }
};

// 安全防护筛选变化处理
const handleSecurityFilterChange = () => {
  if (securityRuleListRef.value) {
    securityRuleListRef.value.filterType = securityFilterType.value;
  }
};

// 安全防护搜索变化处理
const handleSecuritySearchChange = () => {
  if (securityRuleListRef.value) {
    securityRuleListRef.value.searchText = securitySearchText.value;
  }
};

// 滚动条显示/隐藏逻辑
const setupScrollbarBehavior = () => {
  const scrollContainers = [
    topologyScrollRef.value,
    trafficScrollRef.value,
    securityScrollRef.value,
  ];

  scrollContainers.forEach((container) => {
    if (!container) {
      return;
    }

    let scrollTimer: NodeJS.Timeout | null = null;

    // 滚动时显示滚动条
    const handleScroll = () => {
      container.classList.add('scrolling');

      // 清除之前的定时器
      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }

      // 停止滚动后延迟隐藏滚动条
      scrollTimer = setTimeout(() => {
        container.classList.remove('scrolling');
      }, 1000); // 1秒后隐藏
    };

    // 鼠标悬停时显示滚动条
    const handleMouseEnter = () => {
      container.classList.add('hover-scrollbar');
    };

    const handleMouseLeave = () => {
      container.classList.remove('hover-scrollbar');
    };

    container.addEventListener('scroll', handleScroll);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);
  });
};

onMounted(async () => {
  // 等待DOM完全渲染
  await nextTick();

  // 设置滚动条行为
  setupScrollbarBehavior();
});
</script>

<style scoped>
.traffic-management {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 180px);
}

.control-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
  gap: 16px;
  flex-wrap: wrap;
  /* min-height: 48px; */
}

.filter-controls {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.update-time {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.action-controls {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.topology-content,
.traffic-content {
  padding: 0;
  min-height: 400px;
}

.security-content {
  padding: 0;
  height: 100%;
}

.feature-card {
  height: 300px;
  margin-bottom: 16px;
}

.feature-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.feature-description {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
  max-width: 200px;
}

:deep(.ant-card-body) {
  height: calc(100% - 57px);
  padding: 16px;
}

:deep(.ant-empty) {
  margin: 0;
}

:deep(.ant-empty-description) {
  color: rgba(0, 0, 0, 0.25);
}

/* 自定义滚动条样式 - 只在滚动时显示 */
.scrollable-content {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.scrollable-content::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.scrollable-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

/* 鼠标悬停时显示滚动条 */
.scrollable-content.hover-scrollbar {
  scrollbar-color: rgba(193, 193, 193, 0.6) transparent;
}

.scrollable-content.hover-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(193, 193, 193, 0.6);
  opacity: 1;
}

/* 滚动时显示滚动条 */
.scrollable-content.scrolling {
  scrollbar-color: #c1c1c1 transparent;
}

.scrollable-content.scrolling::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  opacity: 1;
}

/* 滚动条悬停效果 */
.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
  opacity: 1;
}

/* 滚动条启用效果 */
.scrollable-content::-webkit-scrollbar-thumb:active {
  background: #999 !important;
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-content-wrapper {
    max-height: calc(100vh - 150px);
  }

  .control-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-controls {
    align-self: center;
  }

  .feature-card {
    height: 250px;
  }
}

@media (max-width: 576px) {
  .tab-content-wrapper {
    max-height: calc(100vh - 120px);
  }

  .feature-card {
    height: 200px;
  }

  .scrollable-content {
    padding-right: 4px;
  }
}
</style>
