<template>
  <div class="security-rule-list">
    <a-table
      :columns="columns"
      :data-source="filteredData"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      size="small"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <a-tag :color="getTypeTagColor(record.type)">
            {{ getTypeLabel(record.type) }}
          </a-tag>
        </template>

        <template v-else-if="column.key === 'enabled'">
          <a-switch
            v-model:checked="record.enabled"
            size="small"
            @change="handleToggleRule(record)"
          />
        </template>

        <template v-else-if="column.key === 'auditStatus'">
          <a-tag :color="getAuditStatusColor(record.auditStatus)">
            {{ getAuditStatusLabel(record.auditStatus) }}
          </a-tag>
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEditRule(record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu
                  @click="(info: any) => handleMoreAction(info.key, record)"
                >
                  <a-menu-item key="copy">复制</a-menu-item>
                  <a-menu-item
                    v-if="
                      record.auditStatus === 'pending' ||
                      record.auditStatus === 'approved'
                    "
                    key="audit"
                  >
                    审核
                  </a-menu-item>
                  <a-menu-item key="history">历史</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger>删除</a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 添加/编辑规则抽屉 -->
    <a-drawer
      v-model:open="drawerVisible"
      :title="drawerTitle"
      :width="800"
      placement="right"
      @close="handleCancelDrawer"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleCancelDrawer">取消</a-button>
          <a-button type="primary" @click="handleSaveRule">保存</a-button>
        </a-space>
      </template>

      <a-tabs v-model:activeKey="editActiveKey" type="card">
        <a-tab-pane key="form" tab="表单编辑">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            layout="horizontal"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-form-item label="规则名称" name="name">
              <a-input
                v-model:value="formData.name"
                placeholder="请输入规则名称"
              />
            </a-form-item>

            <a-form-item label="规则类型" name="type">
              <a-select
                v-model:value="formData.type"
                placeholder="请选择规则类型"
              >
                <a-select-option value="blacklist">黑名单</a-select-option>
                <a-select-option value="whitelist">白名单</a-select-option>
                <a-select-option value="ratelimit">限流</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 黑白名单配置 -->
            <template
              v-if="
                formData.type === 'blacklist' || formData.type === 'whitelist'
              "
            >
              <a-divider>
                {{ formData.type === 'blacklist' ? '黑名单' : '白名单' }}配置
              </a-divider>

              <a-form-item name="ipBlocks">
                <template #label>
                  IP地址/CIDR
                  <a-tooltip title="支持：************* 或 ***********/24">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-select
                  v-model:value="formData.rules.ipBlocks"
                  mode="tags"
                  placeholder="请输入IP地址或CIDR"
                  :token-separators="[',', ' ']"
                />
              </a-form-item>

              <a-form-item name="principals">
                <template #label>
                  用户主体
                  <a-tooltip
                    title="格式：cluster.local/ns/default/sa/productpage"
                  >
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-select
                  v-model:value="formData.rules.principals"
                  mode="tags"
                  placeholder="请输入用户主体"
                  :token-separators="[',', ' ']"
                />
              </a-form-item>

              <a-form-item label="HTTP方法">
                <a-select
                  v-model:value="formData.rules.methods"
                  mode="multiple"
                  placeholder="请选择HTTP方法"
                >
                  <a-select-option value="GET">GET</a-select-option>
                  <a-select-option value="POST">POST</a-select-option>
                  <a-select-option value="PUT">PUT</a-select-option>
                  <a-select-option value="DELETE">DELETE</a-select-option>
                  <a-select-option value="PATCH">PATCH</a-select-option>
                  <a-select-option value="HEAD">HEAD</a-select-option>
                  <a-select-option value="OPTIONS">OPTIONS</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item name="paths">
                <template #label>
                  请求路径
                  <a-tooltip title="支持：/api/v1/users 或 /api/v1/users/*">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-select
                  v-model:value="formData.rules.paths"
                  mode="tags"
                  placeholder="请输入请求路径"
                  :token-separators="[',', ' ']"
                />
              </a-form-item>
            </template>

            <!-- 限流配置 -->
            <template v-if="formData.type === 'ratelimit'">
              <a-divider>限流配置</a-divider>

              <a-form-item name="domain">
                <template #label>
                  限流域
                  <a-tooltip title="Istio限流服务的域名，通常为服务名">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-input
                  v-model:value="formData.rules.rateLimit.domain"
                  placeholder="例如: productpage"
                />
              </a-form-item>

              <a-form-item name="descriptors">
                <template #label>
                  限流描述符
                  <a-tooltip title="定义限流的匹配条件和限制">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <div class="descriptor-list">
                  <div
                    v-for="(descriptor, index) in formData.rules.rateLimit
                      .descriptors"
                    :key="index"
                    class="descriptor-item"
                  >
                    <a-card size="small" :title="`规则 ${index + 1}`">
                      <template #extra>
                        <a-button
                          type="link"
                          size="small"
                          danger
                          @click="removeDescriptor(index)"
                        >
                          删除
                        </a-button>
                      </template>

                      <a-form-item label="匹配键" style="margin-bottom: 12px">
                        <a-select
                          v-model:value="descriptor.key"
                          placeholder="选择匹配键类型"
                          allow-clear
                        >
                          <a-select-option value="header_match">
                            请求头匹配
                          </a-select-option>
                          <a-select-option value="source_cluster">
                            源集群
                          </a-select-option>
                          <a-select-option value="destination_service_name">
                            目标服务名
                          </a-select-option>
                          <a-select-option
                            value="destination_service_namespace"
                          >
                            目标服务命名空间
                          </a-select-option>
                          <a-select-option value="request_headers">
                            请求头
                          </a-select-option>
                          <a-select-option value="remote_address">
                            远程地址
                          </a-select-option>
                          <a-select-option value="generic_key">
                            通用键
                          </a-select-option>
                        </a-select>
                      </a-form-item>

                      <a-form-item label="匹配值" style="margin-bottom: 12px">
                        <a-input
                          v-model:value="descriptor.value"
                          placeholder="例如: user-id"
                        />
                      </a-form-item>

                      <a-row :gutter="16">
                        <a-col :span="12">
                          <a-form-item
                            label="请求数"
                            style="margin-bottom: 12px"
                          >
                            <a-input-number
                              v-model:value="
                                descriptor.rate_limit.requests_per_unit
                              "
                              :min="1"
                              :max="10000"
                              style="width: 100%"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col :span="12">
                          <a-form-item
                            label="时间单位"
                            style="margin-bottom: 12px"
                          >
                            <a-select
                              v-model:value="descriptor.rate_limit.unit"
                            >
                              <a-select-option value="SECOND">
                                秒
                              </a-select-option>
                              <a-select-option value="MINUTE">
                                分钟
                              </a-select-option>
                              <a-select-option value="HOUR">
                                小时
                              </a-select-option>
                              <a-select-option value="DAY">天</a-select-option>
                            </a-select>
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </a-card>
                  </div>

                  <a-button
                    type="dashed"
                    block
                    style="margin-top: 8px"
                    @click="addDescriptor"
                  >
                    <plus-outlined />
                    添加限流规则
                  </a-button>
                </div>
              </a-form-item>

              <a-form-item name="failure_mode_deny">
                <template #label>
                  故障处理模式
                  <a-tooltip title="当限流服务不可用时的处理方式">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-radio-group
                  v-model:value="formData.rules.rateLimit.failure_mode_deny"
                >
                  <a-radio :value="false">允许通过</a-radio>
                  <a-radio :value="true">拒绝请求</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item name="timeout">
                <template #label>
                  超时时间
                  <a-tooltip title="调用限流服务的超时时间(毫秒)">
                    <question-circle-outlined
                      style="margin-left: 4px; color: #8c8c8c"
                    />
                  </a-tooltip>
                </template>
                <a-input-number
                  v-model:value="formData.rules.rateLimit.timeout"
                  :min="100"
                  :max="10000"
                  placeholder="默认 25ms"
                  style="width: 100%"
                  addon-after="ms"
                />
              </a-form-item>
            </template>

            <a-form-item label="描述">
              <a-textarea
                v-model:value="formData.description"
                placeholder="请输入规则描述"
                :rows="3"
              />
            </a-form-item>

            <a-form-item label="状态">
              <a-checkbox v-model:checked="formData.enabled">
                启用规则
              </a-checkbox>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="yaml" tab="YAML编辑">
          <div class="yaml-editor">
            <div class="yaml-editor-content">
              <code-editor
                v-model="editableYamlContent"
                :show-expand-btn="false"
                :minimap="true"
                :height="'540px'"
                :initial-shell-type="'yaml'"
                :label="'YAML配置'"
                :placeholder="'请输入YAML配置'"
              />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-drawer>

    <!-- 审核抽屉 -->
    <a-drawer
      v-model:open="auditDrawerVisible"
      title="规则审核"
      width="1000"
      placement="right"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRejectRule">驳回</a-button>
          <a-button @click="handleCopyYaml">复制YAML</a-button>
          <a-button @click="handleDownloadYaml">下载YAML</a-button>
          <a-button type="primary" @click="handleApproveRule">
            审核通过
          </a-button>
        </a-space>
      </template>

      <div class="audit-content">
        <a-tabs v-model:activeKey="auditActiveKey" type="card">
          <a-tab-pane key="form" tab="表单预览">
            <div class="rule-preview">
              <a-descriptions title="规则信息" bordered :column="2">
                <a-descriptions-item label="规则名称">
                  {{ auditRule?.name }}
                </a-descriptions-item>
                <a-descriptions-item label="规则类型">
                  <a-tag :color="getTypeTagColor(auditRule?.type)">
                    {{ getTypeLabel(auditRule?.type) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="命名空间">
                  {{ auditRule?.namespace }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-tag :color="auditRule?.enabled ? 'green' : 'red'">
                    {{ auditRule?.enabled ? '启用' : '禁用' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="描述" :span="2">
                  {{ auditRule?.description || '无' }}
                </a-descriptions-item>
              </a-descriptions>

              <a-divider>规则配置</a-divider>

              <a-descriptions bordered :column="1">
                <a-descriptions-item
                  v-if="auditRule?.rules?.ipBlocks?.length"
                  label="IP地址/CIDR"
                >
                  <a-tag
                    v-for="ip in auditRule.rules.ipBlocks"
                    :key="ip"
                    style="margin-bottom: 4px"
                  >
                    {{ ip }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="auditRule?.rules?.principals?.length"
                  label="用户主体"
                >
                  <a-tag
                    v-for="principal in auditRule.rules.principals"
                    :key="principal"
                    style="margin-bottom: 4px"
                  >
                    {{ principal }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="auditRule?.rules?.methods?.length"
                  label="HTTP方法"
                >
                  <a-tag
                    v-for="method in auditRule.rules.methods"
                    :key="method"
                    style="margin-bottom: 4px"
                  >
                    {{ method }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="auditRule?.rules?.paths?.length"
                  label="请求路径"
                >
                  <a-tag
                    v-for="path in auditRule.rules.paths"
                    :key="path"
                    style="margin-bottom: 4px"
                  >
                    {{ path }}
                  </a-tag>
                </a-descriptions-item>

                <!-- 限流配置展示 -->
                <template
                  v-if="
                    auditRule?.type === 'ratelimit' &&
                    auditRule?.rules?.rateLimit
                  "
                >
                  <a-descriptions-item label="限流域">
                    {{ auditRule.rules.rateLimit.domain }}
                  </a-descriptions-item>
                  <a-descriptions-item label="限流规则">
                    <div
                      v-for="(descriptor, index) in auditRule.rules.rateLimit
                        .descriptors"
                      :key="index"
                    >
                      <a-card
                        size="small"
                        :title="`规则 ${index + 1}`"
                        style="margin-bottom: 8px"
                      >
                        <p>
                          <strong>匹配键:</strong>
                          {{ descriptor.key }}
                        </p>
                        <p>
                          <strong>匹配值:</strong>
                          {{ descriptor.value }}
                        </p>
                        <p>
                          <strong>限制:</strong>
                          {{ descriptor.rate_limit.requests_per_unit }} 次/{{
                            descriptor.rate_limit.unit
                          }}
                        </p>
                      </a-card>
                    </div>
                  </a-descriptions-item>
                  <a-descriptions-item label="故障处理">
                    <a-tag
                      :color="
                        auditRule.rules.rateLimit.failure_mode_deny
                          ? 'red'
                          : 'green'
                      "
                    >
                      {{
                        auditRule.rules.rateLimit.failure_mode_deny
                          ? '拒绝请求'
                          : '允许通过'
                      }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="超时时间">
                    {{ auditRule.rules.rateLimit.timeout }}ms
                  </a-descriptions-item>
                </template>
              </a-descriptions>
            </div>
          </a-tab-pane>

          <a-tab-pane key="yaml" tab="YAML配置">
            <div class="yaml-preview">
              <a-alert
                message="YAML配置"
                description="审核时的YAML配置预览，只读模式"
                type="info"
                show-icon
                style="margin-bottom: 16px"
              />

              <div class="yaml-content">
                <code-editor
                  v-model="currentYaml"
                  :read-only="true"
                  :show-expand-btn="false"
                  :minimap="true"
                  :height="'calc(100vh - 400px)'"
                  :initial-shell-type="'yaml'"
                  :label="'YAML配置'"
                  :placeholder="'YAML配置内容'"
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>

    <!-- 历史记录抽屉 -->
    <a-drawer
      v-model:open="historyDrawerVisible"
      title="规则历史记录"
      width="800"
      placement="right"
    >
      <template #extra>
        <a-space>
          <a-button @click="historyDrawerVisible = false">关闭</a-button>
        </a-space>
      </template>

      <div class="history-content">
        <a-table
          :columns="historyColumns"
          :data-source="historyData"
          :loading="historyLoading"
          row-key="id"
          size="small"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'version'">
              v{{ record.version }}
            </template>

            <template v-else-if="column.key === 'operation'">
              <a-tag :color="getOperationColor(record.operation)">
                {{ getOperationLabel(record.operation) }}
              </a-tag>
            </template>

            <template v-else-if="column.key === 'time'">
              {{ formatTime(record.createdAt) }}
            </template>

            <template v-else-if="column.key === 'action'">
              <a-button
                type="link"
                size="small"
                @click="handleViewVersionDiff(record)"
              >
                详情
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-drawer>

    <!-- YAML版本对比抽屉 -->
    <a-drawer
      v-model:open="diffDrawerVisible"
      title="版本对比"
      width="1200"
      placement="right"
    >
      <template #extra>
        <a-space>
          <a-button @click="diffDrawerVisible = false">关闭</a-button>
        </a-space>
      </template>
      <div class="yaml-diff-container">
        <yaml-diff
          :old-value="previousVersionYaml"
          :new-value="currentVersionYaml"
          :height="'calc(100vh - 100px)'"
          :options="{ theme: 'vs', readOnly: true }"
        />
        <!-- </div> -->
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, inject, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  QuestionCircleOutlined,
  PlusOutlined,
  DownOutlined,
} from '@ant-design/icons-vue';
import type { TableColumnsType, FormInstance } from 'ant-design-vue';
import CodeEditor from '@/components/code-editor/index.vue';
import YamlDiff from '@/components/yaml-diff/index.vue';
import {
  getSecurityRules,
  createSecurityRule,
  updateSecurityRule,
  deleteSecurityRule,
  applySecurityRuleToCluster,
  getSecurityRuleHistory,
  type SecurityRuleItem,
  type SecurityRuleHistoryItem,
} from '@/api/pilot/security';
import {
  generateAuthorizationPolicyYaml,
  generateRateLimitYaml,
  parseYamlToJSON,
} from '@/utils/yaml';
// import { getNamespaces } from '@/api/pilot/gateway';

// 从父组件注入集群信息
const selectedCluster = inject('selectedCluster', ref(''));

const loading = ref(false);
const drawerVisible = ref(false);
const drawerTitle = ref('');
const formRef = ref<FormInstance>();
const namespaces = ref<string[]>([]);

// 审核相关状态
const auditDrawerVisible = ref(false);
const currentYaml = ref('');
const auditActiveKey = ref('form');
const auditRule = ref<SecurityRuleItem | null>(null);

// 历史记录相关状态
const historyDrawerVisible = ref(false);
const historyData = ref<SecurityRuleHistoryItem[]>([]);
const historyLoading = ref(false);
const currentHistoryRule = ref<SecurityRuleItem | null>(null);

// 版本对比相关状态
const diffDrawerVisible = ref(false);
const currentDiffVersion = ref<SecurityRuleHistoryItem | null>(null);
const currentVersionYaml = ref('');
const previousVersionYaml = ref('');

// 编辑相关状态
const editActiveKey = ref('form');

// 可编辑的YAML内容
const editableYamlContent = ref('');

// 编辑时的YAML内容（只读计算属性）
const editYamlContent = computed(() => {
  if (!formData.name) {
    return '';
  }

  try {
    const ruleData = {
      name: formData.name,
      namespace: 'default',
      type: formData.type,
      rules: formData.rules,
      enabled: formData.enabled,
      description: formData.description,
    };

    if (formData.type === 'ratelimit') {
      return generateRateLimitYaml(ruleData);
    } else {
      return generateAuthorizationPolicyYaml(ruleData);
    }
  } catch (error) {
    console.error('生成编辑YAML失败:', error);
    return '# YAML生成失败\n# 请检查表单配置是否正确';
  }
});

// 筛选条件
const filterType = ref<string | undefined>(undefined);
const searchText = ref<string>('');

// 规则数据
const rulesData = ref<SecurityRuleItem[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = rulesData.value;

  // 按类型筛选
  if (filterType.value) {
    filtered = filtered.filter((item) => item.type === filterType.value);
  }

  // 按搜索文本筛选
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter((item) => {
      // 搜索规则名称
      if (item.name.toLowerCase().includes(search)) {
        return true;
      }

      // 搜索命名空间
      if (item.namespace.toLowerCase().includes(search)) {
        return true;
      }

      // 搜索描述
      if (item.description?.toLowerCase().includes(search)) {
        return true;
      }

      // 搜索规则内容
      const rules = item.rules;

      // 搜索IP地址
      if (rules.ipBlocks?.some((ip) => ip.toLowerCase().includes(search))) {
        return true;
      }

      // 搜索用户主体
      if (
        rules.principals?.some((principal) =>
          principal.toLowerCase().includes(search),
        )
      ) {
        return true;
      }

      // 搜索命名空间规则
      if (rules.namespaces?.some((ns) => ns.toLowerCase().includes(search))) {
        return true;
      }

      // 搜索HTTP方法
      if (
        rules.methods?.some((method) => method.toLowerCase().includes(search))
      ) {
        return true;
      }

      // 搜索路径
      if (rules.paths?.some((path) => path.toLowerCase().includes(search))) {
        return true;
      }

      return false;
    });
  }

  return filtered;
});

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  type: 'blacklist' as 'blacklist' | 'whitelist' | 'ratelimit',
  rules: {
    ipBlocks: [] as string[],
    principals: [] as string[],
    methods: [] as string[],
    paths: [] as string[],
    // 限流相关字段
    rateLimit: {
      domain: '',
      descriptors: [] as Array<{
        key: string;
        value: string;
        rate_limit: {
          requests_per_unit: number;
          unit: 'SECOND' | 'MINUTE' | 'HOUR' | 'DAY';
        };
      }>,
      failure_mode_deny: false,
      timeout: 25,
    },
  },
  enabled: true,
  description: '',
});

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: '请输入规则名称' }],
    type: [{ required: true, message: '请选择规则类型' }],
  };

  // 根据规则类型添加特定验证
  if (formData.type === 'blacklist' || formData.type === 'whitelist') {
    return {
      ...baseRules,
      // 黑白名单至少需要配置一项规则
      ipBlocks: [
        {
          validator: () => {
            const hasIpBlocks = formData.rules.ipBlocks?.length > 0;
            const hasPrincipals = formData.rules.principals?.length > 0;
            const hasMethods = formData.rules.methods?.length > 0;
            const hasPaths = formData.rules.paths?.length > 0;

            if (!hasIpBlocks && !hasPrincipals && !hasMethods && !hasPaths) {
              return Promise.reject(
                new Error(
                  '至少需要配置一项规则（IP地址、用户主体、HTTP方法或请求路径）',
                ),
              );
            }
            return Promise.resolve();
          },
          trigger: 'change',
        },
      ],
    };
  } else if (formData.type === 'ratelimit') {
    return {
      ...baseRules,
      domain: [{ required: true, message: '请输入限流域' }],
      descriptors: [
        {
          validator: () => {
            if (formData.rules.rateLimit.descriptors.length === 0) {
              return Promise.reject(new Error('至少需要配置一个限流描述符'));
            }
            return Promise.resolve();
          },
          trigger: 'change',
        },
      ],
    };
  }

  return baseRules;
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    ellipsis: true,
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    align: 'center',
  },

  {
    title: '启用状态',
    key: 'enabled',
    width: 80,
    align: 'center',
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 100,
    align: 'center',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: 200,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
  },
];

// 历史记录表格列定义
const historyColumns: TableColumnsType = [
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    width: 80,
    align: 'center',
  },
  {
    title: '操作类型',
    key: 'operation',
    width: 100,
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    key: 'operatorName',
    width: 120,
  },
  {
    title: '操作时间',
    key: 'time',
    width: 160,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
  },
];

// 监听集群变化
watch(selectedCluster, (newCluster) => {
  if (newCluster) {
    loadData();
    loadNamespaces();
  }
});

// 生成mock数据
const generateMockData = (): SecurityRuleItem[] => {
  const clusterName = selectedCluster.value || 'test-cluster';

  return [
    {
      id: 'rule-001',
      name: '恶意IP黑名单',
      namespace: 'default',
      cluster: clusterName,
      type: 'blacklist',
      rules: {
        ipBlocks: ['*************', '10.0.0.0/24', '***********'],
        methods: ['POST', 'PUT'],
        paths: ['/api/admin/*', '/api/sensitive/*'],
      },
      enabled: true,
      description: '阻止来自恶意IP的访问请求',
      auditStatus: 'applied',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-16T14:20:00Z',
    },
    {
      id: 'rule-002',
      name: '可信用户白名单',
      namespace: 'production',
      cluster: clusterName,
      type: 'whitelist',
      rules: {
        principals: [
          'cluster.local/ns/default/sa/admin',
          'cluster.local/ns/system/sa/operator',
        ],
        namespaces: ['kube-system', 'istio-system'],
        methods: ['GET', 'POST'],
      },
      enabled: true,
      description: '允许可信用户和系统组件访问',
      auditStatus: 'applied',
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-15T16:45:00Z',
    },
    {
      id: 'rule-003',
      name: '开发环境限制',
      namespace: 'development',
      cluster: clusterName,
      type: 'blacklist',
      rules: {
        ipBlocks: ['***********/24'],
        methods: ['DELETE'],
        paths: ['/api/production/*', '/api/critical/*'],
      },
      enabled: false,
      description: '限制开发环境对生产API的访问',
      auditStatus: 'pending',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-14T13:30:00Z',
    },
    {
      id: 'rule-004',
      name: '内部服务白名单',
      namespace: 'microservices',
      cluster: clusterName,
      type: 'whitelist',
      rules: {
        principals: [
          'cluster.local/ns/microservices/sa/user-service',
          'cluster.local/ns/microservices/sa/order-service',
        ],
        namespaces: ['microservices'],
        methods: ['GET', 'POST', 'PUT'],
        paths: ['/api/internal/*'],
      },
      enabled: true,
      description: '允许内部微服务之间的通信',
      auditStatus: 'approved',
      createdAt: '2024-01-08T14:22:00Z',
      updatedAt: '2024-01-13T10:15:00Z',
    },
    {
      id: 'rule-005',
      name: '外部攻击防护',
      namespace: 'security',
      cluster: clusterName,
      type: 'blacklist',
      rules: {
        ipBlocks: ['************/24', '*************', '**********'],
        methods: ['POST', 'PUT', 'DELETE'],
        paths: ['/api/auth/*', '/api/payment/*'],
      },
      enabled: true,
      description: '阻止来自已知攻击源的请求',
      auditStatus: 'applied',
      createdAt: '2024-01-05T08:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z',
    },
    {
      id: 'rule-006',
      name: '管理员访问白名单',
      namespace: 'admin',
      cluster: clusterName,
      type: 'whitelist',
      rules: {
        principals: ['cluster.local/ns/admin/sa/super-admin'],
        ipBlocks: ['********/28'],
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        paths: ['/api/admin/*', '/api/config/*'],
      },
      enabled: true,
      description: '允许管理员从指定网络访问管理接口',
      auditStatus: 'applied',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T18:30:00Z',
    },
    {
      id: 'rule-007',
      name: '测试环境隔离',
      namespace: 'testing',
      cluster: clusterName,
      type: 'blacklist',
      rules: {
        namespaces: ['production', 'staging'],
        methods: ['POST', 'PUT', 'DELETE'],
        paths: ['/api/data/*'],
      },
      enabled: true,
      description: '防止测试环境访问生产数据',
      auditStatus: 'rejected',
      createdAt: '2024-01-03T12:30:00Z',
      updatedAt: '2024-01-12T15:45:00Z',
    },
    {
      id: 'rule-008',
      name: 'API网关白名单',
      namespace: 'gateway',
      cluster: clusterName,
      type: 'whitelist',
      rules: {
        principals: ['cluster.local/ns/gateway/sa/api-gateway'],
        methods: ['GET', 'POST'],
        paths: ['/api/public/*', '/api/v1/*'],
      },
      enabled: true,
      description: '允许API网关访问公共接口',
      auditStatus: 'applied',
      createdAt: '2024-01-06T16:20:00Z',
      updatedAt: '2024-01-14T09:10:00Z',
    },
    {
      id: 'rule-009',
      name: 'API请求限流',
      namespace: 'default',
      cluster: clusterName,
      type: 'ratelimit',
      rules: {
        paths: ['/api/v1/users', '/api/v1/orders'],
        methods: ['POST', 'PUT'],
        rateLimit: {
          domain: 'productpage',
          descriptors: [
            {
              key: 'header_match',
              value: 'user-id',
              rate_limit: {
                requests_per_unit: 100,
                unit: 'MINUTE',
              },
            },
          ],
          failure_mode_deny: false,
          timeout: 25,
        },
      },
      enabled: true,
      description: '限制API请求频率，防止滥用',
      auditStatus: 'pending',
      createdAt: '2024-01-17T10:00:00Z',
      updatedAt: '2024-01-17T10:00:00Z',
    },
    {
      id: 'rule-010',
      name: '登录接口限流',
      namespace: 'auth',
      cluster: clusterName,
      type: 'ratelimit',
      rules: {
        paths: ['/api/auth/login', '/api/auth/register'],
        ipBlocks: ['0.0.0.0/0'],
        rateLimit: {
          domain: 'auth-service',
          descriptors: [
            {
              key: 'source_cluster',
              value: 'default',
              rate_limit: {
                requests_per_unit: 10,
                unit: 'MINUTE',
              },
            },
          ],
          failure_mode_deny: true,
          timeout: 50,
        },
      },
      enabled: true,
      description: '限制登录接口请求频率，防止暴力破解',
      auditStatus: 'approved',
      createdAt: '2024-01-18T14:30:00Z',
      updatedAt: '2024-01-18T14:30:00Z',
    },
  ];
};

// 生成历史记录mock数据
const generateMockHistoryData = (ruleId: string): SecurityRuleHistoryItem[] => {
  return [
    {
      id: 'history-001',
      ruleId,
      version: 3,
      operation: 'approve',
      operatorId: 'admin-001',
      operatorName: '管理员',
      changes: [
        {
          field: 'auditStatus',
          oldValue: 'pending',
          newValue: 'approved',
        },
      ],
      description: '审核通过，规则已生效',
      createdAt: '2024-01-20T10:30:00Z',
    },
    {
      id: 'history-002',
      ruleId,
      version: 2,
      operation: 'update',
      operatorId: 'user-002',
      operatorName: '开发者张三',
      changes: [
        {
          field: 'rules.ipBlocks',
          oldValue: ['*************'],
          newValue: ['*************', '10.0.0.0/24'],
        },
        {
          field: 'description',
          oldValue: '阻止恶意IP访问',
          newValue: '阻止来自恶意IP的访问请求',
        },
      ],
      description: '更新IP黑名单范围和描述信息',
      createdAt: '2024-01-19T14:20:00Z',
    },
    {
      id: 'history-003',
      ruleId,
      version: 1,
      operation: 'create',
      operatorId: 'user-001',
      operatorName: '安全专员李四',
      changes: [],
      description: '创建新的安全规则',
      createdAt: '2024-01-15T10:30:00Z',
    },
  ];
};

// 加载数据
const loadData = async () => {
  if (!selectedCluster.value) {
    return;
  }

  loading.value = true;

  try {
    // 使用统一的API获取所有类型的安全规则
    const response = await getSecurityRules({
      cluster: selectedCluster.value,
      page: pagination.current,
      pageSize: pagination.pageSize,
      // 不指定type参数，获取所有类型的规则
    });

    rulesData.value = response.data?.list || [];
    pagination.total = response.data?.total || 0;

    message.success('数据加载完成');
  } catch (error) {
    console.error('加载规则数据失败:', error);
    message.error('加载规则数据失败');

    // 如果API调用失败，回退到mock数据
    console.log('API调用失败，使用mock数据');
    const mockData = generateMockData();
    rulesData.value = mockData;
    pagination.total = mockData.length;
  } finally {
    loading.value = false;
  }
};

// 加载命名空间列表
const loadNamespaces = async () => {
  try {
    // 使用mock数据
    await new Promise((resolve) => setTimeout(resolve, 200));

    namespaces.value = [
      'default',
      'production',
      'development',
      'testing',
      'staging',
      'microservices',
      'admin',
      'gateway',
      'security',
      'kube-system',
      'istio-system',
    ];
  } catch (error) {
    console.error('加载命名空间失败:', error);
  }
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

// 添加规则
const handleAddRule = () => {
  drawerTitle.value = '添加安全规则';
  drawerVisible.value = true;

  // 重置表单
  Object.assign(formData, {
    id: '',
    name: '',
    type: 'blacklist',
    rules: {
      ipBlocks: [],
      principals: [],
      methods: [],
      paths: [],
      rateLimit: {
        domain: '',
        descriptors: [],
        failure_mode_deny: false,
        timeout: 25,
      },
    },
    enabled: true,
    description: '',
  });

  // 重置YAML内容
  editableYamlContent.value = '';
  editActiveKey.value = 'form';
};

// 处理更多操作
const handleMoreAction = (key: string, record: SecurityRuleItem) => {
  switch (key) {
    case 'copy':
      handleCopyRule(record);
      break;
    case 'audit':
      handleAuditRule(record);
      break;
    case 'delete':
      handleDeleteRule(record);
      break;
    case 'history':
      handleViewHistory(record);
      break;
    default:
      break;
  }
};

// 编辑规则
const handleEditRule = (record: SecurityRuleItem) => {
  drawerTitle.value = '编辑安全规则';
  drawerVisible.value = true;

  // 填充表单数据，确保限流配置完整
  Object.assign(formData, {
    id: record.id,
    name: record.name,
    type: record.type,
    rules: {
      ipBlocks: record.rules.ipBlocks || [],
      principals: record.rules.principals || [],
      methods: record.rules.methods || [],
      paths: record.rules.paths || [],
      rateLimit: {
        domain: record.rules.rateLimit?.domain || '',
        descriptors: record.rules.rateLimit?.descriptors || [],
        failure_mode_deny: record.rules.rateLimit?.failure_mode_deny || false,
        timeout: record.rules.rateLimit?.timeout || 25,
      },
    },
    enabled: record.enabled,
    description: record.description || '',
  });

  // 同步YAML内容
  setTimeout(() => {
    editableYamlContent.value = editYamlContent.value;
  }, 100);
};

// 复制规则
const handleCopyRule = (record: SecurityRuleItem) => {
  drawerTitle.value = '复制安全规则';
  drawerVisible.value = true;

  // 填充表单数据，但清空ID，确保限流配置完整
  Object.assign(formData, {
    id: '',
    name: `${record.name}_copy`,
    type: record.type,
    rules: {
      ipBlocks: record.rules.ipBlocks || [],
      principals: record.rules.principals || [],
      methods: record.rules.methods || [],
      paths: record.rules.paths || [],
      rateLimit: record.rules.rateLimit || {
        domain: '',
        descriptors: [],
        failure_mode_deny: false,
        timeout: 25,
      },
    },
    enabled: record.enabled,
    description: record.description || '',
  });
};

// 切换规则状态
const handleToggleRule = async (record: SecurityRuleItem) => {
  try {
    await updateSecurityRule(record);
    message.success('规则状态更新成功');
  } catch (error) {
    console.error('更新规则状态失败:', error);
    message.error('更新规则状态失败');
    // 恢复原状态
    record.enabled = !record.enabled;
  }
};

// 查看历史记录
const handleViewHistory = async (record: SecurityRuleItem) => {
  currentHistoryRule.value = record;
  historyDrawerVisible.value = true;
  historyLoading.value = true;

  try {
    // 调用真实API获取历史记录
    const response = await getSecurityRuleHistory({
      ruleId: record.id,
      page: 1,
      pageSize: 50,
    });

    historyData.value = response.data?.list || [];
  } catch (error) {
    console.error('获取历史记录失败:', error);
    message.error('获取历史记录失败');

    // 如果API调用失败，回退到mock数据
    console.log('历史记录API调用失败，使用mock数据');
    const mockHistory = generateMockHistoryData(record.id);
    historyData.value = mockHistory;
  } finally {
    historyLoading.value = false;
  }
};

// 查看版本对比
const handleViewVersionDiff = async (
  historyRecord: SecurityRuleHistoryItem,
) => {
  currentDiffVersion.value = historyRecord;
  diffDrawerVisible.value = true;

  try {
    // 生成当前版本的YAML
    if (currentHistoryRule.value) {
      if (currentHistoryRule.value.type === 'ratelimit') {
        currentVersionYaml.value = generateRateLimitYaml(
          currentHistoryRule.value,
        );
      } else {
        currentVersionYaml.value = generateAuthorizationPolicyYaml(
          currentHistoryRule.value,
        );
      }
    }

    // 生成历史版本的YAML（这里简化处理，实际应该从历史数据中获取）
    // 模拟历史版本的配置
    const historicalRule = {
      ...currentHistoryRule.value!,
      // 根据变更记录模拟历史版本的差异
    };

    if (
      historyRecord.operation === 'update' &&
      historyRecord.changes.length > 0
    ) {
      // 应用变更记录来还原历史版本
      historyRecord.changes.forEach((change) => {
        if (change.field === 'rules.ipBlocks') {
          historicalRule.rules.ipBlocks = change.oldValue as string[];
        } else if (change.field === 'description') {
          historicalRule.description = change.oldValue as string;
        }
        // 可以根据需要添加更多字段的处理
      });
    }

    if (historicalRule.type === 'ratelimit') {
      previousVersionYaml.value = generateRateLimitYaml(historicalRule);
    } else {
      previousVersionYaml.value =
        generateAuthorizationPolicyYaml(historicalRule);
    }
  } catch (error) {
    console.error('生成版本对比YAML失败:', error);
    message.error('生成版本对比失败');
  }
};

// 删除规则
const handleDeleteRule = async (record: SecurityRuleItem) => {
  try {
    await deleteSecurityRule({
      cluster: selectedCluster.value,
      namespace: record.namespace,
      id: record.id,
    });

    message.success('规则删除成功');
    loadData();
  } catch (error) {
    console.error('删除规则失败:', error);
    message.error('删除规则失败');
  }
};

// 保存规则
const handleSaveRule = async () => {
  try {
    await formRef.value?.validate();

    const ruleData = {
      name: formData.name,
      namespace: 'default', // 使用默认命名空间
      cluster: selectedCluster.value,
      type: formData.type,
      rules: formData.rules,
      enabled: formData.enabled,
      description: formData.description,
    };

    if (formData.id) {
      // 更新规则
      await updateSecurityRule({
        ...ruleData,
        id: formData.id,
      } as SecurityRuleItem);
      message.success('规则更新成功');
    } else {
      // 创建规则
      await createSecurityRule(ruleData);
      message.success('规则创建成功');
    }

    drawerVisible.value = false;
    loadData();
  } catch (error) {
    console.error('保存规则失败:', error);
    message.error('保存规则失败');
  }
};

// 取消抽屉
const handleCancelDrawer = () => {
  drawerVisible.value = false;
  formRef.value?.resetFields();
};

// 获取类型标签颜色
const getTypeTagColor = (type: string) => {
  switch (type) {
    case 'blacklist':
      return 'red';
    case 'whitelist':
      return 'green';
    case 'ratelimit':
      return 'blue';
    default:
      return 'default';
  }
};

// 获取类型标签文本
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'blacklist':
      return '黑名单';
    case 'whitelist':
      return '白名单';
    case 'ratelimit':
      return '限流';
    default:
      return type;
  }
};

// 获取审核状态标签颜色
const getAuditStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'orange';
    case 'approved':
      return 'green';
    case 'rejected':
      return 'red';
    case 'applied':
      return 'blue';
    default:
      return 'default';
  }
};

// 获取审核状态标签文本
const getAuditStatusLabel = (status: string) => {
  switch (status) {
    case 'pending':
      return '待审核';
    case 'approved':
      return '已通过';
    case 'rejected':
      return '已驳回';
    case 'applied':
      return '已应用';
    default:
      return '未知';
  }
};

// 获取操作类型颜色
const getOperationColor = (operation: string) => {
  switch (operation) {
    case 'create':
      return 'green';
    case 'update':
      return 'blue';
    case 'delete':
      return 'red';
    case 'approve':
      return 'green';
    case 'reject':
      return 'red';
    default:
      return 'default';
  }
};

// 获取操作类型标签
const getOperationLabel = (operation: string) => {
  switch (operation) {
    case 'create':
      return '创建';
    case 'update':
      return '更新';
    case 'delete':
      return '删除';
    case 'approve':
      return '审核通过';
    case 'reject':
      return '审核驳回';
    default:
      return operation;
  }
};

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// // 获取字段标签
// const getFieldLabel = (field: string) => {
//   const fieldMap: Record<string, string> = {
//     'auditStatus': '审核状态',
//     'rules.ipBlocks': 'IP地址/CIDR',
//     'rules.principals': '用户主体',
//     'rules.methods': 'HTTP方法',
//     'rules.paths': '请求路径',
//     'description': '描述',
//     'enabled': '启用状态',
//   };
//   return fieldMap[field] || field;
// };

// // 格式化值
// const formatValue = (value: any) => {
//   if (Array.isArray(value)) {
//     return value.join(', ');
//   }
//   if (typeof value === 'object') {
//     return JSON.stringify(value, null, 2);
//   }
//   return String(value);
// };

// 添加限流描述符
const addDescriptor = () => {
  formData.rules.rateLimit.descriptors.push({
    key: 'header_match',
    value: 'user-id',
    rate_limit: {
      requests_per_unit: 100,
      unit: 'MINUTE',
    },
  });
};

// 移除限流描述符
const removeDescriptor = (index: number) => {
  if (formData.rules.rateLimit.descriptors.length > 1) {
    formData.rules.rateLimit.descriptors.splice(index, 1);
  } else {
    message.warning('至少需要保留一个限流规则');
  }
};

// 监听规则类型变化，清空不相关配置并设置默认值
watch(
  () => formData.type,
  (newType) => {
    if (newType === 'ratelimit') {
      // 切换到限流类型时，清空黑白名单配置
      formData.rules.ipBlocks = [];
      formData.rules.principals = [];
      formData.rules.methods = [];
      formData.rules.paths = [];

      // 如果切换到限流类型且没有描述符，添加一个默认的
      if (formData.rules.rateLimit.descriptors.length === 0) {
        addDescriptor();
      }
      // 设置默认域名
      if (!formData.rules.rateLimit.domain) {
        formData.rules.rateLimit.domain = 'productpage';
      }
    } else if (newType === 'blacklist' || newType === 'whitelist') {
      // 切换到黑白名单类型时，清空限流配置
      formData.rules.rateLimit = {
        domain: '',
        descriptors: [],
        failure_mode_deny: false,
        timeout: 25,
      };
    }
  },
);

// 监听标签页切换，实现自动同步
watch(editActiveKey, (newKey, oldKey) => {
  if (oldKey === 'form' && newKey === 'yaml') {
    // 从表单切换到YAML，自动同步表单到YAML
    editableYamlContent.value = editYamlContent.value;
  } else if (oldKey === 'yaml' && newKey === 'form') {
    // 从YAML切换到表单，自动同步YAML到表单
    try {
      const yamlData = parseYamlToJSON(editableYamlContent.value);
      if (yamlData) {
        // 解析基本信息
        if (yamlData.metadata) {
          formData.name = yamlData.metadata.name || '';
        }

        // 根据资源类型解析配置
        if (yamlData.kind === 'AuthorizationPolicy') {
          syncAuthorizationPolicyToForm(yamlData);
        } else if (yamlData.kind === 'EnvoyFilter') {
          syncEnvoyFilterToForm(yamlData);
        }
      }
    } catch (error) {
      console.warn('YAML格式错误，跳过自动同步:', error);
    }
  }
});

// 审核规则
const handleAuditRule = (record: SecurityRuleItem) => {
  try {
    let yamlContent = '';

    if (record.type === 'ratelimit') {
      yamlContent = generateRateLimitYaml(record);
    } else {
      yamlContent = generateAuthorizationPolicyYaml(record);
    }

    auditRule.value = record;
    currentYaml.value = yamlContent;
    auditActiveKey.value = 'form';
    auditDrawerVisible.value = true;
  } catch (error) {
    console.error('生成YAML失败:', error);
    message.error('生成YAML配置失败');
  }
};

// 审核通过并自动应用到集群
const handleApproveRule = async () => {
  try {
    if (auditRule.value) {
      // 显示加载状态
      const loadingMessage = message.loading('正在应用规则到集群...', 0);

      // 调用后端API应用规则到集群
      await applySecurityRuleToCluster(auditRule.value, currentYaml.value);

      // 更新审核状态为已应用
      auditRule.value.auditStatus = 'applied';

      // 关闭加载状态
      loadingMessage();

      message.success('规则审核通过，已自动应用到集群');
      auditDrawerVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error('应用规则失败:', error);
    message.error('应用规则到集群失败，请检查集群状态');
  }
};

// 审核驳回
const handleRejectRule = () => {
  Modal.confirm({
    title: '确认驳回',
    content: '确定要驳回这条安全规则吗？驳回后需要重新提交审核。',
    okText: '确认驳回',
    cancelText: '取消',
    onOk: async () => {
      try {
        if (auditRule.value) {
          // 更新审核状态为已驳回
          auditRule.value.auditStatus = 'rejected';

          message.success('规则已驳回');
          auditDrawerVisible.value = false;
          loadData();
        }
      } catch (error) {
        console.error('驳回规则失败:', error);
        message.error('驳回规则失败');
      }
    },
  });
};

// 复制YAML到剪贴板
const handleCopyYaml = async () => {
  try {
    await navigator.clipboard.writeText(currentYaml.value);
    message.success('YAML已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动复制');
  }
};

// 下载YAML文件
const handleDownloadYaml = () => {
  const blob = new Blob([currentYaml.value], { type: 'text/yaml' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'security-rule.yaml';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  message.success('YAML文件下载完成');
};

// 同步AuthorizationPolicy到表单
const syncAuthorizationPolicyToForm = (yamlData: any) => {
  if (!yamlData.spec) {
    return;
  }

  // 设置规则类型
  if (yamlData.spec.action === 'DENY') {
    formData.type = 'blacklist';
  } else if (yamlData.spec.action === 'ALLOW') {
    formData.type = 'whitelist';
  }

  // 解析规则
  if (yamlData.spec.rules && yamlData.spec.rules[0]) {
    const rule = yamlData.spec.rules[0];

    // 来源配置
    if (rule.from && rule.from[0] && rule.from[0].source) {
      const source = rule.from[0].source;
      if (source.ipBlocks) {
        formData.rules.ipBlocks = source.ipBlocks;
      }
      if (source.principals) {
        formData.rules.principals = source.principals;
      }
    }

    // 目标配置
    if (rule.to && rule.to[0] && rule.to[0].operation) {
      const operation = rule.to[0].operation;
      if (operation.methods) {
        formData.rules.methods = operation.methods;
      }
      if (operation.paths) {
        formData.rules.paths = operation.paths;
      }
    }
  }
};

// 同步EnvoyFilter到表单
const syncEnvoyFilterToForm = (yamlData: any) => {
  formData.type = 'ratelimit';

  // 解析限流配置
  if (yamlData.spec && yamlData.spec.configPatches) {
    const configPatch = yamlData.spec.configPatches[0];
    if (configPatch && configPatch.patch && configPatch.patch.value) {
      const value = configPatch.patch.value;
      if (value.typed_config && value.typed_config.value) {
        const rateLimitConfig = value.typed_config.value;

        // 设置默认限流配置
        formData.rules.rateLimit = {
          domain: yamlData.metadata.name.replace('-ratelimit', ''),
          descriptors: [
            {
              key: 'header_match',
              value: 'user-id',
              rate_limit: {
                requests_per_unit:
                  rateLimitConfig.token_bucket?.max_tokens || 100,
                unit: 'MINUTE',
              },
            },
          ],
          failure_mode_deny: false,
          timeout: 25,
        };
      }
    }
  }
};

// 组件挂载
onMounted(() => {
  if (selectedCluster.value) {
    loadData();
    loadNamespaces();
  }
});

// 暴露刷新方法、添加规则方法和筛选器
defineExpose({
  refresh: loadData,
  addRule: handleAddRule,
  filterType,
  searchText,
});
</script>

<style scoped>
.security-rule-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-help {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-top: 4px;
}

:deep(.ant-table-tbody > tr > td) {
  vertical-align: top;
}

:deep(.ant-table-wrapper) {
  flex: 1;
}

:deep(.ant-divider) {
  margin: 16px 0;
}

/* 抽屉表单样式优化 */
:deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-form-horizontal .ant-form-item-label) {
  text-align: right;
  font-weight: 500;
}

:deep(.ant-form-horizontal .ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-horizontal .ant-form-item-control) {
  line-height: 32px;
}

.form-help {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

/* 限流描述符样式 */
.descriptor-list {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.descriptor-item {
  margin-bottom: 16px;
}

.descriptor-item:last-child {
  margin-bottom: 0;
}

/* YAML预览样式 */
.yaml-preview {
  height: 100%;
}

.yaml-content {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  max-height: calc(100vh - 200px);
}

.yaml-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.45;
  color: #24292e;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.yaml-content code {
  background: transparent;
  border: none;
  padding: 0;
  font-family: inherit;
}

/* YAML编辑器样式 */
.yaml-editor {
  height: 100%;
}

.yaml-editor-content {
  margin-top: 8px;
}

/* 历史记录样式 */
.history-content {
  padding: 16px 0;
}

.rule-info {
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.timeline-dot {
  background: #fff;
  border: 2px solid #1890ff;
  border-radius: 50%;
  color: #1890ff;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  display: inline-block;
  min-width: 32px;
  text-align: center;
}

.history-item {
  padding-left: 8px;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.operation-type {
  font-weight: 500;
}

.operator {
  color: #666;
  font-size: 14px;
}

.time {
  color: #999;
  font-size: 12px;
  margin-left: auto;
}

.history-description {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}

.history-changes {
  margin-top: 8px;
}

.change-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
}

.change-field {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.change-values {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.old-value,
.new-value {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.old-value .label {
  color: #ff4d4f;
  font-weight: 500;
  min-width: 40px;
}

.new-value .label {
  color: #52c41a;
  font-weight: 500;
  min-width: 40px;
}

.old-value code,
.new-value code {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  flex: 1;
}

.old-value code {
  background: #fff2f0;
  border-color: #ffccc7;
}

.new-value code {
  background: #f6ffed;
  border-color: #b7eb8f;
}

/* 版本对比样式 */
.diff-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.diff-header {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.yaml-diff-container {
  height: calc(100vh - 170px);
  /* border: 1px solid #d9d9d9; */
  border-radius: 6px;
  overflow: hidden;
}
</style>
