<template>
  <div class="interface-overview">
    <a-tabs
      v-model:activeKey="activeKey"
      tab-position="left"
      size="small"
      :tab-bar-style="{ marginLeft: '-24px' }"
    >
      <a-tab-pane key="1">
        <template #tab>
          <span style="font-size: 12px">接口概览</span>
        </template>
        <div class="tab-content-wrapper">
          <!-- 接口概览的控制栏 -->
          <div class="control-bar">
            <!-- 最后更新时间 -->
            <div v-if="overviewData.lastUpdated" class="update-time">
              <a-typography-text type="secondary" style="font-size: 12px">
                最后更新: {{ formatUpdateTime(overviewData.lastUpdated) }}
                <span v-if="isCustomTimeActive">| 自定义时间范围</span>
              </a-typography-text>
            </div>

            <div class="time-controls">
              <a-space align="center" size="large">
                <a-range-picker
                  v-model:value="customDateRange"
                  show-time
                  format="YYYY-MM-DD HH:mm"
                  style="width: 320px"
                  :placeholder="['开始时间', '结束时间']"
                  :disabled-date="disabledDate"
                  size="small"
                  @change="handleCustomDateChange"
                />
                <a-select
                  v-model:value="timeRange"
                  :options="
                    TimeRangeOptions.filter((item) => item.value !== 'custom')
                  "
                  style="width: 140px"
                  size="small"
                  @change="handleTimeRangeChange"
                />
              </a-space>
              <a-space>
                <a-button
                  :loading="loading"
                  size="small"
                  @click="handleRefresh"
                >
                  <template #icon>
                    <reload-outlined />
                  </template>
                  刷新数据
                </a-button>
                <a-button
                  :disabled="!interfaceList.length"
                  size="small"
                  @click="handleExportData"
                >
                  <template #icon>
                    <download-outlined />
                  </template>
                  导出数据
                </a-button>
                <a-button type="dashed" size="small" @click="handleFullscreen">
                  <template #icon>
                    <fullscreen-outlined />
                  </template>
                  全屏
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 滚动内容区域 -->
          <div ref="overviewScrollRef" class="scrollable-content">
            <div class="overview-content">
              <!-- 概览统计卡片 -->
              <a-row :gutter="[16, 16]" class="stats-cards">
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="总接口数"
                      :value="overviewData.totalInterfaces"
                      :value-style="{ color: '#1890ff' }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="平均QPS"
                      :value="overviewData.avgQPS"
                      :precision="2"
                      suffix="req/s"
                      :value-style="{ color: '#52c41a' }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="平均响应时间"
                      :value="overviewData.avgRT"
                      :precision="2"
                      suffix="ms"
                      :value-style="{
                        color:
                          overviewData.avgRT > 1000 ? '#ff4d4f' : '#52c41a',
                      }"
                    />
                  </a-card>
                </a-col>
                <a-col :span="6">
                  <a-card size="small">
                    <a-statistic
                      title="平均错误率"
                      :value="overviewData.errorRate"
                      :precision="2"
                      suffix="%"
                      :value-style="{
                        color:
                          overviewData.errorRate > 1 ? '#ff4d4f' : '#52c41a',
                      }"
                    />
                  </a-card>
                </a-col>
              </a-row>

              <!-- 接口趋势图表 -->
              <a-row :gutter="[16, 16]">
                <a-col :span="12">
                  <a-card title="QPS 趋势" size="small" class="chart-card">
                    <div class="chart-content">
                      <div v-if="qpsTrendData.length > 0" class="mini-chart">
                        <div class="chart-header">
                          <span class="chart-value">{{ latestQPS }}</span>
                          <span class="chart-unit">req/s</span>
                          <span
                            :class="[
                              'chart-trend',
                              qpsTrend > 0 ? 'up' : 'down',
                            ]"
                          >
                            {{ qpsTrend > 0 ? '↗' : '↘' }}
                            {{ Math.abs(qpsTrend).toFixed(1) }}%
                          </span>
                        </div>
                        <div class="chart-line">
                          <svg width="100%" height="60" viewBox="0 0 200 60">
                            <!-- 背景网格 -->
                            <defs>
                              <pattern
                                id="grid"
                                width="20"
                                height="12"
                                patternUnits="userSpaceOnUse"
                              >
                                <path
                                  d="M 20 0 L 0 0 0 12"
                                  fill="none"
                                  stroke="#f0f0f0"
                                  stroke-width="0.5"
                                />
                              </pattern>
                            </defs>
                            <rect
                              width="100%"
                              height="100%"
                              fill="url(#grid)"
                            />

                            <!-- 趋势线 -->
                            <polyline
                              :points="qpsTrendPoints"
                              fill="none"
                              stroke="#1890ff"
                              stroke-width="2.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />

                            <!-- 渐变填充 -->
                            <defs>
                              <linearGradient
                                id="gradient"
                                x1="0%"
                                y1="0%"
                                x2="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0%"
                                  style="stop-color: #1890ff; stop-opacity: 0.3"
                                />
                                <stop
                                  offset="100%"
                                  style="
                                    stop-color: #1890ff;
                                    stop-opacity: 0.05;
                                  "
                                />
                              </linearGradient>
                            </defs>
                            <polygon
                              :points="`${qpsTrendPoints} 200,60 0,60`"
                              fill="url(#gradient)"
                            />
                          </svg>
                        </div>
                      </div>
                      <a-empty v-else description="暂无数据" />
                    </div>
                  </a-card>
                </a-col>

                <a-col :span="12">
                  <a-card title="响应时间分布" size="small" class="chart-card">
                    <div class="chart-content">
                      <div
                        v-if="responseTimeData.length > 0"
                        class="distribution-chart"
                      >
                        <div class="distribution-bars">
                          <div
                            v-for="item in responseTimeData"
                            :key="item.range"
                            class="distribution-bar"
                          >
                            <div
                              class="bar"
                              :style="{
                                height: `${
                                  (item.count / maxResponseCount) * 100
                                }%`,
                              }"
                            ></div>
                            <div class="bar-label">{{ item.range }}</div>
                            <div class="bar-count">{{ item.count }}</div>
                          </div>
                        </div>
                      </div>
                      <a-empty v-else description="暂无数据" />
                    </div>
                  </a-card>
                </a-col>

                <a-col :span="12">
                  <a-card title="错误率统计" size="small" class="chart-card">
                    <div class="chart-content">
                      <div v-if="errorRateData.length > 0" class="error-chart">
                        <div class="error-summary">
                          <div class="error-item">
                            <span class="error-type">2xx</span>
                            <span class="error-count success">
                              {{
                                errorRateData.find(
                                  (item) => item.type === '2xx',
                                )?.count || 0
                              }}
                            </span>
                          </div>
                          <div class="error-item">
                            <span class="error-type">4xx</span>
                            <span class="error-count warning">
                              {{
                                errorRateData.find(
                                  (item) => item.type === '4xx',
                                )?.count || 0
                              }}
                            </span>
                          </div>
                          <div class="error-item">
                            <span class="error-type">5xx</span>
                            <span class="error-count error">
                              {{
                                errorRateData.find(
                                  (item) => item.type === '5xx',
                                )?.count || 0
                              }}
                            </span>
                          </div>
                        </div>
                        <div class="error-rate-pie">
                          <div
                            class="pie-chart"
                            :style="{
                              background: `conic-gradient(
                                #52c41a 0deg ${successPercentage * 3.6}deg,
                                #faad14 ${successPercentage * 3.6}deg ${
                                (successPercentage + warningPercentage) * 3.6
                              }deg,
                                #ff4d4f ${
                                  (successPercentage + warningPercentage) * 3.6
                                }deg 360deg
                              )`,
                            }"
                          >
                            <div class="pie-center">
                              <span class="pie-total">
                                {{
                                  errorRateData.reduce(
                                    (sum, item) => sum + item.count,
                                    0,
                                  )
                                }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <a-empty v-else description="暂无数据" />
                    </div>
                  </a-card>
                </a-col>

                <a-col :span="12">
                  <a-card title="热门接口排行" size="small" class="chart-card">
                    <div class="chart-content">
                      <div
                        v-if="interfaceList.length > 0"
                        class="hot-interfaces"
                      >
                        <div
                          v-for="(item, index) in topInterfaces"
                          :key="item.path"
                          class="hot-interface-item"
                        >
                          <div class="rank">{{ index + 1 }}</div>
                          <div class="interface-info">
                            <div class="path">{{ item.path }}</div>
                            <div class="method">
                              <a-tag
                                :color="getMethodColor(item.method)"
                                size="small"
                              >
                                {{ item.method }}
                              </a-tag>
                            </div>
                          </div>
                          <div class="qps">{{ item.qps.toFixed(2) }}</div>
                        </div>
                      </div>
                      <a-empty v-else description="暂无数据" />
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="2">
        <template #tab>
          <span style="font-size: 12px">接口详情</span>
        </template>
        <div class="tab-content-wrapper">
          <!-- 接口详情的控制栏 -->
          <div class="control-bar">
            <a-space>
              <a-button :loading="loading" size="small" @click="handleRefresh">
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
              <a-button
                :disabled="!interfaceList.length"
                size="small"
                @click="handleExportData"
              >
                <template #icon>
                  <download-outlined />
                </template>
                导出详情
              </a-button>
            </a-space>

            <!-- 接口统计信息 -->
            <div class="interface-stats">
              <a-typography-text type="secondary" style="font-size: 12px">
                共 {{ interfaceList.length }} 个接口
              </a-typography-text>
            </div>
          </div>

          <!-- 滚动内容区域 -->
          <div ref="detailScrollRef" class="scrollable-content">
            <div class="interface-details">
              <!-- 接口列表表格 -->
              <a-table
                :columns="interfaceColumns"
                :data-source="interfaceList"
                :loading="loading"
                :pagination="{
                  pageSize: 50,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }"
                size="small"
                row-key="path"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'method'">
                    <a-tag :color="getMethodColor(record.method)">
                      {{ record.method }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'qps'">
                    <span
                      :style="{
                        color: record.qps > 10 ? '#52c41a' : '#faad14',
                      }"
                    >
                      {{ record.qps.toFixed(2) }}
                    </span>
                  </template>
                  <template v-if="column.key === 'avgRT'">
                    <span
                      :style="{
                        color: record.avgRT > 1000 ? '#ff4d4f' : '#52c41a',
                      }"
                    >
                      {{ record.avgRT.toFixed(2) }}ms
                    </span>
                  </template>
                  <template v-if="column.key === 'errorRate'">
                    <span
                      :style="{
                        color: record.errorRate > 1 ? '#ff4d4f' : '#52c41a',
                      }"
                    >
                      {{ record.errorRate.toFixed(2) }}%
                    </span>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, watch, onMounted, nextTick } from 'vue';
import {
  ReloadOutlined,
  DownloadOutlined,
  FullscreenOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

// 从父组件注入集群相关状态
const selectedCluster = inject('selectedCluster', ref(''));
const clusterOptionsLoaded = inject('clusterOptionsLoaded', ref(false));

const activeKey = ref('1');
const loading = ref(false);
const timeRange = ref('24h');
const customDateRange = ref<[Dayjs, Dayjs] | null>(null);
const interfaceList = ref([]);
const overviewData = ref({
  totalInterfaces: 0,
  avgQPS: 0,
  avgRT: 0,
  errorRate: 0,
  lastUpdated: null,
});

// 图表数据
const qpsTrendData = ref([]);
const responseTimeData = ref([]);
const errorRateData = ref([]);

// 滚动容器引用
const overviewScrollRef = ref<HTMLDivElement>();
const detailScrollRef = ref<HTMLDivElement>();

// 时间范围选项
const TimeRangeOptions = [
  { label: '最近1小时', value: '1h' },
  { label: '最近6小时', value: '6h' },
  { label: '最近24小时', value: '24h' },
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '自定义', value: 'custom' },
];

// 接口表格列定义
const interfaceColumns = [
  {
    title: '接口路径',
    dataIndex: 'path',
    key: 'path',
    width: 300,
    ellipsis: true,
  },
  {
    title: '方法',
    dataIndex: 'method',
    key: 'method',
    width: 80,
    align: 'center',
  },
  {
    title: 'QPS',
    dataIndex: 'qps',
    key: 'qps',
    width: 100,
    align: 'right',
    sorter: (a: any, b: any) => a.qps - b.qps,
  },
  {
    title: '平均响应时间',
    dataIndex: 'avgRT',
    key: 'avgRT',
    width: 120,
    align: 'right',
    sorter: (a: any, b: any) => a.avgRT - b.avgRT,
  },
  {
    title: '错误率',
    dataIndex: 'errorRate',
    key: 'errorRate',
    width: 100,
    align: 'right',
    sorter: (a: any, b: any) => a.errorRate - b.errorRate,
  },
];

// 计算属性
const isCustomTimeActive = computed(() => {
  return timeRange.value === 'custom' && customDateRange.value;
});

// 获取热门接口（按 QPS 排序）
const topInterfaces = computed(() => {
  return [...interfaceList.value].sort((a, b) => b.qps - a.qps).slice(0, 5);
});

// QPS 趋势相关计算属性
const latestQPS = computed(() => {
  if (qpsTrendData.value.length === 0) {
    return 0;
  }
  return qpsTrendData.value[qpsTrendData.value.length - 1].value.toFixed(1);
});

const qpsTrend = computed(() => {
  if (qpsTrendData.value.length < 2) {
    return 0;
  }
  const current = qpsTrendData.value[qpsTrendData.value.length - 1].value;
  const previous = qpsTrendData.value[qpsTrendData.value.length - 2].value;
  return ((current - previous) / previous) * 100;
});

const qpsTrendPoints = computed(() => {
  if (qpsTrendData.value.length === 0) {
    return '';
  }

  const maxValue = Math.max(...qpsTrendData.value.map((item) => item.value));
  const minValue = Math.min(...qpsTrendData.value.map((item) => item.value));
  const range = maxValue - minValue || 1;

  return qpsTrendData.value
    .map((item, index) => {
      const x = (index / (qpsTrendData.value.length - 1)) * 200;
      const y = 60 - ((item.value - minValue) / range) * 60;
      return `${x},${y}`;
    })
    .join(' ');
});

// 响应时间分布相关计算属性
const maxResponseCount = computed(() => {
  if (responseTimeData.value.length === 0) {
    return 1;
  }
  return Math.max(...responseTimeData.value.map((item) => item.count));
});

// 错误率统计相关计算属性
const successPercentage = computed(() => {
  const total = errorRateData.value.reduce((sum, item) => sum + item.count, 0);
  if (total === 0) {
    return 0;
  }
  const success =
    errorRateData.value.find((item) => item.type === '2xx')?.count || 0;
  return (success / total) * 100;
});

const warningPercentage = computed(() => {
  const total = errorRateData.value.reduce((sum, item) => sum + item.count, 0);
  if (total === 0) {
    return 0;
  }
  const warning =
    errorRateData.value.find((item) => item.type === '4xx')?.count || 0;
  return (warning / total) * 100;
});

// const errorPercentage = computed(() => {
//   const total = errorRateData.value.reduce((sum, item) => sum + item.count, 0);
//   if (total === 0) return 0;
//   const error = errorRateData.value.find(item => item.type === '5xx')?.count || 0;
//   return (error / total) * 100;
// });

// 格式化更新时间
const formatUpdateTime = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
};

// 获取方法颜色
const getMethodColor = (method: string) => {
  const colorMap: Record<string, string> = {
    GET: 'green',
    POST: 'blue',
    PUT: 'orange',
    DELETE: 'red',
    PATCH: 'purple',
  };
  return colorMap[method] || 'default';
};

// 禁用日期
const disabledDate = (current: Dayjs) => {
  return current && current > dayjs().endOf('day');
};

// 处理时间范围变化
const handleTimeRangeChange = (value: string) => {
  timeRange.value = value;
  if (value !== 'custom') {
    customDateRange.value = null;
    loadData();
  }
};

// 处理自定义日期范围变化
const handleCustomDateChange = (dates: [Dayjs, Dayjs] | null) => {
  customDateRange.value = dates;
  if (dates) {
    timeRange.value = 'custom';
    loadData();
  }
};

// 刷新数据
const handleRefresh = () => {
  if (!selectedCluster.value) {
    message.warning('请先选择集群');
    return;
  }
  loadData();
};

// 导出数据
const handleExportData = () => {
  message.info('导出功能开发中');
};

// 全屏
const handleFullscreen = () => {
  message.info('全屏功能开发中');
};

// 加载数据
const loadData = async () => {
  if (!selectedCluster.value) {
    return;
  }

  loading.value = true;
  try {
    // 模拟数据加载
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟接口数据
    interfaceList.value = [
      {
        path: '/api/v1/users',
        method: 'GET',
        qps: 15.6,
        avgRT: 120.5,
        errorRate: 0.2,
      },
      {
        path: '/api/v1/users',
        method: 'POST',
        qps: 8.3,
        avgRT: 200.1,
        errorRate: 0.5,
      },
      {
        path: '/api/v1/orders',
        method: 'GET',
        qps: 25.8,
        avgRT: 85.2,
        errorRate: 0.1,
      },
      {
        path: '/api/v1/products',
        method: 'GET',
        qps: 32.1,
        avgRT: 95.3,
        errorRate: 0.15,
      },
      {
        path: '/api/v1/orders',
        method: 'POST',
        qps: 18.7,
        avgRT: 156.8,
        errorRate: 0.3,
      },
      {
        path: '/api/v1/auth/login',
        method: 'POST',
        qps: 12.4,
        avgRT: 89.2,
        errorRate: 0.05,
      },
      {
        path: '/api/v1/products/search',
        method: 'GET',
        qps: 28.9,
        avgRT: 145.6,
        errorRate: 0.2,
      },
    ] as any;

    // 模拟概览数据
    overviewData.value = {
      totalInterfaces: interfaceList.value.length,
      avgQPS: 16.57,
      avgRT: 135.27,
      errorRate: 0.27,
      lastUpdated: Date.now(),
    };

    // 模拟 QPS 趋势数据（过去24小时）
    qpsTrendData.value = Array.from({ length: 24 }, (_, i) => ({
      time: dayjs()
        .subtract(23 - i, 'hour')
        .format('HH:mm'),
      value: 15 + Math.random() * 20 + Math.sin(i / 4) * 5,
    })) as any;

    // 模拟响应时间分布数据
    responseTimeData.value = [
      { range: '0-100ms', count: 1250 },
      { range: '100-200ms', count: 890 },
      { range: '200-500ms', count: 420 },
      { range: '500ms-1s', count: 180 },
      { range: '>1s', count: 60 },
    ] as any;

    // 模拟错误率统计数据
    errorRateData.value = [
      { type: '2xx', count: 2750 },
      { type: '4xx', count: 45 },
      { type: '5xx', count: 5 },
    ] as any;

    message.success('数据加载完成');
  } catch (error) {
    message.error('数据加载失败');
  } finally {
    loading.value = false;
  }
};

// 监听集群变化
watch(
  selectedCluster,
  (newCluster) => {
    if (newCluster && clusterOptionsLoaded.value) {
      console.log('接口概览 - 集群切换到:', newCluster);
      loadData();
    }
  },
  { immediate: false },
);

// 滚动条显示/隐藏逻辑
const setupScrollbarBehavior = () => {
  const scrollContainers = [overviewScrollRef.value, detailScrollRef.value];

  scrollContainers.forEach((container) => {
    if (!container) {
      return;
    }

    let scrollTimer: NodeJS.Timeout | null = null;

    const handleScroll = () => {
      container.classList.add('scrolling');
      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }
      scrollTimer = setTimeout(() => {
        container.classList.remove('scrolling');
      }, 1000);
    };

    const handleMouseEnter = () => {
      container.classList.add('hover-scrollbar');
    };

    const handleMouseLeave = () => {
      container.classList.remove('hover-scrollbar');
    };

    container.addEventListener('scroll', handleScroll);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);
  });
};

onMounted(async () => {
  await nextTick();
  setupScrollbarBehavior();
  loadData();
});
</script>

<style scoped>
.interface-overview {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 180px);
}

.control-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
  gap: 16px;
  flex-wrap: wrap;
}

.update-time {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.time-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.interface-stats {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.overview-content,
.interface-details {
  padding: 0;
  min-height: 400px;
}

.stats-cards {
  margin-bottom: 24px;
}

.chart-card {
  height: 280px;
  margin-bottom: 16px;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.chart-description {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
  max-width: 200px;
}

.hot-interfaces {
  width: 100%;
  height: 100%;
  background: rgba(24, 144, 255, 0.02);
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hot-interface-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.hot-interface-item:last-child {
  margin-bottom: 0;
}

.hot-interface-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rank {
  width: 28px;
  height: 28px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  margin-right: 12px;
  flex-shrink: 0;
}

.hot-interface-item:first-child .rank {
  background: linear-gradient(135deg, #ffd700, #ffed4a);
  color: #fff;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.hot-interface-item:nth-child(2) .rank {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #666;
}

.hot-interface-item:nth-child(3) .rank {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #fff;
}

.interface-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.path {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.method {
  display: flex;
  align-items: center;
}

.qps {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  flex-shrink: 0;
}

/* 图表样式 */
.mini-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-header {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12px;
  gap: 8px;
  flex-shrink: 0;
}

.chart-value {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.chart-unit {
  font-size: 14px;
  color: #666;
}

.chart-trend {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.04);
}

.chart-trend.up {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.chart-trend.down {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.chart-line {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  background: rgba(24, 144, 255, 0.02);
  border-radius: 6px;
  padding: 8px;
}

.distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.distribution-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  flex: 1;
  gap: 6px;
  padding: 16px 8px 8px 8px;
  background: rgba(24, 144, 255, 0.02);
  border-radius: 6px;
  min-height: 140px;
}

.distribution-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: flex-end;
}

.bar {
  width: 100%;
  background: linear-gradient(to top, #1890ff, #69c0ff);
  border-radius: 3px 3px 0 0;
  min-height: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.bar:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.bar-label {
  font-size: 10px;
  color: #666;
  text-align: center;
  margin-bottom: 4px;
  line-height: 1.1;
  font-weight: 500;
}

.bar-count {
  font-size: 12px;
  font-weight: 600;
  color: #1890ff;
}

.error-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(24, 144, 255, 0.02);
  border-radius: 6px;
  padding: 16px;
}

.error-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.error-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  min-width: 60px;
}

.error-type {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.error-count {
  font-size: 18px;
  font-weight: 600;
}

.error-count.success {
  color: #52c41a;
}

.error-count.warning {
  color: #faad14;
}

.error-count.error {
  color: #ff4d4f;
}

.error-rate-pie {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
  flex-shrink: 0;
}

.pie-chart {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pie-center {
  width: 45%;
  height: 45%;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pie-total {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 自定义滚动条样式 */
.scrollable-content {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.scrollable-content::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.scrollable-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

.scrollable-content.hover-scrollbar {
  scrollbar-color: rgba(193, 193, 193, 0.6) transparent;
}

.scrollable-content.hover-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(193, 193, 193, 0.6);
  opacity: 1;
}

.scrollable-content.scrolling {
  scrollbar-color: #c1c1c1 transparent;
}

.scrollable-content.scrolling::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  opacity: 1;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
  opacity: 1;
}

.scrollable-content::-webkit-scrollbar-thumb:active {
  background: #999 !important;
  opacity: 1;
}

:deep(.ant-card-body) {
  padding: 16px;
  height: calc(100% - 57px);
}

:deep(.ant-card-head) {
  min-height: 57px;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

@media (max-width: 1200px) {
  .stats-cards :deep(.ant-col) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .chart-card {
    height: 260px;
  }
}

@media (max-width: 768px) {
  .tab-content-wrapper {
    max-height: calc(100vh - 150px);
  }

  .control-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .time-controls {
    justify-content: center;
  }

  .interface-stats {
    align-self: center;
  }

  .stats-cards :deep(.ant-col) {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .chart-card {
    height: 240px;
  }

  .chart-value {
    font-size: 24px;
  }

  .distribution-bars {
    min-height: 100px;
    padding: 12px 6px 6px 6px;
  }

  .error-summary {
    gap: 8px;
  }

  .error-item {
    padding: 6px 8px;
    min-width: 50px;
  }

  .error-rate-pie {
    width: 80px;
    height: 80px;
  }

  .hot-interface-item {
    padding: 8px 10px;
  }

  .rank {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }

  .qps {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .chart-card {
    height: 220px;
  }

  .chart-value {
    font-size: 20px;
  }

  .distribution-bars {
    min-height: 80px;
  }

  .error-rate-pie {
    width: 70px;
    height: 70px;
  }
}
</style>
