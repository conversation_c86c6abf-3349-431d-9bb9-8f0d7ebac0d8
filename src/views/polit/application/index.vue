<template>
  <a-card :body-style="{ paddingBottom: '0!important' }">
    <a-space :size="20">
      <a-form
        ref="searchFormRef"
        layout="inline"
        :label-col="{ span: 25 }"
        :wrapper-col="{ span: 45 }"
        :model="searchFormState"
      >
        <a-form-item label="关系" name="typeOf">
          <a-select
            v-model:value="searchFormState.typeOf"
            style="width: 220px"
            @change="doSearch"
          >
            <a-select-option value="member">参与人</a-select-option>
            <a-select-option value="principal">管理人</a-select-option>
            <a-select-option value="memberAndPrincipal">
              参与人 / 管理人
            </a-select-option>
            <a-select-option value="all">全部应用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="项目" name="project">
          <a-select
            v-model:value="searchFormState.project"
            style="width: 220px"
            :options="projects"
            allow-clear
            show-search
            option-filter-prop="label"
            @change="projectChange"
          />
        </a-form-item>
        <a-form-item label="名称" name="name">
          <a-input-search
            v-model:value="searchFormState.name"
            allow-clear
            style="width: 220px"
            enter-button
            @search="doSearch"
          />
        </a-form-item>
      </a-form>

      <!--      <a-button type="primary" @click="handleAdd">-->
      <!--        <template #icon>-->
      <!--          <plus-circle-outlined />-->
      <!--        </template>-->
      <!--        创建应用-->
      <!--      </a-button>-->

      <!--      <a-button @click="handleImport">-->
      <!--        <template #icon>-->
      <!--          <plus-circle-outlined />-->
      <!--        </template>-->
      <!--        导入应用-->
      <!--      </a-button>-->
      <!--      <a-button-->
      <!--        danger-->
      <!--        type="primary"-->
      <!--        :disabled="!hasSelected"-->
      <!--        :loading="state.loading"-->
      <!--        @click="deleteBySelect(state.selectedRowEntryKeys)"-->
      <!--      >-->
      <!--        <template #icon>-->
      <!--          <delete-outlined />-->
      <!--        </template>-->
      <!--        删除-->
      <!--      </a-button>-->
    </a-space>
    <a-divider />
    <a-table
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        columnWidth: 35,
      }"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :columns="columns()"
      :show-index-column="true"
      :bordered="true"
      :scroll="{ x: 150, y: tableConfig.windowHeight - 240 }"
      row-key="uuid"
      class="ant-table-striped"
      size="middle"
      :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
      @change="handlerTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <!--          <a>详情</a>-->
          <!--          <a-divider type="vertical" />-->
          <a style="color: rgba(0, 0, 0, 0.63)" @click="handleView(record)">
            <file-search-outlined />
            详情
          </a>

          <!--          <a-divider type="vertical" />-->
          <!--          <a style="color: #1890ff" @click="handleOpenPipeline(record)">-->
          <!--            <branches-outlined />-->
          <!--            流水线-->
          <!--          </a>-->
          <a-divider type="vertical" />
          <span>
            <!--            <a style="color: rgb(232,197,48)">日志</a>-->
            <!--            <a-divider type="vertical" />-->
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1">
                    <a-button
                      type="text"
                      :disabled="record.relationship !== 'principal'"
                      @click="handleEdit(record)"
                    >
                      编辑
                    </a-button>
                  </a-menu-item>
                  <a-menu-item key="2">
                    <a-popconfirm
                      title="确认删除 ? "
                      :disabled="record.relationship !== 'principal'"
                      @confirm="handleDelete([record.uuid])"
                    >
                      <a-button
                        class="delete-button"
                        type="text"
                        :disabled="record.relationship !== 'principal'"
                      >
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-menu-item>

                  <a-menu-item v-if="!record.relationship" key="3">
                    <a-button type="text" @click="handleRequest(record)">
                      申请
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
              <a
                ref="dropdownRef"
                style="color: rgba(0, 0, 0, 0.63)"
                class="ant-dropdown-link"
              >
                更多
                <down-outlined />
              </a>
            </a-dropdown>
          </span>
        </template>
      </template>
    </a-table>
    <!--    <add-drawer ref="addDrawer" :title="drawerTitle" @ok="handleSuccess" />-->
    <!--    <edit-drawer ref="editDrawer" :title="drawerTitle" @ok="handleSuccess" />-->
    <!--    <import-drawer-->
    <!--      ref="importDrawer"-->
    <!--      :title="drawerTitle"-->
    <!--      @ok="handleSuccess"-->
    <!--    />-->
    <!--    <request-drawer ref="requestDrawer" @ok="handleSuccess" />-->
    <drawer ref="drawer" :title="drawerTitle" />
  </a-card>
</template>

<script lang="ts">
import {
  DownOutlined,
  FileSearchOutlined,
  // DeleteOutlined,
  // PlusCircleOutlined,
  // BranchesOutlined,
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { computed, nextTick, onMounted, reactive, ref, watchEffect } from 'vue';
import { columns, Mode } from './data';
import { deleteProjectApp, pageProjectApp } from '@/api/pipeline/application';
import { listProjectInfo } from '@/api/pipeline/project';
import { useRoute, useRouter } from 'vue-router';
// import addDrawer from './drawer-add.vue';
// import editDrawer from './drawer-edit.vue';
// import ImportDrawer from './import.vue';
// import { PermissionKey } from '@/types/permissionKey';
import Drawer from './drawer.vue';
export default {
  components: {
    DownOutlined,
    FileSearchOutlined,
    Drawer,
  },
  setup() {
    const tableConfig = ref({
      windowHeight: document.documentElement.clientHeight - 40,
    });
    const drawer = ref();
    const editDrawer = ref();
    const importDrawer = ref();
    const requestDrawer = ref();
    const drawerTitle = ref('');
    const dataSource = ref();
    const loading = ref();

    const searchFormState = reactive({
      name: '',
      project: '',
      typeOf: 'memberAndPrincipal',
    });
    const searchFormRef = ref();
    const projects = ref();

    const pagination = ref({
      page: 1,
      pageSize: 10,
      showSizeChanger: true,
      total: 0,
      showTotal: (total: any) => `共 ${total} 条数据`,
    });

    async function doSearch() {
      try {
        loading.value = true;
        const response = await pageProjectApp({
          ...searchFormState,
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
        });
        // if (response.code == 0) {
        dataSource.value = response.data.list;
        pagination.value.total = response.data.total;
        // } else {
        //   message.error(response.message);
        // }
      } catch (err) {
        console.log(err);
        // message.error(err.message);
      } finally {
        loading.value = false;
      }
    }

    const handleImport = () => {
      drawerTitle.value = '导入';
      importDrawer.value.open(Mode.Import, '');
    };

    function handleEdit(item: any) {
      drawerTitle.value = '编辑';
      editDrawer.value.open(Mode.Edit, item.uuid);
    }

    async function handleDelete(ids: any[]) {
      try {
        loading.value = true;
        await deleteProjectApp({ uuids: ids });
        message.success('删除成功');
        await doSearch();
        // 重置选中项
        state.selectedRowKeys = [];
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }

    const route = useRoute();
    const router = useRouter();
    onMounted(function () {
      window.onresize = () => {
        return (() => {
          tableConfig.value.windowHeight =
            document.documentElement.clientHeight;
        })();
      };
    });

    watchEffect(() => {
      const Project = route.query.Project as string;
      if (Project) {
        searchFormState.project = Project;
        doSearch();
      }
    });

    // 多选
    interface Key {
      name: string;
      uuid: string;
      relationship: string;
    }
    const state = reactive<{
      selectedRowEntryKeys: Key[];
      selectedRowKeys: string[];
      loading: boolean;
    }>({
      selectedRowEntryKeys: [], // Check here to configure the default column
      selectedRowKeys: [], // Check here to configure the default column
      loading: false,
    });
    const hasSelected = computed(() => state.selectedRowKeys.length > 0);
    const onSelectChange = (
      selectedRowKeys: string[],
      selectedRowEntryKeys: Key[],
    ) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRowEntryKeys = selectedRowEntryKeys;
    };

    // 多选删除
    function deleteBySelect(ids: Key[]) {
      // 非应用负责人不能删除
      for (let i = 0; ids && i < ids.length; i++) {
        if (ids[i].relationship !== 'principal') {
          message.error(`非 ${ids[i].name} 应用管理员不能删除`);
          return;
        }
      }
      let uuids = ids.map((item) => item.uuid);
      Modal.confirm({
        type: 'warn',
        title: '确认要删除吗?',
        content: '确认要删除吗?',
        centered: true,
        onOk: () => {
          handleDelete(uuids);
        },
      });
    }

    function handleSuccess() {
      doSearch();
    }

    function handleView(item: any) {
      drawerTitle.value = '概览';
      drawer.value.open(Mode.View, item.uuid);
    }

    async function getProjectList() {
      const response = await listProjectInfo({});
      let options: { label: any; value: any }[] = [];
      response.data?.list.forEach((e: { name: any; uuid: any }) => {
        options.push({
          label: e.name,
          value: e.uuid,
        });
      });
      projects.value = options;
    }

    const handlerTableChange = (page: {
      pageSize: number;
      current: number;
    }) => {
      pagination.value.page = page.current;
      pagination.value.pageSize = page.pageSize;
      doSearch();
    };

    getProjectList();

    doSearch();

    function projectChange() {
      // 切换应用重置分页
      pagination.value.page = 1;
      doSearch();
    }

    onMounted(async () => {
      await nextTick();
      const action = route.query.action;
      if (action) {
        await handleAction(action.toString());
      }
    });

    // 处理action
    async function handleAction(action: string) {
      if (action === 'config') {
        const uuid = route.query.uuid;
        const plugin = route.query.plugin;
        if (uuid && plugin) {
          // 打开配置页面
          editDrawer.value.open(Mode.Edit, uuid.toString(), plugin.toString());
          // 清除参数
          let newQuery = JSON.parse(JSON.stringify(route.query));
          delete newQuery.uuid;
          delete newQuery.plugin;
          delete newQuery.action;
          // 可以值替换参数，不改变路由
          await router.replace({
            query: newQuery,
          });
        }
      }
    }

    function handleRequest(item: any) {
      requestDrawer.value.open(item);
    }

    return {
      searchFormState,
      searchFormRef,
      dataSource,
      pagination,
      loading,
      editDrawer,
      importDrawer,
      requestDrawer,
      drawerTitle,
      handleImport,
      handleEdit,
      handleDelete,
      tableConfig,
      hasSelected,
      onSelectChange,
      state,
      deleteBySelect,
      handleSuccess,
      doSearch,
      handleView,
      projects,
      handlerTableChange,
      projectChange,
      handleRequest,
      drawer,
    };
  },
  methods: {
    columns() {
      return columns;
    },
  },
};
</script>

<style scoped>
.ant-card {
  height: 100%;
}
.delete-button {
  color: red !important;
}
.delete-button:disabled {
  color: #bebdbd !important;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
</style>
