<template>
  <div class="changelog-management">
    <a-card title="变更记录" size="small">
      <template #extra>
        <a-space>
          <a-select
            v-model:value="changeTypeFilter"
            size="small"
            style="width: 120px"
            placeholder="操作类型"
            @change="handleFilterChange"
          >
            <a-select-option value="all">全部操作</a-select-option>
            <a-select-option value="create">创建</a-select-option>
            <a-select-option value="update">更新</a-select-option>
            <a-select-option value="delete">删除</a-select-option>
            <a-select-option value="deploy">部署</a-select-option>
          </a-select>
          <a-button size="small" :loading="loading" @click="handleRefresh">
            <template #icon>
              <reload-outlined />
            </template>
            刷新记录
          </a-button>
        </a-space>
      </template>

      <div class="changelog-content">
        <!-- 变更记录列表 -->
        <div class="changelog-list">
          <a-empty description="变更记录功能开发中" />
          <p class="feature-description">
            将显示应用的所有变更记录，包括配置变更、部署记录、版本变更等
          </p>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, onMounted } from 'vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 从父组件注入集群相关状态
const selectedCluster = inject('selectedCluster', ref(''));
const clusterOptionsLoaded = inject('clusterOptionsLoaded', ref(false));

const loading = ref(false);
const changeTypeFilter = ref('all');

// 监听集群变化
watch(
  selectedCluster,
  (newCluster) => {
    if (newCluster && clusterOptionsLoaded.value) {
      console.log('变更记录 - 集群切换到:', newCluster);
      loadChangelogData();
    }
  },
  { immediate: false },
);

// 刷新数据
const handleRefresh = () => {
  if (!selectedCluster.value) {
    message.warning('请先选择集群');
    return;
  }
  loadChangelogData();
};

// 处理过滤条件变化
const handleFilterChange = () => {
  if (selectedCluster.value) {
    loadChangelogData();
  }
};

// 加载变更记录数据
const loadChangelogData = async () => {
  loading.value = true;
  try {
    // 模拟数据加载
    await new Promise((resolve) => setTimeout(resolve, 1000));

    message.success('变更记录加载完成');
  } catch (error) {
    message.error('变更记录加载失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (selectedCluster.value && clusterOptionsLoaded.value) {
    loadChangelogData();
  }
});
</script>

<style scoped>
.changelog-management {
  height: 100%;
  padding: 0;
}

.changelog-content {
  padding: 0;
}

.changelog-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 500px;
}

.feature-description {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
  max-width: 300px;
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-empty) {
  margin: 0;
}

:deep(.ant-empty-description) {
  color: rgba(0, 0, 0, 0.25);
}

@media (max-width: 768px) {
  .changelog-list {
    min-height: 400px;
  }
}
</style>
