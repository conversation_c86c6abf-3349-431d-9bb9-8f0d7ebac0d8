<template>
  <a-drawer
    width="80%"
    :title="title"
    :visible="show"
    :mask-closable="action.mode === Mode.View"
    :footer-style="{ textAlign: 'right' }"
    :body-style="{ paddingTop: '0', paddingBottom: '0', paddingRight: '4px' }"
    destroy-on-close
    @close="close"
  >
    <template #extra>
      <a-space v-if="action.mode != Mode.View">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" :loading="loading" @click="modalOk">
          保存
        </a-button>
      </a-space>
    </template>
    <!--content-->
    <a-tabs v-model:activeKey="activeKey">
      <template #rightExtra>
        <!-- 集群切换 -->
        <a-select
          v-model:value="selectedCluster"
          size="small"
          style="width: 120px; margin-right: 10px; font-size: 10px !important"
          :options="clusterOptions"
          :loading="clusterLoading"
          :disabled="clusterLoading"
          placeholder="选择集群"
          @change="handleClusterChange"
        />
      </template>
      <a-tab-pane key="1" tab="接口概览">
        <interface-tab />
      </a-tab-pane>
      <a-tab-pane key="2" tab="流量治理" force-render>
        <traffic-tab />
      </a-tab-pane>
      <a-tab-pane key="3" tab="事件中心">
        <event-tab />
      </a-tab-pane>
      <a-tab-pane key="99" tab="变更记录">
        <change-log-tab />
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, provide } from 'vue';
import { Mode } from './data';
import { message } from 'ant-design-vue';
import {
  addCluster,
  infoCluster,
  updateCluster,
  listClusterForSelect,
} from '@/api/pilot/cluster';
import interfaceTab from './interface/index.vue';
import trafficTab from './traffic/index.vue';
import eventTab from './event/index.vue';
import changeLogTab from './changelog/index.vue';
import {
  getSelectedCluster,
  setSelectedCluster,
} from '../cluster/clusterStorage';

export default defineComponent({
  name: 'EditDrawer',
  components: {
    interfaceTab,
    trafficTab,
    eventTab,
    changeLogTab,
  },
  props: {
    title: {
      type: String,
      required: true,
    },
  },
  emits: ['ok'],
  setup(props, context) {
    let show = ref<boolean>(false);
    const action = reactive({
      mode: '',
      uuid: '',
    });
    const formRef = ref();
    const formState = reactive({
      name: '',
      kubeConfig: '',
      enable: true,
      description: '',
    });

    // 集群相关状态
    const clusterOptions = ref([]);
    const clusterOptionsLoaded = ref(false);
    const selectedCluster = ref('');
    const clusterLoading = ref(false);

    // 提供集群选项和状态给子组件
    provide('clusterOptions', clusterOptions);
    provide('clusterOptionsLoaded', clusterOptionsLoaded);
    provide('selectedCluster', selectedCluster);
    provide('updateSelectedCluster', (cluster: string) => {
      selectedCluster.value = cluster;
      setSelectedCluster(cluster);
    });

    // 加载集群选项
    const loadClusterOptions = async () => {
      try {
        clusterLoading.value = true;
        clusterOptionsLoaded.value = false;
        console.log('开始加载集群选项...');
        const response = await listClusterForSelect({ enable: true });
        console.log('集群API响应:', response);
        clusterOptions.value =
          response.data?.list?.map((item: any) => ({
            label: item.name,
            value: item.uuid,
          })) || [];
        console.log('处理后的集群选项:', clusterOptions.value);

        // 初始化集群选择
        initializeClusterSelection();
      } catch (error) {
        console.error('Failed to load cluster options:', error);
        clusterOptions.value = [];
        message.error('加载集群选项失败');
      } finally {
        clusterLoading.value = false;
        clusterOptionsLoaded.value = true;
      }
    };

    // 初始化集群选择
    const initializeClusterSelection = () => {
      if (clusterOptions.value.length === 0) {
        return;
      }

      const savedCluster = getSelectedCluster();

      if (
        savedCluster &&
        clusterOptions.value.some(
          (option) =>
            option.value === savedCluster || option.label === savedCluster,
        )
      ) {
        // 如果保存的集群在当前选项中存在，使用保存的集群
        selectedCluster.value = savedCluster;
      } else {
        // 否则使用第一个选项作为默认值
        if (clusterOptions.value.length > 0) {
          const defaultCluster =
            clusterOptions.value[0].value || clusterOptions.value[0].label;
          selectedCluster.value = defaultCluster;
          setSelectedCluster(defaultCluster);
        }
      }
    };

    // 处理集群切换
    const handleClusterChange = (cluster: string) => {
      selectedCluster.value = cluster;
      setSelectedCluster(cluster);

      // 集群切换时，触发各个标签页的数据刷新
      console.log('集群切换到:', cluster);

      // 显示切换成功的消息
      message.success(`已切换到集群: ${cluster}`);
    };

    const modalOk = () => {
      handleSubmit();
    };

    const open = async (mode: string, uuid: string) => {
      resetForm();
      action.mode = mode;
      action.uuid = uuid;
      show.value = true;

      // 加载集群选项
      await loadClusterOptions();

      if (formRef.value && formRef.value) {
        formRef.value.resetFields();
      }
      if (mode === 'edit') {
        try {
          loading.value = true;
          const info = await infoCluster({ uuid: uuid });
          Object.assign(formState, info.data);
        } catch (error) {
          console.error('加载集群信息失败:', error);
          message.error('加载集群信息失败');
        } finally {
          loading.value = false;
        }
      }
    };

    const finish = () => {
      show.value = false;
      context.emit('ok');
    };

    const close = () => {
      show.value = false;
    };

    async function handleSubmit() {
      try {
        loading.value = true;
        const values = await formRef.value.validate();
        if (action.mode === 'edit') {
          values.uuid = action.uuid;
          await updateCluster(values);
        } else {
          await addCluster(values);
        }
        message.success('提交成功');
        finish();
      } finally {
        loading.value = false;
      }
    }

    const rules = {
      name: [{ required: true, message: '请输入集群名称' }],
      kubeConfig: [{ required: true, message: '请输入访问秘钥' }],
    };

    const loading = ref(false);

    const resetForm = () => {
      formRef.value?.resetFields();
    };

    const activeKey = ref('1');

    return {
      loading,
      Mode,
      show,
      action,
      formRef,
      formState,
      modalOk,
      finish,
      open,
      close,
      handleSubmit,
      rules,
      activeKey,
      // 集群相关
      clusterOptions,
      clusterOptionsLoaded,
      selectedCluster,
      clusterLoading,
      handleClusterChange,
    };
  },
});
</script>
