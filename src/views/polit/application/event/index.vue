<template>
  <div class="event-center">
    <a-card title="事件中心" size="small">
      <template #extra>
        <a-space>
          <a-button size="small" :loading="loading" @click="handleRefresh">
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <div class="event-content">
        <!-- 事件统计卡片 -->
        <a-row :gutter="[16, 16]" class="stats-row">
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="今日事件"
                :value="eventStats.today"
                :value-style="{ color: '#1890ff' }"
                suffix="条"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="告警事件"
                :value="eventStats.warning"
                :value-style="{ color: '#faad14' }"
                suffix="条"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="错误事件"
                :value="eventStats.error"
                :value-style="{ color: '#ff4d4f' }"
                suffix="条"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="正常事件"
                :value="eventStats.normal"
                :value-style="{ color: '#52c41a' }"
                suffix="条"
              />
            </a-card>
          </a-col>
        </a-row>

        <!-- 事件列表 -->
        <a-card title="最近事件" size="small" class="event-list-card">
          <div class="event-list">
            <a-empty description="事件列表功能开发中" />
            <p class="feature-description">
              将显示应用的各种事件信息，包括部署事件、告警事件、错误事件等
            </p>
          </div>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, onMounted } from 'vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 从父组件注入集群相关状态
const selectedCluster = inject('selectedCluster', ref(''));
const clusterOptionsLoaded = inject('clusterOptionsLoaded', ref(false));

const loading = ref(false);
const eventStats = ref({
  today: 0,
  warning: 0,
  error: 0,
  normal: 0,
});

// 监听集群变化
watch(
  selectedCluster,
  (newCluster) => {
    if (newCluster && clusterOptionsLoaded.value) {
      console.log('事件中心 - 集群切换到:', newCluster);
      loadEventData();
    }
  },
  { immediate: false },
);

// 刷新数据
const handleRefresh = () => {
  if (!selectedCluster.value) {
    message.warning('请先选择集群');
    return;
  }
  loadEventData();
};

// 加载事件数据
const loadEventData = async () => {
  loading.value = true;
  try {
    // 模拟数据加载
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟事件统计数据
    eventStats.value = {
      today: 125,
      warning: 8,
      error: 3,
      normal: 114,
    };

    message.success('事件数据加载完成');
  } catch (error) {
    message.error('事件数据加载失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (selectedCluster.value && clusterOptionsLoaded.value) {
    loadEventData();
  }
});
</script>

<style scoped>
.event-center {
  height: 100%;
  padding: 0;
}

.event-content {
  padding: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.event-list-card {
  min-height: 400px;
}

.event-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 300px;
}

.feature-description {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
  max-width: 300px;
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.ant-empty) {
  margin: 0;
}

:deep(.ant-empty-description) {
  color: rgba(0, 0, 0, 0.25);
}

@media (max-width: 1200px) {
  .stats-row :deep(.ant-col) {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .stats-row :deep(.ant-col) {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .event-list-card {
    min-height: 300px;
  }
}
</style>
