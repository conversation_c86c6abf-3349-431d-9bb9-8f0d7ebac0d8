export const columns = [
  {
    title: '序号',
    width: 40,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    width: 150,
    title: '服务名称',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 50,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: 130,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    align: 'center',
    width: 130,
  },
];

export class Item {
  id?: number;
  code?: string;
  name: string;
}

export enum Mode {
  Add = 'add',
  Import = 'import',
  Edit = 'edit',
  View = 'view',
  Action = 'action',
  Assign = 'assign',
  Delete = 'delete',
}

export enum Method {
  Get = 'GET',
  Post = 'POST',
  Put = 'PUT',
  Delete = 'DELETE',
  Patch = 'PATCH',
}

export const Methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

export const ParaTypes: Item[] = [
  {
    code: 'headers',
    name: 'Headers',
  },
  {
    code: 'query',
    name: 'Query',
  },
  // {
  //   code: 'path',
  //   name: 'Path',
  // },

  {
    code: 'body',
    name: 'Body',
  },
];

export interface ConfigEntry {
  key: string;
  name: string;
  value: any;
  disabled?: boolean;
}

export interface K8sConfigEntry {
  uuid: string;
  name: string;
  content: string;
}

// 接口监控相关数据类型
export interface ApplicationOverview {
  serviceName: string;
  namespace: string;
  cluster: string;
  totalInterfaces: number;
  avgQPS: number;
  avgRT: number;
  errorRate: number;
  successRate: number;
  lastUpdated: string;
}

export interface QPSData {
  timestamp: string;
  qps: number;
  totalRequests: number;
}

export interface RTData {
  timestamp: string;
  avgRT: number;
  p95RT: number;
  p99RT: number;
  maxRT: number;
  minRT: number;
}

export interface InterfaceInfo {
  path: string;
  method: string;
  qps: number;
  avgRT: number;
  errorRate: number;
  totalRequests: number;
  errorRequests: number;
  lastAccess: string;
}

export interface ApplicationMetrics {
  serviceName: string;
  namespace: string;
  cluster: string;
  qpsData: QPSData[];
  rtData: RTData[];
  interfaces: InterfaceInfo[];
  overview: ApplicationOverview;
}

// 时间范围选项
export const TimeRangeOptions = [
  { label: '最近5分钟', value: '5m' },
  { label: '最近15分钟', value: '15m' },
  { label: '最近30分钟', value: '30m' },
  { label: '最近1小时', value: '1h' },
  { label: '最近3小时', value: '3h' },
  { label: '最近6小时', value: '6h' },
  { label: '最近12小时', value: '12h' },
  { label: '最近24小时', value: '24h' },
  { label: '最近3天', value: '3d' },
  { label: '最近7天', value: '7d' },
  { label: '自定义时间', value: 'custom' },
];

// 自定义时间范围类型
export interface CustomTimeRange {
  startTime: string;
  endTime: string;
}

// 时间范围查询参数
export interface TimeRangeQuery {
  type: 'preset' | 'custom';
  value: string | CustomTimeRange;
}

export const historyColumns = [
  {
    title: '序号',
    width: 40,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    width: 50,
    title: '版本',
    dataIndex: 'version',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 150,
    title: '名称',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 50,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: string }) =>
      text ? text.split('@')[0] : '',
  },
  {
    title: '审批人',
    dataIndex: 'modifier',
    width: 50,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: string }) =>
      text ? text.split('@')[0] : '',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: 80,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    align: 'center',
    width: 30,
  },
];
