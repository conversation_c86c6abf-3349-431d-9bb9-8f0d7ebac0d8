<template>
  <div class="traffic-visualization">
    <div class="visualization-header">
      <h3>流量分配可视化</h3>
      <a-space>
        <a-button size="small" @click="toggleView">
          <EyeOutlined />
          {{ viewMode === 'chart' ? '表格视图' : '图表视图' }}
        </a-button>
        <a-button size="small" @click="refreshData">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 图表视图 -->
    <div v-if="viewMode === 'chart'" class="chart-view">
      <!-- 流量分配饼图 -->
      <div class="traffic-charts">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="chart-container">
              <h4>总流量分配</h4>
              <div class="pie-chart">
                <div class="chart-center">
                  <div class="total-requests">
                    {{ totalRequests.toLocaleString() }}
                  </div>
                  <div class="total-label">总请求</div>
                </div>
                <svg width="200" height="200" viewBox="0 0 200 200">
                  <circle
                    cx="100"
                    cy="100"
                    r="80"
                    fill="none"
                    stroke="#f0f0f0"
                    stroke-width="20"
                  />
                  <circle
                    cx="100"
                    cy="100"
                    r="80"
                    fill="none"
                    stroke="#722ed1"
                    stroke-width="20"
                    :stroke-dasharray="`${grayPercentage * 5.03} ${
                      (100 - grayPercentage) * 5.03
                    }`"
                    stroke-dashoffset="125.7"
                    transform="rotate(-90 100 100)"
                    class="progress-circle"
                  />
                </svg>
              </div>
              <div class="chart-legend">
                <div class="legend-item">
                  <span class="legend-color gray"></span>
                  <span>
                    灰度流量: {{ grayRequests.toLocaleString() }} ({{
                      grayPercentage
                    }}%)
                  </span>
                </div>
                <div class="legend-item">
                  <span class="legend-color base"></span>
                  <span>
                    基线流量: {{ baseRequests.toLocaleString() }} ({{
                      100 - grayPercentage
                    }}%)
                  </span>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="metrics-grid">
              <h4>实时指标</h4>
              <a-row :gutter="12">
                <a-col :span="12">
                  <a-statistic
                    title="成功率"
                    :value="successRate"
                    suffix="%"
                    :value-style="{
                      color: successRate > 95 ? '#52c41a' : '#ff4d4f',
                    }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="错误率"
                    :value="errorRate"
                    suffix="%"
                    :value-style="{
                      color: errorRate < 5 ? '#52c41a' : '#ff4d4f',
                    }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="平均响应时间"
                    :value="avgResponseTime"
                    suffix="ms"
                    :value-style="{
                      color: avgResponseTime < 500 ? '#52c41a' : '#faad14',
                    }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="P99延迟"
                    :value="p99Latency"
                    suffix="ms"
                    :value-style="{
                      color: p99Latency < 1000 ? '#52c41a' : '#ff4d4f',
                    }"
                  />
                </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 服务链路流量图 -->
      <div class="service-flow" style="margin-top: 24px">
        <h4>服务链路流量</h4>
        <div class="flow-diagram">
          <div class="flow-layer">
            <div class="flow-node client-node">
              <div class="node-title">客户端</div>
              <div class="node-stats">
                <div class="stat-item">
                  iOS: {{ Math.round(totalRequests * 0.4).toLocaleString() }}
                </div>
                <div class="stat-item">
                  Android:
                  {{ Math.round(totalRequests * 0.35).toLocaleString() }}
                </div>
                <div class="stat-item">
                  H5: {{ Math.round(totalRequests * 0.25).toLocaleString() }}
                </div>
              </div>
            </div>
          </div>

          <div class="flow-arrows">
            <div class="arrow-container">
              <div class="flow-arrow">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
                <div class="arrow-label">
                  {{ totalRequests.toLocaleString() }} req/min
                </div>
              </div>
            </div>
          </div>

          <div class="flow-layer">
            <div class="flow-node gateway-node">
              <div class="node-title">网关层</div>
              <div class="node-stats">
                <div class="stat-item">云原生网关</div>
                <div class="stat-item">负载均衡</div>
              </div>
            </div>
          </div>

          <div class="flow-arrows">
            <div class="arrow-container">
              <!-- 灰度流量箭头 -->
              <div class="flow-arrow gray-traffic">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
                <div class="arrow-label">
                  灰度: {{ grayRequests.toLocaleString() }}
                </div>
              </div>
              <!-- 基线流量箭头 -->
              <div class="flow-arrow base-traffic">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
                <div class="arrow-label">
                  基线: {{ baseRequests.toLocaleString() }}
                </div>
              </div>
            </div>
          </div>

          <div class="flow-layer services-layer">
            <div
              v-for="service in services"
              :key="service.name"
              class="service-column"
            >
              <div class="service-node gray-service">
                <div class="node-title">{{ service.name }} (灰度)</div>
                <div class="node-stats">
                  <div class="stat-item">副本: {{ service.grayReplicas }}</div>
                  <div class="stat-item">
                    QPS:
                    {{
                      Math.round((grayRequests * service.trafficRatio) / 100)
                    }}
                  </div>
                  <div class="stat-item">延迟: {{ service.grayLatency }}ms</div>
                </div>
              </div>
              <div class="service-node base-service">
                <div class="node-title">{{ service.name }} (基线)</div>
                <div class="node-stats">
                  <div class="stat-item">副本: {{ service.baseReplicas }}</div>
                  <div class="stat-item">
                    QPS:
                    {{
                      Math.round(
                        (baseRequests * (100 - service.trafficRatio)) / 100,
                      )
                    }}
                  </div>
                  <div class="stat-item">延迟: {{ service.baseLatency }}ms</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-else class="table-view">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'trafficRatio'">
            <a-progress
              :percent="record.trafficRatio"
              size="small"
              :stroke-color="record.type === 'gray' ? '#722ed1' : '#52c41a'"
            />
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'healthy' ? 'green' : 'red'">
              {{ record.status === 'healthy' ? '健康' : '异常' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'latency'">
            <span
              :style="{
                color:
                  record.latency < 200
                    ? '#52c41a'
                    : record.latency < 500
                    ? '#faad14'
                    : '#ff4d4f',
              }"
            >
              {{ record.latency }}ms
            </span>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 告警区域 -->
    <div
      v-if="alerts.length > 0"
      class="alerts-section"
      style="margin-top: 24px"
    >
      <h4>实时告警</h4>
      <div class="alerts-list">
        <a-alert
          v-for="alert in alerts"
          :key="alert.id"
          :message="alert.message"
          :type="alert.type"
          :description="alert.description"
          show-icon
          closable
          style="margin-bottom: 8px"
          @close="dismissAlert(alert.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons-vue';

interface Service {
  name: string;
  grayReplicas: number;
  baseReplicas: number;
  trafficRatio: number;
  grayLatency: number;
  baseLatency: number;
}

interface Alert {
  id: string;
  message: string;
  type: 'error' | 'warning' | 'info';
  description: string;
}

// 响应式数据
const viewMode = ref<'chart' | 'table'>('chart');
const totalRequests = ref(1000);
const grayRequests = ref(200);
const successRate = ref(99.2);
const errorRate = ref(0.8);
const avgResponseTime = ref(45);
const p99Latency = ref(120);

// 服务数据
const services = ref<Service[]>([
  {
    name: '服务A',
    grayReplicas: 2,
    baseReplicas: 4,
    trafficRatio: 20,
    grayLatency: 45,
    baseLatency: 42,
  },
  {
    name: '服务B',
    grayReplicas: 1,
    baseReplicas: 3,
    trafficRatio: 15,
    grayLatency: 38,
    baseLatency: 35,
  },
  {
    name: '服务C',
    grayReplicas: 1,
    baseReplicas: 2,
    trafficRatio: 25,
    grayLatency: 52,
    baseLatency: 48,
  },
]);

// 告警数据
const alerts = ref<Alert[]>([
  {
    id: '1',
    message: '服务C灰度版本延迟较高',
    type: 'warning',
    description: 'P99延迟超过阈值，建议检查服务性能',
  },
]);

// 计算属性
const baseRequests = computed(() => totalRequests.value - grayRequests.value);
const grayPercentage = computed(() =>
  Math.round((grayRequests.value / totalRequests.value) * 100),
);

// 表格列定义
const tableColumns = [
  {
    title: '服务名称',
    dataIndex: 'serviceName',
    key: 'serviceName',
    width: 100,
  },
  { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
  { title: '副本数', dataIndex: 'replicas', key: 'replicas', width: 80 },
  { title: '流量比例', key: 'trafficRatio', width: 120 },
  { title: 'QPS', dataIndex: 'qps', key: 'qps', width: 80 },
  { title: '延迟', key: 'latency', width: 80 },
  { title: '状态', key: 'status', width: 80 },
];

// 表格数据
const tableData = computed(() => {
  const data: any[] = [];
  services.value.forEach((service) => {
    data.push({
      serviceName: service.name,
      type: 'gray',
      replicas: service.grayReplicas,
      trafficRatio: service.trafficRatio,
      qps: Math.round((grayRequests.value * service.trafficRatio) / 100),
      latency: service.grayLatency,
      status: service.grayLatency < 100 ? 'healthy' : 'warning',
    });
    data.push({
      serviceName: service.name,
      type: 'base',
      replicas: service.baseReplicas,
      trafficRatio: 100 - service.trafficRatio,
      qps: Math.round(
        (baseRequests.value * (100 - service.trafficRatio)) / 100,
      ),
      latency: service.baseLatency,
      status: service.baseLatency < 100 ? 'healthy' : 'warning',
    });
  });
  return data;
});

// 刷新数据定时器
let refreshTimer: NodeJS.Timeout;

// 方法
const toggleView = () => {
  viewMode.value = viewMode.value === 'chart' ? 'table' : 'chart';
};

const refreshData = () => {
  // 模拟数据更新
  totalRequests.value = Math.floor(Math.random() * 500) + 800;
  grayRequests.value = Math.floor(totalRequests.value * 0.2);
  successRate.value = Math.round((Math.random() * 5 + 95) * 10) / 10;
  errorRate.value = Math.round((5 - successRate.value + 95) * 10) / 10;
  avgResponseTime.value = Math.floor(Math.random() * 100) + 30;
  p99Latency.value = Math.floor(Math.random() * 200) + 80;

  // 更新服务延迟
  services.value.forEach((service) => {
    service.grayLatency = Math.floor(Math.random() * 50) + 30;
    service.baseLatency = Math.floor(Math.random() * 40) + 25;
  });
};

const dismissAlert = (alertId: string) => {
  const index = alerts.value.findIndex((alert) => alert.id === alertId);
  if (index > -1) {
    alerts.value.splice(index, 1);
  }
};

// 生命周期
onMounted(() => {
  // 每30秒自动刷新数据
  refreshTimer = setInterval(refreshData, 30000);
});

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<style scoped>
.traffic-visualization {
  padding: 16px;
  background: white;
  border-radius: 8px;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.visualization-header h3 {
  margin: 0;
  color: #333;
}

.chart-container {
  text-align: center;
  position: relative;
}

.pie-chart {
  position: relative;
  display: inline-block;
  margin: 16px 0;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.total-requests {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.total-label {
  font-size: 12px;
  color: #666;
}

.progress-circle {
  transition: stroke-dasharray 0.5s ease;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.gray {
  background: #722ed1;
}

.legend-color.base {
  background: #52c41a;
}

.metrics-grid {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.flow-diagram {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
  overflow-x: auto;
}

.flow-layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
}

.services-layer {
  flex-direction: row;
  gap: 24px;
}

.service-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 120px;
}

.flow-node {
  padding: 12px;
  border-radius: 8px;
  border: 2px solid;
  background: white;
  min-width: 120px;
  text-align: center;
}

.client-node {
  border-color: #52c41a;
  background: #f6ffed;
}

.gateway-node {
  border-color: #1890ff;
  background: #e6f7ff;
}

.gray-service {
  border-color: #722ed1;
  background: #f9f0ff;
}

.base-service {
  border-color: #52c41a;
  background: #f6ffed;
}

.node-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.node-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  font-size: 12px;
  color: #666;
}

.flow-arrows {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.flow-arrow {
  display: flex;
  align-items: center;
  gap: 4px;
}

.arrow-line {
  width: 40px;
  height: 2px;
  background: #d9d9d9;
}

.gray-traffic .arrow-line {
  background: #722ed1;
}

.base-traffic .arrow-line {
  background: #52c41a;
}

.arrow-head {
  width: 0;
  height: 0;
  border-left: 8px solid #d9d9d9;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.gray-traffic .arrow-head {
  border-left-color: #722ed1;
}

.base-traffic .arrow-head {
  border-left-color: #52c41a;
}

.arrow-label {
  font-size: 10px;
  color: #666;
  white-space: nowrap;
}

.alerts-section h4 {
  color: #333;
  margin-bottom: 12px;
}

.alerts-list {
  max-height: 200px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .flow-diagram {
    flex-direction: column;
    gap: 16px;
  }

  .services-layer {
    flex-direction: column;
    align-items: center;
  }

  .arrow-container {
    transform: rotate(90deg);
  }
}
</style>
