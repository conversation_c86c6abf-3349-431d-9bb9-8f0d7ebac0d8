<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="isEdit ? '编辑工作流' : '创建工作流'"
    width="1200px"
    :confirm-loading="loading"
    :destroy-on-close="true"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <!-- 基本信息 -->
      <a-card
        title="基本信息"
        size="small"
        style="margin-bottom: 16px; border-left: 4px solid #8c8c8c"
      >
        <template #title>
          <span style="color: #595959; font-weight: 500">
            <i
              class="anticon anticon-info-circle"
              style="margin-right: 8px"
            ></i>
            基本信息
          </span>
        </template>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="工作流名称" name="name">
              <a-input
                v-model:value="formData.name"
                placeholder="请输入工作流名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="工作流类型" name="type">
              <a-select
                v-model:value="formData.type"
                placeholder="选择工作流类型"
                @change="handleTypeChange"
              >
                <a-select-option value="canary">灰度发布</a-select-option>
                <a-select-option value="blue-green">
                  蓝绿部署(TODO)
                </a-select-option>
                <a-select-option value="ab-test">A/B测试(TODO)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="描述" name="description">
              <a-textarea
                v-model:value="formData.description"
                placeholder="请输入工作流描述"
                :rows="1"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 集群配置 -->
      <a-card
        title="集群配置"
        size="small"
        style="margin-bottom: 16px; border-left: 4px solid #69c0ff"
      >
        <template #title>
          <span style="color: #096dd9; font-weight: 500">
            <i class="anticon anticon-cluster" style="margin-right: 8px"></i>
            集群配置
          </span>
        </template>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="集群" name="clusterId">
              <a-select
                v-model:value="formData.clusterId"
                placeholder="请选择集群"
                :loading="clusterLoading"
                show-search
                option-filter-prop="label"
                @dropdown-visible-change="(open: boolean) => open && fetchClusterList()"
                @change="onClusterChange"
              >
                <a-select-option
                  v-for="cluster in clusterList"
                  :key="cluster.value"
                  :value="cluster.value"
                  :label="cluster.label"
                >
                  {{ cluster.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="命名空间" name="namespace">
              <a-select
                v-model:value="formData.namespace"
                placeholder="请选择命名空间"
                :loading="namespaceLoading"
                :disabled="!formData.clusterId"
                show-search
                option-filter-prop="label"
                allow-clear
                @dropdown-visible-change="(open: boolean) => open && fetchNamespaceList()"
                @change="onNamespaceChange"
              >
                <a-select-option
                  v-for="namespace in namespaceList"
                  :key="namespace.value"
                  :value="namespace.value"
                  :label="namespace.label"
                >
                  {{ namespace.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 服务配置 -->
      <a-card
        title="服务配置"
        size="small"
        style="margin-bottom: 16px; border-left: 4px solid #95de64"
      >
        <template #title>
          <span style="color: #389e0d; font-weight: 500">
            <i class="anticon anticon-appstore" style="margin-right: 8px"></i>
            服务配置
          </span>
        </template>
        <div class="service-list">
          <div
            v-for="(service, index) in formData.services"
            :key="index"
            class="service-item"
          >
            <a-card
              size="small"
              :title="`服务 ${index + 1}`"
              style="
                margin-bottom: 12px;
                border-radius: 6px;
                border: 1px solid #f0f0f0;
                background: #fafafa;
              "
            >
              <template #extra>
                <a-button size="small" danger @click="removeService(index)">
                  <DeleteOutlined />
                  删除
                </a-button>
              </template>

              <!-- 服务名称 -->
              <a-row :gutter="16" style="margin-bottom: 16px">
                <a-col :span="24">
                  <a-form-item
                    :name="['services', index, 'serviceName']"
                    label="服务名称"
                  >
                    <a-select
                      v-model:value="service.serviceName"
                      placeholder="请选择Deployment"
                      :loading="deploymentLoading"
                      :disabled="!formData.namespace"
                      show-search
                      option-filter-prop="label"
                      allow-clear
                      @dropdown-visible-change="(open: boolean) => open && fetchDeploymentList()"
                      @change="(value: string) => onDeploymentSelect(value, index)"
                    >
                      <a-select-option
                        v-for="deployment in deploymentList"
                        :key="deployment.value"
                        :value="deployment.value"
                        :label="deployment.label"
                      >
                        {{ deployment.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 副本数配置 -->
              <a-row :gutter="16" style="margin-bottom: 16px">
                <a-col :span="12">
                  <a-form-item
                    :name="['services', index, 'canaryReplicas']"
                    label="灰度副本数"
                  >
                    <a-input-number
                      v-model:value="service.canaryReplicas"
                      :min="1"
                      placeholder="1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    :name="['services', index, 'prodReplicas']"
                    label="生产副本数"
                  >
                    <a-input-number
                      v-model:value="service.prodReplicas"
                      :min="1"
                      placeholder="2"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 流量比例 -->
              <a-row :gutter="16" style="margin-bottom: 16px">
                <a-col :span="24">
                  <a-form-item
                    :name="['services', index, 'trafficRatio']"
                    label="流量比例(%)"
                  >
                    <div style="display: flex; align-items: center; gap: 8px">
                      <a-slider
                        v-model:value="service.trafficRatio"
                        :min="0"
                        :max="100"
                        style="flex: 1"
                        :tooltip-formatter="(value: number) => `${value}%`"
                      />
                      <a-input-number
                        v-model:value="service.trafficRatio"
                        :min="0"
                        :max="100"
                        placeholder="10"
                        style="width: 70px"
                        size="small"
                      />
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 执行顺序 -->
              <a-row :gutter="16" style="margin-bottom: 16px">
                <a-col :span="24">
                  <a-form-item
                    :name="['services', index, 'order']"
                    label="执行顺序"
                  >
                    <a-input-number
                      v-model:value="service.order"
                      :min="0"
                      placeholder="执行顺序"
                      style="width: 200px"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 镜像配置 -->
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="镜像配置">
                    <div class="image-config">
                      <div
                        v-for="(imageConfig, imageKey) in service.images"
                        :key="imageKey"
                        class="image-item"
                      >
                        <div
                          style="
                            margin-bottom: 12px;
                            padding: 8px;
                            border: 1px solid #f0f0f0;
                            border-radius: 4px;
                          "
                        >
                          <!-- 容器名称 -->
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                              margin-bottom: 8px;
                            "
                          >
                            <a-input
                              :value="imageKey"
                              placeholder="容器名"
                              readonly
                              style="
                                flex: 1;
                                text-align: center;
                                margin-right: 8px;
                              "
                            />
                            <a-button
                              type="primary"
                              danger
                              size="small"
                              style="
                                width: 32px;
                                height: 32px;
                                padding: 0;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                              "
                              @click="removeImage(service, imageKey)"
                            >
                              <DeleteOutlined />
                            </a-button>
                          </div>
                          <!-- 镜像配置 -->
                          <ImageSelector
                            :model-value="parseImageValue(imageConfig)"
                            :namespace="formData.namespace"
                            label=""
                            @update:model-value="(value: any) => updateImage(service, imageKey, value)"
                          />
                        </div>
                      </div>
                      <a-button type="dashed" block @click="addImage(service)">
                        <PlusOutlined />
                        添加镜像
                      </a-button>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 全链路配置 -->
              <div v-if="formData.type === 'canary'" class="chain-config">
                <a-checkbox v-model:checked="service.isChainEntry">
                  设为链路入口服务
                </a-checkbox>
                <div v-if="service.chainConfig" style="margin-top: 8px">
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <a-form-item label="传播请求头">
                        <a-input
                          v-model:value="service.chainConfig.propagationHeader"
                          placeholder="x-canary-version"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="传播请求头值">
                        <a-input
                          v-model:value="service.chainConfig.propagationValue"
                          placeholder="canary"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="降级策略">
                        <a-select
                          v-model:value="service.chainConfig.fallbackStrategy"
                          placeholder="选择降级策略"
                        >
                          <a-select-option value="stable">
                            稳定版本
                          </a-select-option>
                          <a-select-option value="random">随机</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </a-card>
          </div>

          <a-button
            type="dashed"
            block
            style="
              height: 40px;
              border: 1px dashed #d9d9d9;
              color: #595959;
              background: #fafafa;
              border-radius: 6px;
              font-weight: 500;
            "
            @click="addService"
          >
            <PlusOutlined style="margin-right: 8px" />
            添加服务
          </a-button>
        </div>
      </a-card>

      <!-- 全链路灰度配置 -->
      <a-card
        v-if="formData.type === 'canary'"
        style="margin-bottom: 16px; border-left: 4px solid #ffc069"
      >
        <template #title>
          <span style="color: #262626; font-weight: 500">
            <i
              class="anticon anticon-deployment-unit"
              style="margin-right: 8px"
            ></i>
            全链路灰度配置
          </span>
        </template>
        <a-checkbox v-model:checked="chainCanaryEnabled">
          启用全链路灰度发布
        </a-checkbox>

        <div
          v-if="chainCanaryEnabled && formData.chainConfig"
          style="margin-top: 16px"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="流量策略">
                <a-select
                  v-model:value="formData.chainConfig.trafficStrategy"
                  placeholder="选择流量策略"
                >
                  <a-select-option value="weight">权重分配</a-select-option>
                  <a-select-option value="header">请求头路由</a-select-option>
                  <a-select-option value="cookie">Cookie路由</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="默认流量比例(%)">
                <a-input-number
                  v-model:value="formData.chainConfig.defaultTrafficRatio"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="粘性会话">
                <a-switch
                  v-model:checked="formData.chainConfig.stickySession"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="入口服务">
            <a-select
              v-model:value="formData.chainConfig.entryServices"
              mode="multiple"
              placeholder="选择入口服务"
            >
              <a-select-option
                v-for="service in formData.services"
                :key="service.serviceName"
                :value="service.serviceName"
              >
                {{ service.serviceName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-card>

      <!-- 全局配置 -->
      <a-card title="全局配置" size="small">
        <a-tabs>
          <a-tab-pane key="timeout" tab="超时配置">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="步骤超时(秒)">
                  <a-input-number
                    v-model:value="
                      formData.globalConfig.globalTimeout.stepTimeout
                    "
                    :min="1"
                    placeholder="600"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="审批超时(秒)">
                  <a-input-number
                    v-model:value="
                      formData.globalConfig.globalTimeout.approvalTimeout
                    "
                    :min="1"
                    placeholder="3600"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="监控超时(秒)">
                  <a-input-number
                    v-model:value="
                      formData.globalConfig.globalTimeout.monitoringTimeout
                    "
                    :min="1"
                    placeholder="1800"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="回滚超时(秒)">
                  <a-input-number
                    v-model:value="
                      formData.globalConfig.globalTimeout.rollbackTimeout
                    "
                    :min="1"
                    placeholder="900"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>

          <a-tab-pane key="approval" tab="审批配置">
            <!-- 灰度发布审批 -->
            <a-card
              title="灰度发布审批"
              size="small"
              style="margin-bottom: 16px; border-left: 4px solid #ff7875"
            >
              <template #title>
                <span style="color: #cf1322; font-weight: 500">
                  <i
                    class="anticon anticon-safety-certificate"
                    style="margin-right: 8px"
                  ></i>
                  灰度发布审批
                </span>
              </template>
              <a-checkbox
                v-model:checked="
                  formData.globalConfig.globalApproval.enableCanaryApproval
                "
              >
                启用灰度发布审批
              </a-checkbox>

              <div
                v-if="formData.globalConfig.globalApproval.enableCanaryApproval"
                style="
                  margin-top: 16px;
                  padding: 12px;
                  background: #fff2f0;
                  border-radius: 6px;
                "
              >
                <a-alert
                  message="审批权限说明"
                  description="灰度发布审批将由具有 PilotAdmin 或 WorkflowAdmin 权限的用户进行审批"
                  type="info"
                  show-icon
                  style="margin-bottom: 12px"
                />
                <div style="color: #666; font-size: 12px">
                  • 灰度发布前需要通过审批，确保代码质量和测试验证
                  <br />
                  • 只有管理员用户可以进行审批操作
                  <br />
                  • 审批通过后才能开始灰度发布流程
                </div>
              </div>
            </a-card>

            <!-- 生产上线审批 -->
            <a-card
              title="生产上线审批"
              size="small"
              style="border-left: 4px solid #ff9c6e"
            >
              <template #title>
                <span style="color: #d46b08; font-weight: 500">
                  <i
                    class="anticon anticon-crown"
                    style="margin-right: 8px"
                  ></i>
                  生产上线审批
                </span>
              </template>
              <a-checkbox
                v-model:checked="
                  formData.globalConfig.globalApproval.enableProductionApproval
                "
              >
                启用生产上线审批
              </a-checkbox>

              <div
                v-if="
                  formData.globalConfig.globalApproval.enableProductionApproval
                "
                style="
                  margin-top: 16px;
                  padding: 12px;
                  background: #fff7e6;
                  border-radius: 6px;
                "
              >
                <a-alert
                  message="审批权限说明"
                  description="生产上线审批将由具有 PilotAdmin 权限的用户进行审批"
                  type="warning"
                  show-icon
                  style="margin-bottom: 12px"
                />
                <div style="color: #666; font-size: 12px">
                  • 生产环境上线前需要通过审批，确保业务安全和风险控制
                  <br />
                  • 只有 PilotAdmin 权限用户可以进行生产审批
                  <br />
                  • 审批通过后才能开始生产环境部署
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <a-tab-pane key="notification" tab="通知配置">
            <a-checkbox
              v-model:checked="formData.globalConfig.globalNotification.enabled"
            >
              启用通知
            </a-checkbox>
            <div
              v-if="formData.globalConfig.globalNotification.enabled"
              style="margin-top: 16px"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="通知渠道">
                    <a-select
                      v-model:value="
                        formData.globalConfig.globalNotification.channels
                      "
                      mode="multiple"
                      placeholder="选择通知渠道"
                    >
                      <a-select-option value="email">邮件</a-select-option>
                      <a-select-option value="slack">Slack</a-select-option>
                      <a-select-option value="webhook">Webhook</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="通知事件">
                    <a-select
                      v-model:value="
                        formData.globalConfig.globalNotification.events
                      "
                      mode="multiple"
                      placeholder="选择通知事件"
                    >
                      <a-select-option value="start">开始执行</a-select-option>
                      <a-select-option value="success">
                        执行成功
                      </a-select-option>
                      <a-select-option value="failure">
                        执行失败
                      </a-select-option>
                      <a-select-option value="approval">
                        等待审批
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="通知用户">
                <a-select
                  v-model:value="formData.globalConfig.globalNotification.users"
                  mode="tags"
                  placeholder="输入用户邮箱或用户名"
                />
              </a-form-item>
            </div>
          </a-tab-pane>

          <a-tab-pane key="rollback" tab="回滚配置">
            <a-checkbox
              v-model:checked="formData.globalConfig.globalRollback.enabled"
            >
              启用自动回滚
            </a-checkbox>
            <div
              v-if="formData.globalConfig.globalRollback.enabled"
              style="margin-top: 16px"
            >
              <a-checkbox
                v-model:checked="
                  formData.globalConfig.globalRollback.autoRollback
                "
              >
                自动触发回滚
              </a-checkbox>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import {
  workflowApi,
  type CreateWorkflowParams,
  type CreateWorkflowFromTemplateParams,
  type WorkflowResponse,
  type CanaryService,
} from '@/api/pilot/workflow';
import {
  clusterApi,
  type ClusterSelectOption,
  type NamespaceRequest,
  type ResourceListRequest,
  type DeploymentInfo,
} from '@/api/pilot/cluster';
import ImageSelector from './ImageSelector.vue';

interface Props {
  visible: boolean;
  workflow?: WorkflowResponse | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const formRef = ref();
const loading = ref(false);
const isEdit = computed(() => !!props.workflow?.id);
const chainCanaryEnabled = ref(false);

// 表单数据
const formData = reactive<CreateWorkflowParams>({
  name: '',
  description: '',
  type: 'canary',
  namespace: '',
  clusterId: '',
  services: [],
  globalConfig: {
    globalTimeout: {
      stepTimeout: 600,
      approvalTimeout: 3600,
      monitoringTimeout: 1800,
      rollbackTimeout: 900,
    },
    globalNotification: {
      enabled: false,
      channels: [],
      events: [],
      users: [],
    },
    globalRollback: {
      enabled: false,
      autoRollback: false,
    },
    globalApproval: {
      enabled: false,
      enableCanaryApproval: false,
      enableProductionApproval: false,
    },
  },
});

// 集群列表状态
const clusterList = ref<ClusterSelectOption[]>([]);
const clusterLoading = ref(false);
const clusterListFetched = ref(false);

// 命名空间列表状态
const namespaceList = ref<ClusterSelectOption[]>([]);
const namespaceLoading = ref(false);

// Deployment列表状态
const deploymentList = ref<ClusterSelectOption[]>([]);
const deploymentLoading = ref(false);
const deploymentDetails = ref<Record<string, DeploymentInfo>>({}); // 存储deployment详细信息

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入工作流名称' }],
  type: [{ required: true, message: '请选择工作流类型' }],
  namespace: [{ required: true, message: '请输入命名空间' }],
  clusterId: [{ required: true, message: '请选择集群' }],
  services: [{ required: true, message: '请至少添加一个服务' }],
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: '',
    description: '',
    type: 'canary',
    namespace: '',
    clusterId: '',
    services: [],
    globalConfig: {
      globalTimeout: {
        stepTimeout: 600,
        approvalTimeout: 3600,
        monitoringTimeout: 1800,
        rollbackTimeout: 900,
      },
      globalNotification: {
        enabled: false,
        channels: [],
        events: [],
        users: [],
      },
      globalRollback: {
        enabled: false,
        autoRollback: false,
      },
      globalApproval: {
        enabled: false,
        enableCanaryApproval: false,
        enableProductionApproval: false,
      },
    },
  });
  chainCanaryEnabled.value = false;
};

// 获取集群列表（懒加载）
const fetchClusterList = async () => {
  // 如果已经获取过或正在加载中，则不重复请求
  if (clusterListFetched.value || clusterLoading.value) {
    return;
  }

  try {
    clusterLoading.value = true;
    const response = await clusterApi.listForSelect({ enable: true });
    if (response.data && response.data.list) {
      clusterList.value = response.data.list;
      clusterListFetched.value = true;
    }
  } catch (error) {
    console.error('获取集群列表失败:', error);
    message.error('获取集群列表失败');
  } finally {
    clusterLoading.value = false;
  }
};

// 获取命名空间列表
const fetchNamespaceList = async () => {
  if (!formData.clusterId || namespaceLoading.value) {
    return;
  }

  try {
    namespaceLoading.value = true;
    const params: NamespaceRequest = {
      cluster: formData.clusterId,
    };
    const response = await clusterApi.listNamespaces(params);
    namespaceList.value = response.data || [];
  } catch (error) {
    console.error('获取命名空间列表失败:', error);
    message.error('获取命名空间列表失败');
  } finally {
    namespaceLoading.value = false;
  }
};

// 集群变化处理
const onClusterChange = (clusterId: string) => {
  // 清空命名空间选择
  formData.namespace = '';
  namespaceList.value = [];

  // 清空deployment列表
  deploymentList.value = [];
  deploymentDetails.value = {};

  // 自动加载命名空间列表
  if (clusterId) {
    fetchNamespaceList();
  }
};

// 获取deployment列表
const fetchDeploymentList = async () => {
  if (!formData.clusterId || !formData.namespace || deploymentLoading.value) {
    return;
  }

  try {
    deploymentLoading.value = true;
    const params: ResourceListRequest = {
      cluster: formData.clusterId,
      namespace: formData.namespace,
      page: 1,
      pageSize: 100,
    };
    const response = await clusterApi.listDeployments(params);

    // 存储deployment详细信息
    const details: Record<string, DeploymentInfo> = {};
    const list = response.data?.list || [];

    list.forEach((deployment) => {
      details[deployment.name] = deployment;
    });

    deploymentDetails.value = details;

    // 转换为下拉框格式
    deploymentList.value = list.map((deployment) => ({
      label: deployment.name,
      value: deployment.name,
    }));
  } catch (error) {
    console.error('获取Deployment列表失败:', error);
    message.error('获取Deployment列表失败');
  } finally {
    deploymentLoading.value = false;
  }
};

// 命名空间变化处理
const onNamespaceChange = (namespace: string) => {
  // 清空deployment列表
  deploymentList.value = [];
  deploymentDetails.value = {};

  // 自动加载deployment列表
  if (namespace) {
    fetchDeploymentList();
  }
};

// deployment选择处理
const onDeploymentSelect = (deploymentName: string, serviceIndex: number) => {
  const service = formData.services[serviceIndex];
  if (!service) {
    return;
  }

  const deployment = deploymentDetails.value[deploymentName];
  if (!deployment || !deployment.images) {
    return;
  }

  // 根据deployment的镜像信息自动填充service的images
  const newImages: Record<string, string> = {};
  deployment.images.forEach((containerImage) => {
    newImages[containerImage.containerName] = containerImage.image;
  });

  service.images = newImages;
  // 注意：命名空间直接使用集群配置中的值

  console.log('自动填充镜像信息:', newImages);
};

// 监听工作流数据变化
watch(
  () => props.workflow,
  (workflow) => {
    if (workflow) {
      // 编辑模式，填充数据
      Object.assign(formData, {
        name: workflow.name,
        description: workflow.description,
        type: workflow.type,
        namespace: workflow.namespace,
        clusterId: workflow.clusterId,
        // 这里需要根据实际API返回的数据结构调整
        services: [],
        globalConfig: {
          globalTimeout: {
            stepTimeout: 600,
            approvalTimeout: 3600,
            monitoringTimeout: 1800,
            rollbackTimeout: 900,
          },
          globalNotification: {
            enabled: false,
            channels: [],
            events: [],
            users: [],
          },
          globalRollback: {
            enabled: false,
            autoRollback: false,
          },
        },
        // 如果workflow数据中包含chainConfig，则使用它
        chainConfig: workflow.chainConfig,
      });

      // 同步全链路灰度配置状态
      const hasChainConfig = workflow.chainConfig;
      const isChainEnabled =
        hasChainConfig && workflow.chainConfig.enableChainCanary;
      chainCanaryEnabled.value = !!isChainEnabled;
    } else {
      // 新建模式，重置表单
      resetForm();
    }
  },
  { immediate: true },
);

// 添加服务
const addService = () => {
  const newService: CanaryService = {
    serviceName: '',
    namespace: formData.namespace,
    order: formData.services.length,
    images: {},
    canaryReplicas: 1,
    prodReplicas: 2,
    trafficRatio: 10,
    strategy: 'canary',
    isChainEntry: false,
    chainConfig: {
      enablePropagation: false,
      propagationHeader: 'x-canary-version',
      propagationValue: 'canary',
      downstreamServices: [],
      stickySession: false,
      fallbackStrategy: 'stable',
    },
  };
  formData.services.push(newService);
};

// 删除服务
const removeService = (index: number) => {
  formData.services.splice(index, 1);
  // 重新排序
  formData.services.forEach((service, idx) => {
    service.order = idx;
  });
};

// 添加镜像
const addImage = (service: CanaryService) => {
  const containerName = `container${Object.keys(service.images).length + 1}`;
  service.images[containerName] = '';
};

// 删除镜像
const removeImage = (service: CanaryService, containerName: string) => {
  delete service.images[containerName];
};

// 解析镜像值（从字符串转换为对象格式）
const parseImageValue = (imageString: string) => {
  if (!imageString) {
    return undefined;
  }

  // 正确解析镜像字符串，处理包含registry地址的情况
  // 格式: [registry[:port]/]repository[:tag]

  // 查找最后一个冒号，这通常是tag分隔符
  const lastColonIndex = imageString.lastIndexOf(':');

  if (lastColonIndex === -1) {
    // 没有冒号，只有repository
    return {
      repository: imageString,
      tag: 'latest',
    };
  }

  // 检查最后一个冒号后面是否包含斜杠，如果有斜杠说明是registry端口而不是tag
  const afterLastColon = imageString.substring(lastColonIndex + 1);
  const hasSlashAfterColon = afterLastColon.includes('/');

  if (hasSlashAfterColon) {
    // 最后一个冒号是registry端口的一部分，没有明确的tag
    return {
      repository: imageString,
      tag: 'latest',
    };
  }

  // 最后一个冒号后面没有斜杠，应该是tag分隔符
  const repository = imageString.substring(0, lastColonIndex);
  const tag = afterLastColon;

  // 验证tag是否合法（不应该包含斜杠或其他特殊字符）
  if (tag && /^[a-zA-Z0-9._-]+$/.test(tag)) {
    return {
      repository,
      tag,
    };
  }

  // tag不合法，当作整个字符串都是repository
  return {
    repository: imageString,
    tag: 'latest',
  };
};

// 更新镜像（从对象格式转换为字符串）
const updateImage = (
  service: CanaryService,
  containerName: string,
  imageValue: { repository: string; tag: string } | undefined,
) => {
  if (imageValue && imageValue.repository && imageValue.tag) {
    service.images[
      containerName
    ] = `${imageValue.repository}:${imageValue.tag}`;
  } else {
    service.images[containerName] = '';
  }
};

// 工作流类型变化处理
const handleTypeChange = (type: string) => {
  formData.type = type as any;
  if (type !== 'canary') {
    chainCanaryEnabled.value = false;
    formData.chainConfig = undefined;
  }
};

// 监听全链路灰度配置变化
watch(
  () => chainCanaryEnabled.value,
  (checked: boolean) => {
    if (checked) {
      formData.chainConfig = {
        enableChainCanary: true,
        trafficStrategy: 'weight',
        defaultTrafficRatio: 10,
        entryServices: [],
        serviceDependencies: {},
        serviceChainOrders: {},
        stickySession: false,
        fallbackStrategy: 'stable',
      };
    } else {
      formData.chainConfig = undefined;
    }
  },
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    // 验证服务配置
    if (formData.services.length === 0) {
      message.error('请至少添加一个服务');
      return;
    }

    // 验证服务配置完整性
    for (const service of formData.services) {
      if (!service.serviceName) {
        message.error('请填写所有服务的名称');
        return;
      }
      // 命名空间从集群配置继承，无需单独验证
      if (
        Object.keys(service.images).length === 0 ||
        !Object.values(service.images).some((img) => img)
      ) {
        message.error('请为所有服务配置至少一个镜像');
        return;
      }
    }

    // 确保所有服务的命名空间与集群配置一致
    formData.services.forEach((service) => {
      service.namespace = formData.namespace;
    });

    // 转换为后端期望的CreateWorkflowFromTemplateParams格式
    const requestData: CreateWorkflowFromTemplateParams = {
      name: formData.name,
      description: formData.description,
      type: formData.type,
      namespace: formData.namespace,
      clusterId: formData.clusterId,
      labels: formData.labels || {},
      config: {
        services: formData.services,
        globalConfig: formData.globalConfig,
        chainConfig: formData.chainConfig,
      },
    };

    await workflowApi.createFromTemplate(requestData);
    message.success(isEdit.value ? '工作流更新成功' : '工作流创建成功');
    emit('success');
  } catch (error) {
    console.error('保存工作流失败:', error);
    message.error('保存工作流失败');
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 初始化一个默认服务
watch(
  () => props.visible,
  (visible) => {
    if (visible && formData.services.length === 0 && !props.workflow) {
      addService();
    }
  },
);
</script>

<style scoped>
.service-item {
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.service-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.image-config {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  transition: all 0.2s ease;
}

.image-config:hover {
  border-color: #d9d9d9;
  background: #ffffff;
}

.image-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  transition: background 0.2s ease;
}

.image-item:hover {
  background: rgba(255, 255, 255, 1);
}

.image-item:last-child {
  margin-bottom: 0;
}

.chain-config {
  margin-top: 16px;
  padding: 16px;
  border-radius: 6px;
  background: #f9f9f9;
  border: 1px solid #f0f0f0;
}

:deep(.ant-card-head) {
  min-height: auto;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-card-head-title) {
  padding: 8px 0;
}

:deep(.ant-card-body) {
  padding: 16px;
}

/* 表单标签样式 */
:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #595959;
}

/* 输入框焦点样式 */
:deep(.ant-input:focus),
:deep(.ant-input-focused),
:deep(.ant-select-focused .ant-select-selector) {
  border-color: #69c0ff;
  box-shadow: 0 0 0 2px rgba(105, 192, 255, 0.15);
}

/* 数字输入框样式 */
:deep(.ant-input-number:focus),
:deep(.ant-input-number-focused) {
  border-color: #69c0ff;
  box-shadow: 0 0 0 2px rgba(105, 192, 255, 0.15);
}

/* 审批配置样式 */
:deep(.ant-card-head) {
  min-height: auto;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
  font-size: 14px;
}

/* 审批卡片特殊样式 */
:deep(.ant-card) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-card-body) {
  padding: 16px 20px;
}

/* 审批选项样式 */
:deep(.ant-checkbox-wrapper) {
  font-weight: 500;
  color: #262626;
}
</style>
