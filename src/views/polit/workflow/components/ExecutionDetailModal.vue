<template>
  <a-modal
    v-model:visible="modalVisible"
    title="执行详情"
    width="900px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="executionDetail" class="execution-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="执行ID">
            {{ executionDetail.id }}
          </a-descriptions-item>
          <a-descriptions-item label="执行状态">
            <a-tag :color="getStatusColor(executionDetail.status)">
              <template #icon>
                <SyncOutlined
                  v-if="executionDetail.status === 'running'"
                  spin
                />
                <ClockCircleOutlined
                  v-else-if="executionDetail.status === 'pending'"
                />
                <PauseCircleOutlined
                  v-else-if="executionDetail.status === 'paused'"
                />
                <CheckCircleOutlined
                  v-else-if="executionDetail.status === 'completed'"
                />
                <CloseCircleOutlined
                  v-else-if="executionDetail.status === 'failed'"
                />
                <StopOutlined
                  v-else-if="executionDetail.status === 'cancelled'"
                />
              </template>
              {{ getStatusText(executionDetail.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="触发方式">
            <a-tag :color="getTriggerTypeColor(executionDetail.triggerType)">
              {{ getTriggerTypeText(executionDetail.triggerType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="执行人">
            {{ executionDetail.executor }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ executionDetail.startTime }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ executionDetail.endTime || '进行中' }}
          </a-descriptions-item>
          <a-descriptions-item label="执行时长" :span="2">
            {{
              formatDuration(executionDetail.startTime, executionDetail.endTime)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="执行进度" :span="2">
            <a-progress
              :percent="executionDetail.progress"
              :status="getProgressStatus(executionDetail.status)"
            />
            <div style="margin-top: 4px; font-size: 12px; color: #666">
              {{ executionDetail.completedSteps }}/{{
                executionDetail.totalSteps
              }}
              步骤完成
            </div>
          </a-descriptions-item>
          <a-descriptions-item
            v-if="executionDetail.errorMessage"
            label="错误信息"
            :span="2"
          >
            <a-alert
              :message="executionDetail.errorMessage"
              type="error"
              show-icon
              style="margin: 0"
            />
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 执行步骤 -->
      <a-card title="执行步骤" size="small" style="margin-bottom: 16px">
        <a-timeline>
          <a-timeline-item
            v-for="(step, index) in executionDetail.steps"
            :key="step.id"
            :color="getStepStatusColor(step.status)"
          >
            <template #dot>
              <CheckCircleOutlined
                v-if="step.status === 'completed'"
                style="color: #52c41a"
              />
              <CloseCircleOutlined
                v-else-if="step.status === 'failed'"
                style="color: #ff4d4f"
              />
              <SyncOutlined
                v-else-if="step.status === 'running'"
                spin
                style="color: #1890ff"
              />
              <PauseCircleOutlined
                v-else-if="step.status === 'paused'"
                style="color: #faad14"
              />
              <ClockCircleOutlined v-else style="color: #d9d9d9" />
            </template>

            <div class="step-content">
              <div class="step-header">
                <span class="step-name">
                  步骤 {{ index + 1 }}: {{ step.name }}
                </span>
                <div class="step-actions">
                  <span class="step-type">
                    {{ getStepTypeText(step.type) }}
                  </span>
                  <!-- 审批按钮 -->
                  <a-button
                    v-if="step.type === 'approval' && step.status === 'pending'"
                    type="primary"
                    size="small"
                    @click="openStepApproval(step)"
                  >
                    <CheckCircleOutlined />
                    审批
                  </a-button>
                </div>
              </div>

              <div class="step-info">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <div class="step-time">
                      <ClockCircleOutlined style="margin-right: 4px" />
                      开始: {{ step.startTime || '未开始' }}
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="step-time">
                      <ClockCircleOutlined style="margin-right: 4px" />
                      结束: {{ step.endTime || '进行中' }}
                    </div>
                  </a-col>
                </a-row>

                <div v-if="step.duration" class="step-duration">
                  <HourglassOutlined style="margin-right: 4px" />
                  耗时: {{ step.duration }}
                </div>
              </div>

              <!-- 步骤详情 -->
              <div v-if="step.details" class="step-details">
                <a-collapse size="small">
                  <a-collapse-panel key="details" header="查看详情">
                    <div v-if="step.type === 'deployment'">
                      <a-descriptions size="small" :column="2">
                        <a-descriptions-item label="服务名称">
                          {{ step.details.serviceName }}
                        </a-descriptions-item>
                        <a-descriptions-item label="镜像版本">
                          {{ step.details.imageVersion }}
                        </a-descriptions-item>
                        <a-descriptions-item label="副本数">
                          {{ step.details.replicas }}
                        </a-descriptions-item>
                        <a-descriptions-item label="流量比例">
                          {{ step.details.trafficRatio }}%
                        </a-descriptions-item>
                      </a-descriptions>
                    </div>
                    <div v-else-if="step.type === 'approval'">
                      <a-descriptions size="small" :column="2">
                        <a-descriptions-item label="审批人">
                          {{ step.details.approver }}
                        </a-descriptions-item>
                        <a-descriptions-item label="审批状态">
                          <a-tag
                            :color="
                              step.details.approvalStatus === 'approved'
                                ? 'green'
                                : 'orange'
                            "
                          >
                            {{
                              step.details.approvalStatus === 'approved'
                                ? '已批准'
                                : '待审批'
                            }}
                          </a-tag>
                        </a-descriptions-item>
                        <a-descriptions-item label="审批意见" :span="2">
                          {{ step.details.approvalComment || '无' }}
                        </a-descriptions-item>
                      </a-descriptions>
                    </div>
                    <div v-else>
                      <pre>{{ JSON.stringify(step.details, null, 2) }}</pre>
                    </div>
                  </a-collapse-panel>
                </a-collapse>
              </div>

              <!-- 错误信息 -->
              <div v-if="step.error" class="step-error">
                <a-alert
                  :message="step.error"
                  type="error"
                  show-icon
                  closable
                />
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>

      <!-- 执行参数 -->
      <a-card v-if="executionDetail.parameters" title="执行参数" size="small">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item
            v-for="(value, key) in executionDetail.parameters"
            :key="key"
            :label="key"
          >
            {{ value }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <a-empty v-else description="暂无数据" />

    <!-- 步骤审批模态框 -->
    <StepApprovalModal
      v-model:visible="approvalModalVisible"
      :execution-id="currentExecutionId"
      :step-id="currentStepId"
      @success="handleApprovalSuccess"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import moment from 'moment';
import {
  SyncOutlined,
  ClockCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StopOutlined,
  HourglassOutlined,
} from '@ant-design/icons-vue';
import { workflowApi } from '@/api/pilot/workflow';
import StepApprovalModal from './StepApprovalModal.vue';

interface Props {
  visible: boolean;
  executionId: number;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

interface ExecutionStep {
  id: number;
  name: string;
  type: 'deployment' | 'approval' | 'validation' | 'cleanup';
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  duration?: string;
  error?: string;
  details?: any;
}

interface ExecutionDetail {
  id: number;
  workflowId: number;
  status:
    | 'pending'
    | 'running'
    | 'paused'
    | 'completed'
    | 'failed'
    | 'cancelled';
  triggerType: 'manual' | 'scheduled' | 'webhook' | 'api';
  executor: string;
  startTime: string;
  endTime?: string;
  progress: number;
  completedSteps: number;
  totalSteps: number;
  errorMessage?: string;
  steps: ExecutionStep[];
  parameters?: Record<string, any>;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const loading = ref(false);
const executionDetail = ref<ExecutionDetail | null>(null);

// 审批相关状态
const approvalModalVisible = ref(false);
const currentExecutionId = ref(0);
const currentStepId = ref(0);

// 获取执行详情
const fetchExecutionDetail = async () => {
  if (!props.executionId) {
    return;
  }

  try {
    loading.value = true;

    // 调用真实API
    const response = await workflowApi.getExecution(props.executionId);

    if (response.data) {
      const execution = response.data;

      // 转换步骤数据格式
      const transformedSteps = (execution.stepExecutions || []).map(
        (step: any) => ({
          id: step.id,
          name: step.stepName || step.name || '未知步骤',
          type: mapStepType(step.stepType || step.type),
          status: step.status,
          startTime: step.startTime,
          endTime: step.endTime,
          duration: step.endTime
            ? calculateDuration(step.startTime, step.endTime)
            : undefined,
          error: step.errorMessage,
          details: step.config ? parseStepConfig(step.config) : undefined,
        }),
      );

      // 构建执行详情对象
      executionDetail.value = {
        id: execution.id,
        workflowId: execution.workflowId,
        status: execution.status,
        triggerType: execution.triggerType || 'manual',
        executor: execution.executorName || execution.executor || '未知',
        startTime: execution.startTime,
        endTime: execution.endTime,
        progress: calculateExecutionProgress(execution, transformedSteps),
        completedSteps: transformedSteps.filter(
          (s: ExecutionStep) => s.status === 'completed',
        ).length,
        totalSteps: transformedSteps.length,
        errorMessage: execution.errorMessage,
        steps: transformedSteps,
        parameters:
          execution.parameters || parseExecutionContext(execution.context),
      };
    } else {
      executionDetail.value = null;
    }
  } catch (error) {
    console.error('获取执行详情失败:', error);
    message.error('获取执行详情失败');
    executionDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 映射步骤类型
const mapStepType = (type: string): ExecutionStep['type'] => {
  const typeMap: Record<string, ExecutionStep['type']> = {
    'deployment': 'deployment',
    'approval': 'approval',
    'validation': 'validation',
    'monitoring': 'validation',
    'traffic_split': 'deployment',
    'cleanup': 'cleanup',
  };
  return typeMap[type] || 'deployment';
};

// 计算执行时长
const calculateDuration = (startTime: string, endTime: string): string => {
  const start = moment(startTime);
  const end = moment(endTime);
  const duration = moment.duration(end.diff(start));

  const hours = Math.floor(duration.asHours());
  const minutes = Math.floor(duration.asMinutes() % 60);
  const seconds = Math.floor(duration.asSeconds() % 60);

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${seconds}秒`;
};

// 计算执行进度
const calculateExecutionProgress = (
  execution: any,
  steps: ExecutionStep[],
): number => {
  if (execution.status === 'completed') {
    return 100;
  }
  if (execution.status === 'failed' || execution.status === 'cancelled') {
    const completed = steps.filter((s) => s.status === 'completed').length;
    return steps.length > 0 ? Math.floor((completed / steps.length) * 100) : 0;
  }

  const completed = steps.filter((s) => s.status === 'completed').length;
  const running = steps.filter((s) => s.status === 'running').length;
  const total = steps.length;

  if (total === 0) {
    return 0;
  }

  // 运行中的步骤算作50%完成
  const progressValue = (completed + running * 0.5) / total;
  return Math.floor(progressValue * 100);
};

// 解析步骤配置
const parseStepConfig = (configStr: string): any => {
  try {
    return JSON.parse(configStr);
  } catch {
    return { config: configStr };
  }
};

// 解析执行上下文作为参数
const parseExecutionContext = (
  context?: string,
): Record<string, any> | undefined => {
  if (!context) {
    return undefined;
  }

  try {
    return JSON.parse(context);
  } catch {
    return { context };
  }
};

// 打开步骤审批
const openStepApproval = (step: ExecutionStep) => {
  if (step.status !== 'pending' || step.type !== 'approval') {
    message.warning('只有待审批的步骤才能进行审批操作');
    return;
  }

  currentExecutionId.value = props.executionId;
  currentStepId.value = step.id;
  approvalModalVisible.value = true;
};

// 审批成功回调
const handleApprovalSuccess = () => {
  // 重新获取执行详情以更新状态
  fetchExecutionDetail();
  message.success('审批操作完成');
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'processing',
    paused: 'warning',
    completed: 'success',
    failed: 'error',
    cancelled: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '执行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
  };
  return textMap[status] || status;
};

// 获取触发类型颜色
const getTriggerTypeColor = (triggerType: string) => {
  const colorMap: Record<string, string> = {
    manual: 'blue',
    scheduled: 'green',
    webhook: 'purple',
    api: 'orange',
  };
  return colorMap[triggerType] || 'default';
};

// 获取触发类型文本
const getTriggerTypeText = (triggerType: string) => {
  const textMap: Record<string, string> = {
    manual: '手动执行',
    scheduled: '定时执行',
    webhook: 'Webhook',
    api: 'API调用',
  };
  return textMap[triggerType] || triggerType;
};

// 获取进度状态
const getProgressStatus = (status: string) => {
  if (status === 'failed') {
    return 'exception';
  }
  if (status === 'completed') {
    return 'success';
  }
  if (status === 'running') {
    return 'active';
  }
  return 'normal';
};

// 获取步骤状态颜色
const getStepStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'gray',
    running: 'blue',
    paused: 'orange',
    completed: 'green',
    failed: 'red',
  };
  return colorMap[status] || 'gray';
};

// 获取步骤类型文本
const getStepTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    deployment: '部署',
    approval: '审批',
    validation: '验证',
    cleanup: '清理',
  };
  return textMap[type] || type;
};

// 格式化执行时长
const formatDuration = (startTime: string, endTime?: string) => {
  if (!endTime) {
    return '执行中';
  }

  const start = moment(startTime);
  const end = moment(endTime);
  const duration = moment.duration(end.diff(start));

  const minutes = Math.floor(duration.asMinutes());
  const seconds = Math.floor(duration.asSeconds() % 60);

  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${seconds}秒`;
};

// 监听执行ID变化
watch(
  () => props.executionId,
  (newId) => {
    if (newId && props.visible) {
      fetchExecutionDetail();
    }
  },
);

// 监听visible变化
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.executionId) {
      fetchExecutionDetail();
    }
  },
);
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.execution-detail {
  max-height: 600px;
  overflow-y: auto;
}

.step-content {
  padding-left: 8px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-name {
  font-weight: 500;
  font-size: 14px;
}

.step-type {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.step-info {
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.step-time,
.step-duration {
  margin-bottom: 4px;
}

.step-details {
  margin: 8px 0;
}

.step-error {
  margin: 8px 0;
}

:deep(.ant-timeline-item-content) {
  min-height: auto;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
}
</style>
