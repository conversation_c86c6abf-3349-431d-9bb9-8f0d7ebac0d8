<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="`执行记录 - ${workflowName}`"
    width="1200px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div class="execution-records-container">
      <!-- 搜索和筛选栏 -->
      <div class="execution-header">
        <div class="search-section">
          <a-form layout="inline" :model="searchForm">
            <a-form-item label="执行状态" name="status">
              <a-select
                v-model:value="searchForm.status"
                placeholder="选择执行状态"
                style="width: 150px"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="pending">等待中</a-select-option>
                <a-select-option value="running">执行中</a-select-option>
                <a-select-option value="paused">已暂停</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
                <a-select-option value="cancelled">已取消</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="执行人" name="executor">
              <a-input
                v-model:value="searchForm.executor"
                placeholder="请输入执行人"
                style="width: 150px"
                allow-clear
                @press-enter="handleSearch"
              />
            </a-form-item>
            <a-form-item label="时间范围" name="timeRange">
              <a-range-picker
                v-model:value="searchForm.timeRange"
                :ranges="timeRangePresets"
                @change="handleSearch"
              />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <SearchOutlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>

        <div class="action-section">
          <a-space>
            <a-button @click="handleRefresh">
              <ReloadOutlined />
              刷新
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="exportExecutions">
                    <ExportOutlined />
                    导出记录
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>
                更多操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </div>
      </div>

      <!-- 执行记录列表 -->
      <div class="execution-table">
        <a-table
          :columns="columns"
          :data-source="executionList"
          :loading="loading"
          :pagination="pagination"
          :scroll="{ x: 1000 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                <template #icon>
                  <SyncOutlined v-if="record.status === 'running'" spin />
                  <ClockCircleOutlined
                    v-else-if="record.status === 'pending'"
                  />
                  <PauseCircleOutlined v-else-if="record.status === 'paused'" />
                  <CheckCircleOutlined
                    v-else-if="record.status === 'completed'"
                  />
                  <CloseCircleOutlined v-else-if="record.status === 'failed'" />
                  <StopOutlined v-else-if="record.status === 'cancelled'" />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'triggerType'">
              <a-tag :color="getTriggerTypeColor(record.triggerType)">
                {{ getTriggerTypeText(record.triggerType) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'duration'">
              {{ formatDuration(record.startTime, record.endTime) }}
            </template>

            <template v-if="column.key === 'progress'">
              <a-progress
                :percent="record.progress"
                :size="['small', 14]"
                :status="getProgressStatus(record.status)"
                :show-info="false"
                style="margin-right: 8px"
              />
              <span style="font-size: 12px">
                {{ record.completedSteps }}/{{ record.totalSteps }}
              </span>
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button size="small" @click="viewExecutionDetail(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-dropdown v-if="record.status === 'running'">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="pauseExecution(record)">
                        <PauseCircleOutlined />
                        暂停
                      </a-menu-item>
                      <a-menu-item danger @click="cancelExecution(record)">
                        <StopOutlined />
                        取消
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    操作
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
                <a-button
                  v-else-if="record.status === 'paused'"
                  size="small"
                  type="primary"
                  @click="resumeExecution(record)"
                >
                  <PlayCircleOutlined />
                  继续
                </a-button>
                <a-dropdown v-else>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewLogs(record)">
                        <FileTextOutlined />
                        查看日志
                      </a-menu-item>
                      <a-menu-item @click="rerunExecution(record)">
                        <RedoOutlined />
                        重新执行
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 执行详情模态框 -->
    <ExecutionDetailModal
      v-model:visible="detailModalVisible"
      :execution-id="currentExecutionId"
    />

    <!-- 执行日志模态框 -->
    <ExecutionLogsModal
      v-model:visible="logsModalVisible"
      :execution-id="currentExecutionId"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import moment, { type Moment } from 'moment';
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  DownOutlined,
  EyeOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StopOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import { workflowApi } from '@/api/pilot/workflow';
import ExecutionDetailModal from './ExecutionDetailModal.vue';
import ExecutionLogsModal from './ExecutionLogsModal.vue';

interface Props {
  visible: boolean;
  workflowId: string | number;
  workflowName?: string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

interface ExecutionRecord {
  id: number;
  workflowId: number;
  status:
    | 'pending'
    | 'running'
    | 'paused'
    | 'completed'
    | 'failed'
    | 'cancelled';
  triggerType: 'manual' | 'scheduled' | 'webhook' | 'api';
  executor: string;
  startTime: string;
  endTime?: string;
  progress: number;
  completedSteps: number;
  totalSteps: number;
  errorMessage?: string;
  createTime: string;
}

interface SearchForm {
  status?: string;
  executor: string;
  timeRange?: [Moment, Moment];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 搜索表单
const searchForm = reactive<SearchForm>({
  status: undefined,
  executor: '',
  timeRange: undefined,
});

// 时间范围预设
const timeRangePresets = {
  '今天': [moment().startOf('day'), moment().endOf('day')],
  '近3天': [moment().subtract(2, 'days').startOf('day'), moment().endOf('day')],
  '近7天': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
  '近30天': [
    moment().subtract(29, 'days').startOf('day'),
    moment().endOf('day'),
  ],
};

// 表格状态
const loading = ref(false);
const executionList = ref<ExecutionRecord[]>([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 模态框状态
const detailModalVisible = ref(false);
const logsModalVisible = ref(false);
const currentExecutionId = ref<number>(0);

// 表格列配置
const columns = [
  {
    title: '执行ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true,
  },
  {
    title: '执行状态',
    dataIndex: 'status',
    key: 'status',
    width: 120,
  },
  {
    title: '触发方式',
    dataIndex: 'triggerType',
    key: 'triggerType',
    width: 100,
  },
  {
    title: '执行人',
    dataIndex: 'executor',
    key: 'executor',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 150,
    sorter: true,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    width: 150,
  },
  {
    title: '执行时长',
    key: 'duration',
    width: 100,
  },
  {
    title: '执行进度',
    key: 'progress',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const,
  },
];

// 获取执行记录列表
const fetchExecutionList = async () => {
  if (!props.workflowId) {
    return;
  }

  try {
    loading.value = true;

    // 构建API请求参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      workflowId: Number(props.workflowId),
      status: searchForm.status,
      executor: searchForm.executor,
      startTime: searchForm.timeRange?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endTime: searchForm.timeRange?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
    };

    // 调用真实API
    const response = await workflowApi.listExecutions(params);

    if (response.data) {
      // 转换API响应数据格式
      const transformedList = (response.data.list || []).map((item: any) => ({
        id: item.id,
        workflowId: item.workflowId,
        status: item.status,
        triggerType: item.triggerType || 'manual',
        executor: item.executorName || item.executor || '未知',
        startTime: item.startTime,
        endTime: item.endTime,
        progress: calculateProgress(item),
        completedSteps: item.completedSteps || 0,
        totalSteps: item.totalSteps || 0,
        errorMessage: item.errorMessage,
        createTime: item.startTime,
      }));

      executionList.value = transformedList;
      pagination.total = response.data.total || 0;
    } else {
      executionList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取执行记录失败:', error);
    message.error('获取执行记录失败');

    // 发生错误时清空列表
    executionList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 计算执行进度
const calculateProgress = (execution: any) => {
  if (!execution.totalSteps || execution.totalSteps === 0) {
    return 0;
  }

  const completed = execution.completedSteps || 0;
  const total = execution.totalSteps;

  if (execution.status === 'completed') {
    return 100;
  } else if (
    execution.status === 'failed' ||
    execution.status === 'cancelled'
  ) {
    return Math.floor((completed / total) * 100);
  } else {
    return Math.floor((completed / total) * 100);
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchExecutionList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    executor: '',
    timeRange: undefined,
  });
  pagination.current = 1;
  fetchExecutionList();
};

// 刷新
const handleRefresh = () => {
  fetchExecutionList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchExecutionList();
};

// 查看执行详情
const viewExecutionDetail = (record: ExecutionRecord) => {
  currentExecutionId.value = record.id;
  detailModalVisible.value = true;
};

// 查看日志
const viewLogs = (record: ExecutionRecord) => {
  currentExecutionId.value = record.id;
  logsModalVisible.value = true;
};

// 暂停执行
const pauseExecution = async (record: ExecutionRecord) => {
  try {
    await workflowApi.pause(record.id);
    message.success('执行已暂停');
    await fetchExecutionList();
  } catch (error) {
    message.error('暂停执行失败');
  }
};

// 继续执行
const resumeExecution = async (record: ExecutionRecord) => {
  try {
    await workflowApi.resume(record.id);
    message.success('执行已继续');
    await fetchExecutionList();
  } catch (error) {
    message.error('继续执行失败');
  }
};

// 取消执行
const cancelExecution = (record: ExecutionRecord) => {
  Modal.confirm({
    title: '确认取消执行?',
    content: `即将取消执行记录 ID: ${record.id}，此操作不可撤销。`,
    async onOk() {
      try {
        await workflowApi.cancel(record.id);
        message.success('执行已取消');
        await fetchExecutionList();
      } catch (error) {
        message.error('取消执行失败');
      }
    },
  });
};

// 重新执行
const rerunExecution = (record: ExecutionRecord) => {
  Modal.confirm({
    title: '确认重新执行?',
    content: `即将重新执行此工作流。`,
    async onOk() {
      try {
        await workflowApi.execute({ workflowId: record.workflowId });
        message.success('重新执行已启动');
        await fetchExecutionList();
      } catch (error) {
        message.error('重新执行失败');
      }
    },
  });
};

// 导出记录
const exportExecutions = () => {
  message.info('导出功能开发中');
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'processing',
    paused: 'warning',
    completed: 'success',
    failed: 'error',
    cancelled: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '执行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
  };
  return textMap[status] || status;
};

// 获取触发类型颜色
const getTriggerTypeColor = (triggerType: string) => {
  const colorMap: Record<string, string> = {
    manual: 'blue',
    scheduled: 'green',
    webhook: 'purple',
    api: 'orange',
  };
  return colorMap[triggerType] || 'default';
};

// 获取触发类型文本
const getTriggerTypeText = (triggerType: string) => {
  const textMap: Record<string, string> = {
    manual: '手动执行',
    scheduled: '定时执行',
    webhook: 'Webhook',
    api: 'API调用',
  };
  return textMap[triggerType] || triggerType;
};

// 获取进度状态
const getProgressStatus = (status: string) => {
  if (status === 'failed') {
    return 'exception';
  }
  if (status === 'completed') {
    return 'success';
  }
  if (status === 'running') {
    return 'active';
  }
  return 'normal';
};

// 格式化执行时长
const formatDuration = (startTime: string, endTime?: string) => {
  if (!endTime) {
    return '执行中';
  }

  const start = moment(startTime);
  const end = moment(endTime);
  const duration = moment.duration(end.diff(start));

  const minutes = Math.floor(duration.asMinutes());
  const seconds = Math.floor(duration.asSeconds() % 60);

  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${seconds}秒`;
};

// 监听工作流ID变化
watch(
  () => props.workflowId,
  (newId) => {
    if (newId && props.visible) {
      fetchExecutionList();
    }
  },
);

// 监听visible变化
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.workflowId) {
      fetchExecutionList();
    }
  },
);
</script>

<style scoped>
.execution-records-container {
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
  flex-shrink: 0;
}

.search-section {
  flex: 1;
}

.action-section {
  flex-shrink: 0;
}

.execution-table {
  flex: 1;
  overflow: auto;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

:deep(.ant-progress) {
  display: inline-block;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .execution-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-section .ant-form {
    flex-direction: column;
  }

  .search-section .ant-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
