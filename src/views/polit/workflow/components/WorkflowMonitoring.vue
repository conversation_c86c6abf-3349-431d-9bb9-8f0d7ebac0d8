<template>
  <div class="workflow-monitoring">
    <a-row :gutter="24">
      <!-- 左侧：实时监控 -->
      <a-col :span="16">
        <!-- 概览指标 -->
        <a-card title="实时监控" size="small" class="metrics-card">
          <template #extra>
            <a-space>
              <a-select
                v-model:value="timeRange"
                size="small"
                style="width: 120px"
              >
                <a-select-option value="5m">最近5分钟</a-select-option>
                <a-select-option value="15m">最近15分钟</a-select-option>
                <a-select-option value="1h">最近1小时</a-select-option>
                <a-select-option value="24h">最近24小时</a-select-option>
              </a-select>
              <a-button size="small" :loading="loading" @click="handleRefresh">
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
            </a-space>
          </template>

          <!-- 关键指标 -->
          <div class="metrics-overview">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="执行成功率"
                  :value="metrics.successRate"
                  suffix="%"
                  :value-style="{
                    color: getSuccessRateColor(metrics.successRate),
                  }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="平均响应时间"
                  :value="metrics.avgResponseTime"
                  suffix="ms"
                  :value-style="{
                    color: getResponseTimeColor(metrics.avgResponseTime),
                  }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="错误率"
                  :value="metrics.errorRate"
                  suffix="%"
                  :value-style="{ color: getErrorRateColor(metrics.errorRate) }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="活跃连接"
                  :value="metrics.activeConnections"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 流量监控图表 -->
        <a-card title="流量监控" size="small" class="chart-card">
          <div class="chart-container">
            <div id="traffic-chart" class="chart"></div>
          </div>
        </a-card>

        <!-- 错误率图表 -->
        <a-card title="错误率趋势" size="small" class="chart-card">
          <div class="chart-container">
            <div id="error-chart" class="chart"></div>
          </div>
        </a-card>

        <!-- 响应时间图表 -->
        <a-card title="响应时间分布" size="small" class="chart-card">
          <div class="chart-container">
            <div id="response-chart" class="chart"></div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：服务状态和日志 -->
      <a-col :span="8">
        <!-- 服务健康状态 -->
        <a-card title="服务状态" size="small" class="status-card">
          <div class="service-status">
            <div
              v-for="service in serviceStatus"
              :key="service.name"
              class="service-item"
            >
              <div class="service-info">
                <div class="service-name">{{ service.name }}</div>
                <div class="service-version">{{ service.version }}</div>
              </div>
              <div class="service-health">
                <a-badge
                  :status="getHealthStatus(service.health)"
                  :text="getHealthText(service.health)"
                />
              </div>
              <div class="service-metrics">
                <div class="metric-item">
                  <span class="metric-label">QPS:</span>
                  <span class="metric-value">{{ service.qps }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">RT:</span>
                  <span class="metric-value">{{ service.responseTime }}ms</span>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 告警信息 -->
        <a-card title="告警信息" size="small" class="alert-card">
          <div class="alerts-container">
            <div
              v-for="alert in alerts"
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <div class="alert-header">
                <a-tag :color="getAlertColor(alert.level)">
                  {{ getAlertLevel(alert.level) }}
                </a-tag>
                <span class="alert-time">{{ formatTime(alert.time) }}</span>
              </div>
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-service">服务: {{ alert.service }}</div>
            </div>

            <a-empty v-if="alerts.length === 0" description="暂无告警" />
          </div>
        </a-card>

        <!-- 实时日志 -->
        <a-card title="实时日志" size="small" class="logs-card">
          <template #extra>
            <a-space>
              <a-select
                v-model:value="logLevel"
                size="small"
                style="width: 100px"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="info">INFO</a-select-option>
                <a-select-option value="warn">WARN</a-select-option>
                <a-select-option value="error">ERROR</a-select-option>
              </a-select>
              <a-button
                size="small"
                :type="autoRefresh ? 'primary' : 'default'"
                @click="toggleAutoRefresh"
              >
                {{ autoRefresh ? '停止' : '自动' }}
              </a-button>
            </a-space>
          </template>

          <div class="logs-container">
            <div
              v-for="log in filteredLogs"
              :key="log.id"
              class="log-item"
              :class="log.level"
            >
              <div class="log-header">
                <span class="log-time">{{ formatTime(log.time) }}</span>
                <a-tag size="small" :color="getLogColor(log.level)">
                  {{ log.level.toUpperCase() }}
                </a-tag>
              </div>
              <div class="log-message">{{ log.message }}</div>
              <div class="log-source">{{ log.service }} | {{ log.source }}</div>
            </div>

            <div v-if="filteredLogs.length === 0" class="no-logs">暂无日志</div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { ReloadOutlined } from '@ant-design/icons-vue';

// 接口定义
interface Metrics {
  successRate: number;
  avgResponseTime: number;
  errorRate: number;
  activeConnections: number;
}

interface ServiceStatus {
  name: string;
  version: string;
  health: 'healthy' | 'warning' | 'error';
  qps: number;
  responseTime: number;
}

interface Alert {
  id: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  service: string;
  time: string;
}

interface LogEntry {
  id: string;
  time: string;
  level: 'info' | 'warn' | 'error';
  message: string;
  service: string;
  source: string;
}

// 响应式数据
const loading = ref(false);
const timeRange = ref('15m');
const logLevel = ref('all');
const autoRefresh = ref(true);

// 监控数据
const metrics = reactive<Metrics>({
  successRate: 99.2,
  avgResponseTime: 156,
  errorRate: 0.8,
  activeConnections: 1247,
});

const serviceStatus = ref<ServiceStatus[]>([
  {
    name: 'user-service',
    version: 'v1.0.0',
    health: 'healthy',
    qps: 120,
    responseTime: 145,
  },
  {
    name: 'order-service',
    version: 'v1.1.0',
    health: 'warning',
    qps: 89,
    responseTime: 234,
  },
  {
    name: 'payment-service',
    version: 'v2.0.0',
    health: 'healthy',
    qps: 56,
    responseTime: 112,
  },
]);

const alerts = ref<Alert[]>([
  {
    id: '1',
    level: 'warning',
    message: '服务响应时间超过阈值',
    service: 'order-service',
    time: new Date().toISOString(),
  },
  {
    id: '2',
    level: 'info',
    message: '灰度发布流量切换完成',
    service: 'user-service',
    time: new Date(Date.now() - 300000).toISOString(),
  },
]);

const logs = ref<LogEntry[]>([
  {
    id: '1',
    time: new Date().toISOString(),
    level: 'info',
    message: 'Canary deployment started successfully',
    service: 'user-service',
    source: 'deployment-controller',
  },
  {
    id: '2',
    time: new Date(Date.now() - 30000).toISOString(),
    level: 'warn',
    message: 'High response time detected',
    service: 'order-service',
    source: 'metrics-collector',
  },
  {
    id: '3',
    time: new Date(Date.now() - 60000).toISOString(),
    level: 'error',
    message: 'Failed to connect to database',
    service: 'payment-service',
    source: 'application',
  },
]);

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return logs.value;
  }
  return logs.value.filter((log) => log.level === logLevel.value);
});

// 定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 方法
function getSuccessRateColor(rate: number) {
  if (rate >= 99) {
    return '#52c41a';
  }
  if (rate >= 95) {
    return '#faad14';
  }
  return '#ff4d4f';
}

function getResponseTimeColor(time: number) {
  if (time <= 100) {
    return '#52c41a';
  }
  if (time <= 200) {
    return '#faad14';
  }
  return '#ff4d4f';
}

function getErrorRateColor(rate: number) {
  if (rate <= 1) {
    return '#52c41a';
  }
  if (rate <= 5) {
    return '#faad14';
  }
  return '#ff4d4f';
}

function getHealthStatus(health: string) {
  const statusMap: Record<string, any> = {
    healthy: 'success',
    warning: 'warning',
    error: 'error',
  };
  return statusMap[health] || 'default';
}

function getHealthText(health: string) {
  const textMap: Record<string, string> = {
    healthy: '健康',
    warning: '告警',
    error: '异常',
  };
  return textMap[health] || health;
}

function getAlertColor(level: string) {
  const colorMap: Record<string, string> = {
    info: 'blue',
    warning: 'orange',
    error: 'red',
  };
  return colorMap[level] || 'default';
}

function getAlertLevel(level: string) {
  const levelMap: Record<string, string> = {
    info: '信息',
    warning: '警告',
    error: '错误',
  };
  return levelMap[level] || level;
}

function getLogColor(level: string) {
  const colorMap: Record<string, string> = {
    info: 'blue',
    warn: 'orange',
    error: 'red',
  };
  return colorMap[level] || 'default';
}

function formatTime(time: string) {
  return new Date(time).toLocaleTimeString();
}

function handleRefresh() {
  loading.value = true;

  // 模拟数据刷新
  setTimeout(() => {
    // 更新指标
    metrics.successRate = 99.2 + (Math.random() - 0.5) * 2;
    metrics.avgResponseTime = 156 + Math.floor((Math.random() - 0.5) * 50);
    metrics.errorRate = 0.8 + (Math.random() - 0.5) * 1;
    metrics.activeConnections = 1247 + Math.floor((Math.random() - 0.5) * 200);

    // 更新服务状态
    serviceStatus.value.forEach((service) => {
      service.qps += Math.floor((Math.random() - 0.5) * 20);
      service.responseTime += Math.floor((Math.random() - 0.5) * 30);
    });

    loading.value = false;
  }, 1000);
}

function toggleAutoRefresh() {
  autoRefresh.value = !autoRefresh.value;

  if (autoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}

function startAutoRefresh() {
  if (refreshTimer) {
    return;
  }

  refreshTimer = setInterval(() => {
    if (autoRefresh.value) {
      generateNewLog();
      updateMetrics();
    }
  }, 5000); // 5秒更新一次
}

function stopAutoRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
}

function generateNewLog() {
  const services = ['user-service', 'order-service', 'payment-service'];
  const levels = ['info', 'warn', 'error'];
  const messages = [
    'Request processed successfully',
    'High CPU usage detected',
    'Database connection timeout',
    'Memory usage exceeded threshold',
    'Health check passed',
    'Authentication failed',
  ];

  const newLog: LogEntry = {
    id: Date.now().toString(),
    time: new Date().toISOString(),
    level: levels[Math.floor(Math.random() * levels.length)] as any,
    message: messages[Math.floor(Math.random() * messages.length)],
    service: services[Math.floor(Math.random() * services.length)],
    source: 'system',
  };

  logs.value.unshift(newLog);

  // 保持最新 50 条日志
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
}

function updateMetrics() {
  // 模拟指标更新
  metrics.successRate = Math.max(
    95,
    Math.min(100, metrics.successRate + (Math.random() - 0.5) * 0.5),
  );
  metrics.avgResponseTime = Math.max(
    50,
    metrics.avgResponseTime + Math.floor((Math.random() - 0.5) * 10),
  );
  metrics.errorRate = Math.max(
    0,
    Math.min(5, metrics.errorRate + (Math.random() - 0.5) * 0.2),
  );
  metrics.activeConnections = Math.max(
    0,
    metrics.activeConnections + Math.floor((Math.random() - 0.5) * 50),
  );
}

function initCharts() {
  // 这里可以集成真实的图表库如 ECharts 或 Chart.js
  // 为了演示，我们创建简单的占位图表
  nextTick(() => {
    const charts = ['traffic-chart', 'error-chart', 'response-chart'];

    charts.forEach((chartId) => {
      try {
        const element = document.getElementById(chartId);
        if (element && element.parentNode) {
          element.innerHTML = `
            <div style="
              height: 200px; 
              display: flex; 
              align-items: center; 
              justify-content: center;
              background: #f5f5f5;
              border-radius: 6px;
              color: #999;
            ">
              ${chartId.replace('-', ' ')} 图表占位
              <br>
              <small>可集成 ECharts 或其他图表库</small>
            </div>
          `;
        }
      } catch (error) {
        console.warn(`Failed to initialize chart ${chartId}:`, error);
      }
    });
  });
}

// 生命周期
onMounted(() => {
  try {
    initCharts();
    startAutoRefresh();
  } catch (error) {
    console.error('Failed to mount WorkflowMonitoring component:', error);
  }
});

onUnmounted(() => {
  try {
    stopAutoRefresh();
  } catch (error) {
    console.error('Failed to cleanup WorkflowMonitoring component:', error);
  }
});

// 导出组件方法
defineExpose({
  refresh: handleRefresh,
  getMetrics: () => metrics,
  getServiceStatus: () => serviceStatus.value,
});
</script>

<style scoped>
.workflow-monitoring {
  padding: 16px;
}

.metrics-card,
.chart-card,
.status-card,
.alert-card,
.logs-card {
  margin-bottom: 16px;
}

.metrics-overview {
  margin-bottom: 16px;
}

.chart-container {
  height: 200px;
}

.chart {
  width: 100%;
  height: 100%;
}

.service-status {
  max-height: 300px;
  overflow-y: auto;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: white;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.service-version {
  font-size: 12px;
  color: #666;
}

.service-health {
  margin: 0 16px;
}

.service-metrics {
  text-align: right;
}

.metric-item {
  font-size: 12px;
  margin-bottom: 2px;
}

.metric-label {
  color: #666;
  margin-right: 4px;
}

.metric-value {
  font-weight: 500;
}

.alerts-container {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid #e8e8e8;
}

.alert-item.warning {
  background: #fff7e6;
  border-left-color: #faad14;
}

.alert-item.error {
  background: #fff2f0;
  border-left-color: #ff4d4f;
}

.alert-item.info {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #666;
}

.alert-message {
  font-weight: 500;
  margin-bottom: 4px;
}

.alert-service {
  font-size: 12px;
  color: #666;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Monaco', 'Consolas', monospace;
}

.log-item {
  padding: 8px;
  margin-bottom: 6px;
  border-radius: 4px;
  background: #f8f9fa;
  border-left: 3px solid #e8e8e8;
  font-size: 12px;
}

.log-item.info {
  border-left-color: #1890ff;
}

.log-item.warn {
  border-left-color: #faad14;
  background: #fffbf0;
}

.log-item.error {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.log-time {
  color: #666;
}

.log-message {
  margin-bottom: 4px;
  color: #333;
  word-break: break-all;
}

.log-source {
  color: #999;
  font-size: 11px;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 18px;
}
</style>
