<template>
  <a-modal
    v-model:visible="modalVisible"
    title="工作流详情"
    width="1200px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="workflowDetail" class="workflow-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="工作流名称">
            {{ workflowDetail.name }}
          </a-descriptions-item>
          <a-descriptions-item label="类型">
            <a-tag :color="getTypeColor(workflowDetail.type)">
              {{ getTypeText(workflowDetail.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(workflowDetail.status)">
              {{ getStatusText(workflowDetail.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="命名空间">
            {{ workflowDetail.namespace }}
          </a-descriptions-item>
          <a-descriptions-item label="集群ID">
            {{ workflowDetail.clusterId }}
          </a-descriptions-item>
          <a-descriptions-item label="版本">
            {{ workflowDetail.version }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ workflowDetail.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ workflowDetail.updateTime }}
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="3">
            {{ workflowDetail.description || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 服务配置 -->
      <a-card title="服务配置" size="small" style="margin-bottom: 16px">
        <div
          v-if="workflowDetail.services && workflowDetail.services.length > 0"
        >
          <a-table
            :columns="serviceColumns"
            :data-source="workflowDetail.services"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'images'">
                <div>
                  <a-tag
                    v-for="(image, containerName) in record.images"
                    :key="containerName"
                    color="blue"
                    style="margin-bottom: 4px; display: block"
                  >
                    {{ containerName }}: {{ image }}
                  </a-tag>
                </div>
              </template>
              <template v-if="column.key === 'replicas'">
                灰度: {{ record.canaryReplicas }} / 生产:
                {{ record.prodReplicas }}
              </template>
              <template v-if="column.key === 'trafficRatio'">
                {{ record.trafficRatio }}%
              </template>
              <template v-if="column.key === 'isChainEntry'">
                <a-tag :color="record.isChainEntry ? 'green' : 'default'">
                  {{ record.isChainEntry ? '是' : '否' }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
        <a-empty v-else description="暂无服务配置" />
      </a-card>

      <!-- 全链路配置 -->
      <a-card
        v-if="workflowDetail.type === 'canary' && workflowDetail.chainConfig"
        title="全链路灰度配置"
        size="small"
        style="margin-bottom: 16px"
      >
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="启用全链路">
            <a-tag
              :color="
                workflowDetail.chainConfig.enableChainCanary ? 'green' : 'red'
              "
            >
              {{ workflowDetail.chainConfig.enableChainCanary ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="流量策略">
            {{
              getTrafficStrategyText(workflowDetail.chainConfig.trafficStrategy)
            }}
          </a-descriptions-item>
          <a-descriptions-item label="默认流量比例">
            {{ workflowDetail.chainConfig.defaultTrafficRatio }}%
          </a-descriptions-item>
          <a-descriptions-item label="粘性会话">
            <a-tag
              :color="
                workflowDetail.chainConfig.stickySession ? 'green' : 'default'
              "
            >
              {{ workflowDetail.chainConfig.stickySession ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="入口服务" :span="2">
            <div>
              <a-tag
                v-for="service in workflowDetail.chainConfig.entryServices"
                :key="service"
                color="blue"
              >
                {{ service }}
              </a-tag>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 全局配置 -->
      <a-card title="全局配置" size="small">
        <a-tabs>
          <a-tab-pane key="timeout" tab="超时配置">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="步骤超时">
                {{
                  workflowDetail.globalConfig?.globalTimeout?.stepTimeout || 600
                }}
                秒
              </a-descriptions-item>
              <a-descriptions-item label="审批超时">
                {{
                  workflowDetail.globalConfig?.globalTimeout?.approvalTimeout ||
                  3600
                }}
                秒
              </a-descriptions-item>
              <a-descriptions-item label="监控超时">
                {{
                  workflowDetail.globalConfig?.globalTimeout
                    ?.monitoringTimeout || 1800
                }}
                秒
              </a-descriptions-item>
              <a-descriptions-item label="回滚超时">
                {{
                  workflowDetail.globalConfig?.globalTimeout?.rollbackTimeout ||
                  900
                }}
                秒
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <a-tab-pane key="notification" tab="通知配置">
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="启用通知">
                <a-tag
                  :color="
                    workflowDetail.globalConfig?.globalNotification?.enabled
                      ? 'green'
                      : 'default'
                  "
                >
                  {{
                    workflowDetail.globalConfig?.globalNotification?.enabled
                      ? '启用'
                      : '禁用'
                  }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="通知渠道">
                <div
                  v-if="
                    workflowDetail.globalConfig?.globalNotification?.channels
                      ?.length
                  "
                >
                  <a-tag
                    v-for="channel in workflowDetail.globalConfig
                      .globalNotification.channels"
                    :key="channel"
                    color="blue"
                  >
                    {{ getChannelText(channel) }}
                  </a-tag>
                </div>
                <span v-else>暂无配置</span>
              </a-descriptions-item>
              <a-descriptions-item label="通知事件">
                <div
                  v-if="
                    workflowDetail.globalConfig?.globalNotification?.events
                      ?.length
                  "
                >
                  <a-tag
                    v-for="event in workflowDetail.globalConfig
                      .globalNotification.events"
                    :key="event"
                    color="green"
                  >
                    {{ getEventText(event) }}
                  </a-tag>
                </div>
                <span v-else>暂无配置</span>
              </a-descriptions-item>
              <a-descriptions-item label="通知用户">
                <div
                  v-if="
                    workflowDetail.globalConfig?.globalNotification?.users
                      ?.length
                  "
                >
                  <a-tag
                    v-for="user in workflowDetail.globalConfig
                      .globalNotification.users"
                    :key="user"
                    color="purple"
                  >
                    {{ user }}
                  </a-tag>
                </div>
                <span v-else>暂无配置</span>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <a-tab-pane key="rollback" tab="回滚配置">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="启用回滚">
                <a-tag
                  :color="
                    workflowDetail.globalConfig?.globalRollback?.enabled
                      ? 'green'
                      : 'default'
                  "
                >
                  {{
                    workflowDetail.globalConfig?.globalRollback?.enabled
                      ? '启用'
                      : '禁用'
                  }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="自动回滚">
                <a-tag
                  :color="
                    workflowDetail.globalConfig?.globalRollback?.autoRollback
                      ? 'green'
                      : 'default'
                  "
                >
                  {{
                    workflowDetail.globalConfig?.globalRollback?.autoRollback
                      ? '启用'
                      : '禁用'
                  }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { workflowApi } from '@/api/pilot/workflow';

interface Props {
  visible: boolean;
  workflowId: string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const loading = ref(false);
const workflowDetail = ref<any>(null);

// 服务表格列配置
const serviceColumns = [
  {
    title: '服务名称',
    dataIndex: 'serviceName',
    key: 'serviceName',
    width: 120,
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
    width: 100,
  },
  {
    title: '镜像配置',
    key: 'images',
    width: 200,
  },
  {
    title: '副本数',
    key: 'replicas',
    width: 120,
  },
  {
    title: '流量比例',
    key: 'trafficRatio',
    width: 80,
  },
  {
    title: '执行顺序',
    dataIndex: 'order',
    key: 'order',
    width: 80,
  },
  {
    title: '链路入口',
    key: 'isChainEntry',
    width: 80,
  },
];

// 获取工作流详情
const fetchWorkflowDetail = async () => {
  if (!props.workflowId) {
    return;
  }

  try {
    loading.value = true;
    const response = await workflowApi.getDetail(props.workflowId);
    workflowDetail.value = response.data;
  } catch (error) {
    console.error('获取工作流详情失败:', error);
    message.error('获取工作流详情失败');
  } finally {
    loading.value = false;
  }
};

// 监听workflowId变化
watch(
  () => props.workflowId,
  (newId) => {
    if (newId && props.visible) {
      fetchWorkflowDetail();
    }
  },
);

// 监听visible变化
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.workflowId) {
      fetchWorkflowDetail();
    }
  },
);

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    canary: 'blue',
    'blue-green': 'green',
    'ab-test': 'orange',
  };
  return colorMap[type] || 'default';
};

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    canary: '灰度发布',
    'blue-green': '蓝绿部署',
    'ab-test': 'A/B测试',
  };
  return textMap[type] || type;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    active: 'blue',
    running: 'processing',
    completed: 'success',
    failed: 'error',
    archived: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    active: '启用',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    archived: '已归档',
  };
  return textMap[status] || status;
};

// 获取流量策略文本
const getTrafficStrategyText = (strategy: string) => {
  const textMap: Record<string, string> = {
    weight: '权重分配',
    header: '请求头路由',
    cookie: 'Cookie路由',
  };
  return textMap[strategy] || strategy;
};

// 获取通知渠道文本
const getChannelText = (channel: string) => {
  const textMap: Record<string, string> = {
    email: '邮件',
    slack: 'Slack',
    webhook: 'Webhook',
  };
  return textMap[channel] || channel;
};

// 获取通知事件文本
const getEventText = (event: string) => {
  const textMap: Record<string, string> = {
    start: '开始执行',
    success: '执行成功',
    failure: '执行失败',
    approval: '等待审批',
  };
  return textMap[event] || event;
};
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.workflow-detail {
  max-height: 600px;
  overflow-y: auto;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.ant-table-small) .ant-table-tbody > tr > td {
  padding: 8px;
}
</style>
