<template>
  <div class="deployment-image-selector">
    <!-- 部署选择器 -->
    <a-form-item label="选择部署" :required="required">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-select
            v-model:value="selectedCluster"
            placeholder="请选择集群"
            :loading="clustersLoading"
            allow-clear
            @change="onClusterChange"
          >
            <a-select-option
              v-for="cluster in clusters"
              :key="cluster.value"
              :value="cluster.value"
            >
              {{ cluster.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-select
            v-model:value="selectedNamespace"
            placeholder="请选择命名空间"
            :loading="namespacesLoading"
            :disabled="!selectedCluster"
            allow-clear
            @change="onNamespaceChange"
          >
            <a-select-option
              v-for="ns in namespaces"
              :key="ns.value"
              :value="ns.value"
            >
              {{ ns.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-select
            v-model:value="selectedDeployment"
            placeholder="请选择部署"
            :loading="deploymentsLoading"
            :disabled="!selectedNamespace"
            allow-clear
            show-search
            @change="onDeploymentChange"
          >
            <a-select-option
              v-for="deployment in deployments"
              :key="deployment.name"
              :value="deployment.name"
            >
              {{ deployment.name }}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
    </a-form-item>

    <!-- 镜像选择器 -->
    <div v-if="deploymentImages.length > 0" class="images-section">
      <a-divider>从部署中提取的镜像</a-divider>

      <div
        v-for="(imageInfo, index) in deploymentImages"
        :key="index"
        class="image-item"
      >
        <a-card size="small" :title="`容器: ${imageInfo.containerName}`">
          <a-form-item :label="`当前镜像`">
            <a-tag color="blue">{{ imageInfo.image }}</a-tag>
          </a-form-item>

          <a-form-item :label="`选择新版本`">
            <a-row :gutter="16">
              <a-col :span="16">
                <a-select
                  v-model:value="imageInfo.selectedTag"
                  placeholder="请选择版本"
                  :loading="imageInfo.tagsLoading"
                  show-search
                  :filter-option="filterTagOption"
                  allow-clear
                  @dropdown-visible-change="(open: boolean) => onTagDropdownChange(open, imageInfo)"
                >
                  <a-select-option
                    v-for="tag in imageInfo.availableTags"
                    :key="tag"
                    :value="tag"
                  >
                    <div class="tag-option">
                      <span>{{ tag }}</span>
                      <a-tag
                        v-if="tag === imageInfo.currentTag"
                        color="green"
                        size="small"
                      >
                        当前版本
                      </a-tag>
                    </div>
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="8">
                <a-button
                  type="link"
                  size="small"
                  :loading="imageInfo.tagsLoading"
                  @click="refreshTags(imageInfo)"
                >
                  <ReloadOutlined />
                  刷新版本
                </a-button>
              </a-col>
            </a-row>
          </a-form-item>

          <!-- 版本对比 -->
          <div
            v-if="
              imageInfo.selectedTag &&
              imageInfo.selectedTag !== imageInfo.currentTag
            "
            class="version-compare"
          >
            <a-alert type="info" show-icon>
              <template #message>
                <div>
                  版本变更：
                  <a-tag color="orange">{{ imageInfo.currentTag }}</a-tag>
                  →
                  <a-tag color="green">{{ imageInfo.selectedTag }}</a-tag>
                </div>
              </template>
            </a-alert>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 手动输入模式 -->
    <div v-else-if="!deploymentsLoading" class="manual-input">
      <a-divider>手动配置镜像</a-divider>
      <ImageSelector
        v-model="manualImageValue"
        label="镜像配置"
        :required="required"
        @change="onManualImageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import {
  clusterApi,
  type DeploymentInfo,
  type ContainerImage,
} from '@/api/pilot/cluster';
import { registryApi } from '@/api/pilot/registry';
import ImageSelector from './ImageSelector.vue';

interface Props {
  modelValue?: Array<{
    containerName: string;
    repository: string;
    tag: string;
    originalImage: string;
  }>;
  required?: boolean;
  clusterId?: string; // 可以预设集群
  namespace?: string; // 可以预设命名空间
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void;
  (e: 'change', value: Props['modelValue']): void;
  (e: 'deploymentSelected', deployment: DeploymentInfo | null): void;
}

interface ExtendedImageInfo extends ContainerImage {
  currentTag: string;
  selectedTag?: string;
  availableTags: string[];
  tagsLoading: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态
const selectedCluster = ref('');
const selectedNamespace = ref('');
const selectedDeployment = ref('');
const deploymentImages = ref<ExtendedImageInfo[]>([]);
const manualImageValue = ref<{ repository: string; tag: string } | undefined>();

// 数据
const clusters = ref<Array<{ label: string; value: string }>>([]);
const namespaces = ref<Array<{ label: string; value: string }>>([]);
const deployments = ref<DeploymentInfo[]>([]);

// 加载状态
const clustersLoading = ref(false);
const namespacesLoading = ref(false);
const deploymentsLoading = ref(false);

// 初始化
onMounted(async () => {
  await loadClusters();

  // 如果有预设值，自动设置
  if (props.clusterId) {
    selectedCluster.value = props.clusterId;
    await onClusterChange(props.clusterId);
  }

  if (props.namespace) {
    selectedNamespace.value = props.namespace;
    await onNamespaceChange(props.namespace);
  }
});

// 加载集群列表
const loadClusters = async () => {
  try {
    clustersLoading.value = true;
    const response = await clusterApi.list({ page: 1, pageSize: 100 });

    if (response.data && response.data.list) {
      clusters.value = response.data.list.map((cluster: any) => ({
        label: cluster.name,
        value: cluster.uuid,
      }));
    }
  } catch (error) {
    console.error('加载集群列表失败:', error);
    message.error('加载集群列表失败');
  } finally {
    clustersLoading.value = false;
  }
};

// 集群变化处理
const onClusterChange = async (clusterId: string) => {
  if (!clusterId) {
    namespaces.value = [];
    deployments.value = [];
    deploymentImages.value = [];
    selectedNamespace.value = '';
    selectedDeployment.value = '';
    return;
  }

  try {
    namespacesLoading.value = true;
    const response = await clusterApi.listNamespaces({ cluster: clusterId });

    if (response.data) {
      namespaces.value = response.data;
    }
  } catch (error) {
    console.error('加载命名空间失败:', error);
    message.error('加载命名空间失败');
  } finally {
    namespacesLoading.value = false;
  }
};

// 命名空间变化处理
const onNamespaceChange = async (namespace: string) => {
  if (!namespace || !selectedCluster.value) {
    deployments.value = [];
    deploymentImages.value = [];
    selectedDeployment.value = '';
    return;
  }

  try {
    deploymentsLoading.value = true;
    const response = await clusterApi.listDeployments({
      cluster: selectedCluster.value,
      namespace: namespace,
      page: 1,
      pageSize: 100,
    });

    if (response.data && response.data.list) {
      deployments.value = response.data.list;
    }
  } catch (error) {
    console.error('加载部署列表失败:', error);
    message.error('加载部署列表失败');
  } finally {
    deploymentsLoading.value = false;
  }
};

// 部署变化处理
const onDeploymentChange = async (deploymentName: string) => {
  if (!deploymentName) {
    deploymentImages.value = [];
    emit('deploymentSelected', null);
    updateModelValue();
    return;
  }

  const deployment = deployments.value.find((d) => d.name === deploymentName);
  if (!deployment) {
    return;
  }

  emit('deploymentSelected', deployment);

  // 提取镜像信息并自动获取版本
  const extendedImages: ExtendedImageInfo[] = deployment.images.map((img) => ({
    ...img,
    currentTag: img.tag,
    selectedTag: img.tag, // 默认选择当前版本
    availableTags: [img.tag], // 先显示当前版本
    tagsLoading: false,
  }));

  deploymentImages.value = extendedImages;

  // 异步获取每个镜像的版本列表
  for (const imageInfo of extendedImages) {
    await loadImageTags(imageInfo);
  }

  updateModelValue();
};

// 加载镜像版本列表
const loadImageTags = async (imageInfo: ExtendedImageInfo) => {
  try {
    imageInfo.tagsLoading = true;

    // 尝试从registry API获取版本列表
    const response = await registryApi.getTags({
      page: 1,
      pageSize: 50,
      registryType: 'aliyun', // 默认使用阿里云，后续可以配置
      namespaceName: '', // 可以从repository中解析
      repositoryName: imageInfo.repository,
      keyword: '',
    });

    if (response.data && response.data.list && response.data.list.length > 0) {
      imageInfo.availableTags = response.data.list;

      // 确保当前版本在列表中
      if (!imageInfo.availableTags.includes(imageInfo.currentTag)) {
        imageInfo.availableTags.unshift(imageInfo.currentTag);
      }
    } else {
      // 如果无法获取版本列表，只显示当前版本
      imageInfo.availableTags = [imageInfo.currentTag];
      message.warning(
        `无法获取镜像 ${imageInfo.repository} 的版本列表，将显示当前版本`,
      );
    }
  } catch (error) {
    console.error(`获取镜像 ${imageInfo.repository} 版本失败:`, error);
    // 失败时只显示当前版本
    imageInfo.availableTags = [imageInfo.currentTag];
  } finally {
    imageInfo.tagsLoading = false;
  }
};

// 刷新版本列表
const refreshTags = async (imageInfo: ExtendedImageInfo) => {
  await loadImageTags(imageInfo);
};

// 版本下拉框变化处理
const onTagDropdownChange = async (
  open: boolean,
  imageInfo: ExtendedImageInfo,
) => {
  if (open && imageInfo.availableTags.length <= 1) {
    await loadImageTags(imageInfo);
  }
};

// 版本过滤
const filterTagOption = (input: string, option: any) => {
  const tag = option.value || '';
  return tag.toLowerCase().includes(input.toLowerCase());
};

// 手动镜像变化处理
const onManualImageChange = (
  value: { repository: string; tag: string } | undefined,
) => {
  if (value) {
    deploymentImages.value = [
      {
        containerName: 'main',
        image: `${value.repository}:${value.tag}`,
        repository: value.repository,
        tag: value.tag,
        currentTag: value.tag,
        selectedTag: value.tag,
        availableTags: [value.tag],
        tagsLoading: false,
      },
    ];
  } else {
    deploymentImages.value = [];
  }
  updateModelValue();
};

// 更新模型值
const updateModelValue = () => {
  const value = deploymentImages.value
    .filter((img) => img.selectedTag)
    .map((img) => ({
      containerName: img.containerName,
      repository: img.repository,
      tag: img.selectedTag!,
      originalImage: img.image,
    }));

  emit('update:modelValue', value.length > 0 ? value : undefined);
  emit('change', value.length > 0 ? value : undefined);
};

// 监听镜像选择变化
watch(
  () => deploymentImages.value.map((img) => img.selectedTag),
  () => {
    updateModelValue();
  },
  { deep: true },
);

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue || newValue.length === 0) {
      deploymentImages.value = [];
    }
  },
  { immediate: true },
);
</script>

<style scoped>
.deployment-image-selector {
  width: 100%;
}

.images-section {
  margin-top: 16px;
}

.image-item {
  margin-bottom: 16px;
}

.image-item:last-child {
  margin-bottom: 0;
}

.version-compare {
  margin-top: 12px;
}

.tag-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.manual-input {
  margin-top: 16px;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-select-selection-item) {
  max-width: 100%;
}

:deep(.ant-divider) {
  margin: 12px 0;
}
</style>
