<template>
  <a-modal
    v-model:visible="modalVisible"
    title="步骤审批"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="stepDetail" class="approval-content">
      <!-- 步骤基本信息 -->
      <a-card title="步骤信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="步骤名称">
            {{ stepDetail.stepName }}
          </a-descriptions-item>
          <a-descriptions-item label="步骤类型">
            <a-tag :color="getStepTypeColor(stepDetail.stepType)">
              {{ getStepTypeText(stepDetail.stepType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(stepDetail.status)">
              {{ getStatusText(stepDetail.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ stepDetail.startTime }}
          </a-descriptions-item>
          <a-descriptions-item label="执行人">
            {{ stepDetail.executorName }}
          </a-descriptions-item>
          <a-descriptions-item label="服务名称">
            {{ stepDetail.serviceName || '全局步骤' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 步骤配置详情 -->
      <a-card
        v-if="stepConfig"
        title="步骤配置"
        size="small"
        style="margin-bottom: 16px"
      >
        <a-descriptions
          v-if="stepDetail.stepType === 'approval'"
          :column="1"
          bordered
        >
          <a-descriptions-item label="审批人">
            <a-tag
              v-for="approver in stepConfig.approvers"
              :key="approver"
              color="blue"
            >
              {{ approver }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="审批条件">
            {{
              stepConfig.conditionType === 'and' ? '所有人通过' : '任一人通过'
            }}
          </a-descriptions-item>
          <a-descriptions-item label="超时时间">
            {{ stepConfig.timeout ? `${stepConfig.timeout}秒` : '无限制' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-descriptions
          v-else-if="stepDetail.stepType === 'deployment'"
          :column="1"
          bordered
        >
          <a-descriptions-item label="部署配置">
            <pre class="config-preview">{{
              JSON.stringify(stepConfig, null, 2)
            }}</pre>
          </a-descriptions-item>
        </a-descriptions>

        <a-descriptions v-else :column="1" bordered>
          <a-descriptions-item label="配置详情">
            <pre class="config-preview">{{
              JSON.stringify(stepConfig, null, 2)
            }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 执行日志 -->
      <a-card
        v-if="stepDetail.logs"
        title="执行日志"
        size="small"
        style="margin-bottom: 16px"
      >
        <div class="logs-container">
          <pre class="logs-content">{{ stepDetail.logs }}</pre>
        </div>
      </a-card>

      <!-- 审批操作 -->
      <a-card title="审批操作" size="small">
        <a-form
          ref="formRef"
          :model="approvalForm"
          :rules="rules"
          layout="vertical"
        >
          <a-form-item label="审批决定" name="action" required>
            <a-radio-group v-model:value="approvalForm.action">
              <a-radio value="approve">
                <CheckCircleOutlined style="color: #52c41a" />
                通过
              </a-radio>
              <a-radio value="reject">
                <CloseCircleOutlined style="color: #ff4d4f" />
                拒绝
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="审批意见" name="comment">
            <a-textarea
              v-model:value="approvalForm.comment"
              placeholder="请输入审批意见（可选）"
              :rows="4"
              show-count
              :maxlength="500"
            />
          </a-form-item>

          <!-- 审批建议 -->
          <a-form-item label="快速填写">
            <a-space wrap>
              <a-tag
                v-for="suggestion in approvalSuggestions"
                :key="suggestion"
                style="cursor: pointer"
                @click="selectSuggestion(suggestion)"
              >
                {{ suggestion }}
              </a-tag>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <div v-else class="loading-container">
      <a-spin size="large" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons-vue';
import { workflowApi } from '@/api/pilot/workflow';

interface Props {
  visible: boolean;
  executionId: number;
  stepId: number;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

interface StepDetail {
  id: number;
  stepName: string;
  stepType: string;
  status: string;
  startTime: string;
  endTime?: string;
  executorName: string;
  serviceName?: string;
  config?: string;
  logs?: string;
  errorMessage?: string;
}

interface ApprovalForm {
  action: 'approve' | 'reject' | '';
  comment: string;
}

interface ApprovalSubmitParams {
  executionId: number;
  stepId: number;
  action: 'approve' | 'reject';
  comment: string;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const formRef = ref();
const loading = ref(false);
const stepDetail = ref<StepDetail | null>(null);

// 审批表单
const approvalForm = reactive<ApprovalForm>({
  action: '',
  comment: '',
});

// 表单验证规则
const rules = {
  action: [{ required: true, message: '请选择审批决定' }],
};

// 审批建议
const approvalSuggestions = [
  '代码审查通过，同意发布',
  '测试验证完成，可以继续',
  '风险评估完成，建议继续',
  '需要进一步测试验证',
  '存在安全风险，建议修复后重新审批',
  '性能指标不达标，需要优化',
];

// 解析步骤配置
const stepConfig = computed(() => {
  if (!stepDetail.value?.config) {
    return null;
  }

  try {
    return JSON.parse(stepDetail.value.config);
  } catch {
    return { raw: stepDetail.value.config };
  }
});

// 获取步骤详情
const fetchStepDetail = async () => {
  if (!props.executionId || !props.stepId) {
    return;
  }

  try {
    loading.value = true;

    // 首先获取执行详情，从中找到对应的步骤
    const response = await workflowApi.getExecution(props.executionId);

    if (response.data && response.data.stepExecutions) {
      const step = response.data.stepExecutions.find(
        (s: any) => s.id === props.stepId,
      );

      if (step) {
        stepDetail.value = {
          id: step.id,
          stepName: step.stepName || step.name || '未知步骤',
          stepType: step.stepType || step.type,
          status: step.status,
          startTime: step.startTime,
          endTime: step.endTime,
          executorName: step.executorName || step.executor || '未知',
          serviceName: step.serviceName,
          config: step.config,
          logs: step.logs,
          errorMessage: step.errorMessage,
        };
      } else {
        message.error('未找到对应的步骤信息');
        handleCancel();
      }
    }
  } catch (error) {
    console.error('获取步骤详情失败:', error);
    message.error('获取步骤详情失败');
  } finally {
    loading.value = false;
  }
};

// 提交审批
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    if (!approvalForm.action) {
      message.error('请选择审批决定');
      return;
    }

    const params: ApprovalSubmitParams = {
      executionId: props.executionId,
      stepId: props.stepId,
      action: approvalForm.action as 'approve' | 'reject',
      comment: approvalForm.comment,
    };

    await workflowApi.approveStep(params);

    message.success(
      approvalForm.action === 'approve' ? '审批通过' : '审批拒绝',
    );
    emit('success');
    handleCancel();
  } catch (error: any) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('审批失败:', error);
    message.error('审批操作失败');
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  // 重置表单
  approvalForm.action = '';
  approvalForm.comment = '';
  stepDetail.value = null;

  emit('update:visible', false);
};

// 选择建议
const selectSuggestion = (suggestion: string) => {
  approvalForm.comment = suggestion;
};

// 获取步骤类型颜色
const getStepTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    deployment: 'blue',
    approval: 'orange',
    validation: 'green',
    monitoring: 'purple',
    traffic_split: 'cyan',
    cleanup: 'red',
  };
  return colorMap[type] || 'default';
};

// 获取步骤类型文本
const getStepTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    deployment: '部署',
    approval: '审批',
    validation: '验证',
    monitoring: '监控',
    traffic_split: '流量切换',
    cleanup: '清理',
  };
  return textMap[type] || type;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'processing',
    paused: 'warning',
    completed: 'success',
    failed: 'error',
    cancelled: 'default',
    skipped: 'default',
    approved: 'success',
    rejected: 'error',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '执行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    skipped: '已跳过',
    approved: '已通过',
    rejected: '已拒绝',
  };
  return textMap[status] || status;
};

// 监听props变化
watch(
  () => [props.visible, props.executionId, props.stepId],
  ([visible, executionId, stepId]) => {
    if (visible && executionId && stepId) {
      fetchStepDetail();
    }
  },
  { immediate: true },
);
</script>

<style scoped>
.approval-content {
  max-height: 600px;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.config-preview {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #000;
  border-radius: 4px;
  padding: 12px;
}

.logs-content {
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.ant-radio-wrapper) {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

:deep(.ant-tag) {
  margin-bottom: 4px;
}
</style>
