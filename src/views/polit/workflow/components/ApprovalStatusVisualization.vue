<template>
  <div class="approval-status">
    <div class="status-header">
      <h3>审批流程状态</h3>
      <a-space>
        <a-tag :color="getOverallStatusColor()" class="overall-status">
          {{ getOverallStatusText() }}
        </a-tag>
        <a-button size="small" @click="refreshStatus">
          <ReloadOutlined />
          刷新状态
        </a-button>
      </a-space>
    </div>

    <!-- 审批流程图 -->
    <div class="approval-flow">
      <div class="flow-container">
        <!-- 灰度审批 -->
        <div class="approval-stage">
          <div class="stage-header">
            <h4>灰度发布审批</h4>
            <a-tag :color="getStageStatusColor('canary')">
              {{ getStageStatusText('canary') }}
            </a-tag>
          </div>

          <div class="approvers-list">
            <div
              v-for="approver in canaryApprovers"
              :key="approver.id"
              class="approver-card"
              :class="{
                approved: approver.status === 'approved',
                rejected: approver.status === 'rejected',
              }"
            >
              <div class="approver-info">
                <a-avatar :src="approver.avatar" size="small">
                  {{ approver.name.charAt(0) }}
                </a-avatar>
                <div class="approver-details">
                  <div class="approver-name">{{ approver.name }}</div>
                  <div class="approver-role">{{ approver.role }}</div>
                </div>
              </div>
              <div class="approval-status">
                <a-icon
                  v-if="approver.status === 'approved'"
                  type="check-circle"
                  theme="filled"
                  style="color: #52c41a"
                />
                <a-icon
                  v-else-if="approver.status === 'rejected'"
                  type="close-circle"
                  theme="filled"
                  style="color: #ff4d4f"
                />
                <a-icon
                  v-else-if="approver.status === 'pending'"
                  type="clock-circle"
                  theme="filled"
                  style="color: #faad14"
                />
                <span class="status-text">
                  {{ getApproverStatusText(approver.status) }}
                </span>
              </div>
              <div v-if="approver.comment" class="approval-comment">
                <a-tooltip :title="approver.comment">
                  <MessageOutlined style="color: #1890ff" />
                </a-tooltip>
              </div>
              <div v-if="approver.approvedAt" class="approval-time">
                {{ formatTime(approver.approvedAt) }}
              </div>
            </div>
          </div>

          <div class="stage-actions">
            <a-space v-if="canaryStageStatus === 'pending'">
              <a-button
                type="primary"
                size="small"
                @click="approveStage('canary')"
              >
                批准灰度
              </a-button>
              <a-button danger size="small" @click="rejectStage('canary')">
                拒绝
              </a-button>
            </a-space>
            <div
              v-else-if="canaryStageStatus === 'approved'"
              class="stage-result success"
            >
              <CheckCircleOutlined />
              灰度审批通过
            </div>
            <div
              v-else-if="canaryStageStatus === 'rejected'"
              class="stage-result error"
            >
              <CloseCircleOutlined />
              灰度审批被拒绝
            </div>
          </div>
        </div>

        <!-- 流程箭头 -->
        <div
          class="flow-arrow"
          :class="{ active: canaryStageStatus === 'approved' }"
        >
          <div class="arrow-line"></div>
          <div class="arrow-head"></div>
          <div class="arrow-label">
            {{ canaryStageStatus === 'approved' ? '通过' : '等待' }}
          </div>
        </div>

        <!-- 生产审批 -->
        <div
          class="approval-stage"
          :class="{ disabled: canaryStageStatus !== 'approved' }"
        >
          <div class="stage-header">
            <h4>生产发布审批</h4>
            <a-tag :color="getStageStatusColor('production')">
              {{ getStageStatusText('production') }}
            </a-tag>
          </div>

          <div class="approvers-list">
            <div
              v-for="approver in productionApprovers"
              :key="approver.id"
              class="approver-card"
              :class="{
                approved: approver.status === 'approved',
                rejected: approver.status === 'rejected',
                disabled: canaryStageStatus !== 'approved',
              }"
            >
              <div class="approver-info">
                <a-avatar :src="approver.avatar" size="small">
                  {{ approver.name.charAt(0) }}
                </a-avatar>
                <div class="approver-details">
                  <div class="approver-name">{{ approver.name }}</div>
                  <div class="approver-role">{{ approver.role }}</div>
                </div>
              </div>
              <div class="approval-status">
                <a-icon
                  v-if="approver.status === 'approved'"
                  type="check-circle"
                  theme="filled"
                  style="color: #52c41a"
                />
                <a-icon
                  v-else-if="approver.status === 'rejected'"
                  type="close-circle"
                  theme="filled"
                  style="color: #ff4d4f"
                />
                <a-icon
                  v-else-if="
                    approver.status === 'pending' &&
                    canaryStageStatus === 'approved'
                  "
                  type="clock-circle"
                  theme="filled"
                  style="color: #faad14"
                />
                <span class="status-text">
                  {{ getApproverStatusText(approver.status) }}
                </span>
              </div>
              <div v-if="approver.comment" class="approval-comment">
                <a-tooltip :title="approver.comment">
                  <MessageOutlined style="color: #1890ff" />
                </a-tooltip>
              </div>
              <div v-if="approver.approvedAt" class="approval-time">
                {{ formatTime(approver.approvedAt) }}
              </div>
            </div>
          </div>

          <div class="stage-actions">
            <a-space
              v-if="
                productionStageStatus === 'pending' &&
                canaryStageStatus === 'approved'
              "
            >
              <a-button
                type="primary"
                size="small"
                @click="approveStage('production')"
              >
                批准生产发布
              </a-button>
              <a-button danger size="small" @click="rejectStage('production')">
                拒绝
              </a-button>
            </a-space>
            <div
              v-else-if="productionStageStatus === 'approved'"
              class="stage-result success"
            >
              <CheckCircleOutlined />
              生产审批通过
            </div>
            <div
              v-else-if="productionStageStatus === 'rejected'"
              class="stage-result error"
            >
              <CloseCircleOutlined />
              生产审批被拒绝
            </div>
            <div v-else class="stage-result disabled">
              <ClockCircleOutlined />
              等待灰度审批
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批统计 -->
    <div class="approval-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="总审批人数"
            :value="totalApprovers"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="已审批"
            :value="approvedCount"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="待审批"
            :value="pendingCount"
            :value-style="{ color: '#faad14' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="审批进度"
            :value="approvalProgress"
            suffix="%"
            :value-style="{
              color: approvalProgress === 100 ? '#52c41a' : '#1890ff',
            }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 审批历史 -->
    <div class="approval-history">
      <h4>审批历史</h4>
      <a-timeline>
        <a-timeline-item
          v-for="record in approvalHistory"
          :key="record.id"
          :color="getTimelineColor(record.action)"
        >
          <template #dot>
            <CheckCircleOutlined
              v-if="record.action === 'approved'"
              style="color: #52c41a"
            />
            <CloseCircleOutlined
              v-else-if="record.action === 'rejected'"
              style="color: #ff4d4f"
            />
            <UserOutlined v-else style="color: #1890ff" />
          </template>
          <div class="timeline-content">
            <div class="timeline-header">
              <span class="timeline-user">{{ record.user }}</span>
              <span class="timeline-action">
                {{ getActionText(record.action) }}
              </span>
              <span class="timeline-stage">{{ record.stage }}</span>
            </div>
            <div class="timeline-time">{{ formatTime(record.timestamp) }}</div>
            <div v-if="record.comment" class="timeline-comment">
              {{ record.comment }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>

    <!-- 审批提醒 -->
    <div v-if="pendingApprovals.length > 0" class="approval-reminders">
      <h4>待处理审批</h4>
      <div class="reminders-list">
        <a-alert
          v-for="reminder in pendingApprovals"
          :key="reminder.id"
          :message="reminder.message"
          :description="reminder.description"
          type="warning"
          show-icon
          closable
          style="margin-bottom: 8px"
          @close="dismissReminder(reminder.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';

interface Approver {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  status: 'pending' | 'approved' | 'rejected';
  comment?: string;
  approvedAt?: string;
}

interface ApprovalRecord {
  id: string;
  user: string;
  stage: string;
  action: 'approved' | 'rejected' | 'submitted';
  timestamp: string;
  comment?: string;
}

interface PendingApproval {
  id: string;
  message: string;
  description: string;
}

// 审批人数据
const canaryApprovers = ref<Approver[]>([
  {
    id: '1',
    name: '张三',
    role: '开发负责人',
    status: 'approved',
    comment: '代码审查通过，同意灰度发布',
    approvedAt: '2025-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: '李四',
    role: '测试负责人',
    status: 'approved',
    comment: '测试用例覆盖完整',
    approvedAt: '2025-01-15T11:15:00Z',
  },
  {
    id: '3',
    name: '王五',
    role: '运维工程师',
    status: 'pending',
  },
]);

const productionApprovers = ref<Approver[]>([
  {
    id: '4',
    name: '赵六',
    role: '技术总监',
    status: 'pending',
  },
  {
    id: '5',
    name: '孙七',
    role: '产品负责人',
    status: 'pending',
  },
]);

// 审批历史
const approvalHistory = ref<ApprovalRecord[]>([
  {
    id: '1',
    user: '张三',
    stage: '灰度发布审批',
    action: 'approved',
    timestamp: '2025-01-15T10:30:00Z',
    comment: '代码审查通过，同意灰度发布',
  },
  {
    id: '2',
    user: '李四',
    stage: '灰度发布审批',
    action: 'approved',
    timestamp: '2025-01-15T11:15:00Z',
    comment: '测试用例覆盖完整',
  },
]);

// 待处理提醒
const pendingApprovals = ref<PendingApproval[]>([
  {
    id: '1',
    message: '王五的审批即将超时',
    description: '灰度发布审批已等待2小时，请及时处理',
  },
]);

// 计算属性
const canaryStageStatus = computed(() => {
  const approved = canaryApprovers.value.filter(
    (a) => a.status === 'approved',
  ).length;
  const rejected = canaryApprovers.value.filter(
    (a) => a.status === 'rejected',
  ).length;

  if (rejected > 0) {
    return 'rejected';
  }
  if (approved === canaryApprovers.value.length) {
    return 'approved';
  }
  return 'pending';
});

const productionStageStatus = computed(() => {
  if (canaryStageStatus.value !== 'approved') {
    return 'disabled';
  }

  const approved = productionApprovers.value.filter(
    (a) => a.status === 'approved',
  ).length;
  const rejected = productionApprovers.value.filter(
    (a) => a.status === 'rejected',
  ).length;

  if (rejected > 0) {
    return 'rejected';
  }
  if (approved === productionApprovers.value.length) {
    return 'approved';
  }
  return 'pending';
});

const totalApprovers = computed(
  () => canaryApprovers.value.length + productionApprovers.value.length,
);

const approvedCount = computed(() => {
  return [...canaryApprovers.value, ...productionApprovers.value].filter(
    (a) => a.status === 'approved',
  ).length;
});

const pendingCount = computed(() => {
  return [...canaryApprovers.value, ...productionApprovers.value].filter(
    (a) => a.status === 'pending',
  ).length;
});

const approvalProgress = computed(() => {
  return Math.round((approvedCount.value / totalApprovers.value) * 100);
});

// 方法
const getOverallStatusColor = () => {
  if (productionStageStatus.value === 'approved') {
    return 'green';
  }
  if (
    canaryStageStatus.value === 'rejected' ||
    productionStageStatus.value === 'rejected'
  ) {
    return 'red';
  }
  if (canaryStageStatus.value === 'approved') {
    return 'blue';
  }
  return 'orange';
};

const getOverallStatusText = () => {
  if (productionStageStatus.value === 'approved') {
    return '全部通过';
  }
  if (
    canaryStageStatus.value === 'rejected' ||
    productionStageStatus.value === 'rejected'
  ) {
    return '已拒绝';
  }
  if (canaryStageStatus.value === 'approved') {
    return '等待生产审批';
  }
  return '审批中';
};

const getStageStatusColor = (stage: string) => {
  const status =
    stage === 'canary' ? canaryStageStatus.value : productionStageStatus.value;
  switch (status) {
    case 'approved':
      return 'green';
    case 'rejected':
      return 'red';
    case 'disabled':
      return 'default';
    default:
      return 'orange';
  }
};

const getStageStatusText = (stage: string) => {
  const status =
    stage === 'canary' ? canaryStageStatus.value : productionStageStatus.value;
  switch (status) {
    case 'approved':
      return '已通过';
    case 'rejected':
      return '已拒绝';
    case 'disabled':
      return '未开始';
    default:
      return '审批中';
  }
};

const getApproverStatusText = (status: string) => {
  switch (status) {
    case 'approved':
      return '已批准';
    case 'rejected':
      return '已拒绝';
    default:
      return '待审批';
  }
};

const getTimelineColor = (action: string) => {
  switch (action) {
    case 'approved':
      return 'green';
    case 'rejected':
      return 'red';
    default:
      return 'blue';
  }
};

const getActionText = (action: string) => {
  switch (action) {
    case 'approved':
      return '批准了';
    case 'rejected':
      return '拒绝了';
    default:
      return '提交了';
  }
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const refreshStatus = () => {
  message.info('状态已刷新');
};

const approveStage = (stage: string) => {
  message.success(`${stage === 'canary' ? '灰度' : '生产'}审批已批准`);
};

const rejectStage = (stage: string) => {
  message.error(`${stage === 'canary' ? '灰度' : '生产'}审批已拒绝`);
};

const dismissReminder = (id: string) => {
  const index = pendingApprovals.value.findIndex((r) => r.id === id);
  if (index > -1) {
    pendingApprovals.value.splice(index, 1);
  }
};
</script>

<style scoped>
.approval-status {
  padding: 16px;
  background: white;
  border-radius: 8px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.status-header h3 {
  margin: 0;
  color: #333;
}

.overall-status {
  font-size: 14px;
  font-weight: bold;
}

.approval-flow {
  margin-bottom: 32px;
}

.flow-container {
  display: flex;
  align-items: flex-start;
  gap: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
}

.approval-stage {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 2px solid #e8e8e8;
}

.approval-stage.disabled {
  opacity: 0.6;
  background: #f5f5f5;
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stage-header h4 {
  margin: 0;
  color: #333;
}

.approvers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.approver-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
}

.approver-card.approved {
  border-color: #52c41a;
  background: #f6ffed;
}

.approver-card.rejected {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.approver-card.disabled {
  opacity: 0.5;
}

.approver-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.approver-details {
  display: flex;
  flex-direction: column;
}

.approver-name {
  font-weight: 500;
  color: #333;
}

.approver-role {
  font-size: 12px;
  color: #666;
}

.approval-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.approval-comment {
  margin-left: 8px;
}

.approval-time {
  font-size: 10px;
  color: #999;
  margin-left: 8px;
}

.stage-actions {
  text-align: center;
}

.stage-result {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  font-weight: 500;
}

.stage-result.success {
  color: #52c41a;
  background: #f6ffed;
}

.stage-result.error {
  color: #ff4d4f;
  background: #fff2f0;
}

.stage-result.disabled {
  color: #999;
  background: #f5f5f5;
}

.flow-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 60px;
}

.arrow-line {
  width: 2px;
  height: 40px;
  background: #d9d9d9;
}

.flow-arrow.active .arrow-line {
  background: #52c41a;
}

.arrow-head {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 12px solid #d9d9d9;
}

.flow-arrow.active .arrow-head {
  border-top-color: #52c41a;
}

.arrow-label {
  font-size: 12px;
  color: #666;
  transform: rotate(90deg);
}

.approval-stats {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.approval-history h4,
.approval-reminders h4 {
  color: #333;
  margin-bottom: 16px;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.timeline-user {
  font-weight: 500;
  color: #333;
}

.timeline-action {
  color: #666;
}

.timeline-stage {
  color: #1890ff;
}

.timeline-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-comment {
  font-size: 12px;
  color: #666;
  font-style: italic;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.reminders-list {
  max-height: 200px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .flow-container {
    flex-direction: column;
    gap: 16px;
  }

  .flow-arrow {
    transform: rotate(90deg);
    margin: 0;
  }
}
</style>
