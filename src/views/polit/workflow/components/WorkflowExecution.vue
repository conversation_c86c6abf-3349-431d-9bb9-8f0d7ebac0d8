<template>
  <div class="workflow-execution-container">
    <a-card :body-style="{ paddingBottom: '0' }">
      <!-- 页面头部 -->
      <div class="execution-header">
        <div class="header-info">
          <h2>工作流执行管理</h2>
          <p>管理和监控所有工作流执行实例</p>
        </div>
        <div class="header-actions">
          <a-space>
            <a-button type="primary" @click="showCreateModal">
              <PlayCircleOutlined />
              新建执行
            </a-button>
            <a-button @click="handleRefresh">
              <ReloadOutlined />
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <a-divider />

      <!-- 搜索和筛选栏 -->
      <div class="search-section">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="执行状态" name="status">
            <a-select
              v-model:value="searchForm.status"
              placeholder="选择执行状态"
              style="width: 150px"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="pending">等待中</a-select-option>
              <a-select-option value="running">执行中</a-select-option>
              <a-select-option value="paused">已暂停</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="failed">失败</a-select-option>
              <a-select-option value="cancelled">已取消</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="工作流名称" name="workflowName">
            <a-input
              v-model:value="searchForm.workflowName"
              placeholder="请输入工作流名称"
              style="width: 200px"
              allow-clear
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="执行人" name="executor">
            <a-input
              v-model:value="searchForm.executor"
              placeholder="请输入执行人"
              style="width: 150px"
              allow-clear
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="时间范围" name="timeRange">
            <a-range-picker
              v-model:value="searchForm.timeRange"
              :ranges="timeRangePresets"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 执行统计卡片 -->
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="总执行次数"
                :value="stats.total"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <PlayCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="执行中"
                :value="stats.running"
                :value-style="{ color: '#faad14' }"
              >
                <template #prefix>
                  <SyncOutlined spin />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="成功率"
                :value="stats.successRate"
                suffix="%"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="失败次数"
                :value="stats.failed"
                :value-style="{ color: '#ff4d4f' }"
              >
                <template #prefix>
                  <CloseCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 执行列表 -->
      <div class="execution-table">
        <a-table
          :columns="columns"
          :data-source="executionList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'workflowName'">
              <a @click="viewWorkflowDetail(record)">
                {{ record.workflowName }}
              </a>
            </template>

            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                <template #icon>
                  <SyncOutlined v-if="record.status === 'running'" spin />
                  <ClockCircleOutlined
                    v-else-if="record.status === 'pending'"
                  />
                  <PauseCircleOutlined v-else-if="record.status === 'paused'" />
                  <CheckCircleOutlined
                    v-else-if="record.status === 'completed'"
                  />
                  <CloseCircleOutlined v-else-if="record.status === 'failed'" />
                  <StopOutlined v-else-if="record.status === 'cancelled'" />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'triggerType'">
              <a-tag :color="getTriggerTypeColor(record.triggerType)">
                {{ getTriggerTypeText(record.triggerType) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'progress'">
              <a-progress
                :percent="record.progress"
                :size="['small', 14]"
                :status="getProgressStatus(record.status)"
                :show-info="false"
                style="margin-right: 8px; width: 80px"
              />
              <span style="font-size: 12px">
                {{ record.completedSteps }}/{{ record.totalSteps }}
              </span>
            </template>

            <template v-if="column.key === 'duration'">
              {{ formatDuration(record.startTime, record.endTime) }}
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button size="small" @click="viewExecutionDetail(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-button size="small" @click="viewLogs(record)">
                  <FileTextOutlined />
                  日志
                </a-button>
                <a-dropdown v-if="record.status === 'running'">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="pauseExecution(record)">
                        <PauseCircleOutlined />
                        暂停
                      </a-menu-item>
                      <a-menu-item danger @click="cancelExecution(record)">
                        <StopOutlined />
                        取消
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    操作
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
                <a-button
                  v-else-if="record.status === 'paused'"
                  size="small"
                  type="primary"
                  @click="resumeExecution(record)"
                >
                  <PlayCircleOutlined />
                  继续
                </a-button>
                <a-dropdown v-else>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="rerunExecution(record)">
                        <RedoOutlined />
                        重新执行
                      </a-menu-item>
                      <a-menu-item @click="cloneExecution(record)">
                        <CopyOutlined />
                        克隆执行
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="deleteExecution(record)">
                        <DeleteOutlined />
                        删除记录
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="selectedRowKeys.length > 0" class="batch-actions">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-button :disabled="!canBatchPause" @click="batchPause">
            批量暂停
          </a-button>
          <a-button :disabled="!canBatchCancel" @click="batchCancel">
            批量取消
          </a-button>
          <a-button danger @click="batchDelete">批量删除</a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 创建执行模态框 -->
    <a-modal
      v-model:visible="createModalVisible"
      title="新建执行"
      width="600px"
      @ok="handleCreateExecution"
      @cancel="createModalVisible = false"
    >
      <a-form ref="createFormRef" :model="createForm" :rules="createRules">
        <a-form-item label="选择工作流" name="workflowId" required>
          <a-select
            v-model:value="createForm.workflowId"
            placeholder="请选择要执行的工作流"
            show-search
            :filter-option="false"
            @search="searchWorkflows"
          >
            <a-select-option
              v-for="workflow in availableWorkflows"
              :key="workflow.id"
              :value="workflow.id"
            >
              {{ workflow.name }} ({{ workflow.type }})
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="执行描述" name="description">
          <a-textarea
            v-model:value="createForm.description"
            :rows="3"
            placeholder="请输入执行描述（可选）"
          />
        </a-form-item>
        <a-form-item label="执行参数" name="parameters">
          <a-textarea
            v-model:value="createForm.parameters"
            :rows="4"
            placeholder="请输入执行参数（JSON格式，可选）"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 执行详情模态框 -->
    <ExecutionDetailModal
      v-model:visible="detailModalVisible"
      :execution-id="currentExecutionId"
    />

    <!-- 执行日志模态框 -->
    <ExecutionLogsModal
      v-model:visible="logsModalVisible"
      :execution-id="currentExecutionId"
    />

    <!-- 工作流详情模态框 -->
    <WorkflowDetailModal
      v-model:visible="workflowDetailVisible"
      :workflow-id="currentWorkflowId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import moment, { type Moment } from 'moment';
import {
  PlayCircleOutlined,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EyeOutlined,
  FileTextOutlined,
  DownOutlined,
  RedoOutlined,
  CopyOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue';
import { workflowApi } from '@/api/pilot/workflow';
import ExecutionDetailModal from './ExecutionDetailModal.vue';
import ExecutionLogsModal from './ExecutionLogsModal.vue';
import WorkflowDetailModal from './WorkflowDetailModal.vue';

// 搜索表单
const searchForm = reactive({
  status: undefined as string | undefined,
  workflowName: '',
  executor: '',
  timeRange: undefined as [Moment, Moment] | undefined,
});

// 时间范围预设
const timeRangePresets = {
  '今天': [moment().startOf('day'), moment().endOf('day')],
  '近3天': [moment().subtract(2, 'days').startOf('day'), moment().endOf('day')],
  '近7天': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
  '近30天': [
    moment().subtract(29, 'days').startOf('day'),
    moment().endOf('day'),
  ],
};

// 表格状态
const loading = ref(false);
const executionList = ref<any[]>([]);
const selectedRowKeys = ref<string[]>([]);

// 统计数据
const stats = reactive({
  total: 0,
  running: 0,
  successRate: 0,
  failed: 0,
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 模态框状态
const createModalVisible = ref(false);
const detailModalVisible = ref(false);
const logsModalVisible = ref(false);
const workflowDetailVisible = ref(false);
const currentExecutionId = ref<number>(0);
const currentWorkflowId = ref<string>('');

// 创建执行表单
const createForm = reactive({
  workflowId: undefined as number | undefined,
  description: '',
  parameters: '',
});

const createFormRef = ref();
const createRules = {
  workflowId: [{ required: true, message: '请选择工作流' }],
};

const availableWorkflows = ref<any[]>([]);

// 表格列配置
const columns = [
  {
    title: '执行ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left' as const,
  },
  {
    title: '工作流名称',
    dataIndex: 'workflowName',
    key: 'workflowName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '执行状态',
    key: 'status',
    width: 120,
  },
  {
    title: '触发方式',
    key: 'triggerType',
    width: 100,
  },
  {
    title: '执行进度',
    key: 'progress',
    width: 120,
  },
  {
    title: '执行人',
    dataIndex: 'executor',
    key: 'executor',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 150,
  },
  {
    title: '执行时长',
    key: 'duration',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const,
  },
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys;
  },
}));

// 批量操作可用性
const canBatchPause = computed(() => {
  return selectedRowKeys.value.some((key) => {
    const record = executionList.value.find(
      (item) => item.id.toString() === key,
    );
    return record && record.status === 'running';
  });
});

const canBatchCancel = computed(() => {
  return selectedRowKeys.value.some((key) => {
    const record = executionList.value.find(
      (item) => item.id.toString() === key,
    );
    return record && ['running', 'pending', 'paused'].includes(record.status);
  });
});

// 获取执行列表
const fetchExecutionList = async () => {
  try {
    loading.value = true;

    // 构建请求参数
    const params: any = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      status: searchForm.status,
      workflowName: searchForm.workflowName,
      executor: searchForm.executor,
    };

    // 添加时间范围参数
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.startTime = searchForm.timeRange[0].format('YYYY-MM-DD HH:mm:ss');
      params.endTime = searchForm.timeRange[1].format('YYYY-MM-DD HH:mm:ss');
    }

    const response = await workflowApi.listExecutions(params);
    const data = response.data;

    executionList.value = data.list || [];
    pagination.total = data.total || 0;

    // 更新统计数据
    stats.total = data.total || 0;
    stats.running =
      data.list?.filter((item: any) => item.status === 'running').length || 0;
    stats.failed =
      data.list?.filter((item: any) => item.status === 'failed').length || 0;
    const completedCount =
      data.list?.filter((item: any) => item.status === 'completed').length || 0;
    stats.successRate =
      stats.total > 0 ? Math.round((completedCount / stats.total) * 100) : 0;
  } catch (error) {
    console.error('获取执行列表失败:', error);
    message.error('获取执行列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索工作流
const searchWorkflows = async (searchText: string) => {
  try {
    const params = {
      page: 1,
      pageSize: 50, // 限制搜索结果数量
      name: searchText,
    };

    const response = await workflowApi.list(params);
    const data = response.data;

    availableWorkflows.value = (data.list || []).map((workflow: any) => ({
      id: workflow.id,
      name: workflow.name,
      type: workflow.type,
    }));
  } catch (error) {
    console.error('搜索工作流失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchExecutionList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    workflowName: '',
    executor: '',
    timeRange: undefined,
  });
  pagination.current = 1;
  fetchExecutionList();
};

// 刷新
const handleRefresh = () => {
  fetchExecutionList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchExecutionList();
};

// 显示创建模态框
const showCreateModal = () => {
  createModalVisible.value = true;
  searchWorkflows(''); // 加载工作流列表
};

// 创建执行
const handleCreateExecution = async () => {
  try {
    await createFormRef.value.validate();

    // 调用API创建执行
    await workflowApi.execute({ workflowId: createForm.workflowId! });

    message.success('执行创建成功');
    createModalVisible.value = false;

    // 重置表单
    Object.assign(createForm, {
      workflowId: undefined,
      description: '',
      parameters: '',
    });

    await fetchExecutionList();
  } catch (error) {
    console.error('创建执行失败:', error);
    message.error('创建执行失败');
  }
};

// 查看执行详情
const viewExecutionDetail = (record: any) => {
  currentExecutionId.value = record.id;
  detailModalVisible.value = true;
};

// 查看日志
const viewLogs = (record: any) => {
  currentExecutionId.value = record.id;
  logsModalVisible.value = true;
};

// 查看工作流详情
const viewWorkflowDetail = (record: any) => {
  currentWorkflowId.value = record.workflowId.toString();
  workflowDetailVisible.value = true;
};

// 暂停执行
const pauseExecution = async (record: any) => {
  try {
    await workflowApi.pause(record.id);
    message.success('执行已暂停');
    await fetchExecutionList();
  } catch (error) {
    message.error('暂停执行失败');
  }
};

// 继续执行
const resumeExecution = async (record: any) => {
  try {
    await workflowApi.resume(record.id);
    message.success('执行已继续');
    await fetchExecutionList();
  } catch (error) {
    message.error('继续执行失败');
  }
};

// 取消执行
const cancelExecution = (record: any) => {
  Modal.confirm({
    title: '确认取消执行?',
    content: `即将取消执行记录 ID: ${record.id}`,
    async onOk() {
      try {
        await workflowApi.cancel(record.id);
        message.success('执行已取消');
        await fetchExecutionList();
      } catch (error) {
        message.error('取消执行失败');
      }
    },
  });
};

// 重新执行
const rerunExecution = (record: any) => {
  Modal.confirm({
    title: '确认重新执行?',
    content: `即将重新执行工作流: ${record.workflowName}`,
    async onOk() {
      try {
        await workflowApi.execute({ workflowId: record.workflowId });
        message.success('重新执行已启动');
        await fetchExecutionList();
      } catch (error) {
        message.error('重新执行失败');
      }
    },
  });
};

// 克隆执行
const cloneExecution = (record: any) => {
  createForm.workflowId = record.workflowId;
  createForm.description = `克隆自执行 #${record.id}`;
  createModalVisible.value = true;
};

// 删除执行记录
const deleteExecution = (record: any) => {
  Modal.confirm({
    title: '确认删除执行记录?',
    content: `即将删除执行记录 ID: ${record.id}，此操作不可撤销`,
    async onOk() {
      try {
        await workflowApi.deleteExecution(record.id);
        message.success('执行记录已删除');
        await fetchExecutionList();
      } catch (error) {
        console.error('删除执行记录失败:', error);
        message.error('删除执行记录失败');
      }
    },
  });
};

// 批量暂停
const batchPause = () => {
  Modal.confirm({
    title: '确认批量暂停?',
    content: `即将暂停 ${selectedRowKeys.value.length} 个执行`,
    async onOk() {
      try {
        message.success('批量暂停成功');
        selectedRowKeys.value = [];
        await fetchExecutionList();
      } catch (error) {
        message.error('批量暂停失败');
      }
    },
  });
};

// 批量取消
const batchCancel = () => {
  Modal.confirm({
    title: '确认批量取消?',
    content: `即将取消 ${selectedRowKeys.value.length} 个执行`,
    async onOk() {
      try {
        message.success('批量取消成功');
        selectedRowKeys.value = [];
        await fetchExecutionList();
      } catch (error) {
        message.error('批量取消失败');
      }
    },
  });
};

// 批量删除
const batchDelete = () => {
  Modal.confirm({
    title: '确认批量删除?',
    content: `即将删除 ${selectedRowKeys.value.length} 个执行记录，此操作不可撤销`,
    async onOk() {
      try {
        // 逐个删除执行记录
        const deletePromises = selectedRowKeys.value.map((key) =>
          workflowApi.deleteExecution(parseInt(key)),
        );
        await Promise.all(deletePromises);

        message.success('批量删除成功');
        selectedRowKeys.value = [];
        await fetchExecutionList();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'processing',
    paused: 'warning',
    completed: 'success',
    failed: 'error',
    cancelled: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '执行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
  };
  return textMap[status] || status;
};

// 获取触发类型颜色
const getTriggerTypeColor = (triggerType: string) => {
  const colorMap: Record<string, string> = {
    manual: 'blue',
    scheduled: 'green',
    webhook: 'purple',
    api: 'orange',
  };
  return colorMap[triggerType] || 'default';
};

// 获取触发类型文本
const getTriggerTypeText = (triggerType: string) => {
  const textMap: Record<string, string> = {
    manual: '手动执行',
    scheduled: '定时执行',
    webhook: 'Webhook',
    api: 'API调用',
  };
  return textMap[triggerType] || triggerType;
};

// 获取进度状态
const getProgressStatus = (status: string) => {
  if (status === 'failed') {
    return 'exception';
  }
  if (status === 'completed') {
    return 'success';
  }
  if (status === 'running') {
    return 'active';
  }
  return 'normal';
};

// 格式化执行时长
const formatDuration = (startTime: string, endTime?: string) => {
  if (!endTime) {
    return '执行中';
  }

  const start = moment(startTime);
  const end = moment(endTime);
  const duration = moment.duration(end.diff(start));

  const minutes = Math.floor(duration.asMinutes());
  const seconds = Math.floor(duration.asSeconds() % 60);

  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${seconds}秒`;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchExecutionList();
});
</script>

<style scoped>
.workflow-execution-container {
  padding: 20px;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0;
}

.header-info h2 {
  margin: 0;
  color: #333;
}

.header-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.search-section {
  margin-bottom: 16px;
}

.stats-section {
  margin-bottom: 24px;
}

.execution-table {
  background: #fff;
}

.batch-actions {
  margin-top: 16px;
  padding: 12px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

:deep(.ant-progress) {
  display: inline-block;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .execution-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    margin-top: 16px;
  }
}
</style>
