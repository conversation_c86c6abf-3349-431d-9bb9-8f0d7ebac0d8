<template>
  <div class="image-selector">
    <a-form-item :label="label" :required="required">
      <a-space direction="vertical" style="width: 100%">
        <!-- 镜像仓库选择 -->
        <div class="repository-section">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="仓库名称" :required="true">
                <a-auto-complete
                  v-model:value="selectedRepository"
                  :options="repositoryOptions"
                  placeholder="请选择或输入仓库名称"
                  :filter-option="filterRepositoryOption"
                  allow-clear
                  @select="onRepositorySelect"
                  @search="onRepositorySearch"
                >
                  <template #option="{ label: repoLabel, description }">
                    <div class="repository-option">
                      <div class="repo-name">{{ repoLabel }}</div>
                      <div class="repo-desc">{{ description }}</div>
                    </div>
                  </template>
                </a-auto-complete>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="标签" :required="true">
                <a-select
                  v-model:value="selectedTag"
                  placeholder="请选择镜像标签"
                  :loading="tagsLoading"
                  :disabled="!selectedRepository"
                  show-search
                  :filter-option="filterTagOption"
                  allow-clear
                  @dropdown-visible-change="onTagDropdownChange"
                >
                  <a-select-option
                    v-for="tag in availableTags"
                    :key="tag"
                    :value="tag"
                  >
                    {{ tag }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 完整镜像地址预览 -->
        <div v-if="fullImageName" class="image-preview">
          <a-alert
            :message="`完整镜像地址: ${fullImageName}`"
            type="info"
            show-icon
          />
        </div>
      </a-space>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  registryApi,
  commonRepositories,
  type GetRepositoryTagsParams,
} from '@/api/pilot/registry';

interface Props {
  label?: string;
  required?: boolean;
  modelValue?: {
    repository: string;
    tag: string;
  };
  namespace?: string;
  registryType?: 'aliyun' | 'nexus' | 'harbor';
}

interface Emits {
  (
    e: 'update:modelValue',
    value: { repository: string; tag: string } | undefined,
  ): void;
  (e: 'change', value: { repository: string; tag: string } | undefined): void;
}

const props = withDefaults(defineProps<Props>(), {
  label: '镜像选择',
  required: false,
  modelValue: undefined,
  namespace: 'library',
  registryType: 'aliyun',
});

const emit = defineEmits<Emits>();

// 状态管理
const selectedRepository = ref<string>('');
const selectedTag = ref<string>('');
const availableTags = ref<string[]>([]);
const tagsLoading = ref(false);

// 仓库搜索相关
const repositorySearchText = ref('');
const repositoryOptions = computed(() => {
  const baseOptions = commonRepositories.map((repo) => ({
    value: repo.fullName,
    label: repo.name,
    description: repo.description,
  }));

  // 如果有搜索文本且不在常用列表中，添加自定义选项
  if (
    repositorySearchText.value &&
    !baseOptions.some((opt) => opt.value.includes(repositorySearchText.value))
  ) {
    baseOptions.unshift({
      value: repositorySearchText.value,
      label: repositorySearchText.value,
      description: '自定义仓库',
    });
  }

  return baseOptions;
});

// 计算完整镜像名称
const fullImageName = computed(() => {
  if (selectedRepository.value && selectedTag.value) {
    return `${selectedRepository.value}:${selectedTag.value}`;
  }
  return '';
});

// 监听属性变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      selectedRepository.value = newValue.repository;
      selectedTag.value = newValue.tag;
    } else {
      selectedRepository.value = '';
      selectedTag.value = '';
    }
  },
  { immediate: true },
);

// 监听选择变化，向上传递
watch([selectedRepository, selectedTag], () => {
  const value =
    selectedRepository.value && selectedTag.value
      ? { repository: selectedRepository.value, tag: selectedTag.value }
      : undefined;

  emit('update:modelValue', value);
  emit('change', value);
});

// 仓库选择处理
const onRepositorySelect = async (value: string) => {
  selectedRepository.value = value;
  selectedTag.value = ''; // 清空标签选择
  availableTags.value = []; // 清空标签列表

  // 自动加载标签列表
  await loadRepositoryTags(value);
};

const onRepositorySearch = (searchText: string) => {
  repositorySearchText.value = searchText;
};

const filterRepositoryOption = (inputValue: string, option: any) => {
  return (
    option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
    option.value.toLowerCase().includes(inputValue.toLowerCase())
  );
};

// 标签相关处理
const onTagDropdownChange = async (open: boolean) => {
  if (open && selectedRepository.value && availableTags.value.length === 0) {
    await loadRepositoryTags(selectedRepository.value);
  }
};

const filterTagOption = (inputValue: string, option: any) => {
  return option.value.toLowerCase().includes(inputValue.toLowerCase());
};

// 加载仓库标签
const loadRepositoryTags = async (repository: string) => {
  if (!repository) {
    return;
  }

  try {
    tagsLoading.value = true;

    // 解析仓库名称
    const parts = repository.split('/');
    let namespaceName = props.namespace;
    let repositoryName = repository;

    if (parts.length === 2) {
      namespaceName = parts[0];
      repositoryName = parts[1];
    }

    const params: GetRepositoryTagsParams = {
      page: 1,
      pageSize: 100,
      registryType: props.registryType,
      namespaceName,
      repositoryName,
    };

    const response = await registryApi.getTags(params);

    if (response.data && response.data.list) {
      availableTags.value = response.data.list;
    } else {
      availableTags.value = [];
    }
  } catch (error) {
    console.error('获取镜像标签失败:', error);
    message.error('获取镜像标签失败，请检查仓库配置');
    availableTags.value = [];
  } finally {
    tagsLoading.value = false;
  }
};
</script>

<style scoped>
.image-selector {
  width: 100%;
}

.repository-section {
  width: 100%;
}

.repository-option {
  padding: 4px 0;
}

.repo-name {
  font-weight: 500;
  color: #1890ff;
}

.repo-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.image-preview {
  margin-top: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}
</style>
