<template>
  <a-modal
    v-model:visible="modalVisible"
    title="工作流指标"
    width="1000px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else class="metrics-content">
      <!-- 概览指标 -->
      <a-row :gutter="16" style="margin-bottom: 24px">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总执行次数"
              :value="metrics.totalExecutions || 0"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="成功率"
              :value="metrics.successRate || 0"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="平均执行时间"
              :value="metrics.avgExecutionTime || 0"
              suffix="分钟"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="失败次数"
              :value="metrics.failureCount || 0"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 执行历史 -->
      <a-card title="执行历史" size="small" style="margin-bottom: 16px">
        <a-table
          :columns="executionColumns"
          :data-source="metrics.recentExecutions || []"
          :pagination="{ pageSize: 10 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getExecutionStatusColor(record.status)">
                {{ getExecutionStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'duration'">
              {{ formatDuration(record.startTime, record.endTime) }}
            </template>
            <template v-if="column.key === 'actions'">
              <a-button size="small" @click="viewExecutionDetail(record)">
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 性能趋势图 -->
      <a-card title="执行趋势" size="small" style="margin-bottom: 16px">
        <div id="execution-trend-chart" style="height: 300px"></div>
      </a-card>

      <!-- 错误统计 -->
      <a-card title="错误统计" size="small">
        <div v-if="metrics.errorStats && metrics.errorStats.length > 0">
          <a-table
            :columns="errorColumns"
            :data-source="metrics.errorStats"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'errorType'">
                <a-tag color="red">{{ record.errorType }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
        <a-empty v-else description="暂无错误记录" />
      </a-card>
    </div>

    <!-- 执行详情模态框 -->
    <a-modal
      v-model:visible="executionDetailVisible"
      title="执行详情"
      width="800px"
      :footer="null"
      :destroy-on-close="true"
    >
      <div v-if="currentExecution" class="execution-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="执行ID">
            {{ currentExecution.id }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getExecutionStatusColor(currentExecution.status)">
              {{ getExecutionStatusText(currentExecution.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ currentExecution.startTime }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ currentExecution.endTime || '进行中' }}
          </a-descriptions-item>
          <a-descriptions-item label="执行者">
            {{ currentExecution.executorName }}
          </a-descriptions-item>
          <a-descriptions-item label="触发方式">
            {{ getTriggerTypeText(currentExecution.triggerType) }}
          </a-descriptions-item>
          <a-descriptions-item label="执行时长" :span="2">
            {{
              formatDuration(
                currentExecution.startTime,
                currentExecution.endTime,
              )
            }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 步骤执行详情 -->
        <div v-if="currentExecution.stepExecutions" style="margin-top: 16px">
          <h4>步骤执行详情</h4>
          <a-timeline>
            <a-timeline-item
              v-for="step in currentExecution.stepExecutions"
              :key="step.id"
              :color="getStepStatusColor(step.status)"
            >
              <template #dot>
                <CheckCircleOutlined
                  v-if="step.status === 'completed'"
                  style="color: #52c41a"
                />
                <CloseCircleOutlined
                  v-else-if="step.status === 'failed'"
                  style="color: #ff4d4f"
                />
                <SyncOutlined
                  v-else-if="step.status === 'running'"
                  spin
                  style="color: #1890ff"
                />
                <ClockCircleOutlined v-else style="color: #d9d9d9" />
              </template>
              <div>
                <div style="font-weight: 500">{{ step.stepName }}</div>
                <div style="color: #666; font-size: 12px">
                  {{ step.startTime }} - {{ step.endTime || '进行中' }}
                </div>
                <div v-if="step.error" style="color: #ff4d4f; font-size: 12px">
                  错误: {{ step.error }}
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons-vue';
import { workflowApi } from '@/api/pilot/workflow';

interface Props {
  visible: boolean;
  workflowId: string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const loading = ref(false);
const metrics = ref<any>({});
const executionDetailVisible = ref(false);
const currentExecution = ref<any>(null);

// 执行历史表格列
const executionColumns = [
  {
    title: '执行ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 150,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    width: 150,
  },
  {
    title: '执行时长',
    key: 'duration',
    width: 100,
  },
  {
    title: '执行者',
    dataIndex: 'executorName',
    key: 'executorName',
    width: 100,
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
  },
];

// 错误统计表格列
const errorColumns = [
  {
    title: '错误类型',
    key: 'errorType',
    width: 120,
  },
  {
    title: '错误信息',
    dataIndex: 'errorMessage',
    key: 'errorMessage',
  },
  {
    title: '发生次数',
    dataIndex: 'count',
    key: 'count',
    width: 100,
  },
  {
    title: '最后发生时间',
    dataIndex: 'lastOccurrence',
    key: 'lastOccurrence',
    width: 150,
  },
];

// 获取工作流指标
const fetchWorkflowMetrics = async () => {
  if (!props.workflowId) {
    return;
  }

  try {
    loading.value = true;
    const response = await workflowApi.getMetrics(props.workflowId);
    metrics.value = response.data || {};

    // 渲染图表
    await nextTick();
    renderChart();
  } catch (error) {
    console.error('获取工作流指标失败:', error);
    message.error('获取工作流指标失败');
  } finally {
    loading.value = false;
  }
};

// 渲染执行趋势图表
const renderChart = () => {
  // 这里可以使用 ECharts 或其他图表库渲染趋势图
  // 示例代码，实际使用时需要引入相应的图表库
  const chartContainer = document.getElementById('execution-trend-chart');
  if (chartContainer) {
    chartContainer.innerHTML =
      '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">图表渲染区域</div>';
  }
};

// 查看执行详情
const viewExecutionDetail = (execution: any) => {
  currentExecution.value = execution;
  executionDetailVisible.value = true;
};

// 格式化执行时长
const formatDuration = (startTime: string, endTime?: string) => {
  if (!startTime) {
    return '-';
  }
  if (!endTime) {
    return '进行中';
  }

  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  const duration = Math.round((end - start) / 1000 / 60); // 转换为分钟

  if (duration < 1) {
    return '< 1分钟';
  }
  if (duration < 60) {
    return `${duration}分钟`;
  }

  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  return `${hours}小时${minutes}分钟`;
};

// 获取执行状态颜色
const getExecutionStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'processing',
    completed: 'success',
    failed: 'error',
    cancelled: 'warning',
  };
  return colorMap[status] || 'default';
};

// 获取执行状态文本
const getExecutionStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '执行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
  };
  return textMap[status] || status;
};

// 获取步骤状态颜色
const getStepStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'default',
    running: 'blue',
    completed: 'green',
    failed: 'red',
    skipped: 'orange',
  };
  return colorMap[status] || 'default';
};

// 获取触发类型文本
const getTriggerTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    manual: '手动触发',
    scheduled: '定时触发',
    webhook: 'Webhook触发',
    api: 'API触发',
  };
  return textMap[type] || type;
};

// 监听workflowId变化
watch(
  () => props.workflowId,
  (newId) => {
    if (newId && props.visible) {
      fetchWorkflowMetrics();
    }
  },
);

// 监听visible变化
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.workflowId) {
      fetchWorkflowMetrics();
    }
  },
);
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.metrics-content {
  max-height: 600px;
  overflow-y: auto;
}

.execution-detail {
  max-height: 500px;
  overflow-y: auto;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}

:deep(.ant-timeline-item-content) {
  margin-left: 8px;
}
</style>
