<template>
  <div class="workflow-wizard">
    <!-- 标题 -->
    <div class="wizard-header">
      <h2>灰度发布管理系统 - 横向流程</h2>
    </div>

    <!-- 步骤指示器 -->
    <div class="step-indicators">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="step-indicator"
        :class="{
          active: currentStep === index + 1,
          completed: currentStep > index + 1,
        }"
      >
        <div class="step-number">{{ index + 1 }}</div>
        <div class="step-title">{{ step.title }}</div>
      </div>
      <!-- 连接线 -->
      <div class="step-connectors">
        <div
          v-for="i in 6"
          :key="i"
          class="connector"
          :class="{ active: currentStep > i }"
        ></div>
      </div>
    </div>

    <!-- 步骤内容区域 -->
    <div class="wizard-content">
      <!-- 步骤1：创建灰度发布 -->
      <div v-if="currentStep === 1" class="step-content step1">
        <a-card title="步骤1：创建灰度发布" :bordered="false" class="step-card">
          <a-form layout="vertical" :model="formData.basic">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="环境选择">
                  <a-select
                    v-model:value="formData.basic.environment"
                    placeholder="选择环境"
                  >
                    <a-select-option value="test">测试环境</a-select-option>
                    <a-select-option value="staging">预发环境</a-select-option>
                    <a-select-option value="production">
                      生产环境
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="发布名称">
                  <a-input
                    v-model:value="formData.basic.name"
                    placeholder="gray-release-v1.0"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="标签">
                  <a-input
                    v-model:value="formData.basic.tag"
                    placeholder="feature-new-ui"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="描述">
                  <a-textarea
                    v-model:value="formData.basic.description"
                    placeholder="请输入发布描述"
                    :rows="3"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="命名空间">
                  <a-input
                    v-model:value="formData.basic.namespace"
                    placeholder="default"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="集群ID">
                  <a-input
                    v-model:value="formData.basic.clusterId"
                    placeholder="cluster-001"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 步骤2：选择服务 -->
      <div v-if="currentStep === 2" class="step-content step2">
        <a-card title="步骤2：选择服务" :bordered="false" class="step-card">
          <div class="service-selection">
            <div class="available-services">
              <h4>可选服务：</h4>
              <div class="service-buttons">
                <a-button
                  v-for="service in availableServices"
                  :key="service.name"
                  :type="service.selected ? 'primary' : 'default'"
                  class="service-btn"
                  @click="toggleService(service)"
                >
                  {{ service.name }}
                </a-button>
              </div>
            </div>

            <div class="service-chain">
              <h4>服务链路：</h4>
              <div class="chain-display">
                <div
                  v-for="(service, index) in selectedServices"
                  :key="service.name"
                  class="chain-service"
                >
                  <a-tag color="blue" class="service-tag">
                    {{ service.name }}
                  </a-tag>
                  <span
                    v-if="index < selectedServices.length - 1"
                    class="chain-arrow"
                  >
                    →
                  </span>
                </div>
              </div>
              <a-button
                type="dashed"
                :disabled="selectedServices.length === 0"
                class="generate-btn"
                @click="generateTopology"
              >
                生成拓扑图
              </a-button>
            </div>

            <!-- 服务拓扑图 -->
            <div v-if="showTopology" class="topology-diagram">
              <h4>服务拓扑图：</h4>
              <div class="topology-visual">
                <div class="client-layer">
                  <div class="client-group">
                    <div class="layer-title">客户端</div>
                    <div class="client-item">iOS</div>
                    <div class="client-item">Android</div>
                    <div class="client-item">H5</div>
                  </div>
                </div>
                <div class="gateway-layer">
                  <div class="gateway-group">
                    <div class="layer-title">网关层</div>
                    <div class="gateway-item">云原生网关</div>
                  </div>
                </div>
                <div class="services-layer">
                  <div
                    v-for="service in selectedServices"
                    :key="service.name"
                    class="service-group"
                  >
                    <div class="layer-title">{{ service.name }}</div>
                    <div class="service-instance gray">
                      {{ service.name }} (灰度)
                    </div>
                    <div class="service-instance base">
                      {{ service.name }} (基线)
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 步骤3：服务配置 -->
      <div v-if="currentStep === 3" class="step-content step3">
        <a-card title="步骤3：服务配置" :bordered="false" class="step-card">
          <a-tabs v-model:activeKey="activeServiceTab" type="card">
            <a-tab-pane
              v-for="service in selectedServices"
              :key="service.name"
              :tab="service.name"
            >
              <div class="service-config">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="镜像版本">
                      <a-select
                        v-model:value="service.imageVersion"
                        placeholder="选择镜像版本"
                      >
                        <a-select-option value="v1.2.3">v1.2.3</a-select-option>
                        <a-select-option value="v1.2.4">v1.2.4</a-select-option>
                        <a-select-option value="v1.3.0">v1.3.0</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="流量占比">
                      <a-slider
                        v-model:value="service.trafficRatio"
                        :min="10"
                        :max="100"
                        :marks="{ 10: '10%', 50: '50%', 100: '100%' }"
                      />
                      <div class="traffic-display">
                        当前：{{ service.trafficRatio }}%
                      </div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="副本数配置">
                      <a-input-group compact>
                        <a-input-number
                          v-model:value="service.canaryReplicas"
                          placeholder="灰度副本"
                          :min="1"
                          style="width: 50%"
                        />
                        <a-input-number
                          v-model:value="service.prodReplicas"
                          placeholder="生产副本"
                          :min="1"
                          style="width: 50%"
                        />
                      </a-input-group>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-divider />
                <h4>高级配置</h4>
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="健康检查">
                      <a-switch v-model:checked="service.healthCheck" />
                      <div v-if="service.healthCheck" style="margin-top: 8px">
                        <a-input
                          v-model:value="service.healthCheckPath"
                          placeholder="/health"
                          addon-before="路径"
                        />
                      </div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="链路传播">
                      <a-switch v-model:checked="service.chainPropagation" />
                      <div
                        v-if="service.chainPropagation"
                        style="margin-top: 8px"
                      >
                        <a-input
                          v-model:value="service.propagationHeader"
                          placeholder="x-canary-version"
                          addon-before="请求头"
                        />
                      </div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </div>

      <!-- 步骤4：审批流程 -->
      <div v-if="currentStep === 4" class="step-content step4">
        <a-card title="步骤4：审批流程" :bordered="false" class="step-card">
          <div class="approval-config">
            <a-row :gutter="16">
              <a-col :span="12">
                <h4>灰度发布审批</h4>
                <a-form layout="vertical">
                  <a-form-item label="审批人">
                    <a-select
                      v-model:value="formData.approval.canaryApprovers"
                      mode="multiple"
                      placeholder="选择审批人"
                    >
                      <a-select-option value="admin">管理员</a-select-option>
                      <a-select-option value="dev-lead">
                        开发负责人
                      </a-select-option>
                      <a-select-option value="ops">运维人员</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="审批方式">
                    <a-radio-group v-model:value="formData.approval.canaryType">
                      <a-radio value="and">全部同意</a-radio>
                      <a-radio value="or">任一同意</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="审批超时(小时)">
                    <a-input-number
                      v-model:value="formData.approval.canaryTimeout"
                      :min="1"
                      :max="72"
                    />
                  </a-form-item>
                </a-form>
              </a-col>
              <a-col :span="12">
                <h4>生产发布审批</h4>
                <a-form layout="vertical">
                  <a-form-item label="审批人">
                    <a-select
                      v-model:value="formData.approval.prodApprovers"
                      mode="multiple"
                      placeholder="选择审批人"
                    >
                      <a-select-option value="cto">技术总监</a-select-option>
                      <a-select-option value="prod-owner">
                        产品负责人
                      </a-select-option>
                      <a-select-option value="ops-lead">
                        运维负责人
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="审批方式">
                    <a-radio-group v-model:value="formData.approval.prodType">
                      <a-radio value="and">全部同意</a-radio>
                      <a-radio value="or">任一同意</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="审批超时(小时)">
                    <a-input-number
                      v-model:value="formData.approval.prodTimeout"
                      :min="1"
                      :max="72"
                    />
                  </a-form-item>
                </a-form>
              </a-col>
            </a-row>
          </div>

          <!-- 审批状态可视化 -->
          <a-divider />
          <ApprovalStatusVisualization />
        </a-card>
      </div>

      <!-- 步骤5：监控面板 -->
      <div v-if="currentStep === 5" class="step-content step5">
        <a-card title="步骤5：监控面板" :bordered="false" class="step-card">
          <div class="monitoring-config">
            <a-row :gutter="16">
              <a-col :span="12">
                <h4>监控指标</h4>
                <a-checkbox-group v-model:value="formData.monitoring.metrics">
                  <div class="metrics-grid">
                    <a-checkbox value="response_time">响应时间</a-checkbox>
                    <a-checkbox value="error_rate">错误率</a-checkbox>
                    <a-checkbox value="throughput">吞吐量</a-checkbox>
                    <a-checkbox value="cpu_usage">CPU使用率</a-checkbox>
                    <a-checkbox value="memory_usage">内存使用率</a-checkbox>
                    <a-checkbox value="success_rate">成功率</a-checkbox>
                  </div>
                </a-checkbox-group>
              </a-col>
              <a-col :span="12">
                <h4>告警配置</h4>
                <a-form layout="vertical">
                  <a-form-item label="监控时长(分钟)">
                    <a-input-number
                      v-model:value="formData.monitoring.duration"
                      :min="5"
                      :max="1440"
                      placeholder="30"
                    />
                  </a-form-item>
                  <a-form-item label="错误率阈值(%)">
                    <a-input-number
                      v-model:value="formData.monitoring.errorThreshold"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      placeholder="5.0"
                    />
                  </a-form-item>
                  <a-form-item label="响应时间阈值(ms)">
                    <a-input-number
                      v-model:value="formData.monitoring.responseThreshold"
                      :min="0"
                      placeholder="500"
                    />
                  </a-form-item>
                </a-form>
              </a-col>
            </a-row>

            <!-- 流量监控可视化 -->
            <a-divider />
            <TrafficVisualization />
          </div>
        </a-card>
      </div>

      <!-- 步骤6：生产环境审批 -->
      <div v-if="currentStep === 6" class="step-content step6">
        <a-card title="步骤6：生产环境审批" :bordered="false" class="step-card">
          <div class="production-approval">
            <a-alert
              message="灰度测试完成"
              description="监控指标正常，可以进行生产环境部署审批"
              type="success"
              show-icon
              style="margin-bottom: 16px"
            />

            <div class="approval-summary">
              <h4>审批摘要</h4>
              <a-descriptions bordered :column="2">
                <a-descriptions-item label="发布名称">
                  {{ formData.basic.name }}
                </a-descriptions-item>
                <a-descriptions-item label="环境">
                  {{ formData.basic.environment }}
                </a-descriptions-item>
                <a-descriptions-item label="服务数量">
                  {{ selectedServices.length }}
                </a-descriptions-item>
                <a-descriptions-item label="流量比例">
                  {{ averageTrafficRatio }}%
                </a-descriptions-item>
                <a-descriptions-item label="监控时长">
                  {{ formData.monitoring.duration }} 分钟
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-tag color="green">准备就绪</a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </div>

            <div class="approval-actions" style="margin-top: 24px">
              <a-space size="large">
                <a-button
                  type="primary"
                  size="large"
                  @click="deployToProduction"
                >
                  部署到生产环境
                </a-button>
                <a-button size="large" @click="rollbackGray">回滚灰度</a-button>
                <a-button size="large" @click="extendMonitoring">
                  延长监控
                </a-button>
              </a-space>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 步骤7：清理灰度服务 -->
      <div v-if="currentStep === 7" class="step-content step7">
        <a-card title="步骤7：清理灰度服务" :bordered="false" class="step-card">
          <div class="cleanup-phase">
            <a-result
              status="success"
              title="生产发布成功！"
              sub-title="所有服务已成功部署到生产环境，现在可以清理灰度服务"
            >
              <template #extra>
                <div class="cleanup-options">
                  <h4>清理选项</h4>
                  <a-checkbox-group v-model:value="formData.cleanup.options">
                    <div class="cleanup-grid">
                      <a-checkbox value="gray_instances">
                        删除灰度实例
                      </a-checkbox>
                      <a-checkbox value="gray_configs">删除灰度配置</a-checkbox>
                      <a-checkbox value="monitoring_rules">
                        删除监控规则
                      </a-checkbox>
                      <a-checkbox value="routing_rules">
                        删除路由规则
                      </a-checkbox>
                    </div>
                  </a-checkbox-group>

                  <div style="margin-top: 24px">
                    <a-space size="large">
                      <a-button
                        type="primary"
                        danger
                        size="large"
                        @click="cleanupGrayServices"
                      >
                        清理灰度服务
                      </a-button>
                      <a-button size="large" @click="keepGrayServices">
                        保留灰度服务
                      </a-button>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-result>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="wizard-footer">
      <a-space size="large">
        <a-button v-if="currentStep > 1" size="large" @click="prevStep">
          上一步
        </a-button>
        <a-button
          v-if="currentStep < 7"
          type="primary"
          size="large"
          :disabled="!canProceed"
          @click="nextStep"
        >
          下一步
        </a-button>
        <a-button
          v-if="currentStep === 7"
          type="primary"
          size="large"
          @click="finishWorkflow"
        >
          完成
        </a-button>
        <a-button size="large" @click="saveAsDraft">保存草稿</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import TrafficVisualization from './TrafficVisualization.vue';
import ApprovalStatusVisualization from './ApprovalStatusVisualization.vue';

// 步骤定义
const steps = [
  { title: '创建灰度发布', color: '#e1d5e7' },
  { title: '选择服务', color: '#dae8fc' },
  { title: '服务配置', color: '#f8cecc' },
  { title: '审批流程', color: '#fff2cc' },
  { title: '监控面板', color: '#d5e8d4' },
  { title: '生产环境审批', color: '#ffe6cc' },
  { title: '清理灰度服务', color: '#f5f5f5' },
];

// 当前步骤
const currentStep = ref(1);
const activeServiceTab = ref('');
const showTopology = ref(false);

// 可选服务
const availableServices = ref([
  {
    name: '服务A',
    selected: false,
    imageVersion: '',
    trafficRatio: 20,
    canaryReplicas: 1,
    prodReplicas: 2,
    healthCheck: false,
    healthCheckPath: '/health',
    chainPropagation: true,
    propagationHeader: 'x-canary-version',
  },
  {
    name: '服务B',
    selected: false,
    imageVersion: '',
    trafficRatio: 20,
    canaryReplicas: 1,
    prodReplicas: 2,
    healthCheck: false,
    healthCheckPath: '/health',
    chainPropagation: false,
    propagationHeader: '',
  },
  {
    name: '服务C',
    selected: false,
    imageVersion: '',
    trafficRatio: 20,
    canaryReplicas: 1,
    prodReplicas: 2,
    healthCheck: false,
    healthCheckPath: '/health',
    chainPropagation: false,
    propagationHeader: '',
  },
]);

// 选中的服务
const selectedServices = computed(() =>
  availableServices.value.filter((service) => service.selected),
);

// 表单数据
const formData = reactive({
  basic: {
    environment: 'test',
    name: '',
    tag: '',
    description: '',
    namespace: 'default',
    clusterId: '',
  },
  approval: {
    canaryApprovers: [],
    canaryType: 'or',
    canaryTimeout: 24,
    prodApprovers: [],
    prodType: 'and',
    prodTimeout: 48,
  },
  monitoring: {
    metrics: ['response_time', 'error_rate', 'success_rate'],
    duration: 30,
    errorThreshold: 5.0,
    responseThreshold: 500,
  },
  cleanup: {
    options: ['gray_instances', 'gray_configs'],
  },
});

// 平均流量比例
const averageTrafficRatio = computed(() => {
  if (selectedServices.value.length === 0) {
    return 0;
  }
  const total = selectedServices.value.reduce(
    (sum, service) => sum + service.trafficRatio,
    0,
  );
  return Math.round(total / selectedServices.value.length);
});

// 是否可以进行下一步
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return formData.basic.name && formData.basic.environment;
    case 2:
      return selectedServices.value.length > 0;
    case 3:
      return selectedServices.value.every((service) => service.imageVersion);
    case 4:
      return formData.approval.canaryApprovers.length > 0;
    case 5:
      return formData.monitoring.metrics.length > 0;
    case 6:
      return true;
    default:
      return true;
  }
});

// 切换服务选择
const toggleService = (service: any) => {
  service.selected = !service.selected;
  if (service.selected && !activeServiceTab.value) {
    activeServiceTab.value = service.name;
  }
};

// 生成拓扑图
const generateTopology = () => {
  showTopology.value = true;
  message.success('拓扑图生成成功');
};

// 下一步
const nextStep = () => {
  if (canProceed.value && currentStep.value < 7) {
    currentStep.value++;
    if (currentStep.value === 3 && selectedServices.value.length > 0) {
      activeServiceTab.value = selectedServices.value[0].name;
    }
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 部署到生产环境
const deployToProduction = () => {
  message.success('开始部署到生产环境');
  setTimeout(() => {
    currentStep.value = 7;
  }, 2000);
};

// 回滚灰度
const rollbackGray = () => {
  message.warning('灰度服务已回滚');
};

// 延长监控
const extendMonitoring = () => {
  message.info('监控时间已延长');
};

// 清理灰度服务
const cleanupGrayServices = () => {
  message.success('灰度服务清理完成');
};

// 保留灰度服务
const keepGrayServices = () => {
  message.info('灰度服务已保留');
};

// 完成工作流
const finishWorkflow = () => {
  message.success('工作流创建完成！');
  // 这里可以调用API保存工作流
  emit('success');
};

// 保存草稿
const saveAsDraft = () => {
  message.success('草稿已保存');
};

interface Emits {
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();
</script>

<style scoped>
.workflow-wizard {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.wizard-header {
  text-align: center;
  margin-bottom: 32px;
}

.wizard-header h2 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.step-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  background: #f5f5f5;
  border: 2px solid #d9d9d9;
  color: #666;
  margin-bottom: 8px;
}

.step-indicator.active .step-number {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.step-indicator.completed .step-number {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.step-title {
  font-size: 12px;
  color: #666;
  text-align: center;
  max-width: 80px;
}

.step-connectors {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.connector {
  height: 2px;
  background: #d9d9d9;
  flex: 1;
  margin: 0 20px;
}

.connector.active {
  background: #52c41a;
}

.wizard-content {
  max-width: 1200px;
  margin: 0 auto 32px;
}

.step-card {
  min-height: 500px;
}

.service-selection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.service-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.service-btn {
  min-width: 80px;
}

.chain-display {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.chain-arrow {
  font-size: 18px;
  color: #1890ff;
  margin: 0 4px;
}

.topology-diagram {
  margin-top: 24px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: white;
}

.topology-visual {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
  margin-top: 16px;
}

.client-layer,
.gateway-layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.services-layer {
  display: flex;
  gap: 24px;
  flex: 1;
  justify-content: center;
}

.client-group,
.gateway-group,
.service-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

.layer-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.client-item,
.gateway-item {
  padding: 8px 16px;
  background: #d5e8d4;
  border: 1px solid #82b366;
  border-radius: 4px;
  font-size: 12px;
  min-width: 60px;
  text-align: center;
}

.gateway-item {
  background: #dae8fc;
  border-color: #6c8ebf;
}

.service-instance {
  padding: 8px 16px;
  border: 1px solid #333;
  border-radius: 4px;
  font-size: 12px;
  min-width: 80px;
  text-align: center;
  margin-bottom: 4px;
}

.service-instance.gray {
  background: #999999;
  color: white;
}

.service-instance.base {
  background: #dddddd;
  color: #333;
}

.service-config {
  padding: 16px;
}

.traffic-display {
  text-align: center;
  margin-top: 8px;
  color: #1890ff;
  font-weight: bold;
}

.metrics-grid,
.cleanup-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.monitoring-preview {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.approval-summary {
  margin: 24px 0;
}

.cleanup-options {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.wizard-footer {
  text-align: center;
  margin-bottom: 24px;
}

.status-legend {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-top: 24px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.status-legend h4 {
  margin-bottom: 12px;
  color: #333;
}

@media (max-width: 768px) {
  .step-indicators {
    flex-direction: column;
    gap: 16px;
  }

  .step-connectors {
    display: none;
  }

  .topology-visual {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .services-layer {
    flex-direction: column;
    align-items: center;
  }
}
</style>
