<template>
  <div class="service-selector">
    <a-row :gutter="24">
      <!-- 左侧：可用服务列表 -->
      <a-col :span="8">
        <a-card title="可用服务" size="small" class="services-card">
          <template #extra>
            <a-space>
              <a-button
                size="small"
                :loading="discoveryLoading"
                @click="handleRefresh"
              >
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
              <a-button size="small" @click="showDiscoveryModal">
                <template #icon>
                  <search-outlined />
                </template>
                发现服务
              </a-button>
            </a-space>
          </template>

          <!-- 搜索和过滤 -->
          <div class="search-section">
            <a-input
              v-model:value="searchText"
              placeholder="搜索服务名称"
              allow-clear
              @change="handleSearch"
            >
              <template #prefix>
                <search-outlined />
              </template>
            </a-input>

            <a-select
              v-model:value="filterNamespace"
              placeholder="选择命名空间"
              style="width: 100%; margin-top: 8px"
              allow-clear
              @change="handleFilter"
            >
              <a-select-option v-for="ns in namespaces" :key="ns" :value="ns">
                {{ ns }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 服务列表 -->
          <div class="services-list">
            <div
              v-for="service in filteredServices"
              :key="service.name"
              class="service-item"
              draggable="true"
              @dragstart="handleDragStart($event, service)"
            >
              <div class="service-info">
                <div class="service-name">{{ service.name }}</div>
                <div class="service-meta">
                  <a-tag size="small" color="blue">
                    {{ service.namespace }}
                  </a-tag>
                  <a-tag size="small" color="green">
                    {{ service.version }}
                  </a-tag>
                </div>
                <div class="service-ports">
                  <span
                    v-for="port in service.ports"
                    :key="port"
                    class="port-tag"
                  >
                    {{ port }}
                  </span>
                </div>
              </div>
              <div class="service-actions">
                <a-button
                  type="primary"
                  size="small"
                  @click="addServiceToChain(service)"
                >
                  添加
                </a-button>
              </div>
            </div>

            <a-empty
              v-if="filteredServices.length === 0"
              description="暂无服务"
            />
          </div>
        </a-card>
      </a-col>

      <!-- 中间：链路构建区域 -->
      <a-col :span="10">
        <a-card title="服务链路" size="small" class="chain-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="handleClearChain">
                清空链路
              </a-button>
              <a-button size="small" @click="handleAutoArrange">
                自动排列
              </a-button>
              <a-button size="small" type="primary" @click="handlePreview">
                预览配置
              </a-button>
            </a-space>
          </template>

          <!-- 链路展示区域 -->
          <div
            class="chain-container"
            @dragover="handleDragOver"
            @drop="handleDrop"
          >
            <div
              v-for="(chainService, index) in serviceChain"
              :key="chainService.id"
              class="chain-service"
              :class="{ active: selectedService?.id === chainService.id }"
              @click="selectService(chainService)"
            >
              <div class="chain-service-content">
                <div class="service-header">
                  <span class="service-order">{{ index + 1 }}</span>
                  <span class="service-name">{{ chainService.name }}</span>
                  <a-button
                    type="text"
                    size="small"
                    danger
                    @click="removeFromChain(chainService.id)"
                  >
                    <template #icon>
                      <close-outlined />
                    </template>
                  </a-button>
                </div>

                <div class="service-details">
                  <div class="detail-item">
                    <span class="label">命名空间:</span>
                    <span class="value">{{ chainService.namespace }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">版本:</span>
                    <span class="value">{{ chainService.version }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">端口:</span>
                    <span class="value">
                      {{ chainService.ports.join(', ') }}
                    </span>
                  </div>
                </div>

                <!-- 流量配置 -->
                <div class="traffic-config">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <div class="config-item">
                        <span class="config-label">灰度比例:</span>
                        <a-input-number
                          v-model:value="chainService.trafficRatio"
                          :min="0"
                          :max="100"
                          size="small"
                          style="width: 100%"
                          @change="updateTrafficRatio(chainService.id, $event)"
                        />
                      </div>
                    </a-col>
                    <a-col :span="12">
                      <div class="config-item">
                        <span class="config-label">副本数:</span>
                        <a-input-number
                          v-model:value="chainService.replicas"
                          :min="1"
                          :max="10"
                          size="small"
                          style="width: 100%"
                          @change="updateReplicas(chainService.id, $event)"
                        />
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>

              <!-- 连接线 -->
              <div
                v-if="index < serviceChain.length - 1"
                class="connection-line"
              >
                <a-icon type="arrow-down" />
              </div>
            </div>

            <!-- 拖拽提示 -->
            <div v-if="serviceChain.length === 0" class="drop-hint">
              <a-empty description="拖拽服务到此处构建链路" />
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：依赖关系和配置 -->
      <a-col :span="6">
        <a-card title="依赖关系" size="small" class="dependencies-card">
          <div class="dependencies-view">
            <a-tree
              v-if="dependencyTree.length > 0"
              :tree-data="dependencyTree"
              :default-expand-all="true"
              :show-line="true"
            >
              <template #title="{ title, type }">
                <span :class="{ 'tree-service': type === 'service' }">
                  {{ title }}
                </span>
              </template>
            </a-tree>
            <a-empty v-else description="暂无依赖关系" />
          </div>
        </a-card>

        <a-card title="链路配置" size="small" class="config-card">
          <div v-if="selectedService" class="service-config">
            <h4>{{ selectedService.name }} 配置</h4>

            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <!-- 镜像配置方式选择 -->
              <a-form-item label="配置方式">
                <a-radio-group
                  v-model:value="imageConfigMode"
                  button-style="solid"
                  size="small"
                  @change="onImageConfigModeChange"
                >
                  <a-radio-button value="manual">手动配置</a-radio-button>
                  <a-radio-button value="deployment">从部署选择</a-radio-button>
                </a-radio-group>
              </a-form-item>

              <!-- 手动镜像配置 -->
              <a-form-item v-if="imageConfigMode === 'manual'" label="灰度镜像">
                <ImageSelector
                  :model-value="
                    parseCanaryImageValue(selectedService.canaryImage)
                  "
                  :namespace="selectedService.namespace"
                  label=""
                  @update:model-value="updateCanaryImage"
                />
              </a-form-item>

              <!-- 从部署选择镜像 -->
              <div
                v-else-if="imageConfigMode === 'deployment'"
                class="deployment-image-config"
              >
                <DeploymentImageSelector
                  v-model="selectedService.deploymentImages"
                  :namespace="selectedService.namespace"
                  @change="onDeploymentImagesChange"
                  @deployment-selected="onDeploymentSelected"
                />
              </div>

              <a-form-item label="健康检查">
                <a-input
                  v-model:value="selectedService.healthCheck"
                  placeholder="/health"
                  @change="updateServiceConfig"
                />
              </a-form-item>

              <a-form-item label="超时时间">
                <a-input-number
                  v-model:value="selectedService.timeout"
                  :min="1"
                  :max="300"
                  style="width: 100%"
                  @change="updateServiceConfig"
                />
              </a-form-item>

              <a-form-item label="重试次数">
                <a-input-number
                  v-model:value="selectedService.retries"
                  :min="0"
                  :max="5"
                  style="width: 100%"
                  @change="updateServiceConfig"
                />
              </a-form-item>
            </a-form>
          </div>

          <a-empty v-else description="选择服务进行配置" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 服务发现模态框 -->
    <a-modal
      v-model:visible="discoveryModalVisible"
      title="服务发现"
      width="800px"
      @ok="handleDiscoverServices"
      @cancel="discoveryModalVisible = false"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="集群">
          <a-select
            v-model:value="discoveryConfig.clusterId"
            placeholder="选择集群"
            @change="handleClusterChange"
          >
            <a-select-option
              v-for="cluster in clusters"
              :key="cluster.id"
              :value="cluster.id"
            >
              {{ cluster.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="命名空间">
          <a-select
            v-model:value="discoveryConfig.namespaces"
            mode="multiple"
            placeholder="选择命名空间（默认全部）"
          >
            <a-select-option
              v-for="ns in availableNamespaces"
              :key="ns"
              :value="ns"
            >
              {{ ns }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="服务标签">
          <a-input
            v-model:value="discoveryConfig.labelSelector"
            placeholder="app=myapp,version!=v1"
          />
        </a-form-item>

        <a-form-item label="发现选项">
          <a-checkbox-group v-model:value="discoveryConfig.options">
            <a-checkbox value="includeSystemServices">包含系统服务</a-checkbox>
            <a-checkbox value="analyzeTraffic">分析流量关系</a-checkbox>
            <a-checkbox value="detectDependencies">检测依赖关系</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="discoveryModalVisible = false">取消</a-button>
          <a-button
            type="primary"
            :loading="discoveryLoading"
            @click="handleDiscoverServices"
          >
            开始发现
          </a-button>
        </a-space>
      </template>
    </a-modal>

    <!-- 配置预览模态框 -->
    <a-modal
      v-model:visible="previewModalVisible"
      title="链路配置预览"
      width="1000px"
      @ok="handleExportConfig"
    >
      <a-tabs>
        <a-tab-pane key="istio" tab="Istio 配置">
          <pre class="config-preview">{{ istioConfig }}</pre>
        </a-tab-pane>
        <a-tab-pane key="workflow" tab="工作流配置">
          <pre class="config-preview">{{ workflowConfig }}</pre>
        </a-tab-pane>
        <a-tab-pane key="yaml" tab="YAML 导出">
          <pre class="config-preview">{{ yamlConfig }}</pre>
        </a-tab-pane>
      </a-tabs>

      <template #footer>
        <a-space>
          <a-button @click="previewModalVisible = false">关闭</a-button>
          <a-button @click="handleCopyConfig">复制配置</a-button>
          <a-button type="primary" @click="handleExportConfig">
            导出文件
          </a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  SearchOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue';
import ImageSelector from './ImageSelector.vue';
import DeploymentImageSelector from './DeploymentImageSelector.vue';
import type { DeploymentInfo } from '@/api/pilot/cluster';

// 接口定义
interface Service {
  id: string;
  name: string;
  namespace: string;
  version: string;
  ports: string[];
  labels: Record<string, string>;
  type: string;
  status: string;
}

interface ChainService extends Service {
  trafficRatio: number;
  replicas: number;
  canaryImage?: string;
  healthCheck?: string;
  timeout?: number;
  retries?: number;
  dependencies: string[];
  deploymentImages?: Array<{
    containerName: string;
    repository: string;
    tag: string;
    originalImage: string;
  }>;
  deploymentInfo?: DeploymentInfo;
}

interface DependencyNode {
  title: string;
  key: string;
  type: 'service' | 'dependency';
  children?: DependencyNode[];
}

// 响应式数据
const searchText = ref('');
const filterNamespace = ref('');
const discoveryLoading = ref(false);
const discoveryModalVisible = ref(false);
const previewModalVisible = ref(false);

// 服务数据
const availableServices = ref<Service[]>([]);
const serviceChain = ref<ChainService[]>([]);
const selectedService = ref<ChainService | null>(null);
const namespaces = ref<string[]>(['default', 'kube-system', 'istio-system']);
const clusters = ref([{ id: 'cluster-1', name: '生产集群' }]);
const availableNamespaces = ref<string[]>([]);

// 镜像配置模式
const imageConfigMode = ref<'manual' | 'deployment'>('manual');

// 发现配置
const discoveryConfig = reactive({
  clusterId: '',
  namespaces: [],
  labelSelector: '',
  options: ['analyzeTraffic', 'detectDependencies'],
});

// 计算属性
const filteredServices = computed(() => {
  let services = availableServices.value;

  if (searchText.value) {
    services = services.filter((service) =>
      service.name.toLowerCase().includes(searchText.value.toLowerCase()),
    );
  }

  if (filterNamespace.value) {
    services = services.filter(
      (service) => service.namespace === filterNamespace.value,
    );
  }

  return services;
});

const dependencyTree = computed(() => {
  // 构建依赖关系树
  const tree: DependencyNode[] = [];

  serviceChain.value.forEach((service) => {
    const node: DependencyNode = {
      title: service.name,
      key: service.id,
      type: 'service',
      children: [],
    };

    if (service.dependencies.length > 0) {
      node.children = service.dependencies.map((dep) => ({
        title: dep,
        key: `${service.id}-${dep}`,
        type: 'dependency',
      }));
    }

    tree.push(node);
  });

  return tree;
});

const istioConfig = computed(() => {
  // 生成 Istio 配置
  const config = {
    virtualServices: serviceChain.value.map((service) => ({
      metadata: {
        name: `${service.name}-vs`,
        namespace: service.namespace,
      },
      spec: {
        http: [
          {
            match: [{ headers: { 'x-canary': { exact: 'true' } } }],
            route: [
              {
                destination: {
                  host: service.name,
                  subset: 'canary',
                },
                weight: service.trafficRatio,
              },
              {
                destination: {
                  host: service.name,
                  subset: 'stable',
                },
                weight: 100 - service.trafficRatio,
              },
            ],
          },
        ],
      },
    })),
    destinationRules: serviceChain.value.map((service) => ({
      metadata: {
        name: `${service.name}-dr`,
        namespace: service.namespace,
      },
      spec: {
        host: service.name,
        subsets: [
          {
            name: 'canary',
            labels: { version: 'canary' },
          },
          {
            name: 'stable',
            labels: { version: 'stable' },
          },
        ],
      },
    })),
  };

  return JSON.stringify(config, null, 2);
});

const workflowConfig = computed(() => {
  // 生成工作流配置
  const config = {
    name: 'service-chain-canary',
    type: 'canary',
    services: serviceChain.value.map((service) => ({
      name: service.name,
      namespace: service.namespace,
      canaryImage: service.canaryImage,
      trafficRatio: service.trafficRatio,
      replicas: service.replicas,
      healthCheck: service.healthCheck,
      timeout: service.timeout,
      retries: service.retries,
      dependencies: service.dependencies,
    })),
    dependencies: buildDependencyMatrix(),
  };

  return JSON.stringify(config, null, 2);
});

const yamlConfig = computed(() => {
  // 生成 YAML 配置
  return `# Generated Canary Workflow Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-workflow-config
  namespace: default
data:
  services: |
${serviceChain.value
  .map(
    (service) =>
      `    - name: ${service.name}
      namespace: ${service.namespace}
      trafficRatio: ${service.trafficRatio}
      replicas: ${service.replicas}`,
  )
  .join('\n')}`;
});

// 方法
function handleSearch() {
  // 搜索逻辑已在计算属性中实现
}

function handleFilter() {
  // 过滤逻辑已在计算属性中实现
}

function handleRefresh() {
  loadAvailableServices();
}

function showDiscoveryModal() {
  discoveryModalVisible.value = true;
}

function handleClusterChange() {
  // 加载命名空间
  availableNamespaces.value = [
    'default',
    'production',
    'staging',
    'development',
  ];
}

async function handleDiscoverServices() {
  try {
    discoveryLoading.value = true;

    // 模拟服务发现
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const mockServices: Service[] = [
      {
        id: 'service-1',
        name: 'user-service',
        namespace: 'default',
        version: 'v1.0.0',
        ports: ['8080', '8081'],
        labels: { app: 'user-service', version: 'v1.0.0' },
        type: 'ClusterIP',
        status: 'Running',
      },
      {
        id: 'service-2',
        name: 'order-service',
        namespace: 'default',
        version: 'v1.1.0',
        ports: ['8080'],
        labels: { app: 'order-service', version: 'v1.1.0' },
        type: 'ClusterIP',
        status: 'Running',
      },
      {
        id: 'service-3',
        name: 'payment-service',
        namespace: 'default',
        version: 'v2.0.0',
        ports: ['8080', '9090'],
        labels: { app: 'payment-service', version: 'v2.0.0' },
        type: 'ClusterIP',
        status: 'Running',
      },
    ];

    availableServices.value = mockServices;
    message.success(`发现 ${mockServices.length} 个服务`);
    discoveryModalVisible.value = false;
  } catch (error) {
    message.error('服务发现失败');
  } finally {
    discoveryLoading.value = false;
  }
}

function handleDragStart(event: DragEvent, service: Service) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify(service));
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault();
}

function handleDrop(event: DragEvent) {
  event.preventDefault();

  if (event.dataTransfer) {
    try {
      const serviceData = JSON.parse(event.dataTransfer.getData('text/plain'));
      addServiceToChain(serviceData);
    } catch (error) {
      message.error('添加服务失败');
    }
  }
}

function addServiceToChain(service: Service) {
  // 检查是否已存在
  if (serviceChain.value.some((s) => s.id === service.id)) {
    message.warning('服务已在链路中');
    return;
  }

  const chainService: ChainService = {
    ...service,
    trafficRatio: 20,
    replicas: 1,
    canaryImage: '',
    healthCheck: '/health',
    timeout: 30,
    retries: 3,
    dependencies:
      serviceChain.value.length > 0
        ? [serviceChain.value[serviceChain.value.length - 1].name]
        : [],
  };

  serviceChain.value.push(chainService);
  message.success(`已添加服务 ${service.name}`);
}

function removeFromChain(serviceId: string) {
  const index = serviceChain.value.findIndex((s) => s.id === serviceId);
  if (index > -1) {
    const removedService = serviceChain.value[index];
    serviceChain.value.splice(index, 1);

    // 更新依赖关系
    serviceChain.value.forEach((service) => {
      service.dependencies = service.dependencies.filter(
        (dep) => dep !== removedService.name,
      );
    });

    if (selectedService.value?.id === serviceId) {
      selectedService.value = null;
    }

    message.success(`已移除服务 ${removedService.name}`);
  }
}

function selectService(service: ChainService) {
  selectedService.value = service;
}

function updateTrafficRatio(serviceId: string, ratio: number) {
  const service = serviceChain.value.find((s) => s.id === serviceId);
  if (service) {
    service.trafficRatio = ratio;
  }
}

function updateReplicas(serviceId: string, replicas: number) {
  const service = serviceChain.value.find((s) => s.id === serviceId);
  if (service) {
    service.replicas = replicas;
  }
}

function updateServiceConfig() {
  // 服务配置更新已通过双向绑定实现
}

// 镜像配置模式变化处理
function onImageConfigModeChange() {
  if (selectedService.value) {
    // 切换模式时清除对应的配置
    if (imageConfigMode.value === 'manual') {
      selectedService.value.deploymentImages = undefined;
      selectedService.value.deploymentInfo = undefined;
    } else {
      selectedService.value.canaryImage = '';
    }
  }
}

// 处理deployment镜像变化
function onDeploymentImagesChange(images: any) {
  if (selectedService.value) {
    selectedService.value.deploymentImages = images;

    // 如果有镜像信息，设置第一个容器的镜像为canaryImage以保持兼容性
    if (images && images.length > 0) {
      const firstImage = images[0];
      selectedService.value.canaryImage = `${firstImage.repository}:${firstImage.tag}`;
    } else {
      selectedService.value.canaryImage = '';
    }
  }
}

// 处理deployment选择
function onDeploymentSelected(deployment: DeploymentInfo | null) {
  if (selectedService.value) {
    selectedService.value.deploymentInfo = deployment || undefined;

    // 如果选择了deployment，自动设置服务的命名空间
    if (deployment) {
      selectedService.value.namespace = deployment.namespace;
    }
  }
}

// 解析灰度镜像值（从字符串转换为对象格式）
function parseCanaryImageValue(imageString?: string) {
  if (!imageString) {
    return undefined;
  }

  // 正确解析镜像字符串，处理包含registry地址的情况
  // 格式: [registry[:port]/]repository[:tag]

  // 查找最后一个冒号，这通常是tag分隔符
  const lastColonIndex = imageString.lastIndexOf(':');

  if (lastColonIndex === -1) {
    // 没有冒号，只有repository
    return {
      repository: imageString,
      tag: 'latest',
    };
  }

  // 检查最后一个冒号后面是否包含斜杠，如果有斜杠说明是registry端口而不是tag
  const afterLastColon = imageString.substring(lastColonIndex + 1);
  const hasSlashAfterColon = afterLastColon.includes('/');

  if (hasSlashAfterColon) {
    // 最后一个冒号是registry端口的一部分，没有明确的tag
    return {
      repository: imageString,
      tag: 'latest',
    };
  }

  // 最后一个冒号后面没有斜杠，应该是tag分隔符
  const repository = imageString.substring(0, lastColonIndex);
  const tag = afterLastColon;

  // 验证tag是否合法（不应该包含斜杠或其他特殊字符）
  if (tag && /^[a-zA-Z0-9._-]+$/.test(tag)) {
    return {
      repository,
      tag,
    };
  }

  // tag不合法，当作整个字符串都是repository
  return {
    repository: imageString,
    tag: 'latest',
  };
}

// 更新灰度镜像（从对象格式转换为字符串）
function updateCanaryImage(
  imageValue: { repository: string; tag: string } | undefined,
) {
  if (selectedService.value) {
    if (imageValue && imageValue.repository && imageValue.tag) {
      selectedService.value.canaryImage = `${imageValue.repository}:${imageValue.tag}`;
    } else {
      selectedService.value.canaryImage = '';
    }
    updateServiceConfig();
  }
}

function handleClearChain() {
  serviceChain.value = [];
  selectedService.value = null;
  message.success('已清空链路');
}

function handleAutoArrange() {
  // 自动排列服务（可以实现智能依赖排序）
  serviceChain.value.sort((a, b) => a.name.localeCompare(b.name));
  message.success('已自动排列服务');
}

function handlePreview() {
  if (serviceChain.value.length === 0) {
    message.warning('请先添加服务到链路');
    return;
  }
  previewModalVisible.value = true;
}

function handleCopyConfig() {
  // 复制配置到剪贴板
  const config = istioConfig.value;
  navigator.clipboard.writeText(config).then(() => {
    message.success('配置已复制到剪贴板');
  });
}

function handleExportConfig() {
  // 导出配置文件
  const config = {
    istio: JSON.parse(istioConfig.value),
    workflow: JSON.parse(workflowConfig.value),
    yaml: yamlConfig.value,
  };

  const blob = new Blob([JSON.stringify(config, null, 2)], {
    type: 'application/json',
  });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'canary-workflow-config.json';
  link.click();
  URL.revokeObjectURL(url);

  message.success('配置文件已导出');
}

function buildDependencyMatrix() {
  // 构建依赖关系矩阵
  const matrix: Record<string, string[]> = {};

  serviceChain.value.forEach((service) => {
    matrix[service.name] = service.dependencies;
  });

  return matrix;
}

function loadAvailableServices() {
  // 加载可用服务（这里使用模拟数据）
  const mockServices: Service[] = [
    {
      id: 'service-1',
      name: 'user-service',
      namespace: 'default',
      version: 'v1.0.0',
      ports: ['8080'],
      labels: { app: 'user-service' },
      type: 'ClusterIP',
      status: 'Running',
    },
    {
      id: 'service-2',
      name: 'order-service',
      namespace: 'default',
      version: 'v1.1.0',
      ports: ['8080'],
      labels: { app: 'order-service' },
      type: 'ClusterIP',
      status: 'Running',
    },
    {
      id: 'service-3',
      name: 'payment-service',
      namespace: 'production',
      version: 'v2.0.0',
      ports: ['8080', '9090'],
      labels: { app: 'payment-service' },
      type: 'ClusterIP',
      status: 'Running',
    },
  ];

  availableServices.value = mockServices;
}

// 初始化
onMounted(() => {
  loadAvailableServices();
});

// 导出组件数据（供父组件使用）
defineExpose({
  serviceChain,
  getChainConfig: () => ({
    services: serviceChain.value,
    dependencies: buildDependencyMatrix(),
  }),
  clearChain: handleClearChain,
});
</script>

<style scoped>
.service-selector {
  padding: 16px;
}

.services-card,
.chain-card,
.dependencies-card,
.config-card {
  height: 600px;
}

.search-section {
  margin-bottom: 16px;
}

.services-list {
  max-height: 480px;
  overflow-y: auto;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s;
}

.service-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.service-item:active {
  cursor: grabbing;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.service-meta {
  margin-bottom: 4px;
}

.service-ports {
  font-size: 12px;
  color: #666;
}

.port-tag {
  display: inline-block;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 4px;
  font-size: 11px;
}

.chain-container {
  min-height: 480px;
  padding: 16px;
  border: 2px dashed #e8e8e8;
  border-radius: 6px;
  position: relative;
}

.chain-service {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.chain-service:hover {
  border-color: #1890ff;
}

.chain-service.active {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.service-order {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
}

.service-name {
  flex: 1;
  font-weight: 500;
}

.service-details {
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
}

.label {
  width: 60px;
  color: #666;
}

.value {
  color: #333;
}

.traffic-config {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.config-item {
  margin-bottom: 8px;
}

.config-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.connection-line {
  text-align: center;
  color: #1890ff;
  margin: 8px 0;
}

.drop-hint {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dependencies-view {
  max-height: 480px;
  overflow-y: auto;
}

.tree-service {
  font-weight: 500;
  color: #1890ff;
}

.service-config {
  max-height: 480px;
  overflow-y: auto;
}

.config-preview {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  max-height: 400px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.5;
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-empty) {
  margin: 20px 0;
}
</style>
