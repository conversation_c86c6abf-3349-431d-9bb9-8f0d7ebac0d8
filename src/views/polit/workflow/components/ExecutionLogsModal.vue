<template>
  <a-modal
    v-model:visible="modalVisible"
    title="执行日志"
    width="1000px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div class="logs-container">
      <!-- 日志控制栏 -->
      <div class="logs-header">
        <div class="logs-filters">
          <a-space>
            <a-select
              v-model:value="selectedLogLevel"
              size="small"
              style="width: 100px"
              @change="filterLogs"
            >
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="info">INFO</a-select-option>
              <a-select-option value="warn">WARN</a-select-option>
              <a-select-option value="error">ERROR</a-select-option>
              <a-select-option value="debug">DEBUG</a-select-option>
            </a-select>

            <a-input
              v-model:value="searchKeyword"
              size="small"
              placeholder="搜索日志内容"
              style="width: 200px"
              allow-clear
              @change="filterLogs"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>

            <a-switch
              v-model:checked="autoScroll"
              size="small"
              checked-children="自动滚动"
              un-checked-children="手动滚动"
            />

            <a-switch
              v-model:checked="autoRefresh"
              size="small"
              checked-children="自动刷新"
              un-checked-children="手动刷新"
              @change="toggleAutoRefresh"
            />
          </a-space>
        </div>

        <div class="logs-actions">
          <a-space>
            <a-button size="small" @click="refreshLogs">
              <ReloadOutlined />
              刷新
            </a-button>
            <a-button size="small" @click="clearLogs">
              <ClearOutlined />
              清空
            </a-button>
            <a-button size="small" @click="downloadLogs">
              <DownloadOutlined />
              下载
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 日志统计 -->
      <div class="logs-stats">
        <a-space>
          <a-tag color="blue">总计: {{ logs.length }}</a-tag>
          <a-tag color="green">INFO: {{ getLogCountByLevel('info') }}</a-tag>
          <a-tag color="orange">WARN: {{ getLogCountByLevel('warn') }}</a-tag>
          <a-tag color="red">ERROR: {{ getLogCountByLevel('error') }}</a-tag>
          <a-tag color="default">
            DEBUG: {{ getLogCountByLevel('debug') }}
          </a-tag>
        </a-space>
      </div>

      <!-- 日志内容 -->
      <div ref="logsContentRef" class="logs-content">
        <div v-if="loading" class="logs-loading">
          <a-spin size="large" />
          <div style="margin-top: 16px">加载日志中...</div>
        </div>

        <div v-else-if="filteredLogs.length === 0" class="logs-empty">
          <a-empty description="暂无日志数据" />
        </div>

        <div v-else class="logs-list">
          <div
            v-for="log in paginatedLogs"
            :key="log.id"
            class="log-item"
            :class="`log-level-${log.level}`"
          >
            <div class="log-header">
              <span class="log-timestamp">{{ log.timestamp }}</span>
              <span class="log-level" :class="`level-${log.level}`">
                {{ log.level.toUpperCase() }}
              </span>
              <span v-if="log.step" class="log-step">{{ log.step }}</span>
              <span v-if="log.source" class="log-source">{{ log.source }}</span>
            </div>
            <div class="log-message">
              <pre v-if="isJsonLog(log.message)">{{
                formatJsonLog(log.message)
              }}</pre>
              <span v-else v-html="highlightKeyword(log.message)"></span>
            </div>
            <div v-if="log.stack" class="log-stack">
              <a-collapse size="small">
                <a-collapse-panel key="stack" header="查看堆栈信息">
                  <pre>{{ log.stack }}</pre>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志分页 -->
      <div v-if="filteredLogs.length > 0" class="logs-pagination">
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="filteredLogs.length"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total: number) => `共 ${total} 条日志`"
          size="small"
          @change="handlePageChange"
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  ClearOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue';

interface Props {
  visible: boolean;
  executionId: number;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  step?: string;
  source?: string;
  stack?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const loading = ref(false);
const logs = ref<LogEntry[]>([]);
const selectedLogLevel = ref('all');
const searchKeyword = ref('');
const autoScroll = ref(true);
const autoRefresh = ref(false);
const logsContentRef = ref<HTMLElement>();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(50);

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 过滤后的日志
const filteredLogs = computed(() => {
  let result = logs.value;

  // 按日志级别过滤
  if (selectedLogLevel.value !== 'all') {
    result = result.filter((log) => log.level === selectedLogLevel.value);
  }

  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      (log) =>
        log.message.toLowerCase().includes(keyword) ||
        log.step?.toLowerCase().includes(keyword) ||
        log.source?.toLowerCase().includes(keyword),
    );
  }

  return result;
});

// 分页显示的日志
const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredLogs.value.slice(start, end);
});

// 获取执行日志
const fetchLogs = async () => {
  if (!props.executionId) {
    return;
  }

  try {
    loading.value = true;

    // 模拟API调用
    const mockLogs: LogEntry[] = [
      {
        id: '1',
        timestamp: '2024-01-15 10:30:00.123',
        level: 'info',
        message: '开始执行工作流: user-service-canary',
        step: '初始化',
        source: 'workflow-engine',
      },
      {
        id: '2',
        timestamp: '2024-01-15 10:30:01.456',
        level: 'info',
        message: '验证工作流配置完成',
        step: '初始化',
        source: 'workflow-engine',
      },
      {
        id: '3',
        timestamp: '2024-01-15 10:30:02.789',
        level: 'info',
        message: '开始部署准备步骤',
        step: '部署准备',
        source: 'deployment-manager',
      },
      {
        id: '4',
        timestamp: '2024-01-15 10:30:05.123',
        level: 'info',
        message: 'Pulling image: user-service:v1.2.0',
        step: '部署准备',
        source: 'k8s-deployer',
      },
      {
        id: '5',
        timestamp: '2024-01-15 10:30:15.456',
        level: 'info',
        message: 'Image pulled successfully',
        step: '部署准备',
        source: 'k8s-deployer',
      },
      {
        id: '6',
        timestamp: '2024-01-15 10:30:16.789',
        level: 'info',
        message: 'Creating deployment: user-service-canary',
        step: '部署准备',
        source: 'k8s-deployer',
      },
      {
        id: '7',
        timestamp: '2024-01-15 10:30:20.123',
        level: 'warn',
        message: 'Pod启动时间较长，等待就绪状态',
        step: '部署准备',
        source: 'k8s-deployer',
      },
      {
        id: '8',
        timestamp: '2024-01-15 10:30:45.456',
        level: 'info',
        message: 'Deployment ready with 2/2 replicas',
        step: '部署准备',
        source: 'k8s-deployer',
      },
      {
        id: '9',
        timestamp: '2024-01-15 10:30:46.789',
        level: 'info',
        message: '部署准备步骤完成',
        step: '部署准备',
        source: 'deployment-manager',
      },
      {
        id: '10',
        timestamp: '2024-01-15 10:30:47.123',
        level: 'info',
        message: '开始灰度部署步骤',
        step: '灰度部署',
        source: 'traffic-manager',
      },
      {
        id: '11',
        timestamp: '2024-01-15 10:30:50.456',
        level: 'info',
        message: '配置Istio VirtualService，设置流量比例为20%',
        step: '灰度部署',
        source: 'istio-manager',
      },
      {
        id: '12',
        timestamp: '2024-01-15 10:31:00.789',
        level: 'error',
        message: 'Failed to update VirtualService: connection timeout',
        step: '灰度部署',
        source: 'istio-manager',
        stack:
          'Error: connection timeout\n    at IstioClient.updateVirtualService (/app/src/istio/client.js:45:12)\n    at TrafficManager.setTrafficRatio (/app/src/traffic/manager.js:23:8)',
      },
      {
        id: '13',
        timestamp: '2024-01-15 10:31:01.123',
        level: 'info',
        message: '重试更新VirtualService...',
        step: '灰度部署',
        source: 'istio-manager',
      },
      {
        id: '14',
        timestamp: '2024-01-15 10:31:05.456',
        level: 'info',
        message: 'VirtualService updated successfully',
        step: '灰度部署',
        source: 'istio-manager',
      },
    ];

    logs.value = mockLogs;

    // 自动滚动到底部
    if (autoScroll.value) {
      await nextTick();
      scrollToBottom();
    }
  } catch (error) {
    console.error('获取执行日志失败:', error);
    message.error('获取执行日志失败');
  } finally {
    loading.value = false;
  }
};

// 过滤日志
const filterLogs = () => {
  currentPage.value = 1;
};

// 获取指定级别的日志数量
const getLogCountByLevel = (level: string) => {
  return logs.value.filter((log) => log.level === level).length;
};

// 判断是否为JSON日志
const isJsonLog = (message: string) => {
  try {
    JSON.parse(message);
    return true;
  } catch {
    return false;
  }
};

// 格式化JSON日志
const formatJsonLog = (message: string) => {
  try {
    return JSON.stringify(JSON.parse(message), null, 2);
  } catch {
    return message;
  }
};

// 高亮关键词
const highlightKeyword = (text: string) => {
  if (!searchKeyword.value) {
    return text;
  }

  const keyword = searchKeyword.value;
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

// 刷新日志
const refreshLogs = () => {
  fetchLogs();
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
};

// 下载日志
const downloadLogs = () => {
  const content = filteredLogs.value
    .map(
      (log) =>
        `[${log.timestamp}] [${log.level.toUpperCase()}] ${
          log.step ? `[${log.step}] ` : ''
        }${log.message}`,
    )
    .join('\n');

  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `execution-${props.executionId}-logs.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  message.success('日志下载成功');
};

// 滚动到底部
const scrollToBottom = () => {
  if (logsContentRef.value) {
    logsContentRef.value.scrollTop = logsContentRef.value.scrollHeight;
  }
};

// 切换自动刷新
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      fetchLogs();
    }, 5000); // 每5秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  }
};

// 分页变化处理
const handlePageChange = () => {
  // 分页变化时不需要重新加载数据，只需要更新显示
};

// 监听执行ID变化
watch(
  () => props.executionId,
  (newId) => {
    if (newId && props.visible) {
      fetchLogs();
    }
  },
);

// 监听visible变化
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.executionId) {
      fetchLogs();
    } else {
      // 关闭模态框时清理定时器
      if (refreshTimer) {
        clearInterval(refreshTimer);
        refreshTimer = null;
      }
    }
  },
);

// 组件卸载时清理定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<style scoped>
.logs-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.logs-stats {
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  background: #000;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin: 12px 0;
}

.logs-loading,
.logs-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #fff;
}

.logs-list {
  padding: 8px;
}

.log-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item.log-level-error {
  border-left: 3px solid #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.log-item.log-level-warn {
  border-left: 3px solid #faad14;
  background: rgba(250, 173, 20, 0.1);
}

.log-item.log-level-info {
  border-left: 3px solid #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.log-item.log-level-debug {
  border-left: 3px solid #d9d9d9;
  background: rgba(217, 217, 217, 0.1);
}

.log-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 11px;
}

.log-timestamp {
  color: #888;
  margin-right: 8px;
  min-width: 140px;
}

.log-level {
  margin-right: 8px;
  padding: 1px 6px;
  border-radius: 2px;
  font-weight: bold;
  min-width: 50px;
  text-align: center;
}

.log-level.level-error {
  background: #ff4d4f;
  color: #fff;
}

.log-level.level-warn {
  background: #faad14;
  color: #fff;
}

.log-level.level-info {
  background: #1890ff;
  color: #fff;
}

.log-level.level-debug {
  background: #d9d9d9;
  color: #333;
}

.log-step {
  color: #52c41a;
  margin-right: 8px;
  padding: 1px 4px;
  background: rgba(82, 196, 26, 0.2);
  border-radius: 2px;
}

.log-source {
  color: #722ed1;
  margin-right: 8px;
  padding: 1px 4px;
  background: rgba(114, 46, 209, 0.2);
  border-radius: 2px;
}

.log-message {
  color: #fff;
  word-break: break-all;
  white-space: pre-wrap;
}

.log-message pre {
  color: #fff;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
}

.log-message :deep(mark) {
  background: #faad14;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
}

.log-stack {
  margin-top: 8px;
}

.log-stack :deep(.ant-collapse) {
  background: transparent;
  border: 1px solid #555;
}

.log-stack :deep(.ant-collapse-header) {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1);
  border-bottom: 1px solid #555;
}

.log-stack :deep(.ant-collapse-content) {
  background: rgba(255, 77, 79, 0.05);
  border: none;
}

.log-stack :deep(.ant-collapse-content-box) {
  padding: 8px;
}

.log-stack pre {
  color: #ff7875;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  font-size: 11px;
  line-height: 1.3;
}

.logs-pagination {
  padding: 12px 0;
  border-top: 1px solid #e8e8e8;
  text-align: center;
  flex-shrink: 0;
}

/* 滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 8px;
}

.logs-content::-webkit-scrollbar-track {
  background: #333;
}

.logs-content::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 4px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: #888;
}
</style>
