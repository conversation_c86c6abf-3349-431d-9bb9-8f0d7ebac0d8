<template>
  <div class="workflow-container">
    <a-card :body-style="{ paddingBottom: '0' }">
      <!-- 搜索和操作栏 -->
      <div class="workflow-header">
        <div class="search-section">
          <a-form
            ref="searchFormRef"
            layout="inline"
            :model="searchForm"
            @finish="handleSearch"
          >
            <a-form-item label="工作流名称" name="name">
              <a-input
                v-model:value="searchForm.name"
                placeholder="请输入工作流名称"
                style="width: 200px"
                allow-clear
                @press-enter="handleSearch"
              />
            </a-form-item>
            <a-form-item label="类型" name="type">
              <a-select
                v-model:value="searchForm.type"
                placeholder="选择工作流类型"
                style="width: 150px"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="canary">灰度发布</a-select-option>
                <a-select-option value="blue-green">蓝绿部署</a-select-option>
                <a-select-option value="ab-test">A/B测试</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态" name="status">
              <a-select
                v-model:value="searchForm.status"
                placeholder="选择状态"
                style="width: 150px"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="pending">待审批</a-select-option>
                <a-select-option value="approved">已审批</a-select-option>
                <a-select-option value="rejected">已拒绝</a-select-option>
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="running">运行中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="failed">失败</a-select-option>
                <a-select-option value="archived">已归档</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="命名空间" name="namespace">
              <a-input
                v-model:value="searchForm.namespace"
                placeholder="请输入命名空间"
                style="width: 150px"
                allow-clear
                @press-enter="handleSearch"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button style="margin-left: 8px" @click="handleReset">
                重置
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <div class="action-section">
          <a-space>
            <a-button type="primary" @click="showCreateModal">
              <PlusOutlined />
              快速创建
            </a-button>
            <a-button
              type="primary"
              style="background: #722ed1; border-color: #722ed1"
              @click="showWizard"
            >
              <PlusOutlined />
              向导创建
            </a-button>
            <a-button
              :disabled="!selectedRowKeys.length"
              @click="handleBatchDelete"
            >
              <DeleteOutlined />
              批量删除
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 工作流列表 -->
      <div class="workflow-table">
        <a-table
          :columns="columns"
          :data-source="workflowListWithKey"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button size="small" @click="viewDetail(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button
                  size="small"
                  :disabled="record.status === 'running'"
                  @click="editWorkflow(record)"
                >
                  <EditOutlined />
                  编辑
                </a-button>
                <a-button
                  size="small"
                  type="primary"
                  :disabled="record.status === 'running'"
                  @click="executeWorkflow(record)"
                >
                  <PlayCircleOutlined />
                  执行
                </a-button>
                <!-- 审批相关操作 -->
                <a-button
                  v-if="record.status === 'draft'"
                  size="small"
                  type="primary"
                  ghost
                  @click="submitApproval(record)"
                >
                  提交审批
                </a-button>
                <a-button
                  v-if="record.status === 'pending'"
                  size="small"
                  type="primary"
                  @click="approveWorkflow(record, 'approve')"
                >
                  审批通过
                </a-button>
                <a-button
                  v-if="record.status === 'pending'"
                  size="small"
                  danger
                  @click="approveWorkflow(record, 'reject')"
                >
                  审批拒绝
                </a-button>

                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewMetrics(record)">
                        <LineChartOutlined />
                        查看指标
                      </a-menu-item>
                      <a-menu-item @click="viewExecutionRecords(record)">
                        <UnorderedListOutlined />
                        执行记录
                      </a-menu-item>
                      <a-menu-item @click="copyWorkflow(record)">
                        <CopyOutlined />
                        复制
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        danger
                        :disabled="record.status === 'running'"
                        @click="deleteWorkflow(record)"
                      >
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 创建/编辑工作流模态框 -->
    <WorkflowCreateModal
      v-model:visible="createModalVisible"
      :workflow="currentWorkflow"
      @success="handleCreateSuccess"
    />

    <!-- 工作流向导模态框 -->
    <a-modal
      v-model:visible="wizardModalVisible"
      title="创建工作流向导"
      width="90%"
      :footer="null"
      :destroy-on-close="true"
      style="top: 20px"
    >
      <WorkflowStepWizard @success="handleWizardSuccess" />
    </a-modal>

    <!-- 工作流详情模态框 -->
    <WorkflowDetailModal
      v-model:visible="detailModalVisible"
      :workflow-id="currentWorkflowId"
    />

    <!-- 工作流指标模态框 -->
    <WorkflowMetricsModal
      v-model:visible="metricsModalVisible"
      :workflow-id="currentWorkflowId"
    />

    <!-- 工作流执行记录模态框 -->
    <WorkflowExecutionModal
      v-model:visible="executionModalVisible"
      :workflow-id="currentWorkflowId"
      :workflow-name="currentWorkflowName"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  PlayCircleOutlined,
  LineChartOutlined,
  CopyOutlined,
  DownOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons-vue';
import {
  workflowApi,
  type ListWorkflowParams,
  type WorkflowResponse,
} from '@/api/pilot/workflow';
import WorkflowCreateModal from './components/WorkflowCreateModal.vue';
import WorkflowDetailModal from './components/WorkflowDetailModal.vue';
import WorkflowMetricsModal from './components/WorkflowMetricsModal.vue';
import WorkflowStepWizard from './components/WorkflowStepWizard.vue';
import WorkflowExecutionModal from './components/WorkflowExecutionModal.vue';

// 搜索表单
const searchForm = reactive<Partial<ListWorkflowParams>>({
  name: '',
  type: undefined,
  status: undefined,
  namespace: '',
});

// 表格状态
const loading = ref(false);
const workflowList = ref<WorkflowResponse[]>([]);
const selectedRowKeys = ref<string[]>([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 模态框状态
const createModalVisible = ref(false);
const wizardModalVisible = ref(false);
const detailModalVisible = ref(false);
const metricsModalVisible = ref(false);
const executionModalVisible = ref(false);
const currentWorkflow = ref<WorkflowResponse | null>(null);
const currentWorkflowId = ref<string>('');
const currentWorkflowName = ref<string>('');

// 表格列配置
const columns = [
  {
    title: '工作流名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
    width: 120,
  },
  {
    title: '集群ID',
    dataIndex: 'clusterId',
    key: 'clusterId',
    width: 120,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    key: 'gmtModified',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 350,
    fixed: 'right' as const,
  },
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys;
  },
}));

// 为表格数据添加key
const workflowListWithKey = computed(() => {
  return workflowList.value.map((item) => ({
    ...item,
    key: item.uuid || item.id.toString(),
  }));
});

// 获取工作流列表
const fetchWorkflowList = async () => {
  try {
    loading.value = true;
    const params: ListWorkflowParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm,
    };

    const response = await workflowApi.list(params);
    if (response.data) {
      workflowList.value = response.data.list || [];
      pagination.total = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    message.error('获取工作流列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchWorkflowList();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    (searchForm as any)[key] = undefined;
  });
  pagination.current = 1;
  fetchWorkflowList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchWorkflowList();
};

// 显示创建模态框
const showCreateModal = () => {
  currentWorkflow.value = null;
  createModalVisible.value = true;
};

// 显示向导模态框
const showWizard = () => {
  wizardModalVisible.value = true;
};

// 创建成功处理
const handleCreateSuccess = () => {
  createModalVisible.value = false;
  fetchWorkflowList();
  message.success('工作流创建成功');
};

// 向导创建成功处理
const handleWizardSuccess = () => {
  wizardModalVisible.value = false;
  fetchWorkflowList();
  message.success('工作流创建成功');
};

// 查看详情
const viewDetail = (workflow: WorkflowResponse) => {
  currentWorkflowId.value = workflow.id.toString();
  detailModalVisible.value = true;
};

// 编辑工作流
const editWorkflow = (workflow: WorkflowResponse) => {
  currentWorkflow.value = workflow;
  createModalVisible.value = true;
};

// 执行工作流
const executeWorkflow = async (workflow: WorkflowResponse) => {
  try {
    console.log('开始执行工作流:', workflow);

    // 确保workflowId是数字类型
    const workflowId =
      typeof workflow.id === 'string' ? parseInt(workflow.id) : workflow.id;

    if (!workflowId || isNaN(workflowId)) {
      message.error('工作流ID无效');
      return;
    }

    console.log('执行参数:', { workflowId });

    const response = await workflowApi.execute({ workflowId });
    console.log('执行响应:', response);

    // 显示成功消息
    message.success('工作流执行成功');

    // 使用 nextTick 和 setTimeout 确保 DOM 更新完成后再刷新列表
    await nextTick();
    setTimeout(() => {
      fetchWorkflowList();
    }, 100);
  } catch (error: any) {
    console.error('执行工作流失败:', error);

    // 更详细的错误信息
    if (error.response) {
      console.error('服务器响应:', error.response.data);
      message.error(
        `执行工作流失败: ${
          error.response.data?.message || error.response.status
        }`,
      );
    } else if (error.request) {
      console.error('网络请求失败:', error.request);
      message.error('网络请求失败，请检查后端服务是否启动');
    } else {
      console.error('其他错误:', error.message);
      message.error(`执行工作流失败: ${error.message}`);
    }
  }
};

// 查看指标
const viewMetrics = (workflow: WorkflowResponse) => {
  currentWorkflowId.value = workflow.id.toString();
  metricsModalVisible.value = true;
};

// 查看执行记录
const viewExecutionRecords = (workflow: WorkflowResponse) => {
  currentWorkflowId.value = workflow.id.toString();
  currentWorkflowName.value = workflow.name;
  executionModalVisible.value = true;
};

// 复制工作流
const copyWorkflow = (workflow: WorkflowResponse) => {
  const copiedWorkflow: Partial<WorkflowResponse> = {
    ...workflow,
    name: `${workflow.name}_copy`,
    id: undefined,
  };
  currentWorkflow.value = copiedWorkflow as WorkflowResponse;
  createModalVisible.value = true;
};

// 提交审批
const submitApproval = async (workflow: WorkflowResponse) => {
  try {
    await workflowApi.submitApproval(workflow.uuid || workflow.id.toString());
    message.success('提交审批成功');
    await fetchWorkflowList();
  } catch (error) {
    console.error('提交审批失败:', error);
    message.error('提交审批失败');
  }
};

// 审批工作流
const approveWorkflow = (
  workflow: WorkflowResponse,
  action: 'approve' | 'reject',
) => {
  const actionText = action === 'approve' ? '通过' : '拒绝';
  Modal.confirm({
    title: `确认${actionText}`,
    content: `确定要${actionText}工作流 "${workflow.name}" 吗？`,
    onOk: async () => {
      try {
        await workflowApi.approveWorkflow({
          uuid: workflow.uuid || workflow.id.toString(),
          action,
        });
        message.success(`${actionText}成功`);
        await nextTick();
        setTimeout(() => {
          fetchWorkflowList();
        }, 100);
      } catch (error) {
        console.error(`${actionText}失败:`, error);
        message.error(`${actionText}失败`);
      }
    },
  });
};

// 删除工作流
const deleteWorkflow = (workflow: WorkflowResponse) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除工作流 "${workflow.name}" 吗？`,
    onOk: async () => {
      try {
        await workflowApi.delete({
          uuids: [workflow.uuid || workflow.id.toString()],
        });
        message.success('删除成功');
        await nextTick();
        setTimeout(() => {
          fetchWorkflowList();
        }, 100);
      } catch (error) {
        console.error('删除工作流失败:', error);
        message.error('删除工作流失败');
      }
    },
  });
};

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的工作流');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个工作流吗？`,
    onOk: async () => {
      try {
        await workflowApi.delete({ uuids: selectedRowKeys.value });
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        await nextTick();
        setTimeout(() => {
          fetchWorkflowList();
        }, 100);
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
};

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    canary: 'blue',
    'blue-green': 'green',
    'ab-test': 'orange',
  };
  return colorMap[type] || 'default';
};

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    canary: '灰度发布',
    'blue-green': '蓝绿部署',
    'ab-test': 'A/B测试',
  };
  return textMap[type] || type;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    pending: 'orange',
    approved: 'blue',
    rejected: 'red',
    active: 'green',
    running: 'processing',
    completed: 'success',
    failed: 'error',
    archived: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审批',
    approved: '已审批',
    rejected: '已拒绝',
    active: '启用',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    archived: '已归档',
  };
  return textMap[status] || status;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchWorkflowList();
});
</script>

<style scoped>
.workflow-container {
  padding: 20px;
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-section {
  flex: 1;
  min-width: 0;
}

.action-section {
  flex-shrink: 0;
}

.workflow-table {
  background: #fff;
}

@media (max-width: 768px) {
  .workflow-header {
    flex-direction: column;
    align-items: stretch;
  }

  .action-section {
    margin-top: 16px;
  }
}
</style>
