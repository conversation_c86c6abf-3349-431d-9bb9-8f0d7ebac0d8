export const columns = [
  {
    title: '序号',
    width: 40,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    width: 200,
    title: '工作流名称',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 150,
    title: '描述',
    dataIndex: 'description',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 100,
    title: '类型',
    dataIndex: 'type',
    align: 'center',
    ellipsis: true,
    key: 'type',
  },
  {
    width: 100,
    title: '状态',
    dataIndex: 'status',
    align: 'center',
    ellipsis: true,
    key: 'status',
  },
  {
    width: 120,
    title: '集群',
    dataIndex: 'clusterId',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 120,
    title: '命名空间',
    dataIndex: 'namespace',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 120,
    title: '服务名称',
    dataIndex: 'serviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '版本',
    dataIndex: 'version',
    width: 80,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    align: 'center',
    width: 120,
  },
];

export enum Mode {
  Add = 'add',
  Edit = 'edit',
  View = 'view',
}

// 工作流类型选项
export const workflowTypes = [
  { label: '灰度发布', value: 'canary', color: 'blue' },
  { label: '蓝绿部署', value: 'blue-green', color: 'green' },
  { label: '滚动更新', value: 'rolling', color: 'orange' },
  { label: 'A/B测试', value: 'ab-test', color: 'purple' },
  { label: '自定义', value: 'custom', color: 'gray' },
];

// 工作流状态选项
export const workflowStatuses = [
  { label: '草稿', value: 'draft', color: 'default' },
  { label: '启用', value: 'active', color: 'blue' },
  { label: '运行中', value: 'running', color: 'processing' },
  { label: '已完成', value: 'completed', color: 'success' },
  { label: '失败', value: 'failed', color: 'error' },
  { label: '已归档', value: 'archived', color: 'default' },
];

// 获取工作流类型标签
export const getTypeLabel = (type: string) => {
  const typeOption = workflowTypes.find((t) => t.value === type);
  return typeOption?.label || type;
};

// 获取工作流类型颜色
export const getTypeColor = (type: string) => {
  const typeOption = workflowTypes.find((t) => t.value === type);
  return typeOption?.color || 'default';
};

// 获取工作流状态标签
export const getStatusLabel = (status: string) => {
  const statusOption = workflowStatuses.find((s) => s.value === status);
  return statusOption?.label || status;
};

// 获取工作流状态颜色
export const getStatusColor = (status: string) => {
  const statusOption = workflowStatuses.find((s) => s.value === status);
  return statusOption?.color || 'default';
};
