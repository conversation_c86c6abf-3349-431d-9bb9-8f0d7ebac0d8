<template>
  <a-drawer
    :title="title"
    :width="720"
    :visible="show"
    :mask-closable="action.mode === Mode.View"
    :footer-style="{ textAlign: 'right' }"
    @close="close"
  >
    <template #extra>
      <a-space v-if="action.mode != Mode.View">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" :loading="loading" @click="modalOk">
          保存
        </a-button>
      </a-space>
    </template>
    <!--content-->
    <a-form ref="formRef" :model="formState" :label-col="{ span: 3 }">
      <a-form-item label="集群名称" name="name" :rules="rules.name">
        <a-input v-model:value="formState.name" placeholder="请输入集群名称" />
      </a-form-item>
      <a-form-item label="访问秘钥" name="kubeConfig" :rules="rules.kubeConfig">
        <a-textarea
          v-model:value="formState.kubeConfig"
          type="password"
          placeholder="请输入.kube/config文件内容作为访问秘钥"
          :rows="15"
        />
      </a-form-item>
      <a-form-item label="是否启用" name="enable">
        <a-switch v-model:checked="formState.enable" />
      </a-form-item>
      <a-form-item label="集群描述" name="description">
        <a-textarea v-model:value="formState.description" />
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import { Mode } from './data';
import { message } from 'ant-design-vue';
import { addCluster, infoCluster, updateCluster } from '@/api/pilot/cluster';

export default defineComponent({
  name: 'EditDrawer',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
  emits: ['ok'],
  setup(props, context) {
    let show = ref<boolean>(false);
    const action = reactive({
      mode: '',
      uuid: '',
    });
    const formRef = ref();
    const formState = reactive({
      name: '',
      kubeConfig: '',
      enable: true,
      description: '',
    });

    const modalOk = () => {
      handleSubmit();
    };

    const open = async (mode: string, uuid: string) => {
      resetForm();
      action.mode = mode;
      action.uuid = uuid;
      show.value = true;
      if (formRef.value && formRef.value) {
        formRef.value.resetFields();
      }
      if (mode === 'edit') {
        const info = await infoCluster({ uuid: uuid });
        Object.assign(formState, info.data);
      }
    };

    const finish = () => {
      show.value = false;
      context.emit('ok');
    };

    const close = () => {
      show.value = false;
    };

    async function handleSubmit() {
      try {
        loading.value = false;
        const values = await formRef.value.validate();
        if (action.mode === 'edit') {
          values.uuid = action.uuid;
          await updateCluster(values);
        } else {
          await addCluster(values);
        }
        message.success('提交成功');
        finish();
      } finally {
        loading.value = false;
      }
    }

    const rules = {
      name: [{ required: true, message: '请输入集群名称' }],
      kubeConfig: [{ required: true, message: '请输入访问秘钥' }],
    };

    const loading = ref(false);

    const resetForm = () => {
      formRef.value?.resetFields();
    };

    return {
      loading,
      Mode,
      show,
      action,
      formRef,
      formState,
      modalOk,
      finish,
      open,
      close,
      handleSubmit,
      rules,
    };
  },
});
</script>
