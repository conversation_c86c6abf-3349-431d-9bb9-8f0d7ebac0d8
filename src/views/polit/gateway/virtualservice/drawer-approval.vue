<template>
  <a-drawer
    v-model:open="drawerVisible"
    title="审批"
    width="70%"
    placement="right"
    :body-style="{
      paddingTop: '0px',
      paddingBottom: '4px',
      paddingLeft: '0px',
    }"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button :loading="dryRunLoading" type="default" @click="handleDryRun">
          试跑
        </a-button>
        <a-button
          :loading="loading"
          type="primary"
          danger
          @click="handleReject"
        >
          拒绝
        </a-button>
        <a-button :loading="loading" type="primary" @click="handleApprove">
          通过
        </a-button>
      </a-space>
    </template>
    <div style="height: calc(100vh - 215px)">
      <div v-if="diffLoading" style="text-align: center; padding: 40px">
        <a-spin size="large" />
        <div style="margin-top: 16px">正在获取配置对比...</div>
      </div>
      <div v-else-if="onlineContent && currentContent">
        <yaml-diff
          ref="yamlDiffRef"
          :old-value="onlineContent"
          :new-value="currentContent"
          :height="'calc(100vh - 205px)'"
          :options="{ theme: 'vs', readOnly: false }"
          @new-yaml-code="handleYamlChange"
        />
      </div>
    </div>
    <div style="margin-top: 16px; padding-left: 24px">
      <a-form
        ref="formRef"
        :model="{ comment }"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item name="comment" required>
          <a-textarea
            v-model:value="comment"
            :rows="2"
            placeholder="请输入审批意见"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import YamlDiff from '@c/yaml-diff/index.vue';
import {
  reviewVirtualService, // 新的统一审批API
  dryRunVirtualService,
  getVirtualService,
} from '@/api/pilot/gateway';
import { cleanKubernetesMetadata } from '@/utils/yaml';

// Props
interface Props {
  visible: boolean;
  record: any;
  cluster: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  record: null,
  cluster: '',
});

// Emits
const emit = defineEmits(['update:visible', 'submit']);

// 计算属性处理 visible
const drawerVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
});

// 响应式数据
const loading = ref(false);
const dryRunLoading = ref(false);
const diffLoading = ref(false);
const comment = ref('');
const currentContent = ref('');
const onlineContent = ref('');
const formRef = ref<FormInstance>();
const yamlDiffRef = ref();
const modifiedContent = ref(''); // 存储修改后的YAML内容

// 表单验证规则
const formRules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    {
      min: 1,
      max: 500,
      message: '审批意见长度在 1 到 500 个字符',
      trigger: 'blur',
    },
  ],
};

// YAML 内容变化处理
const handleYamlChange = (newValue: string) => {
  modifiedContent.value = newValue || '';
};

// 监听props变化
watch(
  () => props.record,
  async (newRecord) => {
    if (newRecord) {
      diffLoading.value = true;

      try {
        // 获取线上版本
        const onlineResponse = await getVirtualService({
          cluster: props.cluster,
          namespace: newRecord.namespace,
          name: newRecord.name,
          online: true,
          silent: true, // 静默模式，不抛出异常
        });
        if (!onlineResponse.data || !onlineResponse.data.content) {
          onlineContent.value = '-';
        } else {
          onlineContent.value =
            cleanKubernetesMetadata(onlineResponse?.data?.content) || '';
        }

        // 获取编辑版本（申请版本）
        const editResponse = await getVirtualService({
          cluster: props.cluster,
          namespace: newRecord.namespace,
          name: newRecord.name,
        });

        currentContent.value = editResponse.data.content || '';
      } catch (error) {
        console.error('获取版本数据失败:', error);
        onlineContent.value = '';
        currentContent.value = '';
      } finally {
        diffLoading.value = false;
      }
    }
  },
  { immediate: true },
);

// 取消
const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};

// 测试部署 (Dry Run)
const handleDryRun = async () => {
  try {
    dryRunLoading.value = true;

    // 获取当前要测试的YAML内容（优先使用修改后的内容）
    const testContent = modifiedContent.value || currentContent.value;

    if (!testContent) {
      message.warning('没有可测试的YAML内容');
      return;
    }

    const params = {
      cluster: props.cluster,
      namespace: props.record.namespace,
      name: props.record.name,
      content: testContent,
    };

    const response = await dryRunVirtualService(params);

    if (response.data?.success !== false) {
      message.success('YAML配置验证通过，可以正常部署');
    } else {
      message.error(
        'YAML配置验证失败: ' + (response.data?.message || '配置存在问题'),
      );
    }
  } catch (error: any) {
    console.error('Dry Run测试失败:', error);
    message.error('测试部署失败: ' + (error?.message || error));
  } finally {
    dryRunLoading.value = false;
  }
};

// 审批通过
const handleApprove = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    loading.value = true;

    // 获取修改后的YAML内容
    const finalContent = modifiedContent.value || currentContent.value;

    const params = {
      uuid: props.record.uuid,
      action: 'approve' as const, // 使用统一接口，指定操作类型为批准
      comment: comment.value,
      content: finalContent, // 传递修改后的YAML内容
    };

    await reviewVirtualService(params); // 使用新的统一API
    message.success('审批通过成功');

    emit('update:visible', false);
    emit('submit', true);
    resetForm();
  } catch (error: any) {
    if (error?.errorFields) {
      // 表单验证失败，不显示错误消息
      return;
    }
    console.error('审批操作失败:', error);
    message.error('审批操作失败: ' + (error?.message || error));
  } finally {
    loading.value = false;
  }
};

// 审批拒绝
const handleReject = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    loading.value = true;

    // 获取修改后的YAML内容
    const finalContent = modifiedContent.value || currentContent.value;

    const params = {
      uuid: props.record.uuid,
      action: 'reject' as const, // 使用统一接口，指定操作类型为拒绝
      comment: comment.value,
      content: finalContent, // 传递修改后的YAML内容
    };

    await reviewVirtualService(params); // 使用新的统一API
    message.success('审批拒绝成功');

    emit('update:visible', false);
    emit('submit', true);
    resetForm();
  } catch (error: any) {
    if (error?.errorFields) {
      // 表单验证失败，不显示错误消息
      return;
    }
    console.error('审批操作失败:', error);
    message.error('审批操作失败: ' + (error?.message || error));
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  comment.value = '';
  currentContent.value = '';
  onlineContent.value = '';
  modifiedContent.value = '';
  diffLoading.value = false;
  // 重置表单验证状态
  formRef.value?.resetFields();
};
</script>

<style scoped>
.approval-content {
  height: 100%;
  overflow-y: auto;
}

:deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

h3 {
  margin-bottom: 16px;
  font-weight: 500;
}
</style>
