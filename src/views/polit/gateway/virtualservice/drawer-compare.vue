<template>
  <a-drawer
    v-model:open="drawerVisible"
    title="配置对比"
    width="65%"
    placement="right"
    :body-style="{
      paddingTop: '0px',
      paddingBottom: '4px',
      paddingLeft: '0px',
    }"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleClose">关闭</a-button>
        <a-button
          type="primary"
          :loading="syncLoading"
          @click="handleSyncChanges"
        >
          同步修改
        </a-button>
      </a-space>
    </template>
    <div style="height: calc(100vh - 215px)">
      <div v-if="loading" style="text-align: center; padding: 40px">
        <a-spin size="large" />
        <div style="margin-top: 16px">正在获取配置对比...</div>
      </div>
      <div v-else-if="onlineContent && currentContent">
        <yaml-diff
          ref="yamlDiffRef"
          :old-value="onlineContent"
          :new-value="currentContent"
          :height="'calc(100vh - 205px)'"
          :options="{ theme: 'vs', readOnly: false }"
          @new-yaml-code="handleYamlChange"
        />
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import * as yaml from 'js-yaml';
import YamlDiff from '@c/yaml-diff/index.vue';
import { getVirtualService } from '@/api/pilot/gateway';
import { cleanKubernetesMetadata } from '@/utils/yaml';

// Props
interface Props {
  visible: boolean;
  cluster: string;
  namespace: string;
  name: string;
  currentYaml: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  cluster: '',
  namespace: '',
  name: '',
  currentYaml: '',
});

// Emits
const emit = defineEmits(['update:visible', 'sync']);

// 计算属性处理 visible
const drawerVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
});

// 响应式数据
const loading = ref(false);
const syncLoading = ref(false);
const onlineContent = ref('');
const currentContent = ref('');
const modifiedContent = ref('');
const yamlDiffRef = ref();

// YAML 内容变化处理
const handleYamlChange = (newValue: string) => {
  modifiedContent.value = newValue || '';
};

// 监听props变化，获取对比数据
watch(
  () => [props.visible, props.cluster, props.namespace, props.name],
  async ([visible, cluster, namespace, name]) => {
    if (visible && cluster && namespace && name) {
      await fetchCompareData();
    }
  },
  { immediate: true },
);

// 获取对比数据
const fetchCompareData = async () => {
  try {
    loading.value = true;

    // 获取线上版本
    const onlineResponse = await getVirtualService({
      cluster: props.cluster,
      namespace: props.namespace,
      name: props.name,
      online: true,
    });

    if (onlineResponse.data) {
      onlineContent.value =
        cleanKubernetesMetadata(onlineResponse.data.content) || '';
    } else {
      onlineContent.value = '';
    }

    // 设置当前编辑版本
    currentContent.value = props.currentYaml || '';
  } catch (error) {
    console.error('获取对比数据失败:', error);
    message.error('获取对比数据失败');
  } finally {
    loading.value = false;
  }
};

// 同步修改到编辑表单
const handleSyncChanges = async () => {
  try {
    syncLoading.value = true;

    if (!modifiedContent.value) {
      message.warning('没有修改内容需要同步');
      return;
    }

    // 解析修改后的YAML内容
    let parsedConfig;
    try {
      parsedConfig = yaml.load(modifiedContent.value);
      if (!parsedConfig) {
        message.error('修改后的配置解析失败');
        return;
      }
    } catch (yamlError) {
      console.error('解析修改后的YAML失败:', yamlError);
      message.error('修改后的配置格式错误');
      return;
    }

    // 验证配置格式
    if (
      !parsedConfig ||
      typeof parsedConfig !== 'object' ||
      !('metadata' in parsedConfig) ||
      !('spec' in parsedConfig)
    ) {
      message.error('修改后的配置格式不正确，缺少必要的字段');
      return;
    }

    // 发送同步事件
    emit('sync', {
      content: modifiedContent.value,
      config: parsedConfig,
    });

    message.success('修改已同步到编辑表单');
    handleClose();
  } catch (error) {
    console.error('同步修改失败:', error);
    message.error('同步修改失败');
  } finally {
    syncLoading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
  // 重置状态
  onlineContent.value = '';
  currentContent.value = '';
  modifiedContent.value = '';
  loading.value = false;
  syncLoading.value = false;
};
</script>

<style scoped>
:deep(.ant-drawer-body) {
  padding: 24px;
}
</style>
