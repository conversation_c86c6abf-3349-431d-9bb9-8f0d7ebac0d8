<template>
  <a-drawer title="历史版本" :open="visible" width="70%" @close="handleClose">
    <div class="version-history">
      <!-- 操作栏 -->
      <div class="history-toolbar">
        <div class="toolbar-right">
          <a-space>
            <a-button
              v-if="selectedVersions.length === 2"
              type="default"
              size="small"
              @click="handleCompare"
            >
              对比选中版本
            </a-button>
            <a-button
              v-if="selectedVersion && selectedVersion.version > 1"
              type="primary"
              size="small"
              :loading="rollbackLoading"
              @click="handleRollback"
            >
              回滚到此版本
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 版本列表 -->
      <div class="version-list-container">
        <a-spin :spinning="loading">
          <a-table
            :columns="columns"
            :data-source="versions"
            :pagination="pagination"
            :scroll="{ y: 'calc(100vh - 320px)' }"
            size="small"
            bordered
            :row-class-name="getRowClassName"
            @row="onRowClick"
          >
            <!-- 版本号列 -->
            <template #version="{ record }">
              <a-tag color="blue" size="small" class="mini-tag">
                v{{ record.version }}
              </a-tag>
            </template>

            <!-- 操作类型列 -->
            <template #operation="{ record }">
              <a-tag
                :color="getOperationColor(record.status)"
                size="small"
                class="mini-tag"
              >
                {{ getOperationText(record.status) }}
              </a-tag>
            </template>

            <!-- 时间列 -->
            <template #time="{ record }">
              <span class="time-text">
                {{ formatTime(record.createdTime) }}
              </span>
            </template>

            <!-- 操作人列 -->
            <template #operator="{ record }">
              <span class="operator-text">
                {{ record.modifier?.split('@')[0] || '-' }}
              </span>
            </template>

            <!-- 描述列 -->
            <template #description="{ record }">
              <div class="description-cell">
                <span
                  v-if="record.description"
                  class="description-text"
                  :title="record.description"
                >
                  {{ record.description }}
                </span>
                <span v-else class="no-description">-</span>
              </div>
            </template>

            <!-- 操作列 -->
            <template #actions="{ record }">
              <a-space size="small">
                <a-button
                  type="text"
                  size="small"
                  class="mini-btn"
                  @click.stop="viewVersion(record)"
                >
                  查看
                </a-button>
                <a-popconfirm
                  title="确认回滚"
                  description="回滚操作将加载此版本的配置到编辑表单，需要重新提交审批。是否继续？"
                  @confirm="rollbackToVersion(record)"
                >
                  <a-button type="text" size="small" class="mini-btn">
                    回滚
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table>
        </a-spin>
      </div>

      <!-- 版本详情抽屉 -->
      <a-drawer
        title="详情"
        :open="detailDrawerVisible"
        width="60%"
        @close="detailDrawerVisible = false"
      >
        <div v-if="viewingVersion" class="version-detail">
          <a-tabs>
            <a-tab-pane key="yaml" tab="配置">
              <code-editor
                v-model="viewingVersion.content"
                :read-only="true"
                :show-expand-btn="false"
                :minimap="false"
                :height="'calc(100vh - 300px)'"
                :initial-shell-type="'yaml'"
                :label="'版本配置'"
              />
            </a-tab-pane>

            <a-tab-pane key="metadata" tab="信息">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="版本号">
                  v{{ viewingVersion.version }}
                </a-descriptions-item>
                <a-descriptions-item label="资源版本">
                  {{ viewingVersion.resourceVersion || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="操作类型">
                  <a-tag :color="getOperationColor(viewingVersion.status)">
                    {{ getOperationText(viewingVersion.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="申请人">
                  {{ viewingVersion.creator || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="审批人">
                  {{ viewingVersion.modifier || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDetailTime(viewingVersion.createdTime) }}
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="viewingVersion.description"
                  label="变更描述"
                >
                  {{ viewingVersion.description }}
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-drawer>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import CodeEditor from '@c/code-editor/index.vue';
import { getVirtualServiceHistory } from '@/api/pilot/gateway';

interface Props {
  visible: boolean;
  resourceInfo: {
    cluster: string;
    namespace: string;
    resourceType: string;
    resourceName: string;
  };
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'rollback', version: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 数据状态
const versions = ref([]);
const selectedVersion = ref<any>(null);
const selectedVersions = ref<any[]>([]);
const viewingVersion = ref<any>(null);
const loading = ref(false);
const rollbackLoading = ref(false);

// UI状态
const detailDrawerVisible = ref(false);

// 分页
const pagination = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: any) => `共 ${total} 条数据`,
  onChange: (page: number, pageSize: number) => {
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;
    fetchVersions();
  },
});

// 表格列定义
const columns = [
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    width: 80,
    slots: { customRender: 'version' },
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'status',
    key: 'operation',
    width: 80,
    slots: { customRender: 'operation' },
    align: 'center',
  },
  {
    title: '时间',
    dataIndex: 'createdTime',
    key: 'time',
    width: 140,
    slots: { customRender: 'time' },
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'modifier',
    key: 'operator',
    width: 100,
    slots: { customRender: 'operator' },
    align: 'center',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    slots: { customRender: 'description' },
    align: 'center',
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    slots: { customRender: 'actions' },
    align: 'center',
  },
];

// 获取版本历史列表
const fetchVersions = async () => {
  if (!props.resourceInfo.resourceName) {
    return;
  }

  loading.value = true;
  try {
    const response = await getVirtualServiceHistory({
      cluster: props.resourceInfo.cluster,
      namespace: props.resourceInfo.namespace,
      name: props.resourceInfo.resourceName,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });

    if (response && response.data) {
      versions.value = response.data.list || [];
      pagination.value.total = response.data.total || 0;
    } else {
      versions.value = [];
      pagination.value.total = 0;
    }
  } catch (error) {
    message.error('获取版本历史失败：' + error);
    versions.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 行点击事件
const onRowClick = (record: any) => {
  return {
    onClick: () => {
      selectedVersion.value = record;
    },
  };
};

// 获取行样式
const getRowClassName = (record: any) => {
  if (selectedVersion.value?.uuid === record.uuid) {
    return 'selected-row';
  }
  return '';
};

// 查看版本
const viewVersion = (record: any) => {
  viewingVersion.value = record;
  detailDrawerVisible.value = true;
};

// 回滚到指定版本
const rollbackToVersion = (record: any) => {
  selectedVersion.value = record;
  handleRollback();
};

// 版本对比
const handleCompare = () => {
  if (selectedVersions.value.length === 2) {
    // TODO: 实现版本对比功能
    message.info('版本对比功能待实现');
  }
};

// 回滚
const handleRollback = () => {
  if (selectedVersion.value) {
    emit('rollback', selectedVersion.value);
  }
};

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
};

// 获取操作类型颜色
const getOperationColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'pending':
      return 'processing';
    case 'failed':
      return 'error';
    case 'rejected':
      return 'error';
    case 'deleted':
      return 'default';
    default:
      return 'default';
  }
};

// 获取操作类型文本
const getOperationText = (status: string) => {
  switch (status) {
    case 'active':
      return '启用';
    case 'pending':
      return '审批';
    case 'failed':
      return '失败';
    case 'rejected':
      return '拒绝';
    case 'deleted':
      return '删除';
    default:
      return status;
  }
};

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm');
};

const formatDetailTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 监听资源信息变化
watch(
  () => props.resourceInfo,
  () => {
    if (props.resourceInfo.resourceName) {
      pagination.value.current = 1;
      fetchVersions();
    }
  },
  { immediate: true, deep: true },
);

// 监听抽屉显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.resourceInfo.resourceName) {
      pagination.value.current = 1;
      fetchVersions();
    }
  },
);

onMounted(() => {
  if (props.visible && props.resourceInfo.resourceName) {
    fetchVersions();
  }
});
</script>

<style scoped>
.version-history {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .toolbar-right {
    .version-count {
      font-size: 12px;
      color: #8c8c8c;
      background: #f5f5f5;
      padding: 2px 8px;
      border-radius: 12px;
    }
  }
}

.version-list-container {
  flex: 1;
  overflow: hidden;

  :deep(.ant-table-tbody) {
    .ant-table-row {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected-row {
        background-color: #e6f7ff;
      }
    }
  }

  :deep(.ant-table-cell) {
    padding: 8px 12px !important;
  }
}

.time-text {
  font-size: 12px;
  color: #8c8c8c;
}

.operator-text {
  font-size: 12px;
  color: #595959;
}

.description-cell {
  .description-text {
    font-size: 12px;
    color: #262626;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .no-description {
    font-size: 12px;
    color: #d9d9d9;
  }
}

.version-detail {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .detail-time {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 500;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 12px;
}

/* 更小的操作按钮样式 */
.mini-btn {
  font-size: 12px !important;
  padding: 0 6px !important;
  height: 22px !important;
  line-height: 20px !important;
}

.mini-tag {
  font-size: 11px !important;
  height: 18px !important;
  line-height: 16px !important;
  padding: 0 6px !important;
  vertical-align: middle !important;
}
</style>
