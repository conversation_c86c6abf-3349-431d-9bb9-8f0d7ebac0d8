<template>
  <a-card :body-style="{ paddingBottom: '0!important' }">
    <a-space style="margin: 10px 0px 28px 0px">
      <a-form layout="inline" :model="formState" @submit="onSearch">
        <a-form-item label="集群" name="cluster">
          <a-select
            v-model:value="formState.cluster"
            :options="clusterOptions"
            :loading="!clusterOptionsLoaded"
            style="width: 180px"
            placeholder="请选择集群"
            @change="onClusterChange"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select
            v-model:value="formState.status"
            style="width: 80px"
            placeholder="请选择状态"
            @change="onStatusChange"
          >
            <a-select-option value="active">启用</a-select-option>
            <a-select-option value="pending">审批</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="命名空间" name="namespace">
          <a-select
            v-model:value="formState.namespace"
            :options="namespaceOptions"
            :loading="!namespaceOptionsLoaded"
            allow-clear
            show-search
            style="width: 180px"
            placeholder="请选择命名空间"
            @change="onNamespaceChange"
          />
        </a-form-item>
        <a-form-item label="名称" name="virtualServiceName">
          <a-input-search
            v-model:value="formState.virtualServiceName"
            allow-clear
            style="width: 200px"
            enter-button
            @search="onSearch"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleAdd">
            <!--          <plus-outlined />-->
            新增路由
          </a-button>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleQuickAdd">
            <!--          <plus-outlined />-->
            快速新增
          </a-button>
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            danger
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDelete"
          >
            删除 ({{ selectedRowKeys.length }})
          </a-button>
        </a-form-item>
      </a-form>
    </a-space>
    <a-table
      :key="`table-${pagination.pageSize}-${formState.cluster}-${formState.status}-${formState.namespace}`"
      :columns="columns"
      :pagination="pagination"
      :data-source="dataSource"
      class="ant-table-striped"
      :loading="loading"
      row-key="name"
      size="middle"
      :bordered="true"
      :show-index-column="true"
      :scroll="{ x: 150, y: tableConfig.windowHeight - 210 }"
      :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
      :row-selection="rowSelection"
      @change="handlerTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'operation'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="handleViewDetail(record)"
            >
              详情
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1">
                    <a-button
                      type="text"
                      :loading="editLoading && editData?.name === record.name"
                      @click="handleEdit(record)"
                    >
                      编辑
                    </a-button>
                  </a-menu-item>
                  <a-menu-item v-if="record.status === 'active'" key="2">
                    <a-button type="text" @click="handleCopy(record)">
                      复制
                    </a-button>
                  </a-menu-item>
                  <a-menu-item key="3">
                    <a-button
                      style="color: red"
                      type="text"
                      @click="handleDeleteClick(record)"
                    >
                      删除
                    </a-button>
                  </a-menu-item>
                  <a-menu-item v-if="record.status === 'pending'" key="4">
                    <a-button type="text" @click="handleApprove(record)">
                      审批
                    </a-button>
                  </a-menu-item>
                  <a-menu-item key="5">
                    <a-button
                      v-if="record.status === 'active'"
                      type="text"
                      @click="handleHistory(record)"
                    >
                      历史
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">更多</a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 路由编辑器 -->
    <Drawer
      :visible="editorVisible"
      :mode="drawerMode"
      :title="drawerTitle"
      :edit-data="editData"
      :cluster="formState.cluster"
      :namespace-options="namespaceOptions"
      :gateway-options="gatewayOptions"
      :service-options="serviceOptions"
      @close="handleEditorClose"
      @submit="handleEditorSubmit"
    />

    <!-- 审批抽屉 -->
    <Approval
      v-model:visible="approvalVisible"
      :record="approvalRecord"
      :cluster="formState.cluster"
      @submit="handleApprovalSubmit"
    />

    <!-- 历史抽屉 -->
    <HistoryDrawer
      v-model:visible="historyVisible"
      :resource-info="{
        cluster: formState.cluster,
        namespace: historyRecord?.namespace || '',
        resourceType: 'virtualservice',
        resourceName: historyRecord?.name || '',
      }"
      @rollback="handleHistoryRollback"
    />

    <!-- 删除抽屉 -->
    <DeleteDrawer
      ref="deleteDrawer"
      :cluster="formState.cluster"
      @success="handleDeleteSuccess"
    />
  </a-card>
</template>

<script setup lang="ts">
import {
  getVirtualServices,
  getVirtualService,
  getNamespaces,
  getServices,
  getGateways,
} from '@/api/pilot/gateway';
import { listClusterForSelect } from '@/api/pilot/cluster';
import { reactive, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { TableColumnType } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { parseYamlToJSON } from '@/utils/yaml';
import Drawer from './drawer.vue';
import Approval from './drawer-approval.vue';
import HistoryDrawer from './drawer-history.vue';
import DeleteDrawer from './drawer-delete.vue';

const tableConfig = ref({
  windowHeight: document.documentElement.clientHeight - 40,
});

const formState = reactive({
  cluster: '',
  namespace: '',
  virtualServiceName: '',
  status: 'active',
});

const loading = ref(false);
const dataSource = ref([]);
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: any) => `共 ${total} 条数据`,
});

// 编辑器相关状态
const editorVisible = ref(false);
const drawerMode = ref<'add' | 'edit' | 'view' | 'copy'>('add');
const drawerTitle = ref('新增');
const editData = ref<any>(null);
const editLoading = ref(false);

// 审批相关状态
const approvalVisible = ref(false);
const approvalRecord = ref(null);

// 集群选项相关状态
const clusterOptions = ref([]);
const clusterOptionsLoaded = ref(false);

// 命名空间选项相关状态
const namespaceOptions = ref([]);
const namespaceOptionsLoaded = ref(false);

// 网关选项相关状态
const gatewayOptions = ref([]);

// 服务选项相关状态
const serviceOptions = ref([]);

// 删除抽屉相关状态
const deleteDrawer = ref();

// 行选择相关状态
const selectedRowKeys = ref<string[]>([]);
const selectedRows = ref<any[]>([]);

const columns: TableColumnType[] = [
  {
    title: '序号',
    width: 70,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    width: 300,
    ellipsis: true,
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '域名',
    dataIndex: 'hosts',
    key: 'hosts',
    align: 'center',
    width: 300,
    ellipsis: true,
    customRender: ({ record }) => {
      if (record.hosts && record.hosts.length > 0) {
        return record.hosts.join(', ');
      }
      return '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 100,
  },
  {
    title: '操作',
    key: 'operation',
    align: 'center',
    width: 120,
  },
];

// 加载集群列表
const loadClusterOptions = async () => {
  try {
    // console.log('开始加载集群选项...');
    const response = await listClusterForSelect({ enable: true });
    // console.log('集群API响应:', response);

    // 根据实际API响应格式解析数据
    const clusterList = response.data?.list || [];
    clusterOptions.value =
      clusterList.map((item: any) => ({
        label: item.label || item.name,
        value: item.value || item.uuid || item.name, // 使用value或uuid作为value，label或name作为label
      })) || [];

    // console.log('处理后的集群选项:', clusterOptions.value);

    // 如果有集群选项且当前没有选中的集群，默认选择第一个
    if (clusterOptions.value.length > 0 && !formState.cluster) {
      formState.cluster = clusterOptions.value[0].value;
    }
    clusterOptionsLoaded.value = true;
  } catch (error) {
    console.error('加载集群列表失败:', error);
    clusterOptionsLoaded.value = true;
  }
};

// 加载命名空间列表
const loadNamespaceOptions = async (cluster: string) => {
  if (!cluster) {
    namespaceOptions.value = [];
    namespaceOptionsLoaded.value = true;
    return;
  }

  namespaceOptionsLoaded.value = false;
  try {
    // console.log('开始加载命名空间选项，集群:', cluster);
    const response = await getNamespaces({ cluster });
    // console.log('命名空间API响应:', response);

    // 根据后端返回的格式处理数据
    // 从ListNamespaces API可以看到，后端返回的是SelectOption数组
    let namespaceList = response.data || [];
    if (Array.isArray(namespaceList)) {
      namespaceOptions.value = namespaceList.map((item: any) => ({
        label: item.label || item.value || item,
        value: item.value || item.label || item,
      }));
    } else {
      namespaceOptions.value = [];
    }

    // console.log('处理后的命名空间选项:', namespaceOptions.value);
  } catch (error) {
    console.error('加载命名空间列表失败:', error);
    namespaceOptions.value = [];
  } finally {
    namespaceOptionsLoaded.value = true;
  }
};

// 加载网关列表
const loadGatewayOptions = async (cluster: string) => {
  if (!cluster) {
    gatewayOptions.value = [];
    return;
  }

  try {
    // console.log('开始加载网关选项，集群:', cluster);
    const response = await getGateways({
      cluster,
      page: 1,
      pageSize: 1000,
    });
    // console.log('网关API响应:', response);

    // 根据API响应格式解析数据
    const gatewayList = response.data?.list || response.data || [];
    gatewayOptions.value =
      gatewayList.map((item: any) => ({
        label: `${item.name} (${item.namespace})`,
        value: item.name,
        namespace: item.namespace,
      })) || [];

    // console.log('处理后的网关选项:', gatewayOptions.value);
  } catch (error) {
    console.error('加载网关列表失败:', error);
    gatewayOptions.value = [];
  }
};

// 加载服务列表
const loadServiceOptions = async (cluster: string, namespace?: string) => {
  if (!cluster) {
    serviceOptions.value = [];
    return;
  }

  try {
    // console.log('开始加载服务选项，集群:', cluster, '命名空间:', namespace);
    const params: any = {
      cluster,
      page: 1,
      pageSize: 1000,
    };
    if (namespace) {
      params.namespace = namespace;
    }
    const response = await getServices(params);
    // console.log('服务API响应:', response);

    // 根据API响应格式解析数据
    const serviceList = response.data?.list || response.data || [];
    serviceOptions.value =
      serviceList.map((item: any) => ({
        label: `${item.name}.${item.namespace}`,
        value: `${item.name}.${item.namespace}`,
        name: item.name,
        namespace: item.namespace,
        ports: item.ports || [],
      })) || [];

    // console.log('处理后的服务选项:', serviceOptions.value);
  } catch (error) {
    console.error('加载服务列表失败:', error);
    serviceOptions.value = [];
  }
};

// 主搜索函数，参考cluster模块的doSearch实现
async function doSearch() {
  try {
    loading.value = true;

    if (!formState.cluster) {
      dataSource.value = [];
      pagination.value.total = 0;
      return;
    }

    const response = await getVirtualServices({
      cluster: formState.cluster,
      namespace: formState.namespace,
      name: formState.virtualServiceName,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      status: formState.status,
    });

    dataSource.value = response.data.list;
    pagination.value.total = response.data.total;
    // 只在后端返回pageSize时更新，不要覆盖前端设置的page
    if (response.data.pageSize) {
      pagination.value.pageSize = response.data.pageSize;
    }
  } catch (err) {
    console.error('搜索失败:', err);
    dataSource.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
}

const onClusterChange = (cluster: string) => {
  // 清空namespace选择
  formState.namespace = '';
  // 重置分页到第一页
  pagination.value.page = 1;
  // 重新加载命名空间选项
  loadNamespaceOptions(cluster);
  // 重新加载网关选项
  loadGatewayOptions(cluster);
  // 重新加载服务选项
  loadServiceOptions(cluster);
  // 执行搜索
  doSearch();
};

// 状态筛选变化时重置分页
const onStatusChange = () => {
  pagination.value.page = 1;
  doSearch();
};

// 命名空间筛选变化时重置分页
const onNamespaceChange = () => {
  pagination.value.page = 1;
  doSearch();
};

// 重命名原来的onSearch为doSearch的别名，保持兼容性
const onSearch = doSearch;

const handlerTableChange = (page: { pageSize: number; current: number }) => {
  pagination.value.page = page.current;
  pagination.value.pageSize = page.pageSize;
  doSearch();
};

// 简化watch监听器，不重复调用搜索
watch(
  () => formState.cluster,
  (newCluster) => {
    if (newCluster) {
      loadNamespaceOptions(newCluster);
      loadGatewayOptions(newCluster);
      loadServiceOptions(newCluster);
    }
  },
);

onMounted(async () => {
  // 检查URL参数，如果有审批参数则自动弹出审批抽屉
  await checkApprovalParams();
  // 加载集群列表
  await loadClusterOptions();
  // 如果有默认选中的集群，加载相关数据
  if (formState.cluster) {
    await loadNamespaceOptions(formState.cluster);
    await loadGatewayOptions(formState.cluster);
    await loadServiceOptions(formState.cluster);
    await doSearch();
  }
});

// 检查URL参数并处理审批逻辑
const checkApprovalParams = async () => {
  const route = useRoute();
  const router = useRouter();

  const approval = route.query.approval as string;
  const cluster = route.query.cluster as string;
  const namespace = route.query.namespace as string;
  const name = route.query.name as string;

  if (approval === 'true' && cluster && namespace && name) {
    // 设置表单状态
    formState.cluster = cluster;
    formState.namespace = namespace;
    // 查找对应的记录并弹出审批抽屉
    const response = await getVirtualService({
      name,
      cluster,
      namespace,
    });
    await handleApprove(response.data);
    // 清除URL参数，避免刷新页面时重复触发
    const newQuery = { ...route.query };
    delete newQuery.approval;
    delete newQuery.cluster;
    delete newQuery.namespace;
    delete newQuery.name;
    router.replace({ query: newQuery });
  }
};

// 新增VirtualService
const handleAdd = () => {
  editData.value = null;
  drawerMode.value = 'add';
  drawerTitle.value = '新增';
  editorVisible.value = true;
};

// 查看详情（只读模式，直接从集群获取最新数据）
const handleViewDetail = async (record: any) => {
  editLoading.value = true;
  try {
    const response = await getVirtualService({
      cluster: formState.cluster,
      namespace: record.namespace,
      name: record.name,
    });
    editData.value = response.data;
    drawerMode.value = 'view';
    drawerTitle.value = '详情';
    editorVisible.value = true;
  } catch (error) {
    console.error('获取VirtualService详情失败:', error);
    message.error('获取详情失败');
  } finally {
    editLoading.value = false;
  }
};

// 编辑VirtualService（从K8s获取最新数据）
const handleEdit = async (record: any) => {
  editLoading.value = true;
  try {
    const response = await getVirtualService({
      cluster: formState.cluster,
      namespace: record.namespace,
      name: record.name,
      fallback: true,
    });
    editData.value = response.data;
    drawerMode.value = 'edit';
    drawerTitle.value = '编辑';
    editorVisible.value = true;
  } catch (error) {
    console.error('获取VirtualService数据失败:', error);
  } finally {
    editLoading.value = false;
  }
};

// 复制VirtualService
const handleCopy = async (record: any) => {
  const response = await getVirtualService({
    name: record.name,
    cluster: formState.cluster,
    namespace: record.namespace,
  });
  editData.value = response.data;
  debugger;
  editData.value.name += '-copy';
  drawerMode.value = 'copy';
  drawerTitle.value = '复制';
  editorVisible.value = true;
};

// 点击删除按钮，打开删除抽屉
const handleDeleteClick = (record: any) => {
  deleteDrawer.value?.open(record);
};

// 删除成功回调
const handleDeleteSuccess = async () => {
  // 清空选中状态
  selectedRowKeys.value = [];
  selectedRows.value = [];
  await doSearch(); // 重新加载数据
};

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[], rows: any[]) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
  },
  onSelectAll: (selected: boolean, rows: any[], changeRows: any[]) => {
    console.log('onSelectAll', selected, rows, changeRows);
  },
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请选择要删除的VirtualService');
    return;
  }
  deleteDrawer.value?.open(selectedRows.value);
};

// 审批VirtualService
const handleApprove = async (record: any) => {
  // console.log('handleApprove called with record:', record);
  try {
    // 检查记录状态
    if (!record || record.status !== 'pending') {
      message.warning('只有待审批状态的VirtualService可以进行审批操作');
      return;
    }

    approvalRecord.value = record;
    approvalVisible.value = true;
  } catch (error) {
    console.error('审批操作失败:', error);
    message.error('审批操作失败');
  }
};

// 审批提交
const handleApprovalSubmit = async (success: boolean) => {
  if (success) {
    // 重新加载数据
    await doSearch();
  }
};

// 编辑器关闭
const handleEditorClose = () => {
  editorVisible.value = false;
  editData.value = null;
  drawerMode.value = 'add';
  drawerTitle.value = '新增';
};

// 编辑器提交
const handleEditorSubmit = async (data: any) => {
  console.log('提交VirtualService配置:', data);
  try {
    if (drawerMode.value === 'edit') {
      // 编辑模式，调用更新API
      // 这里需要根据后端接口调用相应的API
      message.success('更新成功');
    } else {
      // 新增模式，调用创建API
      // 这里需要根据后端接口调用相应的API
      message.success('创建成功');
    }
    await doSearch(); // 重新加载数据
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  }
};

function handleQuickAdd() {
  // 快速新增：预填充一些常用配置
  editData.value = {
    name: '',
    namespace: formState.namespace || namespaceOptions.value[0]?.value || '',
    hosts: ['*'],
    gateways: [],
    http: [
      {
        match: [
          {
            pathType: 'prefix',
            path: '/',
            method: [],
            headers: [],
            queryParams: [],
            scheme: '',
          },
        ],
        route: [
          {
            destination: {
              host: '',
              port: {
                number: 8080,
              },
              subset: '',
            },
            weight: 100,
          },
        ],
        rewrite: { uri: '', authority: '' },
        redirect: { uri: '', authority: '', redirectCode: 301 },
        fault: {
          delay: { percentage: 0, fixedDelay: '' },
          abort: { percentage: 0, httpStatus: 500 },
        },
        mirror: { host: '', subset: '', percentage: 0 },
        retries: { attempts: 3, perTryTimeout: '', retryOn: '' },
        timeout: '',
        headers: {
          request: { set: {}, add: {}, remove: [] },
          response: { set: {}, add: {}, remove: [] },
        },
        corsPolicy: {
          allowOrigins: [],
          allowMethods: [],
          allowHeaders: [],
          exposeHeaders: [],
          maxAge: '',
          allowCredentials: false,
        },
        expanded: true,
      },
    ],
  };
  drawerMode.value = 'add';
  drawerTitle.value = '新增';
  editorVisible.value = true;
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'orange',
    active: 'green',
    failed: 'red',
    rejected: 'red',
  };
  return statusMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '启用',
    pending: '审批',
    failed: '失败',
    rejected: '驳回',
  };
  return statusMap[status] || '未知';
};

// 历史相关状态
const historyVisible = ref(false);
const historyRecord = ref<any>(null);
// 查看历史
const handleHistory = (record: any) => {
  historyRecord.value = record;
  historyVisible.value = true;
};

// 处理历史回滚
const handleHistoryRollback = async (version: any) => {
  try {
    // 检查版本数据
    if (!version || !version.content) {
      message.error('历史版本数据不完整');
      return;
    }

    // 解析历史版本的 YAML 配置
    let parsedConfig;
    try {
      parsedConfig = parseYamlToJSON(version.content);
      if (!parsedConfig) {
        message.error('历史版本配置解析失败');
        return;
      }
    } catch (yamlError) {
      console.error('解析历史版本YAML失败:', yamlError);
      message.error('历史版本配置格式错误');
      return;
    }

    // 验证配置格式
    if (!parsedConfig.metadata || !parsedConfig.spec) {
      message.error('历史版本配置格式不正确，缺少必要的字段');
      return;
    }

    // 设置编辑数据为历史版本配置
    editData.value = parsedConfig;
    drawerMode.value = 'edit';
    drawerTitle.value = `回滚到版本 v${version.version}`;
    editorVisible.value = true;

    // 关闭历史抽屉
    historyVisible.value = false;

    message.success(`已加载版本 v${version.version} 的配置，请检查并提交审批`);
  } catch (error) {
    console.error('历史回滚失败:', error);
    message.error('回滚失败');
  }
};
</script>

<style scoped>
.ant-card {
  height: 100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
:deep(.pilot-tag) {
  line-height: 16px !important;
}

.approval-content {
  height: 100%;
  overflow-y: auto;
}

:deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.ant-tabs-content-holder) {
  height: calc(100vh - 300px);
}

:deep(.ant-tabs-tabpane) {
  height: 100%;
}
</style>
