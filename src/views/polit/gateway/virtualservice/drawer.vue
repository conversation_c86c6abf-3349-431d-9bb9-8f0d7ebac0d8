<template>
  <a-drawer
    :title="props.title"
    :open="visible"
    width="68%"
    :closable="true"
    :body-style="{ paddingTop: '0!important' }"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleClose">取消</a-button>
        <a-tooltip
          v-if="!(props.mode === 'view') && !isAllRulesValid"
          title="请先修复路由规则配置问题"
          placement="bottom"
        >
          <a-button
            type="default"
            :loading="dryRunLoading"
            :disabled="!isAllRulesValid"
            @click="handleDryRun"
          >
            <!--          <experiment-outlined />-->
            试跑
          </a-button>
        </a-tooltip>
        <a-button
          v-else-if="!(props.mode === 'view')"
          type="default"
          :loading="dryRunLoading"
          @click="handleDryRun"
        >
          <!--          <experiment-outlined />-->
          试跑
        </a-button>
        <a-button
          v-if="!(props.mode === 'view') && !(props.mode === 'add')"
          type="default"
          @click="handleCompare"
        >
          对比
        </a-button>
        <a-tooltip
          v-if="!(props.mode === 'view') && !isAllRulesValid"
          title="请先修复路由规则配置问题"
          placement="bottom"
        >
          <a-button
            type="primary"
            :loading="submitLoading"
            :disabled="!isAllRulesValid"
            @click="handleSubmit"
          >
            提交
          </a-button>
        </a-tooltip>
        <a-button
          v-else-if="!(props.mode === 'view')"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </a-button>
      </a-space>
    </template>
    <div class="virtual-service-editor">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- 基本信息Tab -->
        <a-tab-pane key="basic" tab="基本信息">
          <a-form :model="formData">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item
                  label="配置名称"
                  name="name"
                  :rules="[{ required: true, message: '请输入名称' }]"
                >
                  <a-input
                    v-model:value="formData.name"
                    :disabled="props.mode === 'view' || props.mode === 'edit'"
                    placeholder="请输入流量路由名称"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="命名空间"
                  name="namespace"
                  :rules="[{ required: true, message: '请选择命名空间' }]"
                >
                  <a-select
                    v-model:value="formData.namespace"
                    show-search
                    :disabled="props.mode === 'view' || props.mode === 'edit'"
                    placeholder="请选择命名空间"
                  >
                    <a-select-option
                      v-for="ns in namespaceOptions"
                      :key="ns.value"
                      :value="ns.value"
                    >
                      {{ ns.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="所属网关"
                  name="gateways"
                  :rules="[{ required: true, message: '请选择网关' }]"
                >
                  <a-select
                    v-model:value="formData.gateways"
                    mode="multiple"
                    placeholder="请选择网关"
                    :disabled="props.mode === 'view'"
                  >
                    <a-select-option
                      v-for="gw in gatewayOptions"
                      :key="gw.value"
                      :value="gw.value"
                    >
                      {{ gw.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item
                  label="主机域名"
                  name="hosts"
                  :rules="[{ required: true, message: '请输入主机域名' }]"
                >
                  <a-select
                    v-model:value="formData.hosts"
                    mode="tags"
                    placeholder="请输入主机域名，支持多个"
                    :disabled="props.mode === 'view'"
                  >
                    <template #suffixIcon>
                      <plus-outlined />
                    </template>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>

        <!-- 路由规则Tab -->
        <a-tab-pane key="routes" tab="路由规则">
          <div class="route-rules-container">
            <div class="route-rules">
              <div class="rule-header">
                <div class="header-left">
                  <span class="header-title">路由规则</span>
                  <span class="header-count">
                    {{ formData.http.length }} 个规则
                  </span>
                  <a-tag
                    v-if="formData.http.length > 0 && !isAllRulesValid"
                    color="orange"
                    size="small"
                  >
                    存在配置问题
                  </a-tag>
                </div>
                <a-button
                  :disabled="props.mode === 'view'"
                  type="primary"
                  @click="addHttpRule"
                >
                  添加规则
                </a-button>
              </div>

              <div v-if="formData.http.length === 0" class="empty-rules">
                <a-empty description="暂无路由规则">
                  <a-button type="primary" @click="addHttpRule">
                    添加第一个规则
                  </a-button>
                </a-empty>
              </div>

              <!-- 路由规则列表展示 -->
              <div class="rules-list">
                <div
                  v-for="(rule, index) in formData.http"
                  :key="index"
                  class="rule-item-wrapper"
                >
                  <div class="rule-card-modern">
                    <div class="rule-header-modern">
                      <div class="rule-left">
                        <div class="rule-order">
                          <span class="rule-number">{{ index + 1 }}</span>
                        </div>
                        <div class="rule-info">
                          <div class="rule-title-modern">
                            <span class="rule-name">路由规则</span>
                            <a-tag
                              v-if="rule.match?.[0]?.path"
                              color="blue"
                              size="small"
                            >
                              {{ rule.match[0].pathType }}:
                              {{ rule.match[0].path }}
                            </a-tag>
                            <a-tag
                              v-if="!isRuleValid(rule)"
                              color="red"
                              size="small"
                            >
                              配置错误
                            </a-tag>
                          </div>
                          <div class="rule-description">
                            <span class="match-count">
                              {{ rule.match?.length || 0 }} 个匹配条件
                            </span>
                            <span class="separator">·</span>
                            <span class="route-count">
                              {{ rule.route?.filter((r: any) => r.destination?.host?.trim()).length || 0 }}
                              个目标服务
                            </span>
                            <span
                              v-if="
                                rule.rewrite?.uri || rule.rewrite?.authority
                              "
                              class="separator"
                            >
                              ·
                            </span>
                            <span
                              v-if="
                                rule.rewrite?.uri || rule.rewrite?.authority
                              "
                              class="rewrite-indicator"
                            >
                              包含重写规则
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="rule-actions">
                        <a-space size="small">
                          <a-button
                            type="text"
                            size="small"
                            :disabled="index === 0 || props.mode === 'view'"
                            @click="moveRule(index, 'up')"
                          >
                            上移
                          </a-button>
                          <a-button
                            type="text"
                            size="small"
                            :disabled="
                              index === formData.http.length - 1 ||
                              props.mode === 'view'
                            "
                            @click="moveRule(index, 'down')"
                          >
                            下移
                          </a-button>
                          <a-divider type="vertical" />
                          <a-button
                            v-if="!(props.mode === 'view')"
                            type="text"
                            size="small"
                            @click="editRule(index)"
                          >
                            编辑
                          </a-button>
                          <a-button
                            v-if="!(props.mode === 'view')"
                            type="text"
                            size="small"
                            @click="copyRule(index)"
                          >
                            复制
                          </a-button>
                          <a-button
                            v-if="!(props.mode === 'view')"
                            type="text"
                            size="small"
                            danger
                            @click="removeHttpRule(index)"
                          >
                            删除
                          </a-button>
                        </a-space>
                      </div>
                    </div>

                    <div class="rule-content-modern">
                      <div class="rule-flow">
                        <!-- 匹配条件 -->
                        <div class="flow-item">
                          <div class="flow-label">匹配</div>
                          <div class="flow-content">
                            <div
                              v-for="(match, matchIndex) in rule.match"
                              :key="matchIndex"
                              class="match-item-simple"
                            >
                              <span class="path-info">
                                {{ match.pathType }}: {{ match.path }}
                              </span>
                              <span
                                v-if="match.method?.length"
                                class="method-info"
                              >
                                {{ match.method.join(',') }}
                              </span>
                            </div>
                          </div>
                        </div>

                        <!-- 箭头 -->
                        <div class="flow-arrow">→</div>

                        <!-- 目标服务 -->
                        <div
                          v-if="rule.route?.some((r: any) => r.destination?.host?.trim())"
                          class="flow-item"
                        >
                          <div class="flow-label">转发</div>
                          <div class="flow-content">
                            <div
                              v-for="(destination, destIndex) in rule.route.filter((r: any) => r.destination?.host?.trim())"
                              :key="destIndex"
                              class="destination-item-simple"
                            >
                              <span class="service-info">
                                {{ destination.destination.host }}:{{
                                  destination.destination.port.number
                                }}
                              </span>
                              <span
                                v-if="destination.weight !== 100"
                                class="weight-info"
                              >
                                {{ destination.weight }}%
                              </span>
                            </div>
                          </div>
                        </div>

                        <!-- 重定向 -->
                        <div
                          v-if="rule.redirect?.uri || rule.redirect?.authority"
                          class="flow-item"
                        >
                          <div class="flow-label">重定向</div>
                          <div class="flow-content">
                            <div class="destination-item-simple">
                              <span class="service-info">
                                {{
                                  rule.redirect.uri || rule.redirect.authority
                                }}
                              </span>
                              <span
                                v-if="
                                  rule.redirect.redirectCode &&
                                  rule.redirect.redirectCode !== 301
                                "
                                class="weight-info"
                              >
                                {{ rule.redirect.redirectCode }}
                              </span>
                            </div>
                          </div>
                        </div>

                        <!-- 重写规则 -->
                        <template
                          v-if="rule.rewrite?.uri || rule.rewrite?.authority"
                        >
                          <div class="flow-arrow">→</div>
                          <div class="flow-item">
                            <div class="flow-label">重写</div>
                            <div class="flow-content">
                              <div
                                v-if="rule.rewrite.uri"
                                class="rewrite-item-simple"
                              >
                                URI: {{ rule.rewrite.uri }}
                              </div>
                              <div
                                v-if="rule.rewrite.authority"
                                class="rewrite-item-simple"
                              >
                                Authority: {{ rule.rewrite.authority }}
                              </div>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 规则编辑Drawer -->
          <a-drawer
            :title="
              currentRuleIndex === -1 ? '新增' : `编辑 ${currentRuleIndex + 1}`
            "
            :open="ruleDrawerVisible"
            width="65%"
            :body-style="{
              paddingBottom: '2px',
              paddingTop: '0!important',
              paddingRight: '16px',
            }"
            @close="closeRuleDrawer"
          >
            <template #extra>
              <a-space>
                <a-button @click="closeRuleDrawer">取消</a-button>
                <a-button
                  type="primary"
                  :disabled="
                    !currentRule.match?.length || !currentRule.route?.length
                  "
                  @click="saveRule"
                >
                  保存
                </a-button>
              </a-space>
            </template>

            <div class="rule-editor-container">
              <a-tabs
                v-model:activeKey="ruleEditorTab"
                class="rule-editor-tabs"
              >
                <!-- 基础配置Tab -->
                <a-tab-pane key="basic" tab="基础配置">
                  <!-- 匹配条件 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">匹配条件</h3>
                      <a-button
                        type="primary"
                        size="small"
                        @click="addMatchToCurrent"
                      >
                        <plus-outlined />
                        添加条件
                      </a-button>
                    </div>

                    <div class="section-content">
                      <div
                        v-for="(match, matchIndex) in currentRule.match"
                        :key="matchIndex"
                        class="match-card"
                      >
                        <div class="card-header">
                          <span class="card-title">
                            条件 {{ matchIndex + 1 }}
                          </span>
                          <div class="header-actions">
                            <div class="add-actions">
                              <a-button
                                type="text"
                                size="small"
                                class="add-btn"
                                @click="addHeaderToCurrent(matchIndex)"
                              >
                                <plus-outlined />
                                请求头
                              </a-button>
                              <a-button
                                type="text"
                                size="small"
                                class="add-btn"
                                @click="addQueryParamToCurrent(matchIndex)"
                              >
                                <plus-outlined />
                                参数
                              </a-button>
                            </div>
                            <a-button
                              type="text"
                              size="small"
                              danger
                              @click="removeMatchFromCurrent(matchIndex)"
                            >
                              <DeleteOutlined />
                            </a-button>
                          </div>
                        </div>

                        <div class="card-body">
                          <!-- 基础匹配 -->
                          <div class="form-group">
                            <a-row :gutter="12">
                              <a-col :span="6">
                                <a-form-item
                                  label="匹配类型"
                                  class="compact-form-item"
                                >
                                  <a-select
                                    v-model:value="match.pathType"
                                    @change="onPathTypeChange(-1, matchIndex)"
                                  >
                                    <a-select-option value="prefix">
                                      前缀匹配
                                    </a-select-option>
                                    <a-select-option value="exact">
                                      精确匹配
                                    </a-select-option>
                                    <a-select-option value="regex">
                                      正则匹配
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col :span="8">
                                <a-form-item
                                  label="路径"
                                  class="compact-form-item"
                                >
                                  <a-input
                                    v-model:value="match.path"
                                    placeholder="如: /api/v1"
                                  />
                                </a-form-item>
                              </a-col>
                              <a-col :span="5">
                                <a-form-item
                                  label="请求方法"
                                  class="compact-form-item"
                                >
                                  <a-select
                                    v-model:value="match.method"
                                    mode="multiple"
                                    placeholder="选择方法"
                                    :max-tag-count="2"
                                  >
                                    <a-select-option value="GET">
                                      GET
                                    </a-select-option>
                                    <a-select-option value="POST">
                                      POST
                                    </a-select-option>
                                    <a-select-option value="PUT">
                                      PUT
                                    </a-select-option>
                                    <a-select-option value="DELETE">
                                      DELETE
                                    </a-select-option>
                                    <a-select-option value="PATCH">
                                      PATCH
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col :span="5">
                                <a-form-item
                                  label="协议"
                                  class="compact-form-item"
                                >
                                  <a-select
                                    v-model:value="match.scheme"
                                    placeholder="选择协议"
                                    allow-clear
                                  >
                                    <a-select-option value="http">
                                      HTTP
                                    </a-select-option>
                                    <a-select-option value="https">
                                      HTTPS
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </div>

                          <!-- 请求头匹配 -->
                          <div
                            v-if="match.headers?.length > 0"
                            class="headers-group"
                          >
                            <!-- <div class="group-title">请求头匹配</div> -->
                            <div
                              v-for="(header, headerIndex) in match.headers"
                              :key="headerIndex"
                              class="header-row"
                            >
                              <a-row :gutter="20">
                                <a-col :span="8">
                                  <a-form-item
                                    label="Header名称"
                                    class="compact-form-item"
                                  >
                                    <a-input
                                      v-model:value="header.name"
                                      placeholder="Header名称"
                                      size="small"
                                    />
                                  </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                  <a-form-item
                                    label="匹配类型"
                                    class="compact-form-item"
                                  >
                                    <a-select
                                      v-model:value="header.matchType"
                                      size="small"
                                    >
                                      <a-select-option value="exact">
                                        精确匹配
                                      </a-select-option>
                                      <a-select-option value="regex">
                                        正则匹配
                                      </a-select-option>
                                    </a-select>
                                  </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                  <a-form-item
                                    label="Header值"
                                    class="compact-form-item"
                                  >
                                    <a-input
                                      v-model:value="header.value"
                                      placeholder="Header值"
                                      size="small"
                                    />
                                  </a-form-item>
                                </a-col>
                                <a-col :span="2">
                                  <a-form-item
                                    :colon="false"
                                    label=" "
                                    class="compact-form-item"
                                  >
                                    <a-button
                                      type="text"
                                      size="small"
                                      danger
                                      @click="
                                        removeHeaderFromCurrent(
                                          matchIndex,
                                          headerIndex,
                                        )
                                      "
                                    >
                                      <DeleteOutlined />
                                    </a-button>
                                  </a-form-item>
                                </a-col>
                              </a-row>
                            </div>
                          </div>

                          <!-- 查询参数匹配 -->
                          <div
                            v-if="match.queryParams?.length > 0"
                            class="query-params-group"
                          >
                            <div
                              v-for="(param, paramIndex) in match.queryParams"
                              :key="paramIndex"
                              class="param-row"
                            >
                              <a-row :gutter="20">
                                <a-col :span="8">
                                  <a-form-item
                                    label="参数名称"
                                    class="compact-form-item"
                                  >
                                    <a-input
                                      v-model:value="param.name"
                                      placeholder="参数名称"
                                      size="small"
                                    />
                                  </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                  <a-form-item
                                    label="匹配类型"
                                    class="compact-form-item"
                                  >
                                    <a-select
                                      v-model:value="param.matchType"
                                      size="small"
                                    >
                                      <a-select-option value="exact">
                                        精确匹配
                                      </a-select-option>
                                      <a-select-option value="regex">
                                        正则匹配
                                      </a-select-option>
                                    </a-select>
                                  </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                  <a-form-item
                                    label="参数值"
                                    class="compact-form-item"
                                  >
                                    <a-input
                                      v-model:value="param.value"
                                      placeholder="参数值"
                                      size="small"
                                    />
                                  </a-form-item>
                                </a-col>
                                <a-col :span="2">
                                  <a-form-item
                                    :colon="false"
                                    label=" "
                                    class="compact-form-item"
                                  >
                                    <a-button
                                      type="text"
                                      size="small"
                                      danger
                                      @click="
                                        removeQueryParamFromCurrent(
                                          matchIndex,
                                          paramIndex,
                                        )
                                      "
                                    >
                                      <DeleteOutlined />
                                    </a-button>
                                  </a-form-item>
                                </a-col>
                              </a-row>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 目标服务 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">目标服务</h3>
                      <a-button
                        type="primary"
                        size="small"
                        @click="addDestinationToCurrent"
                      >
                        <plus-outlined />
                        添加服务
                      </a-button>
                    </div>

                    <div class="section-content">
                      <div
                        v-for="(destination, destIndex) in currentRule.route"
                        :key="destIndex"
                        class="destination-card"
                      >
                        <div class="card-header">
                          <span class="card-title">
                            服务 {{ destIndex + 1 }}
                          </span>
                          <a-button
                            type="text"
                            size="small"
                            danger
                            @click="removeDestinationFromCurrent(destIndex)"
                          >
                            <DeleteOutlined />
                          </a-button>
                        </div>

                        <div class="card-body">
                          <a-row :gutter="24">
                            <a-col :span="7">
                              <a-form-item
                                label="服务"
                                class="compact-form-item"
                              >
                                <a-select
                                  v-model:value="destination.destination.host"
                                  placeholder="选择服务"
                                  show-search
                                  style="max-width: 185px"
                                >
                                  <a-select-option
                                    v-for="svc in serviceOptions"
                                    :key="svc.value"
                                    :value="svc.value"
                                  >
                                    {{ svc.label }}
                                  </a-select-option>
                                </a-select>
                              </a-form-item>
                            </a-col>
                            <a-col :span="5">
                              <a-form-item
                                label="端口"
                                class="compact-form-item"
                              >
                                <a-input-number
                                  v-model:value="
                                    destination.destination.port.number
                                  "
                                  placeholder="80"
                                  :min="1"
                                  :max="65535"
                                  style="width: 85px"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="5">
                              <a-form-item
                                label="权重"
                                class="compact-form-item"
                              >
                                <a-input-number
                                  v-model:value="destination.weight"
                                  :min="0"
                                  :max="100"
                                  placeholder="100"
                                  style="width: 65px"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="7">
                              <a-form-item
                                label="子集"
                                class="compact-form-item"
                              >
                                <a-input
                                  v-model:value="destination.destination.subset"
                                  style="width: 150px"
                                  placeholder="可选"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 路径重写 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">路径重写</h3>
                      <span class="section-subtitle">可选配置</span>
                    </div>

                    <div class="section-content">
                      <div class="rewrite-card">
                        <div class="card-body">
                          <a-row :gutter="16">
                            <a-col :span="12">
                              <a-form-item
                                label="重写路径"
                                class="compact-form-item"
                              >
                                <a-input
                                  v-model:value="currentRule.rewrite.uri"
                                  placeholder="如: /new-path"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="12">
                              <a-form-item
                                label="重写Host"
                                class="compact-form-item"
                              >
                                <a-input
                                  v-model:value="currentRule.rewrite.authority"
                                  placeholder="如: new-host.com"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-tab-pane>

                <!-- 高级配置Tab -->
                <a-tab-pane key="advanced" tab="高级配置">
                  <!-- 重定向 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">重定向</h3>
                      <span class="section-subtitle">
                        可选配置，与目标服务互斥
                      </span>
                    </div>

                    <div class="section-content">
                      <div class="redirect-card">
                        <div class="card-body">
                          <a-row :gutter="16">
                            <a-col :span="10">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    路径
                                    <a-tooltip title="重定向的目标路径">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="currentRule.redirect.uri"
                                  placeholder="/redirect-path"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="9">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    Host
                                    <a-tooltip title="重定向的目标主机">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="currentRule.redirect.authority"
                                  placeholder="redirect-host.com"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="5">
                              <a-form-item
                                label="状态码"
                                class="compact-form-item"
                              >
                                <a-select
                                  v-model:value="
                                    currentRule.redirect.redirectCode
                                  "
                                >
                                  <a-select-option :value="301">
                                    301
                                  </a-select-option>
                                  <a-select-option :value="302">
                                    302
                                  </a-select-option>
                                  <a-select-option :value="307">
                                    307
                                  </a-select-option>
                                  <a-select-option :value="308">
                                    308
                                  </a-select-option>
                                </a-select>
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 流量镜像 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">流量镜像</h3>
                      <span class="section-subtitle">复制流量到测试环境</span>
                    </div>

                    <div class="section-content">
                      <div class="mirror-card">
                        <div class="card-body">
                          <a-row :gutter="16">
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    服务
                                    <a-tooltip title="镜像流量的目标服务">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-select
                                  v-model:value="currentRule.mirror.host"
                                  placeholder="选择服务"
                                  show-search
                                >
                                  <a-select-option
                                    v-for="svc in serviceOptions"
                                    :key="svc.value"
                                    :value="svc.value"
                                  >
                                    {{ svc.label }}
                                  </a-select-option>
                                </a-select>
                              </a-form-item>
                            </a-col>
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    子集
                                    <a-tooltip title="服务子集名称（可选）">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="currentRule.mirror.subset"
                                  placeholder="可选"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    比例
                                    <a-tooltip title="镜像流量的百分比">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input-number
                                  v-model:value="currentRule.mirror.percentage"
                                  :min="0"
                                  :max="100"
                                  style="width: 100%"
                                  placeholder="0"
                                  addon-after="%"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 重试策略 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">重试策略</h3>
                      <span class="section-subtitle">提高服务可靠性</span>
                    </div>

                    <div class="section-content">
                      <div class="retry-card">
                        <div class="card-body">
                          <a-row :gutter="16">
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    次数
                                    <a-tooltip title="最大重试次数">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input-number
                                  v-model:value="currentRule.retries.attempts"
                                  :min="0"
                                  :max="10"
                                  style="width: 100%"
                                  placeholder="3"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    超时
                                    <a-tooltip title="每次重试的超时时间">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="
                                    currentRule.retries.perTryTimeout
                                  "
                                  placeholder="2s"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    条件
                                    <a-tooltip
                                      title="触发重试的条件，如: 5xx,reset,connect-failure"
                                    >
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="currentRule.retries.retryOn"
                                  placeholder="5xx,reset"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 故障注入 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">故障注入</h3>
                      <span class="section-subtitle">用于测试和混沌工程</span>
                    </div>

                    <div class="section-content">
                      <div class="fault-card">
                        <div class="card-body">
                          <a-row :gutter="24" align="middle">
                            <a-col :xs="24" :sm="12" :md="6">
                              <a-form-item
                                class="inline-form-item"
                                :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 16 }"
                              >
                                <template #label>
                                  <span>
                                    延迟
                                    <a-tooltip title="延迟时间，如: 5s, 100ms">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="
                                    currentRule.fault.delay.fixedDelay
                                  "
                                  placeholder="5s"
                                  class="form-control"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :xs="24" :sm="12" :md="6">
                              <a-form-item
                                class="inline-form-item"
                                :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 16 }"
                              >
                                <template #label>
                                  <span>
                                    比例
                                    <a-tooltip title="受影响的请求百分比">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input-number
                                  v-model:value="
                                    currentRule.fault.delay.percentage
                                  "
                                  :min="0"
                                  :max="100"
                                  placeholder="0"
                                  addon-after="%"
                                  class="form-control"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :xs="24" :sm="12" :md="6">
                              <a-form-item
                                class="inline-form-item"
                                :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 16 }"
                              >
                                <template #label>
                                  <span>
                                    状态码
                                    <a-tooltip title="注入的HTTP错误状态码">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input-number
                                  v-model:value="
                                    currentRule.fault.abort.httpStatus
                                  "
                                  :min="400"
                                  :max="599"
                                  placeholder="500"
                                  class="form-control"
                                />
                              </a-form-item>
                            </a-col>
                            <a-col :xs="24" :sm="12" :md="6">
                              <a-form-item
                                class="inline-form-item"
                                :label-col="{ span: 8 }"
                                :wrapper-col="{ span: 16 }"
                              >
                                <template #label>
                                  <span>
                                    比例
                                    <a-tooltip title="受影响的请求百分比">
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input-number
                                  v-model:value="
                                    currentRule.fault.abort.percentage
                                  "
                                  :min="0"
                                  :max="100"
                                  placeholder="0"
                                  addon-after="%"
                                  class="form-control"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 超时设置 -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3 class="section-title">超时设置</h3>
                      <span class="section-subtitle">请求超时时间</span>
                    </div>

                    <div class="section-content">
                      <div class="timeout-card">
                        <div class="card-body">
                          <a-row :gutter="16">
                            <a-col :span="8">
                              <a-form-item class="compact-form-item">
                                <template #label>
                                  <span>
                                    超时
                                    <a-tooltip
                                      title="请求超时时间，如: 30s, 5m"
                                    >
                                      <question-circle-outlined
                                        class="field-help-icon"
                                      />
                                    </a-tooltip>
                                  </span>
                                </template>
                                <a-input
                                  v-model:value="currentRule.timeout"
                                  placeholder="30s"
                                />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-drawer>
        </a-tab-pane>

        <!-- YAML编辑Tab -->
        <a-tab-pane key="yaml" tab="代码编辑">
          <div class="yaml-editor">
            <div class="yaml-editor-content">
              <code-editor
                v-model="yamlContent"
                :show-expand-btn="false"
                :readonly="props.mode === 'view'"
                :minimap="true"
                :height="editorHeight"
                :initial-shell-type="'yaml'"
                :label="'YAML配置'"
                :placeholder="'请输入YAML配置'"
              />
            </div>
            <div v-if="yamlError" class="yaml-error">
              <a-alert
                type="error"
                :message="yamlError"
                show-icon
                closable
                @close="yamlError = ''"
              />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 对比抽屉 -->
    <CompareDrawer
      v-model:visible="compareDrawerVisible"
      :cluster="props.cluster"
      :namespace="formData.namespace"
      :name="formData.name"
      :current-yaml="yamlContent"
      @sync="handleCompareSync"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons-vue';
import * as yaml from 'js-yaml';
import CodeEditor from '@c/code-editor/index.vue';
import CompareDrawer from './drawer-compare.vue';
import {
  createVirtualService,
  updateVirtualService,
  dryRunVirtualService,
} from '@/api/pilot/gateway';

interface Props {
  visible: boolean;
  mode: 'add' | 'edit' | 'view' | 'copy';
  title: string;
  editData?: any;
  cluster: string;
  namespaceOptions: Array<{ label: string; value: string }>;
  gatewayOptions: Array<{ label: string; value: string }>;
  serviceOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'close'): void;
  (e: 'submit', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 使用props中的数据
const namespaceOptions = computed(() => props.namespaceOptions);
const gatewayOptions = computed(() => props.gatewayOptions);
const serviceOptions = computed(() => props.serviceOptions);

// 检查所有规则是否有效
const isAllRulesValid = computed(() => {
  if (!formData.http.length) {
    return true;
  }
  return formData.http.every((rule: any) => isRuleValid(rule));
});

const dryRunLoading = ref(false);
const submitLoading = ref(false);
const activeTab = ref('basic');
const yamlContent = ref('');
const yamlError = ref('');
const previousTab = ref('basic');
const currentRuleIndex = ref(-1);
const ruleDrawerVisible = ref(false);
const ruleEditorTab = ref('basic');

// 对比相关状态
const compareDrawerVisible = ref(false);

// 动态计算编辑器高度
const editorHeight = computed(() => {
  return `${document.documentElement.clientHeight - 320}px`;
});
// 表单数据
const formData = reactive({
  name: '',
  namespace: '',
  hosts: [],
  gateways: [],
  http: [],
  yaml: '',
});

// 默认值常量
const DEFAULT_VALUES = {
  weight: 100,
  retriesAttempts: 3,
  portNumber: 80,
  redirectCode: 301,
  faultDelayPercentage: 0,
  faultAbortPercentage: 0,
  faultAbortHttpStatus: 500,
  mirrorPercentage: 0,
  timeout: '',
  perTryTimeout: '',
  retryOn: '',
} as const;

// 检查是否为默认值
const isDefaultValue = (value: any, defaultValue: any): boolean => {
  if (value === undefined || value === null || value === '') {
    return true;
  }
  return value === defaultValue;
};

// 初始化目标服务模板
const createDestination = () => ({
  destination: {
    host: '',
    port: {
      number: DEFAULT_VALUES.portNumber,
    },
    subset: '',
  },
  weight: DEFAULT_VALUES.weight,
});

// 初始化查询参数匹配模板
const createQueryParam = () => ({
  name: '',
  matchType: 'exact',
  value: '',
});

// 初始化请求头匹配模板
const createHeader = () => ({
  name: '',
  matchType: 'exact',
  value: '',
});

// 初始化HTTP规则模板
const createHttpRule = () => ({
  match: [createMatchRule()],
  route: [createDestination()],
  rewrite: {
    uri: '',
    authority: '',
  },
  redirect: {
    uri: '',
    authority: '',
    redirectCode: DEFAULT_VALUES.redirectCode,
  },
  fault: {
    delay: {
      percentage: DEFAULT_VALUES.faultDelayPercentage,
      fixedDelay: '',
    },
    abort: {
      percentage: DEFAULT_VALUES.faultAbortPercentage,
      httpStatus: DEFAULT_VALUES.faultAbortHttpStatus,
    },
  },
  mirror: {
    host: '',
    subset: '',
    percentage: DEFAULT_VALUES.mirrorPercentage,
  },
  retries: {
    attempts: DEFAULT_VALUES.retriesAttempts,
    perTryTimeout: DEFAULT_VALUES.perTryTimeout,
    retryOn: DEFAULT_VALUES.retryOn,
  },
  timeout: DEFAULT_VALUES.timeout,
  headers: {
    request: {
      set: {} as any,
      add: {} as any,
      remove: [] as string[],
    },
    response: {
      set: {} as any,
      add: {} as any,
      remove: [] as string[],
    },
  },
  corsPolicy: {
    allowOrigins: [] as string[],
    allowMethods: [] as string[],
    allowHeaders: [] as string[],
    exposeHeaders: [] as string[],
    maxAge: '',
    allowCredentials: false,
  },
  expanded: true,
});

// 初始化匹配规则模板
const createMatchRule = () => ({
  pathType: 'prefix',
  path: '/',
  method: [] as any[],
  headers: [] as any[],
  queryParams: [] as any[],
  scheme: '',
});

// 当前编辑的规则
const currentRule = ref(createHttpRule());

// YAML预览
const yamlPreview = computed(() => {
  try {
    const virtualService = {
      apiVersion: 'networking.istio.io/v1',
      kind: 'VirtualService',
      metadata: {
        name: formData.name,
        namespace: formData.namespace,
      },
      spec: {
        gateways: formData.gateways,
        hosts: formData.hosts,
        http: formData.http.map((rule) => ({
          match: rule.match.map((match: any) => {
            const matchObj: any = {};

            // 路径匹配
            if (match.pathType === 'prefix') {
              matchObj.uri = { prefix: match.path };
            } else if (match.pathType === 'exact') {
              matchObj.uri = { exact: match.path };
            } else if (match.pathType === 'regex') {
              matchObj.uri = { regex: match.path };
            }

            // HTTP方法匹配
            if (match.method && match.method.length > 0) {
              matchObj.method = { regex: match.method.join('|') };
            }

            // 请求头匹配
            if (match.headers && match.headers.length > 0) {
              matchObj.headers = {};
              match.headers.forEach((header: any) => {
                if (header.name && header.value) {
                  matchObj.headers[header.name] = {
                    [header.matchType]: header.value,
                  };
                }
              });
            }

            // 查询参数匹配
            if (match.queryParams && match.queryParams.length > 0) {
              matchObj.queryParams = {};
              match.queryParams.forEach((param: any) => {
                if (param.name && param.value) {
                  matchObj.queryParams[param.name] = {
                    [param.matchType]: param.value,
                  };
                }
              });
            }

            // Scheme匹配
            if (match.scheme) {
              matchObj.scheme = { exact: match.scheme };
            }

            return matchObj;
          }),

          // 故障注入（按照k8s官方顺序，fault在route前面）
          ...((rule.fault.delay.fixedDelay &&
            !isDefaultValue(
              rule.fault.delay.percentage,
              DEFAULT_VALUES.faultDelayPercentage,
            )) ||
          (rule.fault.abort.httpStatus &&
            !isDefaultValue(
              rule.fault.abort.percentage,
              DEFAULT_VALUES.faultAbortPercentage,
            ))
            ? {
                fault: {
                  ...(rule.fault.delay.fixedDelay &&
                    !isDefaultValue(
                      rule.fault.delay.percentage,
                      DEFAULT_VALUES.faultDelayPercentage,
                    ) && {
                      delay: {
                        percentage: { value: rule.fault.delay.percentage },
                        fixedDelay: rule.fault.delay.fixedDelay,
                      },
                    }),
                  ...(rule.fault.abort.httpStatus &&
                    !isDefaultValue(
                      rule.fault.abort.percentage,
                      DEFAULT_VALUES.faultAbortPercentage,
                    ) && {
                      abort: {
                        percentage: { value: rule.fault.abort.percentage },
                        httpStatus: rule.fault.abort.httpStatus,
                      },
                    }),
                },
              }
            : {}),

          // 重试策略（按照k8s官方顺序，retries在route前面）
          ...(!isDefaultValue(
            rule.retries.attempts,
            DEFAULT_VALUES.retriesAttempts,
          ) ||
          rule.retries.perTryTimeout ||
          rule.retries.retryOn
            ? {
                retries: {
                  ...(!isDefaultValue(
                    rule.retries.attempts,
                    DEFAULT_VALUES.retriesAttempts,
                  ) && {
                    attempts: rule.retries.attempts,
                  }),
                  ...(rule.retries.perTryTimeout && {
                    perTryTimeout: rule.retries.perTryTimeout,
                  }),
                  ...(rule.retries.retryOn && {
                    retryOn: rule.retries.retryOn,
                  }),
                },
              }
            : {}),

          // 超时设置（按照k8s官方顺序，timeout在route前面）
          ...(rule.timeout && { timeout: rule.timeout }),

          // 流量镜像（按照k8s官方顺序，mirror在route前面）
          ...(rule.mirror.host &&
          !isDefaultValue(
            rule.mirror.percentage,
            DEFAULT_VALUES.mirrorPercentage,
          )
            ? {
                mirror: {
                  host: rule.mirror.host,
                  ...(rule.mirror.subset && { subset: rule.mirror.subset }),
                  percentage: { value: rule.mirror.percentage },
                },
              }
            : {}),

          // 路径重写（按照k8s官方顺序，rewrite在route前面）
          ...(rule.rewrite.uri && {
            rewrite: {
              uri: rule.rewrite.uri,
              ...(rule.rewrite.authority && {
                authority: rule.rewrite.authority,
              }),
            },
          }),

          // 重定向（与路由目标互斥，按照k8s官方顺序在route前面）
          ...(rule.redirect?.uri || rule.redirect?.authority
            ? {
                redirect: {
                  ...(rule.redirect.uri && { uri: rule.redirect.uri }),
                  ...(rule.redirect.authority && {
                    authority: rule.redirect.authority,
                  }),
                  ...(!isDefaultValue(
                    rule.redirect.redirectCode,
                    DEFAULT_VALUES.redirectCode,
                  ) && {
                    redirectCode: rule.redirect.redirectCode,
                  }),
                },
              }
            : {}),

          // 路由目标（按照k8s官方顺序，route在最后）
          ...(rule.redirect?.uri || rule.redirect?.authority
            ? {}
            : {
                route: rule.route.map((route: any) => {
                  const destination: any = {
                    host: route.destination.host,
                  };

                  // 只有当端口不是默认值80时才包含
                  if (
                    !isDefaultValue(
                      route.destination.port.number,
                      DEFAULT_VALUES.portNumber,
                    )
                  ) {
                    destination.port = {
                      number: route.destination.port.number,
                    };
                  }

                  // 只有当子集存在时才包含
                  if (route.destination.subset) {
                    destination.subset = route.destination.subset;
                  }

                  const routeObj: any = {
                    destination,
                  };

                  // 只有当权重不是默认值100时才包含
                  if (!isDefaultValue(route.weight, DEFAULT_VALUES.weight)) {
                    routeObj.weight = route.weight;
                  }

                  return routeObj;
                }),
              }),
        })),
      },
    };

    return yaml.dump(virtualService, { indent: 2 });
  } catch (error) {
    return '# YAML生成错误\n' + error;
  }
});

// Tab切换处理
const handleTabChange = (key: string) => {
  try {
    // 如果从YAML tab切换到其他tab，先尝试同步YAML到表单
    if (previousTab.value === 'yaml' && key !== 'yaml') {
      const success = syncFromYaml();
      if (!success) {
        // 同步失败，阻止tab切换
        activeTab.value = 'yaml';
        return;
      }
    }

    // 如果切换到YAML tab，同步表单数据到YAML
    if (key === 'yaml') {
      syncToYaml();
    }

    // 更新previousTab
    previousTab.value = key;
  } catch (error) {
    // 发生错误时，阻止tab切换
    activeTab.value = previousTab.value;
    message.error('Tab切换失败：' + error);
  }
};

// 同步表单数据到YAML
const syncToYaml = () => {
  yamlContent.value = yamlPreview.value;
  yamlError.value = '';
};

// 同步YAML到表单数据
const syncFromYaml = (): boolean => {
  try {
    const parsedYaml = yaml.load(yamlContent.value) as any;

    if (!parsedYaml || !parsedYaml.spec) {
      throw new Error('无效的YAML格式');
    }

    const spec = parsedYaml.spec;

    // 更新基本信息
    formData.name = parsedYaml.metadata?.name || '';
    formData.namespace = parsedYaml.metadata?.namespace || '';
    formData.hosts = spec.hosts || [];
    formData.gateways = spec.gateways || [];

    // 更新HTTP规则
    formData.http = (spec.http || []).map((httpRule: any) => {
      const rule = createHttpRule();

      // 解析匹配条件
      rule.match = (httpRule.match || []).map((match: any) => {
        const matchRule = createMatchRule();

        // 解析路径匹配
        if (match.uri) {
          if (match.uri.prefix) {
            matchRule.pathType = 'prefix';
            matchRule.path = match.uri.prefix;
          } else if (match.uri.exact) {
            matchRule.pathType = 'exact';
            matchRule.path = match.uri.exact;
          } else if (match.uri.regex) {
            matchRule.pathType = 'regex';
            matchRule.path = match.uri.regex;
          }
        }

        // 解析HTTP方法
        if (match.method && match.method.regex) {
          matchRule.method = match.method.regex.split('|');
        }

        // 解析请求头
        if (match.headers) {
          matchRule.headers = Object.entries(match.headers).map(
            ([name, value]: [string, any]) => {
              const header = createHeader();
              header.name = name;
              if (value.exact) {
                header.matchType = 'exact';
                header.value = value.exact;
              } else if (value.regex) {
                header.matchType = 'regex';
                header.value = value.regex;
              }
              return header;
            },
          );
        }

        // 解析查询参数
        if (match.queryParams) {
          matchRule.queryParams = Object.entries(match.queryParams).map(
            ([name, value]: [string, any]) => {
              const param = createQueryParam();
              param.name = name;
              if (value.exact) {
                param.matchType = 'exact';
                param.value = value.exact;
              } else if (value.regex) {
                param.matchType = 'regex';
                param.value = value.regex;
              }
              return param;
            },
          );
        }

        // 解析Scheme
        if (match.scheme && match.scheme.exact) {
          matchRule.scheme = match.scheme.exact;
        }

        // 解析查询参数
        if (match.queryParams) {
          matchRule.queryParams = Object.entries(match.queryParams).map(
            ([name, value]: [string, any]) => {
              const param = createQueryParam();
              param.name = name;
              if (value.exact) {
                param.matchType = 'exact';
                param.value = value.exact;
              } else if (value.regex) {
                param.matchType = 'regex';
                param.value = value.regex;
              }
              return param;
            },
          );
        }

        // 解析Scheme
        if (match.scheme && match.scheme.exact) {
          matchRule.scheme = match.scheme.exact;
        }

        return matchRule;
      });

      // 解析路由目标
      if (httpRule.route) {
        rule.route = httpRule.route.map((route: any) => {
          const destination = createDestination();
          destination.destination.host = route.destination?.host || '';
          destination.destination.port.number =
            route.destination?.port?.number || DEFAULT_VALUES.portNumber;
          destination.destination.subset = route.destination?.subset || '';
          destination.weight = route.weight || DEFAULT_VALUES.weight;
          return destination;
        });
      }

      // 解析重定向
      if (httpRule.redirect) {
        rule.redirect.uri = httpRule.redirect.uri || '';
        rule.redirect.authority = httpRule.redirect.authority || '';
        rule.redirect.redirectCode =
          httpRule.redirect.redirectCode || DEFAULT_VALUES.redirectCode;
      }

      // 解析重写规则
      if (httpRule.rewrite) {
        rule.rewrite.uri = httpRule.rewrite.uri || '';
        rule.rewrite.authority = httpRule.rewrite.authority || '';
      }

      // 解析故障注入
      if (httpRule.fault) {
        if (httpRule.fault.delay) {
          rule.fault.delay.fixedDelay = httpRule.fault.delay.fixedDelay || '';
          rule.fault.delay.percentage =
            httpRule.fault.delay.percentage?.value ||
            DEFAULT_VALUES.faultDelayPercentage;
        }
        if (httpRule.fault.abort) {
          rule.fault.abort.httpStatus =
            httpRule.fault.abort.httpStatus ||
            DEFAULT_VALUES.faultAbortHttpStatus;
          rule.fault.abort.percentage =
            httpRule.fault.abort.percentage?.value ||
            DEFAULT_VALUES.faultAbortPercentage;
        }
      }

      // 解析流量镜像
      if (httpRule.mirror) {
        rule.mirror.host = httpRule.mirror.host || '';
        rule.mirror.subset = httpRule.mirror.subset || '';
        rule.mirror.percentage =
          httpRule.mirror.percentage?.value || DEFAULT_VALUES.mirrorPercentage;
      }

      // 解析重试策略
      if (httpRule.retries) {
        rule.retries.attempts =
          httpRule.retries.attempts !== undefined
            ? httpRule.retries.attempts
            : DEFAULT_VALUES.retriesAttempts;
        rule.retries.perTryTimeout =
          httpRule.retries.perTryTimeout || DEFAULT_VALUES.perTryTimeout;
        rule.retries.retryOn =
          httpRule.retries.retryOn || DEFAULT_VALUES.retryOn;
      }

      // 解析超时设置
      if (httpRule.timeout) {
        rule.timeout = httpRule.timeout;
      }

      // 确保expanded属性存在
      rule.expanded = true;

      return rule;
    });

    yamlError.value = '';
    // message.success('YAML已同步到表单');
    return true;
  } catch (error) {
    yamlError.value = `YAML解析错误: ${error}`;
    message.error('YAML解析失败，请检查格式');
    return false;
  }
};

// 添加HTTP规则
const addHttpRule = () => {
  currentRuleIndex.value = -1;
  currentRule.value = createHttpRule();
  ruleDrawerVisible.value = true;
  ruleEditorTab.value = 'basic'; // 重置编辑器Tab
};

// 删除HTTP规则
const removeHttpRule = (index: number) => {
  formData.http.splice(index, 1);
};

// 路径类型变更处理
const onPathTypeChange = (ruleIndex: number, matchIndex: number) => {
  const match = formData.http[ruleIndex].match[matchIndex];
  if (match.pathType === 'prefix' && !match.path.startsWith('/')) {
    match.path = '/' + match.path;
  }
};

// Dry Run测试
const handleDryRun = async () => {
  dryRunLoading.value = true;
  try {
    // 确保YAML内容是最新的
    if (activeTab.value !== 'yaml') {
      syncToYaml();
    }

    // 验证YAML格式
    if (!syncFromYaml()) {
      message.error('YAML格式错误，请检查配置');
      return;
    }

    // 验证表单数据
    const validation = validateFormData();
    if (!validation.valid) {
      message.error('配置验证失败：\n' + validation.errors.join('\n'));
      return;
    }

    const params = {
      cluster: props.cluster,
      namespace: formData.namespace,
      name: formData.name,
      content: yamlContent.value,
    };

    const response = await dryRunVirtualService(params);

    if (response.data.valid) {
      message.success('Dry Run 测试通过！');

      // 显示差异对比
      if (response.data.diff) {
        // 这里可以显示差异对比对话框
        console.log('差异对比:', response.data.diff);
      }

      if (response.data.warnings && response.data.warnings.length > 0) {
        message.warning('警告信息: ' + response.data.warnings.join(', '));
      }
    } else {
      message.error('Dry Run 测试失败：' + response.data.message);
    }
  } catch (error: any) {
    message.error('Dry Run 测试失败：' + (error?.message || error));
  } finally {
    dryRunLoading.value = false;
  }
};

// 提交审核
const handleSubmit = async () => {
  submitLoading.value = true;
  try {
    // 确保YAML内容是最新的
    if (activeTab.value !== 'yaml') {
      syncToYaml();
    }

    // 验证YAML格式
    if (!syncFromYaml()) {
      message.error('YAML格式错误，请检查配置');
      return;
    }

    // 验证表单数据
    const validation = validateFormData();
    if (!validation.valid) {
      message.error('配置验证失败：\n' + validation.errors.join('\n'));
      return;
    }

    const params = {
      cluster: props.cluster,
      namespace: formData.namespace,
      name: formData.name,
      content: yamlContent.value,
    };

    if (props.mode === 'add' || props.mode === 'copy') {
      await createVirtualService(params);
    } else if (props.mode === 'edit') {
      await updateVirtualService(params);
    }

    emit('submit', params);
    resetState(); // 重置状态
    handleClose();
  } catch (error: any) {
    message.error('提交失败：' + (error?.message || error));
  } finally {
    submitLoading.value = false;
  }
};

// 对比功能
const handleCompare = () => {
  // 确保YAML内容是最新的
  if (activeTab.value !== 'yaml') {
    syncToYaml();
  }
  compareDrawerVisible.value = true;
};

// 处理对比同步
const handleCompareSync = (data: { content: string; config: any }) => {
  // 同步到表单数据
  initializeFormData(data.config);

  // 更新YAML内容
  yamlContent.value = data.content;

  // 切换到YAML标签页
  activeTab.value = 'yaml';
};

// 关闭抽屉
const handleClose = () => {
  resetState();
  emit('close');
};

// 重置所有状态
const resetState = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: '',
    namespace: '',
    hosts: [],
    gateways: [],
    http: [],
    yaml: '',
  });

  // 重置 YAML 相关状态
  yamlContent.value = '';
  yamlError.value = '';

  // 重置 Tab 状态
  activeTab.value = 'basic';
  previousTab.value = 'basic';

  // 重置规则编辑器状态
  currentRule.value = createHttpRule();
  currentRuleIndex.value = -1;
  ruleDrawerVisible.value = false;
  ruleEditorTab.value = 'basic';

  // 重置加载状态
  dryRunLoading.value = false;
  submitLoading.value = false;

  // 重置对比相关状态
  compareDrawerVisible.value = false;
};

// 初始化表单数据
const initializeFormData = (data: any) => {
  if (!data) {
    resetState();
    return;
  }

  // 如果是新的数据格式（包含content字段）
  if (data.content) {
    // 设置YAML内容
    yamlContent.value = data.content;

    // 同步YAML到表单
    if (syncFromYaml()) {
      console.log('数据已从YAML同步到表单');
    } else {
      console.error('YAML同步失败');
      message.error('数据格式错误');
      resetState();
    }
  } else if (data.metadata && data.spec) {
    // 如果是Kubernetes对象格式（包含metadata和spec），需要转换
    const spec = data.spec;
    const metadata = data.metadata;

    // 转换为表单格式
    formData.name = metadata.name || '';
    formData.namespace = metadata.namespace || '';
    formData.gateways = spec.gateways || [];
    formData.hosts = spec.hosts || [];

    // 转换HTTP规则
    formData.http = (spec.http || []).map((httpRule: any) => {
      const rule = createHttpRule();

      // 解析匹配条件
      rule.match = (httpRule.match || []).map((match: any) => {
        const matchRule = createMatchRule();

        // 解析路径匹配
        if (match.uri) {
          if (match.uri.prefix) {
            matchRule.pathType = 'prefix';
            matchRule.path = match.uri.prefix;
          } else if (match.uri.exact) {
            matchRule.pathType = 'exact';
            matchRule.path = match.uri.exact;
          } else if (match.uri.regex) {
            matchRule.pathType = 'regex';
            matchRule.path = match.uri.regex;
          }
        }

        // 解析HTTP方法
        if (match.method && match.method.regex) {
          matchRule.method = match.method.regex.split('|');
        }

        // 解析请求头
        if (match.headers) {
          matchRule.headers = Object.entries(match.headers).map(
            ([name, value]: [string, any]) => {
              const header = createHeader();
              header.name = name;
              if (value.exact) {
                header.matchType = 'exact';
                header.value = value.exact;
              } else if (value.regex) {
                header.matchType = 'regex';
                header.value = value.regex;
              }
              return header;
            },
          );
        }

        return matchRule;
      });

      // 解析路由目标
      if (httpRule.route) {
        rule.route = httpRule.route.map((route: any) => {
          const destination = createDestination();
          destination.destination.host = route.destination?.host || '';
          destination.destination.port.number =
            route.destination?.port?.number || DEFAULT_VALUES.portNumber;
          destination.destination.subset = route.destination?.subset || '';
          destination.weight = route.weight || DEFAULT_VALUES.weight;
          return destination;
        });
      }

      // 解析重定向
      if (httpRule.redirect) {
        rule.redirect.uri = httpRule.redirect.uri || '';
        rule.redirect.authority = httpRule.redirect.authority || '';
        rule.redirect.redirectCode =
          httpRule.redirect.redirectCode || DEFAULT_VALUES.redirectCode;
      }

      // 解析重写规则
      if (httpRule.rewrite) {
        rule.rewrite.uri = httpRule.rewrite.uri || '';
        rule.rewrite.authority = httpRule.rewrite.authority || '';
      }

      // 解析故障注入
      if (httpRule.fault) {
        if (httpRule.fault.delay) {
          rule.fault.delay.fixedDelay = httpRule.fault.delay.fixedDelay || '';
          rule.fault.delay.percentage =
            httpRule.fault.delay.percentage?.value ||
            DEFAULT_VALUES.faultDelayPercentage;
        }
        if (httpRule.fault.abort) {
          rule.fault.abort.httpStatus =
            httpRule.fault.abort.httpStatus ||
            DEFAULT_VALUES.faultAbortHttpStatus;
          rule.fault.abort.percentage =
            httpRule.fault.abort.percentage?.value ||
            DEFAULT_VALUES.faultAbortPercentage;
        }
      }

      // 解析流量镜像
      if (httpRule.mirror) {
        rule.mirror.host = httpRule.mirror.host || '';
        rule.mirror.subset = httpRule.mirror.subset || '';
        rule.mirror.percentage =
          httpRule.mirror.percentage?.value || DEFAULT_VALUES.mirrorPercentage;
      }

      // 解析重试策略
      if (httpRule.retries) {
        rule.retries.attempts =
          httpRule.retries.attempts !== undefined
            ? httpRule.retries.attempts
            : DEFAULT_VALUES.retriesAttempts;
        rule.retries.perTryTimeout =
          httpRule.retries.perTryTimeout || DEFAULT_VALUES.perTryTimeout;
        rule.retries.retryOn =
          httpRule.retries.retryOn || DEFAULT_VALUES.retryOn;
      }

      // 解析超时设置
      if (httpRule.timeout) {
        rule.timeout = httpRule.timeout;
      }

      // 确保expanded属性存在
      rule.expanded = true;

      return rule;
    });
  } else {
    // 如果是普通对象格式，直接赋值
    Object.assign(formData, data);
  }

  // 同步到YAML
  if (activeTab.value === 'yaml') {
    syncToYaml();
  }
};

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData && (props.mode === 'edit' || props.mode === 'view')) {
      initializeFormData(newData);
    }
  },
  { immediate: true },
);

// 监听抽屉开关，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 抽屉打开时，根据模式初始化数据
      if (props.mode === 'add') {
        // 新增模式：重置所有状态
        resetState();
      } else if (
        props.mode === 'edit' ||
        props.mode === 'view' ||
        props.mode === 'copy'
      ) {
        // 编辑或查看模式：如果有编辑数据则初始化，否则重置
        if (props.editData) {
          initializeFormData(props.editData);
        } else {
          resetState();
        }
      }
    } else {
      // 抽屉关闭时重置状态
      resetState();
    }
  },
  { immediate: true },
);

// 编辑规则
const editRule = (index: number) => {
  currentRuleIndex.value = index;
  // 深拷贝当前规则到编辑器
  currentRule.value = JSON.parse(JSON.stringify(formData.http[index]));
  ruleDrawerVisible.value = true;
  ruleEditorTab.value = 'basic'; // 重置编辑器Tab
};

// 复制规则
const copyRule = (index: number) => {
  const ruleToCopy = JSON.parse(JSON.stringify(formData.http[index]));
  formData.http.push(ruleToCopy);
  message.success('规则已复制');
};

// 保存规则
const saveRule = () => {
  // 验证当前规则
  const validation = validateCurrentRule();
  if (!validation.valid) {
    message.error('规则验证失败：\n' + validation.errors.join('\n'));
    return;
  }

  if (currentRuleIndex.value === -1) {
    // 新增规则
    formData.http.push(JSON.parse(JSON.stringify(currentRule.value)));
  } else {
    // 更新规则
    formData.http[currentRuleIndex.value] = JSON.parse(
      JSON.stringify(currentRule.value),
    );
  }
  ruleDrawerVisible.value = false;
  currentRuleIndex.value = -1; // 重置当前编辑的规则索引
  currentRule.value = createHttpRule(); // 重置当前规则
  message.success('规则已保存');
};

// 关闭规则编辑Drawer
const closeRuleDrawer = () => {
  ruleDrawerVisible.value = false;
  currentRuleIndex.value = -1;
  currentRule.value = createHttpRule(); // 重置当前规则
  ruleEditorTab.value = 'basic'; // 重置编辑器Tab
};

// 添加请求头到当前规则
const addHeaderToCurrent = (matchIndex: number) => {
  currentRule.value.match[matchIndex].headers.push(createHeader());
};

// 从当前规则移除请求头
const removeHeaderFromCurrent = (matchIndex: number, headerIndex: number) => {
  currentRule.value.match[matchIndex].headers.splice(headerIndex, 1);
};

// 添加查询参数到当前规则
const addQueryParamToCurrent = (matchIndex: number) => {
  currentRule.value.match[matchIndex].queryParams.push(createQueryParam());
};

// 从当前规则移除查询参数
const removeQueryParamFromCurrent = (
  matchIndex: number,
  paramIndex: number,
) => {
  currentRule.value.match[matchIndex].queryParams.splice(paramIndex, 1);
};

// 移除匹配条件
const removeMatchFromCurrent = (index: number) => {
  currentRule.value.match.splice(index, 1);
};

// 添加匹配条件
const addMatchToCurrent = () => {
  currentRule.value.match.push(createMatchRule());
};

// 移除目标服务
const removeDestinationFromCurrent = (index: number) => {
  currentRule.value.route.splice(index, 1);
};

// 添加目标服务
const addDestinationToCurrent = () => {
  currentRule.value.route.push(createDestination());
};

// 移动规则
const moveRule = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1;
  if (newIndex >= 0 && newIndex < formData.http.length) {
    const temp = formData.http[index];
    formData.http[index] = formData.http[newIndex];
    formData.http[newIndex] = temp;
    message.success('规则顺序已更新');
  }
};

// 表单数据验证
const validateFormData = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // 基本信息验证
  if (!formData.name?.trim()) {
    errors.push('配置名称不能为空');
  }
  if (!formData.namespace?.trim()) {
    errors.push('请选择命名空间');
  }
  if (!formData.gateways?.length) {
    errors.push('请选择至少一个网关');
  }
  if (!formData.hosts?.length) {
    errors.push('请至少输入一个主机域名');
  }

  // // 路由规则验证
  // if (!formData.http?.length) {
  //   errors.push('至少需要添加一个路由规则');
  //   return { valid: false, errors };
  // }

  // 验证每个路由规则
  formData.http.forEach((rule: any, ruleIndex: number) => {
    const ruleNumber = ruleIndex + 1;

    // // 验证匹配条件
    // if (!rule.match?.length) {
    //   errors.push(`路由规则 ${ruleNumber}: 至少需要一个匹配条件`);
    // } else {
    rule.match.forEach((match: any, matchIndex: number) => {
      const matchNumber = matchIndex + 1;
      if (!match.path?.trim()) {
        errors.push(
          `路由规则 ${ruleNumber} 匹配条件 ${matchNumber}: 路径不能为空`,
        );
      }
    });
    // }

    // 验证目标服务（与重定向互斥）
    const hasRedirect = rule.redirect?.uri || rule.redirect?.authority;
    const hasRoute =
      rule.route?.length > 0 &&
      rule.route.some((r: any) => r.destination?.host?.trim());

    if (!hasRedirect && !hasRoute) {
      errors.push(`路由规则 ${ruleNumber}: 必须配置目标服务或重定向`);
    } else if (hasRedirect && hasRoute) {
      errors.push(`路由规则 ${ruleNumber}: 目标服务和重定向不能同时配置`);
    } else if (hasRoute) {
      // 验证目标服务
      rule.route.forEach((route: any, routeIndex: number) => {
        const routeNumber = routeIndex + 1;
        if (!route.destination?.host?.trim()) {
          errors.push(
            `路由规则 ${ruleNumber} 目标服务 ${routeNumber}: 服务名称不能为空`,
          );
        }
        if (route.destination?.port?.number) {
          const port = route.destination.port.number;
          if (port < 1 || port > 65535) {
            errors.push(
              `路由规则 ${ruleNumber} 目标服务 ${routeNumber}: 端口号必须在1-65535之间`,
            );
          }
        }
        if (
          route.weight !== undefined &&
          (route.weight < 0 || route.weight > 100)
        ) {
          errors.push(
            `路由规则 ${ruleNumber} 目标服务 ${routeNumber}: 权重必须在0-100之间`,
          );
        }
      });
    } else if (hasRedirect) {
      // 验证重定向
      if (!rule.redirect.uri?.trim() && !rule.redirect.authority?.trim()) {
        errors.push(`路由规则 ${ruleNumber}: 重定向必须配置路径或主机`);
      }
    }

    // 验证故障注入配置
    if (
      rule.fault?.delay?.fixedDelay &&
      rule.fault.delay.percentage !== undefined
    ) {
      if (
        rule.fault.delay.percentage < 0 ||
        rule.fault.delay.percentage > 100
      ) {
        errors.push(`路由规则 ${ruleNumber}: 延迟故障注入比例必须在0-100之间`);
      }
    }
    if (
      rule.fault?.abort?.httpStatus &&
      rule.fault.abort.percentage !== undefined
    ) {
      if (
        rule.fault.abort.percentage < 0 ||
        rule.fault.abort.percentage > 100
      ) {
        errors.push(`路由规则 ${ruleNumber}: 中断故障注入比例必须在0-100之间`);
      }
    }

    // 验证流量镜像配置
    if (rule.mirror?.host && rule.mirror.percentage !== undefined) {
      if (rule.mirror.percentage < 0 || rule.mirror.percentage > 100) {
        errors.push(`路由规则 ${ruleNumber}: 流量镜像比例必须在0-100之间`);
      }
    }

    // 验证重试策略
    if (rule.retries?.attempts !== undefined) {
      if (rule.retries.attempts < 0 || rule.retries.attempts > 10) {
        errors.push(`路由规则 ${ruleNumber}: 重试次数必须在0-10之间`);
      }
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
};

// 验证当前编辑的规则
const validateCurrentRule = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // 验证匹配条件
  // if (!currentRule.value.match?.length) {
  //   errors.push('至少需要一个匹配条件');
  // } else {
  currentRule.value.match.forEach((match: any, matchIndex: number) => {
    const matchNumber = matchIndex + 1;
    if (!match.path?.trim()) {
      errors.push(`匹配条件 ${matchNumber}: 路径不能为空`);
    }
  });
  // }

  // 验证目标服务（与重定向互斥）
  const hasRedirect =
    currentRule.value.redirect?.uri || currentRule.value.redirect?.authority;
  const hasRoute =
    currentRule.value.route?.length > 0 &&
    currentRule.value.route.some((r: any) => r.destination?.host?.trim());

  if (!hasRedirect && !hasRoute) {
    errors.push('必须配置目标服务或重定向');
  } else if (hasRedirect && hasRoute) {
    errors.push('目标服务和重定向不能同时配置');
  } else if (hasRoute) {
    // 验证目标服务
    currentRule.value.route.forEach((route: any, routeIndex: number) => {
      const routeNumber = routeIndex + 1;
      if (!route.destination?.host?.trim()) {
        errors.push(`目标服务 ${routeNumber}: 服务名称不能为空`);
      }
      if (route.destination?.port?.number) {
        const port = route.destination.port.number;
        if (port < 1 || port > 65535) {
          errors.push(`目标服务 ${routeNumber}: 端口号必须在1-65535之间`);
        }
      }
      if (
        route.weight !== undefined &&
        (route.weight < 0 || route.weight > 100)
      ) {
        errors.push(`目标服务 ${routeNumber}: 权重必须在0-100之间`);
      }
    });
  } else if (hasRedirect) {
    // 验证重定向
    if (
      !currentRule.value.redirect.uri?.trim() &&
      !currentRule.value.redirect.authority?.trim()
    ) {
      errors.push('重定向必须配置路径或主机');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

// 检查单个规则是否有效
const isRuleValid = (rule: any): boolean => {
  // 检查匹配条件
  // if (!rule.match?.length) {
  //   return false;
  // }

  // 检查匹配条件的路径
  for (const match of rule.match) {
    if (!match.path?.trim()) {
      return false;
    }
  }

  // 检查目标服务或重定向
  const hasRedirect = rule.redirect?.uri || rule.redirect?.authority;
  const hasRoute =
    rule.route?.length > 0 &&
    rule.route.some((r: any) => r.destination?.host?.trim());

  if (!hasRedirect && !hasRoute) {
    return false;
  }
  if (hasRedirect && hasRoute) {
    return false;
  }

  if (hasRoute) {
    // 检查目标服务
    for (const route of rule.route) {
      if (!route.destination?.host?.trim()) {
        return false;
      }
      if (route.destination?.port?.number) {
        const port = route.destination.port.number;
        if (port < 1 || port > 65535) {
          return false;
        }
      }
      if (
        route.weight !== undefined &&
        (route.weight < 0 || route.weight > 100)
      ) {
        return false;
      }
    }
  } else if (hasRedirect) {
    // 检查重定向
    if (!rule.redirect.uri?.trim() && !rule.redirect.authority?.trim()) {
      return false;
    }
  }

  return true;
};

// 组件卸载时清理状态
onUnmounted(() => {
  resetState();
});
</script>

<style scoped>
.virtual-service-editor {
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;
}

:deep(.ant-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-tabs-content-holder) {
  flex: 1;
  overflow: hidden;
}

:deep(.ant-tabs-tabpane) {
  height: 100%;
  overflow-y: auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.route-rules {
  .rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }

      .header-count {
        font-size: 14px;
        color: #8c8c8c;
        background: #f5f5f5;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }
  }

  .empty-rules {
    text-align: center;
    padding: 40px 0;
  }

  .rules-list {
    .rule-item-wrapper {
      margin-bottom: 16px;

      .rule-card-modern {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        .rule-header-modern {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px 12px;
          border-bottom: 1px solid #f0f0f0;

          .rule-left {
            display: flex;
            align-items: center;
            gap: 12px;

            .rule-order {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: linear-gradient(135deg, #1890ff, #40a9ff);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-weight: 600;
              font-size: 14px;
            }

            .rule-info {
              .rule-title-modern {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                .rule-name {
                  font-weight: 500;
                  font-size: 16px;
                  color: #262626;
                }
              }

              .rule-description {
                font-size: 12px;
                color: #8c8c8c;

                .separator {
                  margin: 0 4px;
                }

                .rewrite-indicator {
                  color: #fa8c16;
                }
              }
            }
          }

          .rule-actions {
            .ant-btn {
              border: none;
              box-shadow: none;
            }
          }
        }

        .rule-content-modern {
          padding: 16px 20px;

          .rule-flow {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;

            .flow-item {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .flow-label {
                font-size: 12px;
                font-weight: 500;
                color: #8c8c8c;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }

              .flow-content {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .match-item-simple,
                .destination-item-simple,
                .rewrite-item-simple {
                  padding: 4px 8px;
                  background: #f5f5f5;
                  border-radius: 4px;
                  font-size: 13px;
                  color: #262626;

                  .method-info {
                    margin-left: 8px;
                    font-size: 11px;
                    color: #1890ff;
                    background: #e6f7ff;
                    padding: 1px 4px;
                    border-radius: 2px;
                  }

                  .weight-info {
                    margin-left: 8px;
                    font-size: 11px;
                    color: #52c41a;
                    background: #f6ffed;
                    padding: 1px 4px;
                    border-radius: 2px;
                  }

                  .service-info {
                    color: #262626;
                    font-weight: 500;
                  }

                  .path-info {
                    color: #262626;
                    font-weight: 500;
                  }
                }

                .destination-item-simple {
                  background: #f0f9ff;
                  border-left: 3px solid #1890ff;
                }

                .rewrite-item-simple {
                  background: #fff7e6;
                  border-left: 3px solid #fa8c16;
                }
              }
            }

            .flow-arrow {
              font-size: 16px;
              color: #8c8c8c;
              font-weight: bold;
              margin: 0 4px;
            }
          }
        }
      }
    }
  }
}

.rule-editor-container {
  height: calc(100vh - 120px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;

  .validation-tips {
    margin-bottom: 16px;

    .validation-tips-content {
      .tips-title {
        font-weight: 500;
        margin-bottom: 8px;
        color: #262626;
      }

      .tips-list {
        margin: 0;
        padding-left: 16px;
        color: #595959;

        li {
          margin-bottom: 4px;
          font-size: 13px;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .rule-editor-tabs {
    height: 100%;

    :deep(.ant-tabs-content-holder) {
      height: calc(100% - 46px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 16px 0;
      overflow-x: hidden;
    }
  }

  .editor-section {
    margin-bottom: 10px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .section-subtitle {
        font-size: 12px;
        color: #8c8c8c;
        margin-left: auto;
      }
    }

    .section-content {
      .match-card,
      .destination-card,
      .rewrite-card {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        margin-bottom: 12px;
        overflow: hidden;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #fafbfc;
          border-bottom: 1px solid #e8e8e8;

          .card-title {
            font-weight: 500;
            color: #262626;
          }
        }

        .card-body {
          padding: 16px;
          overflow-x: hidden;

          .form-group {
            margin-bottom: 4px;
          }

          .headers-group,
          .query-params-group {
            margin-top: 12px;
            padding: 12px 12px 4px;
            background: #f8f9fa;
            border-radius: 4px;

            .group-title {
              font-size: 12px;
              font-weight: 500;
              color: #595959;
              margin-bottom: 8px;
            }

            .header-row,
            .param-row {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .query-params-group {
            background: #fff7e6;
            border-left: 0px solid #fa8c16;
          }

          .fault-group {
            .fault-group-title {
              font-size: 13px;
              font-weight: 500;
              color: #262626;
              margin-bottom: 12px;
              padding-bottom: 6px;
              border-bottom: 1px solid #f0f0f0;
            }
          }

          .add-header-btn {
            margin-top: 8px;
          }
        }
      }

      .rewrite-card .card-body {
        padding: 16px;
      }
    }
  }

  .compact-form-item {
    margin-bottom: 12px !important;

    :deep(.ant-form-item-label) {
      padding-bottom: 4px;

      label {
        font-size: 12px;
        font-weight: 500;
        color: #595959;
      }
    }

    :deep(.ant-form-item-control) {
      .ant-input,
      .ant-select,
      .ant-input-number {
        font-size: 12px;
      }
    }

    .field-help-icon {
      margin-left: 4px;
      color: #8c8c8c;
      font-size: 12px;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .inline-form-item {
    margin-bottom: 12px !important;

    :deep(.ant-form-item-label) {
      padding-bottom: 0;
      text-align: right;

      label {
        font-size: 12px;
        font-weight: 500;
        color: #595959;
        line-height: 32px;
      }
    }

    :deep(.ant-form-item-control) {
      .ant-input,
      .ant-select,
      .ant-input-number {
        font-size: 12px;
      }
    }

    .field-help-icon {
      margin-left: 4px;
      color: #8c8c8c;
      font-size: 12px;

      &:hover {
        color: #1890ff;
      }
    }
  }

  /* 统一表单控件宽度 */
  .form-control {
    width: 100% !important;
    min-width: 80px;
    max-width: 100%;
  }

  /* 卡片头部操作按钮样式 */
  .card-header {
    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .add-actions {
        display: flex;
        align-items: center;
        background: #f8fafc;
        border-radius: 4px;
        padding: 1px 2px;
        transition: all 0.2s ease;

        &:hover {
          border-color: #bfdbfe;
          background: #f0f9ff;
        }

        .add-btn {
          display: flex;
          align-items: center;
          gap: 2px;
          padding: 2px 4px;
          height: auto;
          line-height: 1.2;
          font-size: 12px;
          color: #64748b;
          border-radius: 3px;
          transition: all 0.2s ease;

          .anticon {
            font-size: 9px;
          }

          &:hover {
            color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
          }
        }

        .action-divider {
          height: 12px;
          margin: 0 1px;
          border-color: #d1d5db;
        }
      }
    }
  }

  /* 响应式调整 */
  @media (max-width: 576px) {
    .inline-form-item {
      margin-bottom: 16px !important;

      :deep(.ant-form-item-label) {
        text-align: left;
        padding-bottom: 4px;

        label {
          line-height: 1.5;
        }
      }
    }

    .form-control {
      min-width: 60px;
    }
  }

  @media (min-width: 577px) and (max-width: 768px) {
    .form-control {
      min-width: 80px;
      max-width: 150px;
    }
  }

  @media (min-width: 769px) {
    .form-control {
      min-width: 100px;
      max-width: 180px;
    }
  }
}

.yaml-editor {
  .yaml-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 500;
  }

  .yaml-editor-content {
    margin-bottom: 16px;
  }

  .yaml-error {
    margin-top: 16px;
  }
}

.editor-actions {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

:deep(.ant-tabs-content-holder) {
  padding-top: 16px;
}
</style>
