<template>
  <a-drawer
    :title="title"
    :visible="visible"
    :width="600"
    placement="right"
    @close="handleCancel"
  >
    <!-- 右上角按钮 -->
    <template #extra>
      <a-space size="middle">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          type="primary"
          danger
          :loading="loading"
          @click="handleSubmit"
        >
          确认
        </a-button>
      </a-space>
    </template>

    <!-- 删除内容 -->
    <div class="delete-content">
      <a-descriptions
        :column="1"
        bordered
        size="small"
        style="margin-bottom: 24px"
      >
        <a-descriptions-item label="操作类型">
          <a-tag color="red">{{ isBatch ? '批量删除' : '单个删除' }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="删除数量">
          {{ deleteRecords.length }} 个VirtualService
        </a-descriptions-item>
      </a-descriptions>

      <div style="max-height: 300px; overflow-y: auto; margin-bottom: 24px">
        <a-list
          :data-source="deleteRecords"
          size="small"
          :split="false"
          style="background: #fafafa; border-radius: 6px; padding: 8px"
        >
          <template #renderItem="{ item }">
            <a-list-item
              style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0"
            >
              <a-list-item-meta>
                <template #title>
                  <span style="font-weight: 500; color: #1890ff">
                    {{ item.name }}
                  </span>
                </template>
                <template #description>
                  <a-space size="small">
                    <a-tag size="small" color="blue">
                      {{ item.namespace }}
                    </a-tag>
                    <a-tag size="small" color="green">
                      {{ item.cluster }}
                    </a-tag>
                    <span
                      v-if="item.hosts"
                      style="font-size: 12px; color: #666"
                    >
                      Hosts:
                      {{
                        Array.isArray(item.hosts)
                          ? item.hosts.join(', ')
                          : item.hosts
                      }}
                    </span>
                  </a-space>
                </template>
              </a-list-item-meta>
              <template #actions>
                <a-tag :color="getStatusColor(item.status)">
                  {{ getStatusText(item.status) }}
                </a-tag>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <h4>删除原因</h4>
      <a-form ref="formRef" :model="formState" :rules="rules">
        <a-form-item name="reason">
          <a-textarea
            v-model:value="formState.reason"
            :rows="3"
            placeholder="请简单说明删除原因..."
            show-count
            :maxlength="200"
          />
        </a-form-item>
      </a-form>

      <a-alert
        message="删除确认"
        :description="alertDescription"
        type="warning"
        show-icon
        style="margin-top: 8px"
      />
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { deleteVirtualService } from '@/api/pilot/gateway';

// Props & Emits
interface Props {
  cluster: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['success']);

// State
const visible = ref(false);
const loading = ref(false);
const formRef = ref();
const deleteRecords = ref<any[]>([]);

const formState = reactive({
  reason: '',
});

// Rules
const rules = {
  reason: [{ required: true, message: '请输入删除原因', trigger: 'blur' }],
};

// Computed
const isBatch = computed(() => deleteRecords.value.length > 1);
const title = computed(() =>
  isBatch.value ? `删除 (${deleteRecords.value.length}个)` : '删除',
);

const alertDescription = computed(() => {
  const count = deleteRecords.value.length;
  const recordText =
    count > 1 ? `以上 ${count} 个VirtualService` : '以上VirtualService';
  return `确认要删除${recordText}吗？删除请求将提交审批，审批通过后资源将从集群中移除。`;
});

// Methods
function open(records: any | any[]) {
  visible.value = true;
  deleteRecords.value = Array.isArray(records) ? records : [records];
  formState.reason = '';
}

function handleCancel() {
  visible.value = false;
  formRef.value?.resetFields();
}

async function handleSubmit() {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // 构建删除请求
    const resources = deleteRecords.value.map((record) => ({
      cluster: props.cluster,
      namespace: record.namespace,
      name: record.name,
    }));

    await deleteVirtualService({
      resources,
      reason: formState.reason,
    });

    const successMsg = isBatch.value
      ? `${deleteRecords.value.length} 个VirtualService删除请求已提交`
      : 'VirtualService删除请求已提交';

    message.success(successMsg);
    visible.value = false;
    emit('success');
  } catch (error) {
    console.error('提交删除请求失败:', error);
    message.error('提交删除请求失败');
  } finally {
    loading.value = false;
  }
}

// Status helpers
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    active: 'green',
    pending: 'orange',
    failed: 'red',
    rejected: 'volcano',
    deleted: 'gray',
  };
  return colorMap[status] || 'default';
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    active: '已部署',
    pending: '待审批',
    failed: '失败',
    rejected: '已拒绝',
    deleted: '已删除',
  };
  return textMap[status] || status;
}

// Expose methods
defineExpose({
  open,
});
</script>

<style scoped>
.delete-content {
  padding: 0;
}

h4 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}

:deep(.ant-list-item:last-child) {
  border-bottom: none !important;
}
</style>
