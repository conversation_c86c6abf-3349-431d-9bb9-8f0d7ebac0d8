<template>
  <a-drawer
    :title="title"
    :visible="visible"
    :width="600"
    placement="right"
    @close="handleCancel"
  >
    <!-- 右上角按钮 -->
    <template #extra>
      <a-space size="middle">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          type="primary"
          danger
          :loading="loading"
          @click="handleSubmit"
        >
          确认
        </a-button>
      </a-space>
    </template>

    <!-- 删除内容 -->
    <div class="delete-content">
      <a-descriptions
        :column="1"
        bordered
        size="small"
        style="margin-bottom: 24px"
      >
        <a-descriptions-item label="操作类型">
          <a-tag color="red">{{ isBatch ? '批量删除' : '单个删除' }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="删除数量">
          {{ deleteRecords.length }} 条记录
        </a-descriptions-item>
      </a-descriptions>
      <div style="max-height: 300px; overflow-y: auto; margin-bottom: 24px">
        <a-list
          :data-source="deleteRecords"
          size="small"
          :split="false"
          style="background: #fafafa; border-radius: 6px; padding: 8px"
        >
          <template #renderItem="{ item }">
            <a-list-item
              style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0"
            >
              <a-list-item-meta>
                <template #title>
                  <span style="font-weight: 500; color: #1890ff">
                    {{ item.subdomain ? item.subdomain + '.' : ''
                    }}{{ item.domain }}
                  </span>
                </template>
                <template #description>
                  <a-space size="small">
                    <a-tag
                      size="small"
                      :color="getRecordTypeColor(item.recordType)"
                    >
                      {{ item.recordType }}
                    </a-tag>
                    <span style="font-size: 12px; color: #666">
                      {{ item.value }}
                    </span>
                    <span v-if="item.ttl" style="font-size: 12px; color: #999">
                      TTL: {{ item.ttl }}
                    </span>
                  </a-space>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <h4>删除原因</h4>
      <a-form ref="formRef" :model="formState" :rules="rules">
        <a-form-item name="reason">
          <a-textarea
            v-model:value="formState.reason"
            :rows="3"
            placeholder="请简单说明删除原因..."
            show-count
            :maxlength="200"
          />
        </a-form-item>
      </a-form>

      <a-alert
        message="删除确认"
        description="确认要删除以上DNS记录吗？删除请求将提交审批，审批通过后记录将从云平台移除。"
        type="warning"
        show-icon
        style="margin-top: 8px"
      />
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { deleteDomainRecord, getRecordTypeColor } from '@/api/pilot/domain';

// Props & Emits
interface Props {
  domainOptions: any[];
  selectedProvider: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['success']);

// State
const visible = ref(false);
const loading = ref(false);
const formRef = ref();
const deleteRecords = ref<any[]>([]);

const formState = reactive({
  reason: '',
});

// Rules
const rules = {
  reason: [{ required: true, message: '请输入删除原因', trigger: 'blur' }],
};

// Computed
const isBatch = computed(() => deleteRecords.value.length > 1);
const title = computed(() =>
  isBatch.value ? `删除 (${deleteRecords.value.length}条)` : '删除',
);

// Methods
function open(records: any | any[]) {
  visible.value = true;
  deleteRecords.value = Array.isArray(records) ? records : [records];
  formState.reason = '';
}

function handleCancel() {
  visible.value = false;
  formRef.value?.resetFields();
}

async function handleSubmit() {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // 提交删除请求
    const promises = deleteRecords.value.map(async (record: any) => {
      const comment = isBatch.value
        ? `批量删除 ${deleteRecords.value.length} 条DNS记录`
        : `删除记录: ${record.subdomain ? record.subdomain + '.' : ''}${
            record.domain
          }`;

      return deleteDomainRecord({
        domain: record.domain, // 直接使用记录中的域名字符串
        providerUuid: props.selectedProvider?.value || '',
        subdomain: record.subdomain || '',
        recordType: record.recordType,
        comment: comment,
        reason: formState.reason,
        value: record.value,
      });
    });

    await Promise.all(promises);

    const successMsg = isBatch.value
      ? `${deleteRecords.value.length} 条删除请求已提交，等待审批`
      : '删除请求已提交，等待审批';

    message.success(successMsg);
    visible.value = false;
    emit('success');
  } catch (error) {
    console.error('提交删除请求失败:', error);
    message.error('提交删除请求失败');
  } finally {
    loading.value = false;
  }
}

// Expose methods
defineExpose({
  open,
});
</script>

<style scoped>
.delete-content {
  padding: 0;
}

h4 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}

:deep(.ant-list-item:last-child) {
  border-bottom: none !important;
}
</style>
