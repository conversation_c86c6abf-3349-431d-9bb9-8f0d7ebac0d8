<template>
  <a-card :body-style="{ paddingBottom: '0!important' }">
    <a-space :size="20">
      <a-form
        ref="searchFormRef"
        layout="inline"
        :label-col="{ span: 25 }"
        :wrapper-col="{ span: 45 }"
        :model="searchFormState"
      >
        <a-form-item label="服务商" name="provider">
          <a-select
            v-model:value="searchFormState.providerUuid"
            style="width: 100px"
            placeholder="请选择服务商"
            :options="providerOptions"
            :loading="providerLoading"
            show-search
            :filter-option="filterProviderOption"
            @change="onProviderChange"
          />
        </a-form-item>
        <a-form-item label="域名" name="domain">
          <a-select
            v-model:value="searchFormState.domain"
            style="width: 180px"
            placeholder="请选择主域名"
            :options="domainOptions"
            :loading="domainLoading"
            show-search
            :filter-option="filterDomainOption"
            @change="onDomainChange"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select
            v-model:value="searchFormState.status"
            style="width: 100px"
            placeholder="请选择状态"
            :options="RECORD_STATUS_OPTIONS"
            @change="doSearch"
          />
        </a-form-item>
        <a-form-item label="类型" name="recordType">
          <a-select
            v-model:value="searchFormState.recordType"
            style="width: 100px"
            allow-clear
            placeholder="请选择类型"
            :options="RECORD_TYPE_OPTIONS"
            @change="doSearch"
          />
        </a-form-item>
        <a-form-item label="搜索" name="searchQuery">
          <a-input-group compact>
            <a-select
              v-model:value="searchFormState.searchType"
              style="width: 70px"
              @change="doSearch"
            >
              <a-select-option value="subdomain">记录</a-select-option>
              <a-select-option value="value">值</a-select-option>
            </a-select>
            <a-input-search
              v-model:value="searchFormState.searchQuery"
              style="width: 150px"
              allow-clear
              enter-button
              :placeholder="
                searchFormState.searchType === 'subdomain'
                  ? '请输入子域名'
                  : '请输入记录值'
              "
              @search="doSearch"
            />
          </a-input-group>
        </a-form-item>
      </a-form>

      <a-button
        type="primary"
        :disabled="!searchFormState.domain || !searchFormState.providerUuid"
        @click="handleAdd"
      >
        <template #icon>
          <plus-circle-outlined />
        </template>
        添加
      </a-button>
      <a-button
        danger
        type="primary"
        :disabled="!hasSelected"
        :loading="state.loading"
        @click="deleteBySelect(state.selectedRowKeys)"
      >
        <template #icon>
          <delete-outlined />
        </template>
        删除
      </a-button>
    </a-space>
    <a-divider />
    <a-table
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        columnWidth: 35,
      }"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :columns="columns()"
      :show-index-column="true"
      size="middle"
      :bordered="true"
      :scroll="{ x: 150, y: tableConfig.windowHeight - 240 }"
      row-key="uuid"
      class="ant-table-striped"
      :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
      @change="handlerTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'recordType'">
          <a-tooltip :title="getRecordTypeDescription(record.recordType)">
            <a-tag :color="getRecordTypeColor(record.recordType)" size="small">
              {{ record.recordType }}
            </a-tag>
          </a-tooltip>
        </template>
        <template v-if="column.key === 'proxied'">
          <template
            v-if="
              selectedProviderObject.type === 'cloudflare' &&
              isProxyableRecord(record.recordType)
            "
          >
            <a-tag v-if="record.proxied" color="orange" size="small">
              代理中
            </a-tag>
            <a-tag v-else color="default" size="small">仅DNS</a-tag>
          </template>
          <span v-else style="color: #ccc">-</span>
        </template>
        <template v-if="column.key === 'action'">
          <a-tag v-if="record.action" :color="getActionColor(record.action)">
            {{ getStatusText(record.action) }}
          </a-tag>
          <span v-else>-</span>
        </template>
        <template v-if="column.key === 'operation'">
          <!-- 待审批状态只显示审批按钮 -->
          <template v-if="record.status === 'pending'">
            <a style="color: #1890ff" @click="handleApprove(record)">审批</a>
          </template>
          <!-- 其他状态显示编辑等操作 -->
          <template v-else>
            <a style="color: rgba(0, 0, 0, 0.63)" @click="handleEdit(record)">
              编辑
            </a>
            <a-divider type="vertical" />
            <span>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1">
                      <a-button type="text" @click="handleValidate(record)">
                        验证
                      </a-button>
                    </a-menu-item>
                    <a-menu-item key="2">
                      <a-button
                        style="color: red"
                        type="text"
                        @click="handleDeleteSingle(record)"
                      >
                        删除
                      </a-button>
                    </a-menu-item>
                  </a-menu>
                </template>
                <a style="color: rgba(0, 0, 0, 0.63)" class="ant-dropdown-link">
                  更多
                  <down-outlined />
                </a>
              </a-dropdown>
            </span>
          </template>
        </template>
      </template>
    </a-table>
    <edit-drawer
      ref="editDrawer"
      :title="drawerTitle"
      :selected-provider="selectedProviderObject"
      :selected-domain="selectedDomainObject"
      @ok="handleSuccess"
    />
    <approval-drawer ref="approvalDrawer" @success="handleSuccess" />
    <delete-drawer
      ref="deleteDrawer"
      :domain-options="domainOptions"
      :selected-provider="selectedProviderObject"
      @success="handleSuccess"
    />
  </a-card>
</template>

<script lang="ts">
import {
  DeleteOutlined,
  DownOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { columns, Mode } from './data';
import EditDrawer from './drawer.vue';
import ApprovalDrawer from './drawer-approval.vue';
import DeleteDrawer from './drawer-delete.vue';
import {
  getDomainProviders,
  getDomains,
  getDomainRecords,
  getDomainRecordInfo,
  validateDomainRecord,
  RECORD_TYPE_OPTIONS,
  RECORD_STATUS_OPTIONS,
  getStatusColor,
  getStatusText,
  getRecordTypeDescription,
  getRecordTypeColor,
} from '@/api/pilot/domain';
import { useRoute, useRouter } from 'vue-router';

export default {
  components: {
    EditDrawer,
    ApprovalDrawer,
    DeleteDrawer,
    PlusCircleOutlined,
    DownOutlined,
    DeleteOutlined,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();

    const tableConfig = ref({
      windowHeight: document.documentElement.clientHeight - 40,
    });
    const editDrawer = ref();
    const approvalDrawer = ref();
    const deleteDrawer = ref();
    const drawerTitle = ref('');
    const dataSource = ref();
    const loading = ref();
    const providerOptions = ref([]);
    const providerLoading = ref(false);
    const domainOptions = ref([]);
    const domainLoading = ref(false);

    const pagination = ref({
      page: 1,
      pageSize: 10,
      showSizeChanger: true,
      total: 0,
      showTotal: (total: any) => `共 ${total} 条数据`,
    });

    async function doSearch() {
      if (!searchFormState.domain) {
        dataSource.value = [];
        pagination.value.total = 0;
        return;
      }

      try {
        loading.value = true;

        // 根据搜索类型设置参数
        const searchParams: any = {
          domain: searchFormState.domain,
          providerUuid: searchFormState.providerUuid,
          recordType: searchFormState.recordType,
          status: searchFormState.status,
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
          subdomain: '',
          value: '',
        };

        // 根据搜索类型设置subdomain或value
        if (searchFormState.searchType === 'subdomain') {
          searchParams.subdomain = searchFormState.searchQuery;
        } else {
          searchParams.value = searchFormState.searchQuery;
        }

        const response = await getDomainRecords(searchParams);
        dataSource.value = response.data.list;
        pagination.value.total = response.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }

    async function loadProviders() {
      try {
        providerLoading.value = true;
        const response = await getDomainProviders({
          page: 1,
          pageSize: 100,
        });
        providerOptions.value = response.data.list.map((provider: any) => ({
          label: provider.name,
          value: provider.uuid,
          type: provider.type, // 保存provider类型
        }));

        // 默认选中第一个服务商
        if (providerOptions.value.length > 0 && !searchFormState.providerUuid) {
          searchFormState.providerUuid = providerOptions.value[0].value;
          await loadDomains();
        }
      } catch (err) {
        console.log(err);
      } finally {
        providerLoading.value = false;
      }
    }

    async function loadDomains() {
      if (!searchFormState.providerUuid) {
        domainOptions.value = [];
        return;
      }

      try {
        domainLoading.value = true;
        const response = await getDomains({
          providerUuid: searchFormState.providerUuid,
        });
        domainOptions.value = response.data.map((domain: any) => ({
          label: domain.domain,
          value: domain.domain, // 现在直接使用域名字符串
        }));

        // 默认选中第一个域名
        if (domainOptions.value.length > 0 && !searchFormState.domain) {
          searchFormState.domain = domainOptions.value[0].value;
          doSearch();
        }
      } catch (err) {
        console.log(err);
      } finally {
        domainLoading.value = false;
      }
    }

    const onProviderChange = async () => {
      searchFormState.domain = '';
      searchFormState.subdomain = '';
      searchFormState.recordType = undefined;
      searchFormState.searchQuery = '';
      pagination.value.page = 1;
      await loadDomains();
    };

    const onDomainChange = () => {
      searchFormState.subdomain = '';
      searchFormState.recordType = undefined;
      searchFormState.searchQuery = '';
      // searchFormState.status = undefined;
      pagination.value.page = 1;
      doSearch();
    };

    const handleAdd = () => {
      if (!searchFormState.providerUuid) {
        message.warning('请先选择服务商');
        return;
      }
      if (!searchFormState.domain) {
        message.warning('请先选择主域名');
        return;
      }
      drawerTitle.value = '新增';
      editDrawer.value.open(Mode.Add, '');
    };

    function handleEdit(item: any) {
      drawerTitle.value = '编辑';
      editDrawer.value.open(Mode.Edit, item);
    }

    async function handleValidate(item: any) {
      try {
        loading.value = true;
        const response = await validateDomainRecord({
          domain: searchFormState.domain, // 使用当前选中的域名UUID
          subdomain: item.subdomain,
          recordType: item.recordType,
          value: item.value,
        });

        if (response.data.success) {
          message.success(`验证成功: ${response.data.message}`);
        } else {
          message.error(`验证失败: ${response.data.message}`);
        }
      } catch (err) {
        message.error('验证失败');
        console.log(err);
      } finally {
        loading.value = false;
      }
    }

    // 删除单个记录
    function handleDeleteSingle(record: any) {
      deleteDrawer.value.open(record);
    }

    // 批量删除记录
    function handleDeleteBatch(records: any[]) {
      if (!records || records.length === 0) {
        message.warning('请选择要删除的记录');
        return;
      }
      deleteDrawer.value.open(records);
    }

    // 统一的删除处理函数
    function handleDelete(records: any[]) {
      if (!records || records.length === 0) {
        message.warning('请选择要删除的记录');
        return;
      }

      if (records.length === 1) {
        handleDeleteSingle(records[0]);
      } else {
        handleDeleteBatch(records);
      }
      // 清空选择
      state.selectedRowKeys = [];
    }

    // 检查URL参数并自动打开审批抽屉
    async function checkAndOpenApprovalDrawer() {
      const { approval, uuid } = route.query;
      if (approval === 'true' && uuid) {
        try {
          const response = await getDomainRecordInfo({ uuid: uuid });
          const recordInfo = response.data;
          // 检查记录状态
          if (recordInfo.status === 'pending') {
            debugger;
            handleApprove(recordInfo);
          } else {
            message.warning('该记录不是待审批状态');
          }
        } catch (err) {
          message.warning('未找到指定的记录');
        }
        // 清除URL参数
        router.replace({
          path: route.path,
          query: { ...route.query, approval: undefined, uuid: undefined },
        });
      }
    }

    onMounted(async function () {
      window.onresize = () => {
        return (() => {
          tableConfig.value.windowHeight =
            document.documentElement.clientHeight;
        })();
      };
      await loadProviders();
      checkAndOpenApprovalDrawer();
    });

    // 多选
    type Key = string | number;
    const state = reactive<{
      selectedRowKeys: Key[];
      loading: boolean;
    }>({
      selectedRowKeys: [], // Check here to configure the default column
      loading: false,
    });
    const hasSelected = computed(() => state.selectedRowKeys.length > 0);
    const onSelectChange = (selectedRowKeys: Key[]) => {
      console.log('selectedRowKeys changed: ', selectedRowKeys);
      state.selectedRowKeys = selectedRowKeys;
    };

    // 多选删除
    function deleteBySelect(ids: any[]) {
      // 获取选中记录的完整信息
      const selectedRecords = dataSource.value.filter((record: any) =>
        ids.includes(record.uuid),
      );
      handleDelete(selectedRecords);
    }

    function handleSuccess() {
      doSearch();
    }

    const searchFormRef = ref();
    const searchFormState = reactive({
      providerUuid: '',
      domain: '',
      subdomain: '',
      recordType: undefined,
      status: 'active',
      searchType: 'subdomain',
      searchQuery: '',
    });

    const handlerTableChange = (page: {
      pageSize: number;
      current: number;
    }) => {
      pagination.value.page = page.current;
      pagination.value.pageSize = page.pageSize;
      doSearch();
    };

    // 根据选中的服务商UUID获取服务商对象
    const selectedProviderObject = computed(() => {
      const selectedOption = providerOptions.value.find(
        (option: any) => option.value === searchFormState.providerUuid,
      );
      return selectedOption
        ? {
            label: selectedOption.label,
            value: selectedOption.value,
            type: selectedOption.type,
          }
        : { label: '', value: '', type: '' };
    });

    // 根据选中的域名获取域名对象
    const selectedDomainObject = computed(() => {
      const selectedOption = domainOptions.value.find(
        (option: any) => option.value === searchFormState.domain,
      );
      return selectedOption
        ? { label: selectedOption.label, value: selectedOption.value }
        : { label: '', value: '' };
    });

    function handleApprove(item: any) {
      approvalDrawer.value.open(item);
    }

    // 获取操作类型的颜色
    function getActionColor(action: string) {
      const actionColorMap: Record<string, string> = {
        create: 'blue',
        update: 'orange',
        delete: 'red',
      };
      return actionColorMap[action] || 'default';
    }

    // 服务商下拉框搜索过滤
    function filterProviderOption(input: string, option: any) {
      return option.label.toLowerCase().includes(input.toLowerCase());
    }

    // 域名下拉框搜索过滤
    function filterDomainOption(input: string, option: any) {
      return option.label.toLowerCase().includes(input.toLowerCase());
    }

    // 判断记录类型是否可代理
    function isProxyableRecord(recordType: string) {
      return ['A', 'AAAA', 'CNAME'].includes(recordType);
    }

    return {
      searchFormState,
      searchFormRef,
      dataSource,
      pagination,
      loading,
      editDrawer,
      approvalDrawer,
      deleteDrawer,
      drawerTitle,
      providerOptions,
      providerLoading,
      domainOptions,
      domainLoading,
      selectedProviderObject,
      selectedDomainObject,
      handleAdd,
      handleEdit,
      handleApprove,
      handleValidate,
      handleDelete,
      handleDeleteSingle,
      handleDeleteBatch,
      tableConfig,
      hasSelected,
      onSelectChange,
      state,
      deleteBySelect,
      handleSuccess,
      doSearch,
      handlerTableChange,
      onProviderChange,
      onDomainChange,
      RECORD_TYPE_OPTIONS,
      RECORD_STATUS_OPTIONS,
      getStatusColor,
      getStatusText,
      getRecordTypeDescription,
      getRecordTypeColor,
      getActionColor,
      checkAndOpenApprovalDrawer,
      filterProviderOption,
      filterDomainOption,
      isProxyableRecord,
    };
  },
  methods: {
    columns() {
      return columns;
    },
  },
};
</script>

<style scoped>
.ant-card {
  height: 100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}

/* 去掉组合输入框中相邻元素的圆角 */
:deep(.pilot-input-group .pilot-select) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

:deep(.pilot-input-group .pilot-select .pilot-select-selector) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

:deep(.pilot-input-group .pilot-input-affix-wrapper) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

:deep(.pilot-input-group .pilot-input-search) {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
</style>
