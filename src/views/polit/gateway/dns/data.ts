export const columns = [
  {
    title: '序号',
    width: 60,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    width: 200,
    title: '完整域名',
    dataIndex: 'fullDomain',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 80,
    title: '记录类型',
    dataIndex: 'recordType',
    align: 'center',
    key: 'recordType',
  },
  {
    width: 200,
    title: '记录值',
    dataIndex: 'value',
    align: 'center',
    ellipsis: true,
  },
  // {
  //   width: 80,
  //   title: 'TTL',
  //   dataIndex: 'ttl',
  //   align: 'center',
  // },
  // {
  //   width: 80,
  //   title: '优先级',
  //   dataIndex: 'priority',
  //   align: 'center',
  // },
  // {
  //   title: '变更类型',
  //   dataIndex: 'action',
  //   width: 80,
  //   align: 'center',
  //   key: 'action',
  // },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   width: 80,
  //   align: 'center',
  //   key: 'status',
  // },
  // {
  //   title: '创建人',
  //   dataIndex: 'creator',
  //   width: 100,
  //   align: 'center',
  //   ellipsis: true,
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createdAt',
  //   width: 130,
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    align: 'center',
    width: 120,
  },
];

export enum Mode {
  Add = 'add',
  Edit = 'edit',
  View = 'view',
}
