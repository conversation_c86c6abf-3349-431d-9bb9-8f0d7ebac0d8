<template>
  <a-drawer
    title="审批DNS记录"
    :visible="visible"
    :width="600"
    placement="right"
    @close="handleCancel"
  >
    <!-- 右上角按钮 -->
    <template #extra>
      <a-space size="middle">
        <a-button
          type="primary"
          danger
          :loading="loading"
          @click="handleReject"
        >
          拒绝
        </a-button>
        <a-button type="primary" :loading="loading" @click="handleApprove">
          通过
        </a-button>
      </a-space>
    </template>

    <!-- 记录信息对比 -->
    <div class="approval-content">
      <h4>待审批记录信息</h4>
      <a-descriptions :column="1" bordered size="small">
        <a-descriptions-item label="变更类型">
          <a-tag :color="getActionColor(record.action)">
            {{ getActionText(record.action) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="完整域名">
          {{ record.fullDomain }}
        </a-descriptions-item>
        <a-descriptions-item label="记录类型">
          <a-tag :color="getRecordTypeColor(record.recordType)" size="small">
            {{ record.recordType }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="记录值">
          {{ record.value }}
        </a-descriptions-item>
        <a-descriptions-item label="TTL">
          {{ record.ttl }}
        </a-descriptions-item>
        <a-descriptions-item v-if="record.priority" label="优先级">
          {{ record.priority }}
        </a-descriptions-item>
        <a-descriptions-item v-if="record.comment" label="备注">
          {{ record.comment }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 审批意见 -->
      <div style="margin-top: 24px">
        <h4>审批意见</h4>
        <a-textarea
          v-model:value="comment"
          :rows="6"
          placeholder="请输入审批意见..."
          :maxlength="500"
          show-count
        />
      </div>
    </div>
  </a-drawer>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { message } from 'ant-design-vue';
import {
  reviewDomainRecord,
  getStatusText,
  getRecordTypeColor,
} from '@/api/pilot/domain';

export default defineComponent({
  name: 'ApprovalDrawer',
  emits: ['success', 'cancel'],
  setup(props, { emit }) {
    const visible = ref(false);
    const loading = ref(false);
    const comment = ref('');
    const record = ref<any>({});

    const open = (recordData: any) => {
      record.value = recordData;
      comment.value = '';
      visible.value = true;
    };

    const handleCancel = () => {
      visible.value = false;
      emit('cancel');
    };

    const handleApprove = async () => {
      if (!comment.value.trim()) {
        message.warning('请输入审批意见');
        return;
      }

      try {
        loading.value = true;
        const params = {
          uuid: record.value.uuid,
          action: 'approved' as const,
          comment: comment.value.trim(),
        };
        await reviewDomainRecord(params);
        message.success('审批通过，记录已应用到云平台');
        visible.value = false;
        emit('success');
      } catch (err) {
        message.error('审批失败');
        console.error(err);
      } finally {
        loading.value = false;
      }
    };

    const handleReject = async () => {
      if (!comment.value.trim()) {
        message.warning('请输入拒绝原因');
        return;
      }

      try {
        loading.value = true;
        const params = {
          uuid: record.value.uuid,
          action: 'rejected' as const,
          comment: comment.value.trim(),
        };
        await reviewDomainRecord(params);
        message.success('已拒绝该记录');
        visible.value = false;
        emit('success');
      } catch (err) {
        message.error('操作失败');
        console.error(err);
      } finally {
        loading.value = false;
      }
    };

    // 获取操作类型的颜色
    const getActionColor = (action: string) => {
      const actionColorMap: Record<string, string> = {
        create: 'blue',
        update: 'orange',
        delete: 'red',
      };
      return actionColorMap[action] || 'default';
    };

    // 获取操作类型的文本
    const getActionText = (action: string) => {
      return getStatusText(action);
    };

    return {
      visible,
      loading,
      comment,
      record,
      open,
      handleCancel,
      handleApprove,
      handleReject,
      getActionColor,
      getActionText,
      getRecordTypeColor,
      getStatusText,
    };
  },
});
</script>

<style scoped>
.approval-content {
  padding: 0;
}

h4 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}
</style>
