<template>
  <a-drawer
    :title="title"
    :width="720"
    :visible="show"
    :mask-closable="action.mode === Mode.View"
    :footer-style="{ textAlign: 'right' }"
    @close="close"
  >
    <template #extra>
      <a-space v-if="action.mode != Mode.View">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" :loading="loading" @click="modalOk">
          保存
        </a-button>
      </a-space>
    </template>
    <!--content-->
    <a-spin :spinning="dataLoading" tip="正在加载数据...">
      <a-form ref="formRef" :model="formState" :label-col="{ span: 4 }">
        <a-form-item label="主域名" name="domain" :rules="rules.domain">
          <a-input
            v-model:value="formState.domainDisplay"
            placeholder="请输入主域名"
            disabled
          />
        </a-form-item>
        <a-form-item label="子域名" name="subdomain">
          <a-input
            v-model:value="formState.subdomain"
            placeholder="请输入子域名（可选）"
          />
        </a-form-item>
        <a-form-item
          label="记录类型"
          name="recordType"
          :rules="rules.recordType"
        >
          <a-select
            v-model:value="formState.recordType"
            placeholder="请选择记录类型"
            :options="RECORD_TYPE_OPTIONS"
          />
        </a-form-item>
        <a-form-item label="记录值" name="value" :rules="rules.value">
          <a-input
            v-model:value="formState.value"
            :placeholder="getValuePlaceholder()"
          />
        </a-form-item>
        <a-form-item label="TTL" name="ttl">
          <a-input-number
            v-model:value="formState.ttl"
            :min="60"
            :max="86400"
            placeholder="请输入TTL值（秒）"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="优先级" name="priority">
          <a-input-number
            v-model:value="formState.priority"
            :min="0"
            :max="65535"
            placeholder="请输入优先级（MX记录使用）"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item v-if="isProxySupported()" label="DNS代理" name="proxied">
          <a-switch
            v-model:checked="formState.proxied"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <!--          <div style="margin-top: 8px; color: #666; font-size: 12px;">-->
          <!--            仅支持A、AAAA、CNAME记录类型。开启后将通过Cloudflare CDN代理，提供缓存、安全防护等功能。-->
          <!--          </div>-->
        </a-form-item>
        <a-form-item label="备注" name="comment">
          <a-textarea
            v-model:value="formState.comment"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="申请原因" name="reason" :rules="rules.reason">
          <a-textarea
            v-model:value="formState.reason"
            placeholder="请输入申请原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, watch, nextTick } from 'vue';
import { Mode } from './data';
import { message } from 'ant-design-vue';
import {
  addDomainRecord,
  updateDomainRecord,
  RECORD_TYPE_OPTIONS,
} from '@/api/pilot/domain';

export default defineComponent({
  name: 'EditDrawer',
  props: {
    title: {
      type: String,
      required: true,
    },
    selectedProvider: {
      type: Object,
      required: true,
    },
    selectedDomain: {
      type: Object,
      required: true,
    },
  },
  emits: ['ok'],
  setup(props, context) {
    let show = ref<boolean>(false);
    const action = reactive({
      mode: '',
      uuid: '',
    });
    const formRef = ref();
    const formState = reactive({
      providerUuid: '', // 服务商UUID
      domain: '', // 域名字符串
      domainDisplay: '', // 用于显示的域名名称
      subdomain: '',
      recordType: undefined,
      value: '',
      ttl: 600,
      priority: 0,
      comment: '',
      reason: '',
      prevRecord: null, // 原记录信息，用于更新线上记录
      proxied: false, // 新增：DNS代理开关
    });

    const modalOk = () => {
      handleSubmit();
    };

    const open = async (mode: string, record: string | any) => {
      action.mode = mode;

      // 先重置表单
      resetForm();

      // 等待下一个 tick 确保表单引用存在，然后重置表单字段
      await nextTick();
      if (formRef.value) {
        formRef.value.resetFields();
      }

      show.value = true;

      // 设置选中的主域名
      formState.domain = props.selectedDomain.value;
      formState.domainDisplay = props.selectedDomain.label;

      // 根据模式自动设置操作类型
      if (mode === 'edit') {
        try {
          dataLoading.value = true;
          action.uuid = record.uuid || '';
          Object.assign(formState, {
            providerUuid: props.selectedProvider.value,
            domain: props.selectedDomain.value,
            domainDisplay: props.selectedDomain.label,
            subdomain: record.subdomain || '',
            recordType: record.recordType,
            value: record.value || '',
            ttl: record.ttl || 600,
            priority: record.priority || 0,
            comment: record.comment || '',
            reason: '', // 重置申请原因
            prevRecord: record, // 保存原记录信息
            proxied: record.proxied || false, // 加载DNS代理开关状态
          });
        } catch (err) {
          console.log(err);
          message.error('获取记录数据失败');
        } finally {
          dataLoading.value = false;
        }
      } else {
        // 新增模式：确保所有字段都被重置
        Object.assign(formState, {
          providerUuid: props.selectedProvider.value,
          domain: props.selectedDomain.value,
          domainDisplay: props.selectedDomain.label,
          subdomain: '',
          recordType: undefined,
          value: '',
          ttl: 600,
          priority: 0,
          comment: '',
          reason: '',
          prevRecord: null,
          proxied: false, // 新增：新增模式下默认关闭
        });
      }
    };

    const finish = () => {
      show.value = false;
      context.emit('ok');
    };

    const close = () => {
      show.value = false;
    };

    async function handleSubmit() {
      try {
        loading.value = true;
        const values = await formRef.value.validate();

        // 构建完整的提交数据，包含formState中的重要字段
        const submitValues = {
          ...values,
          providerUuid: formState.providerUuid,
          domain: formState.domain,
        };

        // 移除用于显示的字段
        delete submitValues.domainDisplay;

        if (action.mode === 'edit') {
          submitValues.prevRecord = formState.prevRecord;
          submitValues.uuid = action.uuid; // 如果是编辑模式，可能需要UUID
          await updateDomainRecord(submitValues);
        } else {
          // 新增模式，确保包含正确的providerUuid和domain
          submitValues.providerUuid = props.selectedProvider.value;
          submitValues.domain = props.selectedDomain.value;
          await addDomainRecord(submitValues);
        }
        message.success('提交成功');
        finish();
      } catch (err) {
        console.log(err);
        message.error('提交失败，请重试');
      } finally {
        loading.value = false;
      }
    }

    // 验证规则
    const validateValue = (rule: any, value: string) => {
      if (!value) {
        return Promise.reject('请输入记录值');
      }

      const recordType = formState.recordType;

      switch (recordType) {
        case 'A':
          // IPv4地址验证
          const ipv4Regex =
            /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
          if (!ipv4Regex.test(value)) {
            return Promise.reject('请输入有效的IPv4地址，如：***********');
          }
          break;

        case 'AAAA':
          // IPv6地址验证
          const ipv6Regex =
            /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
          const ipv6CompressedRegex =
            /^(?:[0-9a-fA-F]{1,4}:)*::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$|^(?:[0-9a-fA-F]{1,4}:)+[0-9a-fA-F]{1,4}$/;
          if (!ipv6Regex.test(value) && !ipv6CompressedRegex.test(value)) {
            return Promise.reject('请输入有效的IPv6地址，如：2001:db8::1');
          }
          break;

        case 'CNAME':
        case 'NS':
          // 域名格式验证
          const domainRegex =
            /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.?$/;
          if (!domainRegex.test(value)) {
            return Promise.reject('请输入有效的域名，如：example.com');
          }
          break;

        case 'MX':
          // MX记录值通常只是域名，优先级在priority字段设置
          const mxDomainRegex =
            /^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.?$/;
          if (!mxDomainRegex.test(value)) {
            return Promise.reject(
              '请输入有效的邮件服务器域名，如：mail.example.com',
            );
          }
          break;

        case 'SRV':
          // SRV记录格式验证：优先级 权重 端口 目标
          const srvRegex =
            /^(\d{1,5})\s+(\d{1,5})\s+(\d{1,5})\s+([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.?$/;
          if (!srvRegex.test(value)) {
            return Promise.reject(
              '请输入有效的SRV记录，格式：优先级 权重 端口 目标，如：10 20 80 target.example.com',
            );
          }
          break;

        case 'TXT':
          // TXT记录相对宽松，但有长度限制
          if (value.length > 255) {
            return Promise.reject('TXT记录内容不能超过255个字符');
          }
          break;

        default:
          // 默认非空验证
          if (!value.trim()) {
            return Promise.reject('请输入记录值');
          }
      }

      return Promise.resolve();
    };

    const rules = {
      domain: [{ required: true, message: '请输入主域名' }],
      recordType: [{ required: true, message: '请选择记录类型' }],
      value: [{ validator: validateValue, trigger: 'blur' }],
      reason: [{ required: true, message: '请输入申请原因' }],
    };

    const loading = ref(false);
    const dataLoading = ref(false);

    const resetForm = () => {
      formRef.value?.resetFields();
      Object.assign(formState, {
        domain: props.selectedDomain.value, // UUID用于提交
        domainDisplay: props.selectedDomain.label, // 域名名称用于显示
        subdomain: '',
        recordType: undefined,
        value: '',
        ttl: 600,
        priority: 0,
        comment: '',
        reason: '',
        prevRecord: null, // 重置原记录信息
        proxied: false, // 重置DNS代理开关
      });
    };

    // 监听selectedDomain变化
    watch(
      () => props.selectedDomain,
      (newDomain) => {
        if (newDomain && !action.uuid) {
          formState.domain = newDomain.value;
          formState.domainDisplay = newDomain.label;
        }
      },
    );

    // 获取value字段的占位符文本
    const getValuePlaceholder = () => {
      switch (formState.recordType) {
        case 'A':
          return '请输入IPv4地址，如：***********';
        case 'AAAA':
          return '请输入IPv6地址，如：2001:db8::1';
        case 'CNAME':
          return '请输入目标域名，如：example.com';
        case 'MX':
          return '请输入邮件服务器域名，如：mail.example.com';
        case 'TXT':
          return '请输入文本内容';
        case 'SRV':
          return '请输入优先级 权重 端口 目标，如：10 20 80 target.example.com';
        case 'NS':
          return '请输入名称服务器，如：ns1.example.com';
        default:
          return '请输入记录值';
      }
    };

    // 检查是否支持DNS代理
    const isProxySupported = () => {
      // 首先检查provider是否为cloudflare
      const isCloudflareProvider =
        props.selectedProvider?.type === 'cloudflare';

      // 然后检查记录类型是否支持代理
      const isSupportedRecordType = ['A', 'AAAA', 'CNAME'].includes(
        formState.recordType,
      );

      return isCloudflareProvider && isSupportedRecordType;
    };

    return {
      loading,
      dataLoading,
      Mode,
      show,
      action,
      formRef,
      formState,
      modalOk,
      finish,
      open,
      close,
      handleSubmit,
      rules,
      RECORD_TYPE_OPTIONS,
      getValuePlaceholder,
      isProxySupported,
    };
  },
});
</script>
