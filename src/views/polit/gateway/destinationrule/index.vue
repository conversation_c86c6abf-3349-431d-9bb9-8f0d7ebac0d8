<template>
  <a-card :body-style="{ paddingBottom: '0!important' }">
    <!--    <a-tabs id="resource-tabs" v-model:activeKey="activeKey">-->
    <!--      <a-tab-pane key="virtualServices" tab="流量路由">-->
    <index />
    <!--      </a-tab-pane>-->
    <!--      <a-tab-pane key="destinationRules" tab="服务策略">-->
    <!--        <destination-rule-table />-->
    <!--      </a-tab-pane>-->
    <!--      <a-tab-pane key="gateways" tab="Gateways">-->
    <!--        <gateway-table />-->
    <!--      </a-tab-pane>-->
    <!--      <a-tab-pane key="services" tab="Services">-->
    <!--        <service-table />-->
    <!--      </a-tab-pane>-->
    <!--      <a-tab-pane key="deployments" tab="Deployments">-->
    <!--        <deployment-table />-->
    <!--      </a-tab-pane>-->
    <!--    </a-tabs>-->
  </a-card>
</template>

<script setup lang="ts">
import { ref, provide, onMounted, watch } from 'vue';
// import ServiceTable from './components/ServiceTable.vue';
// import DeploymentTable from './components/DeploymentTable.vue';
import Index from '@/views/polit/gateway/virtualservice/index.vue';
// import DestinationRuleTable from './destinationrule/DestinationRuleTable.vue';
// import GatewayTable from './components/GatewayTable.vue';
import { listClusterForSelect } from '@/api/pilot/cluster';
import {
  getNamespaces,
  getGateways,
  getK8sServices,
} from '@/api/pilot/gateway';
import {
  getSelectedCluster,
  setSelectedCluster,
} from '../../cluster/clusterStorage';

const activeKey = ref('virtualServices');
// 提供当前启用的tab给子组件
provide('activeTab', activeKey);

// 拉取集群列表
const clusterOptions = ref([]);
const clusterOptionsLoaded = ref(false);

// 统一管理的集群选择状态
const selectedCluster = ref('');

// namespace相关状态
const namespaceOptions = ref([]);
const namespaceOptionsLoaded = ref(false);

// 网关选项相关状态
const gatewayOptions = ref([]);
const gatewayOptionsLoaded = ref(false);

// 服务选项相关状态
const serviceOptions = ref([]);
const serviceOptionsLoaded = ref(false);

// 提供集群选项和加载状态给子组件
provide('clusterOptions', clusterOptions);
provide('clusterOptionsLoaded', clusterOptionsLoaded);

// 提供namespace选项和加载状态给子组件
provide('namespaceOptions', namespaceOptions);
provide('namespaceOptionsLoaded', namespaceOptionsLoaded);

// 提供网关选项和加载状态给子组件
provide('gatewayOptions', gatewayOptions);
provide('gatewayOptionsLoaded', gatewayOptionsLoaded);

// 提供服务选项和加载状态给子组件
provide('serviceOptions', serviceOptions);
provide('serviceOptionsLoaded', serviceOptionsLoaded);

// 提供统一的集群选择状态和更新方法给子组件
provide('selectedCluster', selectedCluster);
provide('updateSelectedCluster', (cluster: string) => {
  selectedCluster.value = cluster;
  setSelectedCluster(cluster);
  // 当集群改变时，重新加载namespace、网关和服务列表
  loadNamespaces(cluster);
  loadGateways(cluster);
  loadServices(cluster);
});

// 加载namespace列表
const loadNamespaces = async (cluster: string) => {
  if (!cluster) {
    namespaceOptions.value = [];
    return;
  }

  try {
    namespaceOptionsLoaded.value = false;
    const response = await getNamespaces({ cluster });
    namespaceOptions.value = response.data || [];
  } catch (error) {
    console.error('Failed to load namespace options:', error);
    namespaceOptions.value = [];
  } finally {
    namespaceOptionsLoaded.value = true;
  }
};

// 加载网关列表
const loadGateways = async (cluster: string) => {
  if (!cluster) {
    gatewayOptions.value = [];
    return;
  }

  try {
    gatewayOptionsLoaded.value = false;
    const response = await getGateways({
      cluster,
      page: 1,
      pageSize: 1000, // 获取所有网关
    });

    // 转换为选项格式
    gatewayOptions.value = (response.data?.list || []).map((item: any) => ({
      label: `${item.name} (${item.namespace})`,
      value: item.name,
      namespace: item.namespace,
    }));
  } catch (error) {
    console.error('Failed to load gateway options:', error);
    gatewayOptions.value = [];
  } finally {
    gatewayOptionsLoaded.value = true;
  }
};

// 加载服务列表
const loadServices = async (cluster: string) => {
  if (!cluster) {
    serviceOptions.value = [];
    return;
  }

  try {
    serviceOptionsLoaded.value = false;
    const response = await getK8sServices({
      cluster,
      page: 1,
      pageSize: 1000, // 获取所有服务
    });

    // 转换为选项格式
    serviceOptions.value = (response.data?.list || []).map((item: any) => ({
      label: `${item.name}.${item.namespace}`,
      value: `${item.name}.${item.namespace}`,
      name: item.name,
      namespace: item.namespace,
      ports: item.ports || [],
    }));
  } catch (error) {
    console.error('Failed to load service options:', error);
    serviceOptions.value = [];
  } finally {
    serviceOptionsLoaded.value = true;
  }
};

// 初始化集群选择
const initializeClusterSelection = () => {
  if (clusterOptions.value.length === 0) {
    return;
  }

  const savedCluster = getSelectedCluster();

  if (
    savedCluster &&
    clusterOptions.value.some(
      (option) =>
        option.value === savedCluster || option.label === savedCluster,
    )
  ) {
    // 如果保存的集群在当前选项中存在，使用保存的集群
    selectedCluster.value = savedCluster;
  } else {
    // 否则使用第一个选项作为默认值
    if (clusterOptions.value.length > 0) {
      const defaultCluster =
        clusterOptions.value[0].value || clusterOptions.value[0].label;
      selectedCluster.value = defaultCluster;
      setSelectedCluster(defaultCluster);
    }
  }

  // 加载对应集群的相关列表
  if (selectedCluster.value) {
    loadNamespaces(selectedCluster.value);
    loadGateways(selectedCluster.value);
    loadServices(selectedCluster.value);
  }
};

onMounted(async () => {
  try {
    const response = await listClusterForSelect({ enable: true });
    clusterOptions.value = response.data?.list?.map((item: any) => ({
      label: item.name,
      value: item.uuid,
    }));
    clusterOptionsLoaded.value = true;
    // 集群选项加载完成后初始化集群选择
    initializeClusterSelection();
  } catch (error) {
    console.error('Failed to load cluster options:', error);
  } finally {
    clusterOptionsLoaded.value = true; // 即使失败也标记为已加载，避免子组件一直等待
  }
});

// 监听集群选项变化，重新初始化集群选择
watch(
  clusterOptions,
  () => {
    if (clusterOptions.value.length > 0) {
      initializeClusterSelection();
    }
  },
  { deep: true },
);
</script>

<style scoped>
:deep(#resource-tabs .pilot-tabs-tab) {
  padding-top: 0px !important;
}
</style>
