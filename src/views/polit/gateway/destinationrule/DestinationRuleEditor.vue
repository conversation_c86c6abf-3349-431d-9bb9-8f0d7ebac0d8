<template>
  <a-drawer
    :title="isEdit ? '编辑' : '新增'"
    :open="visible"
    width="65%"
    :closable="true"
    :body-style="{ paddingTop: '0!important' }"
    @close="handleClose"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleClose">取消</a-button>
        <a-button type="default" :loading="dryRunLoading" @click="handleDryRun">
          <experiment-outlined />
          Dry Run
        </a-button>
        <a-button type="primary" :loading="submitLoading" @click="handleSubmit">
          提交
        </a-button>
      </a-space>
    </template>
    <div class="destination-rule-editor">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- 基本信息Tab -->
        <a-tab-pane key="basic" tab="基本信息">
          <a-form :model="formData">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item
                  label="配置名称"
                  name="name"
                  :rules="[{ required: true, message: '请输入名称' }]"
                >
                  <a-input
                    v-model:value="formData.name"
                    :disabled="isEdit"
                    placeholder="请输入服务策略名称"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="命名空间"
                  name="namespace"
                  :rules="[{ required: true, message: '请选择命名空间' }]"
                >
                  <a-select
                    v-model:value="formData.namespace"
                    :disabled="isEdit"
                    placeholder="请选择命名空间"
                  >
                    <a-select-option
                      v-for="ns in namespaceOptions"
                      :key="ns.value"
                      :value="ns.value"
                    >
                      {{ ns.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="目标主机"
                  name="host"
                  :rules="[{ required: true, message: '请输入目标主机' }]"
                >
                  <a-input
                    v-model:value="formData.host"
                    placeholder="请输入目标主机，如：example-service.default.svc.cluster.local"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>

        <!-- 流量策略Tab -->
        <a-tab-pane key="traffic" tab="流量策略">
          <div class="traffic-policy-container">
            <div class="policy-section">
              <div class="section-header">
                <h3 class="section-title">连接池配置</h3>
                <a-switch
                  v-model:checked="
                    formData.trafficPolicy.connectionPool.enabled
                  "
                  size="small"
                />
              </div>
              <div
                v-if="formData.trafficPolicy.connectionPool.enabled"
                class="section-content"
              >
                <a-row :gutter="24" align="middle">
                  <a-col :span="8">
                    <a-form-item
                      label="TCP连接数"
                      class="inline-form-item"
                      :label-col="{ span: 10 }"
                      :wrapper-col="{ span: 14 }"
                    >
                      <a-input-number
                        v-model:value="
                          formData.trafficPolicy.connectionPool.tcp
                            .maxConnections
                        "
                        :min="1"
                        placeholder="100"
                        size="small"
                        style="width: 100px"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="连接超时"
                      class="inline-form-item"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <a-input
                        v-model:value="
                          formData.trafficPolicy.connectionPool.tcp
                            .connectTimeout
                        "
                        placeholder="30s"
                        size="small"
                        style="width: 100px"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="TCP保活时间"
                      class="inline-form-item"
                      :label-col="{ span: 12 }"
                      :wrapper-col="{ span: 12 }"
                    >
                      <a-input
                        v-model:value="
                          formData.trafficPolicy.connectionPool.tcp.tcpKeepalive
                            .time
                        "
                        placeholder="7200s"
                        size="small"
                        style="width: 100px"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>

            <div class="policy-section">
              <div class="section-header">
                <h3 class="section-title">负载均衡</h3>
                <a-switch
                  v-model:checked="formData.trafficPolicy.loadBalancer.enabled"
                  size="small"
                />
              </div>
              <div
                v-if="formData.trafficPolicy.loadBalancer.enabled"
                class="section-content"
              >
                <a-row :gutter="24" align="middle">
                  <a-col :span="12">
                    <a-form-item
                      label="负载均衡算法"
                      class="inline-form-item"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <a-select
                        v-model:value="
                          formData.trafficPolicy.loadBalancer.simple
                        "
                        placeholder="选择负载均衡算法"
                        size="small"
                        style="width: 160px"
                      >
                        <a-select-option value="ROUND_ROBIN">
                          轮询
                        </a-select-option>
                        <a-select-option value="LEAST_CONN">
                          最少连接
                        </a-select-option>
                        <a-select-option value="RANDOM">随机</a-select-option>
                        <a-select-option value="PASSTHROUGH">
                          透传
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item
                      label="一致性哈希"
                      class="inline-form-item"
                      :label-col="{ span: 7 }"
                      :wrapper-col="{ span: 17 }"
                    >
                      <a-input
                        v-model:value="
                          formData.trafficPolicy.loadBalancer.consistentHash
                            .httpCookie.name
                        "
                        placeholder="Cookie名称（可选）"
                        size="small"
                        style="width: 180px"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>

            <!-- 异常检测（熔断）-->
            <div class="policy-section">
              <div class="section-header">
                <h3 class="section-title">异常检测（熔断）</h3>
                <a-switch
                  v-model:checked="
                    formData.trafficPolicy.outlierDetection.enabled
                  "
                  size="small"
                />
              </div>
              <div
                v-if="formData.trafficPolicy.outlierDetection.enabled"
                class="section-content"
              >
                <a-row :gutter="24" align="middle">
                  <a-col :span="8">
                    <a-form-item
                      label="连续错误数"
                      class="inline-form-item"
                      :label-col="{ span: 10 }"
                      :wrapper-col="{ span: 14 }"
                      help="连续5xx错误次数阈值"
                    >
                      <a-input-number
                        v-model:value="
                          formData.trafficPolicy.outlierDetection
                            .consecutive5xxErrors
                        "
                        :min="1"
                        :max="100"
                        placeholder="5"
                        size="small"
                        style="width: 80px"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="检测间隔"
                      class="inline-form-item"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      help="异常检测分析间隔"
                    >
                      <a-input
                        v-model:value="
                          formData.trafficPolicy.outlierDetection.interval
                        "
                        placeholder="30s"
                        size="small"
                        style="width: 100px"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="基础驱逐时间"
                      class="inline-form-item"
                      :label-col="{ span: 12 }"
                      :wrapper-col="{ span: 12 }"
                      help="最小驱逐时间"
                    >
                      <a-input
                        v-model:value="
                          formData.trafficPolicy.outlierDetection
                            .baseEjectionTime
                        "
                        placeholder="30s"
                        size="small"
                        style="width: 100px"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24" align="middle">
                  <a-col :span="12">
                    <a-form-item
                      label="最大驱逐比例"
                      class="inline-form-item"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      help="可驱逐的最大实例比例"
                    >
                      <a-input-number
                        v-model:value="
                          formData.trafficPolicy.outlierDetection
                            .maxEjectionPercent
                        "
                        :min="0"
                        :max="100"
                        placeholder="50"
                        addon-after="%"
                        size="small"
                        style="width: 120px"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item
                      label="最小健康比例"
                      class="inline-form-item"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      help="保持健康的最小实例比例"
                    >
                      <a-input-number
                        v-model:value="
                          formData.trafficPolicy.outlierDetection
                            .minHealthPercent
                        "
                        :min="0"
                        :max="100"
                        placeholder="50"
                        addon-after="%"
                        size="small"
                        style="width: 120px"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>

            <!-- TLS设置 -->
            <div class="policy-section">
              <div class="section-header">
                <h3 class="section-title">TLS设置</h3>
                <a-switch
                  v-model:checked="formData.trafficPolicy.tls.enabled"
                  size="small"
                />
              </div>
              <div
                v-if="formData.trafficPolicy.tls.enabled"
                class="section-content"
              >
                <a-row :gutter="24" align="middle">
                  <a-col :span="12">
                    <a-form-item
                      label="TLS模式"
                      class="inline-form-item"
                      :label-col="{ span: 6 }"
                      :wrapper-col="{ span: 18 }"
                    >
                      <a-select
                        v-model:value="formData.trafficPolicy.tls.mode"
                        placeholder="选择TLS模式"
                        size="small"
                        style="width: 160px"
                      >
                        <a-select-option value="DISABLE">禁用</a-select-option>
                        <a-select-option value="SIMPLE">
                          简单TLS
                        </a-select-option>
                        <a-select-option value="MUTUAL">
                          双向TLS
                        </a-select-option>
                        <a-select-option value="ISTIO_MUTUAL">
                          Istio双向TLS
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item
                      label="SNI"
                      class="inline-form-item"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                      help="服务器名称指示"
                    >
                      <a-input
                        v-model:value="formData.trafficPolicy.tls.sni"
                        placeholder="example.com"
                        size="small"
                        style="width: 200px"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <template v-if="formData.trafficPolicy.tls.mode === 'MUTUAL'">
                  <a-row :gutter="24" align="middle">
                    <a-col :span="12">
                      <a-form-item
                        label="客户端证书"
                        class="inline-form-item"
                        :label-col="{ span: 8 }"
                        :wrapper-col="{ span: 16 }"
                      >
                        <a-input
                          v-model:value="
                            formData.trafficPolicy.tls.clientCertificate
                          "
                          placeholder="/etc/certs/client.pem"
                          size="small"
                          style="width: 200px"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item
                        label="私钥"
                        class="inline-form-item"
                        :label-col="{ span: 4 }"
                        :wrapper-col="{ span: 20 }"
                      >
                        <a-input
                          v-model:value="formData.trafficPolicy.tls.privateKey"
                          placeholder="/etc/certs/client-key.pem"
                          size="small"
                          style="width: 200px"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="24" align="middle">
                    <a-col :span="24">
                      <a-form-item
                        label="CA证书"
                        class="inline-form-item"
                        :label-col="{ span: 3 }"
                        :wrapper-col="{ span: 21 }"
                      >
                        <a-input
                          v-model:value="
                            formData.trafficPolicy.tls.caCertificates
                          "
                          placeholder="/etc/certs/ca.pem"
                          size="small"
                          style="width: 300px"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 子集配置Tab -->
        <a-tab-pane key="subsets" tab="子集配置">
          <div class="subsets-container">
            <div class="subsets-header">
              <div class="header-left">
                <span class="header-title">子集配置</span>
                <span class="header-count">
                  {{ formData.subsets.length }} 个子集
                </span>
              </div>
              <a-button type="primary" @click="addSubset">
                <plus-outlined />
                添加子集
              </a-button>
            </div>

            <div v-if="formData.subsets.length === 0" class="empty-subsets">
              <a-empty description="暂无子集配置">
                <a-button type="primary" @click="addSubset">
                  添加第一个子集
                </a-button>
              </a-empty>
            </div>

            <div class="subsets-list">
              <div
                v-for="(subset, index) in formData.subsets"
                :key="index"
                class="subset-card"
              >
                <div class="subset-header">
                  <div class="subset-left">
                    <div class="subset-order">
                      <span class="subset-number">{{ index + 1 }}</span>
                    </div>
                    <div class="subset-info">
                      <div class="subset-title">
                        <span class="subset-name">
                          {{ subset.name || '未命名子集' }}
                        </span>
                        <a-tag
                          v-if="Object.keys(subset.labels).length > 0"
                          color="blue"
                          size="small"
                        >
                          {{ Object.keys(subset.labels).length }} 个标签
                        </a-tag>
                      </div>
                      <div class="subset-description">
                        {{
                          Object.entries(subset.labels)
                            .map(([k, v]) => `${k}=${v}`)
                            .join(', ') || '无标签'
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="subset-actions">
                    <a-space size="small">
                      <a-button
                        type="text"
                        size="small"
                        @click="editSubset(index)"
                      >
                        编辑
                      </a-button>
                      <a-button
                        type="text"
                        size="small"
                        @click="copySubset(index)"
                      >
                        复制
                      </a-button>
                      <a-button
                        type="text"
                        size="small"
                        danger
                        @click="removeSubset(index)"
                      >
                        删除
                      </a-button>
                    </a-space>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 子集编辑Drawer -->
          <a-drawer
            :title="
              currentSubsetIndex === -1
                ? '新增子集'
                : `编辑子集 ${currentSubsetIndex + 1}`
            "
            :open="subsetDrawerVisible"
            width="50%"
            @close="closeSubsetDrawer"
          >
            <template #extra>
              <a-space>
                <a-button @click="closeSubsetDrawer">取消</a-button>
                <a-button type="primary" @click="saveSubset">保存</a-button>
              </a-space>
            </template>

            <div class="subset-editor">
              <a-form :model="currentSubset">
                <a-form-item
                  label="子集名称"
                  name="name"
                  :rules="[{ required: true, message: '请输入子集名称' }]"
                >
                  <a-input
                    v-model:value="currentSubset.name"
                    placeholder="请输入子集名称"
                  />
                </a-form-item>

                <a-form-item label="标签选择器">
                  <div class="labels-editor">
                    <div
                      v-for="(_, key, labelIndex) in currentSubset.labels"
                      :key="labelIndex"
                      class="label-row"
                    >
                      <a-row :gutter="8">
                        <a-col :span="10">
                          <a-input
                            :value="key"
                            placeholder="标签名"
                            @change="(e: any) => updateLabelKey(key, e.target.value)"
                          />
                        </a-col>
                        <a-col :span="10">
                          <a-input
                            v-model:value="currentSubset.labels[key]"
                            placeholder="标签值"
                          />
                        </a-col>
                        <a-col :span="4">
                          <a-button
                            type="text"
                            size="small"
                            danger
                            @click="removeLabel(key)"
                          >
                            删除
                          </a-button>
                        </a-col>
                      </a-row>
                    </div>
                    <a-button
                      type="dashed"
                      style="width: 100%; margin-top: 8px"
                      @click="addLabel"
                    >
                      <plus-outlined />
                      添加标签
                    </a-button>
                  </div>
                </a-form-item>
              </a-form>
            </div>
          </a-drawer>
        </a-tab-pane>

        <!-- YAML编辑Tab -->
        <a-tab-pane key="yaml" tab="代码模式">
          <div class="yaml-editor">
            <div class="yaml-editor-content">
              <code-editor
                v-model="yamlContent"
                :show-expand-btn="false"
                :minimap="true"
                :height="editorHeight"
                :initial-shell-type="'yaml'"
                :label="'YAML配置'"
                :placeholder="'请输入YAML配置'"
              />
            </div>
            <div v-if="yamlError" class="yaml-error">
              <a-alert
                type="error"
                :message="yamlError"
                show-icon
                closable
                @close="yamlError = ''"
              />
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="history" tab="历史版本">
          <tab-history
            :resource-info="{
              cluster: selectedCluster,
              namespace: formData.namespace,
              resourceType: 'destinationrule',
              resourceName: formData.name,
            }"
            @rollback="handleVersionRollback"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, inject } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, ExperimentOutlined } from '@ant-design/icons-vue';
import * as yaml from 'js-yaml';
import CodeEditor from '@c/code-editor/index.vue';
import TabHistory from '@/views/polit/gateway/virtualservice/tab-history.vue';

interface Props {
  visible: boolean;
  isEdit: boolean;
  editData?: any;
}

interface Emits {
  (e: 'close'): void;
  (e: 'submit', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 从父组件注入选项数据
const namespaceOptions = inject('namespaceOptions', ref([]));
const selectedCluster = inject('selectedCluster', ref(''));

const dryRunLoading = ref(false);
const submitLoading = ref(false);
const activeTab = ref('basic');
const yamlContent = ref('');
const yamlError = ref('');
const previousTab = ref('basic');
const currentSubsetIndex = ref(-1);
const subsetDrawerVisible = ref(false);

// 动态计算编辑器高度
const editorHeight = computed(() => {
  return `${document.documentElement.clientHeight - 320}px`;
});

// 表单数据
const formData = reactive({
  name: '',
  namespace: '',
  host: '',
  trafficPolicy: {
    connectionPool: {
      enabled: false,
      tcp: {
        maxConnections: 100,
        connectTimeout: '30s',
        tcpKeepalive: {
          time: '7200s',
        },
      },
    },
    loadBalancer: {
      enabled: false,
      simple: 'ROUND_ROBIN',
      consistentHash: {
        httpCookie: {
          name: '',
        },
      },
    },
    outlierDetection: {
      enabled: false,
      consecutive5xxErrors: 5,
      interval: '30s',
      baseEjectionTime: '30s',
      maxEjectionPercent: 50,
      minHealthPercent: 50,
    },
    tls: {
      enabled: false,
      mode: 'SIMPLE',
      sni: '',
      clientCertificate: '',
      privateKey: '',
      caCertificates: '',
    },
  },
  subsets: [],
});

// 初始化子集模板
const createSubset = () => ({
  name: '',
  labels: {} as Record<string, string>,
});

// 当前编辑的子集
const currentSubset = ref(createSubset());

// YAML预览
const yamlPreview = computed(() => {
  try {
    const destinationRule = {
      apiVersion: 'networking.istio.io/v1',
      kind: 'DestinationRule',
      metadata: {
        name: formData.name,
        namespace: formData.namespace,
      },
      spec: {
        host: formData.host,
        ...((formData.trafficPolicy.connectionPool.enabled ||
          formData.trafficPolicy.loadBalancer.enabled ||
          formData.trafficPolicy.outlierDetection.enabled ||
          formData.trafficPolicy.tls.enabled) && {
          trafficPolicy: {
            ...(formData.trafficPolicy.connectionPool.enabled && {
              connectionPool: {
                tcp: {
                  maxConnections:
                    formData.trafficPolicy.connectionPool.tcp.maxConnections,
                  connectTimeout:
                    formData.trafficPolicy.connectionPool.tcp.connectTimeout,
                  tcpKeepalive: {
                    time: formData.trafficPolicy.connectionPool.tcp.tcpKeepalive
                      .time,
                  },
                },
              },
            }),
            ...(formData.trafficPolicy.loadBalancer.enabled && {
              loadBalancer: {
                simple: formData.trafficPolicy.loadBalancer.simple,
                ...(formData.trafficPolicy.loadBalancer.consistentHash
                  .httpCookie.name && {
                  consistentHash: {
                    httpCookie: {
                      name: formData.trafficPolicy.loadBalancer.consistentHash
                        .httpCookie.name,
                    },
                  },
                }),
              },
            }),
            ...(formData.trafficPolicy.outlierDetection.enabled && {
              outlierDetection: {
                consecutive5xxErrors:
                  formData.trafficPolicy.outlierDetection.consecutive5xxErrors,
                interval: formData.trafficPolicy.outlierDetection.interval,
                baseEjectionTime:
                  formData.trafficPolicy.outlierDetection.baseEjectionTime,
                maxEjectionPercent:
                  formData.trafficPolicy.outlierDetection.maxEjectionPercent,
                minHealthPercent:
                  formData.trafficPolicy.outlierDetection.minHealthPercent,
              },
            }),
            ...(formData.trafficPolicy.tls.enabled && {
              tls: {
                mode: formData.trafficPolicy.tls.mode,
                ...(formData.trafficPolicy.tls.sni && {
                  sni: formData.trafficPolicy.tls.sni,
                }),
                ...(formData.trafficPolicy.tls.mode === 'MUTUAL' && {
                  ...(formData.trafficPolicy.tls.clientCertificate && {
                    clientCertificate:
                      formData.trafficPolicy.tls.clientCertificate,
                  }),
                  ...(formData.trafficPolicy.tls.privateKey && {
                    privateKey: formData.trafficPolicy.tls.privateKey,
                  }),
                  ...(formData.trafficPolicy.tls.caCertificates && {
                    caCertificates: formData.trafficPolicy.tls.caCertificates,
                  }),
                }),
              },
            }),
          },
        }),
        ...(formData.subsets.length > 0 && {
          subsets: formData.subsets.map((subset) => ({
            name: subset.name,
            labels: subset.labels,
          })),
        }),
      },
    };

    return yaml.dump(destinationRule, { indent: 2 });
  } catch (error) {
    return '# YAML生成错误\n' + error;
  }
});

// Tab切换处理
const handleTabChange = (key: string) => {
  try {
    // 如果从YAML tab切换到其他tab，先尝试同步YAML到表单
    if (previousTab.value === 'yaml' && key !== 'yaml') {
      const success = syncFromYaml();
      if (!success) {
        // 同步失败，阻止tab切换
        activeTab.value = 'yaml';
        return;
      }
    }

    // 如果切换到YAML tab，同步表单数据到YAML
    if (key === 'yaml') {
      syncToYaml();
    }

    // 更新previousTab
    previousTab.value = key;
  } catch (error) {
    // 发生错误时，阻止tab切换
    activeTab.value = previousTab.value;
    message.error('Tab切换失败：' + error);
  }
};

// 同步表单数据到YAML
const syncToYaml = () => {
  yamlContent.value = yamlPreview.value;
  yamlError.value = '';
};

// 同步YAML到表单数据
const syncFromYaml = (): boolean => {
  try {
    const parsedYaml = yaml.load(yamlContent.value) as any;

    if (!parsedYaml || !parsedYaml.spec) {
      throw new Error('无效的YAML格式');
    }

    const spec = parsedYaml.spec;

    // 更新基本信息
    formData.name = parsedYaml.metadata?.name || '';
    formData.namespace = parsedYaml.metadata?.namespace || '';
    formData.host = spec.host || '';

    // 更新流量策略
    if (spec.trafficPolicy) {
      const tp = spec.trafficPolicy;

      // 连接池配置
      if (tp.connectionPool) {
        formData.trafficPolicy.connectionPool.enabled = true;
        if (tp.connectionPool.tcp) {
          Object.assign(
            formData.trafficPolicy.connectionPool.tcp,
            tp.connectionPool.tcp,
          );
        }
      }

      // 负载均衡配置
      if (tp.loadBalancer) {
        formData.trafficPolicy.loadBalancer.enabled = true;
        formData.trafficPolicy.loadBalancer.simple =
          tp.loadBalancer.simple || 'ROUND_ROBIN';
        if (tp.loadBalancer.consistentHash?.httpCookie?.name) {
          formData.trafficPolicy.loadBalancer.consistentHash.httpCookie.name =
            tp.loadBalancer.consistentHash.httpCookie.name;
        }
      }

      // 异常检测配置
      if (tp.outlierDetection) {
        formData.trafficPolicy.outlierDetection.enabled = true;
        formData.trafficPolicy.outlierDetection.consecutive5xxErrors =
          tp.outlierDetection.consecutive5xxErrors || 5;
        formData.trafficPolicy.outlierDetection.interval =
          tp.outlierDetection.interval || '30s';
        formData.trafficPolicy.outlierDetection.baseEjectionTime =
          tp.outlierDetection.baseEjectionTime || '30s';
        formData.trafficPolicy.outlierDetection.maxEjectionPercent =
          tp.outlierDetection.maxEjectionPercent || 50;
        formData.trafficPolicy.outlierDetection.minHealthPercent =
          tp.outlierDetection.minHealthPercent || 50;
      }

      // TLS配置
      if (tp.tls) {
        formData.trafficPolicy.tls.enabled = true;
        formData.trafficPolicy.tls.mode = tp.tls.mode || 'SIMPLE';
        formData.trafficPolicy.tls.sni = tp.tls.sni || '';
        formData.trafficPolicy.tls.clientCertificate =
          tp.tls.clientCertificate || '';
        formData.trafficPolicy.tls.privateKey = tp.tls.privateKey || '';
        formData.trafficPolicy.tls.caCertificates = tp.tls.caCertificates || '';
      }
    }

    // 更新子集配置
    formData.subsets = (spec.subsets || []).map((subset: any) => ({
      name: subset.name,
      labels: subset.labels || {},
    }));

    yamlError.value = '';
    return true;
  } catch (error) {
    yamlError.value = `YAML解析错误: ${error}`;
    message.error('YAML解析失败，请检查格式');
    return false;
  }
};

// 添加子集
const addSubset = () => {
  currentSubsetIndex.value = -1;
  currentSubset.value = createSubset();
  subsetDrawerVisible.value = true;
};

// 编辑子集
const editSubset = (index: number) => {
  currentSubsetIndex.value = index;
  currentSubset.value = JSON.parse(JSON.stringify(formData.subsets[index]));
  subsetDrawerVisible.value = true;
};

// 复制子集
const copySubset = (index: number) => {
  const subsetToCopy = JSON.parse(JSON.stringify(formData.subsets[index]));
  subsetToCopy.name = subsetToCopy.name + '-copy';
  formData.subsets.push(subsetToCopy);
  message.success('子集已复制');
};

// 删除子集
const removeSubset = (index: number) => {
  formData.subsets.splice(index, 1);
  message.success('子集已删除');
};

// 保存子集
const saveSubset = () => {
  if (!currentSubset.value.name) {
    message.error('请输入子集名称');
    return;
  }

  if (currentSubsetIndex.value === -1) {
    // 新增子集
    formData.subsets.push(JSON.parse(JSON.stringify(currentSubset.value)));
  } else {
    // 更新子集
    formData.subsets[currentSubsetIndex.value] = JSON.parse(
      JSON.stringify(currentSubset.value),
    );
  }
  subsetDrawerVisible.value = false;
  message.success('子集已保存');
};

// 关闭子集编辑Drawer
const closeSubsetDrawer = () => {
  subsetDrawerVisible.value = false;
  currentSubsetIndex.value = -1;
};

// 添加标签
const addLabel = () => {
  const newKey = `label-${Object.keys(currentSubset.value.labels).length + 1}`;
  currentSubset.value.labels[newKey] = '';
};

// 删除标签
const removeLabel = (key: string) => {
  delete currentSubset.value.labels[key];
};

// 更新标签键
const updateLabelKey = (oldKey: string, newKey: string) => {
  if (oldKey !== newKey && newKey) {
    const value = currentSubset.value.labels[oldKey];
    delete currentSubset.value.labels[oldKey];
    currentSubset.value.labels[newKey] = value;
  }
};

// Dry Run测试
const handleDryRun = async () => {
  dryRunLoading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success('Dry Run 测试通过！');
  } catch (error) {
    message.error('Dry Run 测试失败：' + error);
  } finally {
    dryRunLoading.value = false;
  }
};

// 提交审核
const handleSubmit = async () => {
  submitLoading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success('已提交审核，请等待审核结果！');
    emit('submit', formData);
    handleClose();
  } catch (error) {
    message.error('提交失败：' + error);
  } finally {
    submitLoading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  emit('close');
};

// 版本回滚处理
const handleVersionRollback = (version: any) => {
  message.success(`已回滚到版本 v${version.version_number}`);
  // 这里可以添加回滚后的处理逻辑，比如刷新数据等
  emit('submit', formData);
  handleClose();
};

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData && props.isEdit) {
      // 如果是Kubernetes对象格式（包含metadata和spec），需要转换
      if (newData.metadata && newData.spec) {
        const spec = newData.spec;
        const metadata = newData.metadata;

        // 转换为表单格式
        formData.name = metadata.name || '';
        formData.namespace = metadata.namespace || '';
        formData.host = spec.host || '';

        // 转换流量策略
        if (spec.trafficPolicy) {
          const tp = spec.trafficPolicy;

          // 连接池配置
          if (tp.connectionPool) {
            formData.trafficPolicy.connectionPool.enabled = true;
            if (tp.connectionPool.tcp) {
              Object.assign(
                formData.trafficPolicy.connectionPool.tcp,
                tp.connectionPool.tcp,
              );
            }
          }

          // 负载均衡配置
          if (tp.loadBalancer) {
            formData.trafficPolicy.loadBalancer.enabled = true;
            formData.trafficPolicy.loadBalancer.simple =
              tp.loadBalancer.simple || 'ROUND_ROBIN';
            if (tp.loadBalancer.consistentHash?.httpCookie?.name) {
              formData.trafficPolicy.loadBalancer.consistentHash.httpCookie.name =
                tp.loadBalancer.consistentHash.httpCookie.name;
            }
          }

          // 异常检测配置
          if (tp.outlierDetection) {
            formData.trafficPolicy.outlierDetection.enabled = true;
            formData.trafficPolicy.outlierDetection.consecutive5xxErrors =
              tp.outlierDetection.consecutive5xxErrors || 5;
            formData.trafficPolicy.outlierDetection.interval =
              tp.outlierDetection.interval || '30s';
            formData.trafficPolicy.outlierDetection.baseEjectionTime =
              tp.outlierDetection.baseEjectionTime || '30s';
            formData.trafficPolicy.outlierDetection.maxEjectionPercent =
              tp.outlierDetection.maxEjectionPercent || 50;
            formData.trafficPolicy.outlierDetection.minHealthPercent =
              tp.outlierDetection.minHealthPercent || 50;
          }

          // TLS配置
          if (tp.tls) {
            formData.trafficPolicy.tls.enabled = true;
            formData.trafficPolicy.tls.mode = tp.tls.mode || 'SIMPLE';
            formData.trafficPolicy.tls.sni = tp.tls.sni || '';
            formData.trafficPolicy.tls.clientCertificate =
              tp.tls.clientCertificate || '';
            formData.trafficPolicy.tls.privateKey = tp.tls.privateKey || '';
            formData.trafficPolicy.tls.caCertificates =
              tp.tls.caCertificates || '';
          }
        }

        // 转换子集配置
        formData.subsets = (spec.subsets || []).map((subset: any) => ({
          name: subset.name || '',
          labels: subset.labels || {},
        }));
      } else {
        // 如果是普通对象格式，直接赋值
        Object.assign(formData, newData);
      }
    }
  },
  { immediate: true },
);

// 监听抽屉开关，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible && !props.isEdit) {
      Object.assign(formData, {
        name: '',
        namespace: '',
        host: '',
        trafficPolicy: {
          connectionPool: {
            enabled: false,
            tcp: {
              maxConnections: 100,
              connectTimeout: '30s',
              tcpKeepalive: {
                time: '7200s',
              },
            },
          },
          loadBalancer: {
            enabled: false,
            simple: 'ROUND_ROBIN',
            consistentHash: {
              httpCookie: {
                name: '',
              },
            },
          },
          outlierDetection: {
            enabled: false,
            consecutive5xxErrors: 5,
            interval: '30s',
            baseEjectionTime: '30s',
            maxEjectionPercent: 50,
            minHealthPercent: 50,
          },
          tls: {
            enabled: false,
            mode: 'SIMPLE',
            sni: '',
            clientCertificate: '',
            privateKey: '',
            caCertificates: '',
          },
        },
        subsets: [],
      });
      activeTab.value = 'basic';
      previousTab.value = 'basic';
    }
  },
);
</script>

<style scoped>
.destination-rule-editor {
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;
}

:deep(.ant-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-tabs-content-holder) {
  flex: 1;
  overflow: hidden;
}

:deep(.ant-tabs-tabpane) {
  height: 100%;
  overflow-y: auto;
}

.traffic-policy-container {
  .policy-section {
    margin-bottom: 24px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fafbfc;
      border-bottom: 1px solid #e8e8e8;

      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .section-content {
      padding: 20px;
    }
  }
}

.subsets-container {
  .subsets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }

      .header-count {
        font-size: 14px;
        color: #8c8c8c;
        background: #f5f5f5;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }
  }

  .empty-subsets {
    text-align: center;
    padding: 40px 0;
  }

  .subsets-list {
    .subset-card {
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }

      .subset-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;

        .subset-left {
          display: flex;
          align-items: center;
          gap: 12px;

          .subset-order {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
          }

          .subset-info {
            .subset-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .subset-name {
                font-weight: 500;
                font-size: 16px;
                color: #262626;
              }
            }

            .subset-description {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
}

.subset-editor {
  .labels-editor {
    .label-row {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.compact-form-item {
  margin-bottom: 12px !important;

  :deep(.ant-form-item-label) {
    padding-bottom: 4px;

    label {
      font-size: 12px;
      font-weight: 500;
      color: #595959;
    }
  }
}

.inline-form-item {
  margin-bottom: 8px !important;

  :deep(.ant-form-item-label) {
    padding-bottom: 0;
    text-align: right;

    label {
      font-size: 13px;
      font-weight: 500;
      color: #262626;
      line-height: 32px;
    }
  }

  :deep(.ant-form-item-control) {
    .ant-input,
    .ant-select,
    .ant-input-number,
    .ant-switch {
      font-size: 13px;
    }
  }

  :deep(.ant-form-item-explain) {
    font-size: 12px;
    margin-top: 4px;
  }
}

.yaml-editor {
  .yaml-editor-content {
    margin-bottom: 16px;
  }

  .yaml-error {
    margin-top: 16px;
  }
}

:deep(.ant-tabs-content-holder) {
  padding-top: 16px;
}
</style>
