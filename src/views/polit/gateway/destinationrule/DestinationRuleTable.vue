<template>
  <a-space style="margin: 10px 0px 28px 0px">
    <a-form layout="inline" :model="formState" @submit="onSearch">
      <a-form-item label="所属集群" name="cluster">
        <a-select
          v-model:value="formState.cluster"
          :options="clusterOptions"
          style="width: 220px"
          placeholder="请选择集群"
          @change="onClusterChange"
        />
      </a-form-item>
      <a-form-item label="命名空间" name="namespace">
        <a-select
          v-model:value="formState.namespace"
          :options="namespaceOptions"
          :loading="!namespaceOptionsLoaded"
          allow-clear
          show-search
          style="width: 220px"
          placeholder="请选择命名空间"
          @change="onSearch"
        />
      </a-form-item>
      <a-form-item label="搜索名称" name="destinationRuleName">
        <a-input-search
          v-model:value="formState.destinationRuleName"
          allow-clear
          style="width: 220px"
          enter-button
          @search="onSearch"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleAdd">
          <!--          <plus-outlined />-->
          新增服务策略
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleQuickAdd">
          <!--          <plus-outlined />-->
          快速新增
        </a-button>
      </a-form-item>
    </a-form>
  </a-space>
  <a-table
    :columns="columns"
    :pagination="pagination"
    :data-source="dataSource"
    class="ant-table-striped"
    :loading="loading"
    row-key="name"
    size="middle"
    :bordered="true"
    :show-index-column="true"
    :scroll="{ x: 150, y: tableConfig.windowHeight - 265 }"
    :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
    @change="handlerTableChange"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'status'">
        <a-tag v-if="record.status === 'success'" size="small" color="green">
          {{ record.status }}
        </a-tag>
        <a-tag v-else-if="record.status === 'pending'" color="blue">
          {{ record.status }}
        </a-tag>
        <a-tag v-else-if="record.status === 'failed'" color="red">
          {{ record.status }}
        </a-tag>
        <a-tag v-else>暂无</a-tag>
      </template>
      <template v-if="column.key === 'operation'">
        <a-space>
          <a-button
            type="link"
            size="small"
            :loading="editLoading && editData?.name === record.name"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="1">
                  <a-button type="text" @click="handleCopy(record)">
                    <copy-outlined />
                    复制
                  </a-button>
                </a-menu-item>
                <a-menu-item key="2">
                  <a-popconfirm
                    title="确认删除此服务策略？"
                    @confirm="handleDelete(record)"
                  >
                    <a-button style="color: red" type="text">
                      <delete-outlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </template>
            <a-button type="link" size="small">更多</a-button>
          </a-dropdown>
        </a-space>
      </template>
    </template>
  </a-table>

  <!-- 规则编辑器 -->
  <DestinationRuleEditor
    :visible="editorVisible"
    :is-edit="isEdit"
    :edit-data="editData"
    @close="handleEditorClose"
    @submit="handleEditorSubmit"
  />
</template>

<script setup lang="ts">
import { getDestinationRules, getDestinationRule } from '@/api/pilot/gateway';
import { reactive, onMounted, ref, inject, watch } from 'vue';
import type { TableColumnType } from 'ant-design-vue';
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import DestinationRuleEditor from './DestinationRuleEditor.vue';

// 从父组件注入集群选项和加载状态
const clusterOptions = inject('clusterOptions', ref([]));
const clusterOptionsLoaded = inject('clusterOptionsLoaded', ref(false));

// 从父组件注入namespace选项和加载状态
const namespaceOptions = inject('namespaceOptions', ref([]));
const namespaceOptionsLoaded = inject('namespaceOptionsLoaded', ref(false));

// 从父组件注入统一的集群选择状态和更新方法
const selectedCluster = inject('selectedCluster', ref(''));
const updateSelectedCluster = inject(
  'updateSelectedCluster',
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  (_cluster: string) => {},
);

const formState = reactive({
  cluster: '',
  namespace: '',
  destinationRuleName: '',
});

const loading = ref(false);
const dataSource = ref([]);
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: any) => `共 ${total} 条数据`,
});

const tableConfig = ref({
  windowHeight: document.documentElement.clientHeight - 40,
});

// 编辑器相关状态
const editorVisible = ref(false);
const isEdit = ref(false);
const editData = ref<any>(null);
const editLoading = ref(false);

const columns: TableColumnType[] = [
  {
    title: '序号',
    width: 70,
    align: 'center',
    customRender: (record: { index: number }) => `${record.index + 1}`,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    width: 300,
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
    align: 'center',
    width: 180,
  },
  {
    title: '主机',
    dataIndex: 'host',
    key: 'host',
    align: 'center',
    width: 300,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 65,
  },
  {
    title: '操作',
    key: 'operation',
    align: 'center',
    width: 120,
  },
];

const onClusterChange = (cluster: string) => {
  updateSelectedCluster(cluster);
  // 清空namespace选择
  formState.namespace = '';
  onSearch();
};

const onSearch = () => {
  loading.value = true;
  if (!formState.cluster) {
    formState.cluster = clusterOptions.value[0]?.value;
  }
  getDestinationRules({
    ...formState,
    page: pagination.value.page,
    pageSize: pagination.value.pageSize,
  })
    .then((res) => {
      dataSource.value = res.data.list;
      pagination.value.total = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

const handlerTableChange = (page: { pageSize: number; current: number }) => {
  pagination.value.page = page.current;
  pagination.value.pageSize = page.pageSize;
  onSearch();
};

// 同步父组件的集群选择状态到本地表单
watch(
  selectedCluster,
  (newCluster) => {
    formState.cluster = newCluster;
  },
  { immediate: true },
);

// 监听集群选项加载状态，加载完成后执行搜索
watch([clusterOptionsLoaded, selectedCluster], ([loaded, _cluster]) => {
  if (loaded && _cluster) {
    onSearch();
  }
});

onMounted(() => {
  // 如果集群选项已经加载完成且有选中的集群，直接搜索
  if (clusterOptionsLoaded.value && selectedCluster.value) {
    onSearch();
  }
});

// 新增DestinationRule
const handleAdd = () => {
  editData.value = null;
  isEdit.value = false;
  editorVisible.value = true;
};

// 编辑DestinationRule
const handleEdit = async (record: any) => {
  editLoading.value = true;
  try {
    // 获取完整的详情信息
    const response = await getDestinationRule({
      cluster: selectedCluster.value,
      namespace: record.namespace,
      name: record.name,
    });
    editData.value = response.data;
    isEdit.value = true;
    editorVisible.value = true;
  } catch (error) {
    console.error('获取DestinationRule详情失败:', error);
    // 如果获取详情失败，仍然使用列表数据
    editData.value = record;
    isEdit.value = true;
    editorVisible.value = true;
  } finally {
    editLoading.value = false;
  }
};

// 复制DestinationRule
const handleCopy = (record: any) => {
  editData.value = { ...record, name: record.name + '-copy' };
  isEdit.value = false;
  editorVisible.value = true;
};

// 删除服务策略
const handleDelete = (record: any) => {
  console.log('删除服务策略:', record);
  // 这里调用删除API
  onSearch(); // 重新加载数据
};

// 编辑器关闭
const handleEditorClose = () => {
  editorVisible.value = false;
  editData.value = null;
  isEdit.value = false;
};

// 编辑器提交
const handleEditorSubmit = (data: any) => {
  console.log('提交服务策略配置:', data);
  // 这里处理提交逻辑
  onSearch(); // 重新加载数据
};

function handleQuickAdd() {
  console.log('quick add');
}
</script>

<style scoped>
.ant-card {
  height: 100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
:deep(.pilot-tag) {
  line-height: 16px !important;
}
</style>
