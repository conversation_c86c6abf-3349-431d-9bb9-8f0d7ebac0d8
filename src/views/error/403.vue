<template>
  <div class="not_found_page">
    <icon-font type="icon-a-404 " class="not_found_icon ml-[-24px]" />
    <div class="flex flex-col items-center">
      <div class="text_404">403</div>
      <div class="not_found_text">没有访问权限</div>
      <div style="margin-top: 20px">当前账号：{{ currentUser }}</div>
      <div style="margin-top: 20px">
        <a-space size="large">
          <a-button v-if="MustMakeblock" @click="requestPermission">
            申请权限
          </a-button>
          <a-button type="primary" @click="goBackHome">返回首页</a-button>
          <a-button type="primary" @click="goToPreviousPage">返上一页</a-button>
          <a-button type="primary" @click="logout">退出登录</a-button>
        </a-space>
      </div>
    </div>
    <!--    <request-drawer ref="requestPermissionDrawerRef" title="申请权限" />-->
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router';
import { appUtil } from '@/utils/app';
import { computed, onMounted, ref } from 'vue';
// import RequestDrawer from '@/views/Permission/request-drawer.vue';
const router = useRouter();

const goBackHome = () => {
  router.push('/');
};

const goToPreviousPage = () => {
  router.go(-2);
};

const logout = async () => {
  try {
    await appUtil.logout();
  } catch (e) {
    console.error(e);
  }
  appUtil.goToLoginOrigin();
};

const requestPermissionDrawerRef = ref();
const requestPermission = async () => {
  try {
    // 拿到当前url中的redirect值取路由表查询对应的id
    requestPermissionDrawerRef.value.open();
  } catch (e) {
    console.error(e);
  }
};
// must makeblock.com email
const MustMakeblock = computed(() => {
  // return true;
  let email = currentUser.value;
  let show =
    email.endsWith('@makeblock.com') || email.endsWith('@makeblock.cc');
  return show;
});

const currentUser = ref('');

onMounted(async () => {
  let user = await appUtil.getUserInfo();
  currentUser.value = user.email || user.userName;
});
</script>
<style scoped>
.not_found_page {
  display: flex;
  position: relative;
  z-index: 100;
  background: white;
  flex-direction: row;
  padding-top: 184px;
  justify-content: center;
  height: 100vh;
  font-weight: 500;
  font-family: 'Poppins';
  color: #1a1a1e;
}
.not_found_icon {
  font-size: 278px;
}
.text_404 {
  font-size: 140px;
  line-height: 168px;
  color: #4b4f57;
}
.not_found_text {
  font-size: 20px;
}

.back_btn {
  height: 40px;
  width: 266px;
  margin-top: 40px;
  background-color: #5151ec;
  border: none;
  border-radius: 2px;
  text-align: center;
  font-size: 14px;
  color: #1a1a1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transition: 0.2s;
  font-family: 'Poppins';
  font-weight: 500;
  cursor: pointer;
  &:hover {
    background-color: #63eb7c;
    color: #1a1a1e;
  }

  &:active {
    color: #1a1a1e;
    background-color: #30ce4d;
  }
}
@media screen and (max-width: 743px) {
  .not_found_page {
    flex-direction: column;
    justify-content: normal;
  }
  .not_found_icon {
    font-size: 200px;
  }
  .text_404 {
    font-size: 80px;
    line-height: 96px;
    color: #4b4f57;
    margin-top: 32px;
  }
  .not_found_text {
    font-size: 16px;
  }
  .back_btn {
    margin-top: 32px;
    width: 226px;
  }
}

::v-deep(.ant-drawer) {
  margin-top: 0 !important;
}
</style>
