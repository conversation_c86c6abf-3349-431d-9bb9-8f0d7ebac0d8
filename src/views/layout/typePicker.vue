<template>
  <div :class="prefixCls">
    <template v-for="item in menuTypeList || []" :key="item.title">
      <a-tooltip
        :title="item.title"
        placement="bottom"
        :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
      >
        <div
          :class="[
            `${prefixCls}__item`,
            `${prefixCls}__item--${item.type}`,
            {
              [`${prefixCls}__item--active`]: themeMode === item.type,
            },
          ]"
          @click="changeThemeMode(item)"
        >
          <div class="mix-sidebar"></div>
        </div>
      </a-tooltip>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, onActivated, ref } from 'vue';

export enum MenuModeEnum {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
  VERTICAL_RIGHT = 'vertical-right',
  INLINE = 'inline',
}

export enum MenuTypeEnum {
  // left menu
  SIDEBAR = 'sidebar',
  MIX_SIDEBAR = 'mix-sidebar',
  // mixin menu
  Drawer = 'drawer',
  // top menu
  TOP_MENU = 'top-menu',
}

export enum ThemeTypeEnum {
  DARK = 'dark',
  LIGHT = 'light',
}

export default defineComponent({
  name: 'MenuTypePicker',
  props: {
    handler: {
      type: Function,
      default: () => ({}),
    },
  },
  setup() {
    const key = 'theme';
    const prefixCls = 'efficacy-setting-menu-type-picker';
    const defaultMode = 'light';

    const themeMode = ref<string>(defaultMode);
    themeMode.value = localStorage.getItem(key) || defaultMode;

    onActivated(() => {
      themeMode.value = localStorage.getItem(key);
    });

    const menuTypeList = [
      {
        title: 'dark',
        mode: MenuModeEnum.INLINE,
        type: ThemeTypeEnum.DARK,
        // type: MenuTypeEnum.SIDEBAR,
      },
      {
        title: 'light',
        mode: MenuModeEnum.INLINE,
        type: ThemeTypeEnum.LIGHT,
        // type: MenuTypeEnum.Drawer,
      },
    ];

    function changeThemeMode(type: any) {
      // 相同的就不需要设置了
      if (type.type === localStorage.getItem(key)) {
        return;
      }
      // 保存到localStorage
      localStorage.setItem(key, type.type);
      // 强制刷新
      location.reload();
    }
    return {
      prefixCls,
      menuTypeList,
      changeThemeMode,
      themeMode,
    };
  },
});
</script>
<style lang="scss" scoped>
$primary-color: #1890ff;
$prefix-cls: 'efficacy-setting-menu-type-picker';

.#{$prefix-cls} {
  display: flex;

  &__item {
    position: relative;
    width: 56px;
    height: 48px;
    margin-right: 16px;
    overflow: hidden;
    border-radius: 4px;
    background-color: #f0f2f5;
    box-shadow: 0 1px 2.5px 0 rgb(0 0 0 / 18%);
    cursor: pointer;

    &::before,
    &::after {
      content: '';
      position: absolute;
    }

    &--dark,
    &--light {
      &::before {
        z-index: 1;
        top: 0;
        left: 0;
        width: 33%;
        height: 100%;
        border-radius: 4px 0 0 4px;
        background-color: #273352;
      }

      &::after {
        top: 0;
        left: 0;
        width: 100%;
        height: 25%;
        background-color: #273352;
      }
    }

    &--light {
      &::before {
        top: 0;
        left: 0;
        width: 35%;
        height: 100%;
        border-radius: 4px 0 0 4px;
        background-color: #fff;
      }

      &::after {
        z-index: 1;
        top: 0;
        left: 0;
        width: 100%;
        height: 25%;
        background-color: #fff;
      }
    }

    &--top-menu {
      &::after {
        top: 0;
        left: 0;
        width: 100%;
        height: 25%;
        background-color: #fff;
      }
    }

    &--dark {
      background-color: #fff;
    }

    &--mix-sidebar {
      &::before {
        z-index: 1;
        top: 0;
        left: 0;
        width: 25%;
        height: 100%;
        border-radius: 4px 0 0 4px;
        background-color: #273352;
      }

      &::after {
        top: 0;
        left: 0;
        width: 100%;
        height: 25%;
        background-color: #fff;
      }

      .mix-sidebar {
        position: absolute;
        left: 25%;
        width: 15%;
        height: 100%;
        background-color: #fff;
      }
    }

    &:hover,
    &--active {
      padding: 12px;
      border: 2px solid $primary-color;

      &::before,
      &::after {
        border-radius: 0;
      }
    }
  }

  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
</style>
