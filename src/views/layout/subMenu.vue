<template>
  <a-sub-menu :key="menuInfo.key">
    <template #icon>
      <component :is="menuInfo.icon" />
    </template>
    <template #title>{{ menuInfo.title }}</template>
    <template v-for="item in menuInfo.subMenus" :key="item.key">
      <template v-if="item.subMenus && item.subMenus.length > 0">
        <sub-menu :key="item.key" :menu-info="item" />
      </template>
      <template v-else>
        <a-menu-item :key="item.key">
          <template #icon>
            <component :is="item.icon" />
          </template>
          {{ item.title }}
        </a-menu-item>
      </template>
    </template>
  </a-sub-menu>
</template>

<script lang="ts">
import { Menu } from '@/store/modules/menu';
import { defineComponent } from 'vue';

const SubMenu = defineComponent({
  name: 'SubMenu',
  props: {
    menuInfo: {
      type: Menu,
      default() {
        return {};
      },
    },
  },
});

export default SubMenu;
</script>
