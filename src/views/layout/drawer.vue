<template>
  <a-drawer
    :closable="false"
    :destroy-on-close="false"
    :header-style="{ 'text-align': 'center', padding: '0px' }"
    :z-index="9999"
    :width="800"
    mask-closable
    :body-style="{ paddingBottom: '64px' }"
    :footer-style="{ textAlign: 'right' }"
    placement="left"
    :loading="true"
    @close="close"
  >
    <a-layout style="height: 100%; overflow: hidden">
      <a-layout>
        <a-layout-sider style="background-color: #fff" width="120px">
          <a-anchor
            v-for="(menu, idx) in menuList"
            :key="idx"
            :affix="false"
            :get-container="getAnchorContainer"
            @click="handleAnchorClick"
          >
            <div
              v-if="!menu.hide"
              class="menu-dir"
              @click="handleAnchorClick($event, menu.key)"
            >
              <span style="display: inline">
                <component :is="menu.icon" :size="12" />
              </span>
              <a-anchor-link
                class="CB-fLeXOC"
                :href="menu.key"
                :title="menu.title || menu.name"
              />
            </div>
          </a-anchor>
        </a-layout-sider>
        <a-layout-content
          style="
            margin-left: 1px;
            padding-right: 5px;
            padding-left: 5px;
            overflow-x: hidden;
            overflow-y: scroll;
            background-color: #fff;
          "
        >
          <div>
            <a-input-search
              v-model:value="searchMenuValue"
              class="menu-search-input menu-search-input-line"
              placeholder="请输入功能名称搜索"
              allow-clear
              @change="searchMenu"
              @search="searchMenu"
            >
              <template #prefix>
                <search-outlined />
              </template>
            </a-input-search>
          </div>
          <div style="margin-top: 45px; padding-left: 1px">
            <!--最近访问-->

            <!--菜单列表-->
            <div v-for="(menu, idx) in menuList" :key="idx">
              <h4 :id="menu.key" class="menu-group-title">
                {{ menu.title || menu.name }}
              </h4>
              <a-row :gutter="16">
                <a-col
                  v-for="(func, sIdx) in menu.subMenus"
                  :key="sIdx"
                  :span="6"
                >
                  <a-card
                    class="func-card"
                    :hoverable="true"
                    @click="goPath(func)"
                  >
                    <a-card-meta>
                      <template #description>
                        {{ func.title || func.name }}
                      </template>
                    </a-card-meta>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-drawer>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { SearchOutlined } from '@ant-design/icons-vue';
import lodash from 'lodash';

export default defineComponent({
  name: 'MenuDrawer',
  components: {
    SearchOutlined,
  },
  props: {
    title: {
      type: String,
      required: true,
    },
  },
  emits: ['resetAutoShow', 'close'],
  setup(props, context) {
    let show = ref<boolean>(false);
    const formRef = ref();
    const formState = reactive({
      name: '',
      projectUuid: '',
      identity: '',
      principals: [],
      members: [],
      description: '',
      template: 'cmd',
      repoId: '',
      source: '',
      category: '',
      level: '',
      download: false,
    });
    const paramFormRef = ref();
    const paramFormState = reactive({});

    const originMenuList = ref([]);
    const menuList = ref([]);

    onMounted(() => {
      let list = computed(() => store.state.app.menuList);
      // 移除首页
      // if (
      //   list.value &&
      //   list.value.length > 0 &&
      //   list.value[0].title === '首页'
      // ) {
      //   list.value.shift();
      // }

      function flatChildMenu(menuList: any[]): any[] {
        let menuChildren: any[] = [];
        menuList.forEach((subMenu: any) => {
          // 如果有子菜单，递归铺平
          if (subMenu.subMenus && subMenu.subMenus.length > 0) {
            menuChildren.push(...flatChildMenu(subMenu.subMenus));
          } else {
            menuChildren.push(subMenu);
          }
        });
        return menuChildren;
      }

      // 为了适配之前的菜单设计, 所有三、四..级菜单全部铺平
      // const flatMenu = (menu: any) => {
      //   if (menu.subMenus && menu.subMenus.length > 0) {
      //     let menuChildren: any[] = [];
      //     menu.subMenus.forEach((subMenu: any) => {
      //       // 如果有子菜单，递归铺平
      //       if (subMenu.subMenus && subMenu.subMenus.length > 0) {
      //         // 如果是菜单没有子菜单，直接添加，否则需要将其铺平到当前这级
      //         menuChildren.push(...flatChildMenu(subMenu.subMenus));
      //       } else {
      //         menuChildren.push(subMenu);
      //       }
      //     });
      //     menu.subMenus = menuChildren;
      //   }
      //   return menu;
      // };

      const flatMenu = (menu: any) => {
        // 创建 menu 的深拷贝
        let menuCopy = lodash.cloneDeep(menu);
        if (menuCopy.subMenus && menuCopy.subMenus.length > 0) {
          let menuChildren: any[] = [];
          menuCopy.subMenus.forEach((subMenu: any) => {
            // 如果有子菜单，递归铺平
            if (subMenu.subMenus && subMenu.subMenus.length > 0) {
              // 如果是菜单没有子菜单，直接添加，否则需要将其铺平到当前这级
              menuChildren.push(...flatChildMenu(subMenu.subMenus));
            } else {
              menuChildren.push(subMenu);
            }
          });
          menuCopy.subMenus = menuChildren;
        }
        return menuCopy;
      };

      let flatMenuList = list.value.map((menu: any) => flatMenu(menu));

      originMenuList.value = flatMenuList || [];
      menuList.value = [...originMenuList.value];
    });

    const open = async () => {
      // 插入最近访问的菜单
      const recentMenuList = await store.dispatch('app/getRecentMenuItem');

      if (recentMenuList.length > 0) {
        const recent = {
          key: '-',
          hide: true,
          title: '最近访问',
          subMenus: recentMenuList,
        };
        // 判断是添加还是更新
        if (
          menuList.value.length > 0 &&
          menuList.value[0].title === '最近访问'
        ) {
          menuList.value[0] = recent;
        } else {
          menuList.value.unshift(recent);
        }
      }
      // show.value = true;
    };

    const finish = () => {
      show.value = false;
      // context.emit('ok');
    };

    const close = () => {
      show.value = false;
    };

    // @ts-ignore
    function handleAnchorClick(e, link) {
      // 阻止点击的默认事件修改路由
      e.preventDefault();
      let id = link.href || link;
      const scrolls = document.getElementById(id);
      scrolls.scrollIntoView({ block: 'start', behavior: 'smooth' });
    }

    const pilotRef = ref();
    function getAnchorContainer() {
      return pilotRef;
    }

    const searchMenuValue = ref('');
    // 搜索step（前端搜索）
    function searchMenu() {
      if (searchMenuValue.value === '') {
        menuList.value = [...originMenuList.value];
        return;
      }
      // 遍历originMenuList进行模糊搜索赋值给menuList
      menuList.value = searchInMenuList(searchMenuValue.value);
    }

    // 搜索菜单列表
    function searchInMenuList(searchValue: string) {
      return originMenuList.value
        .map((menu) => {
          // 创建当前菜单的副本
          let menuCopy = { ...menu };
          // 搜索当前菜单
          if (menuCopy.title.includes(searchValue)) {
            return menuCopy;
          }
          // 如果当前菜单有子菜单，搜索子菜单
          if (menuCopy.subMenus && menuCopy.subMenus.length > 0) {
            let matchedSubMenus = menuCopy.subMenus.filter(
              (subMenu: { title: string | string[] }) =>
                subMenu.title.includes(searchValue),
            );
            // 如果子菜单中有符合搜索条件的，保留当前菜单
            if (matchedSubMenus.length > 0) {
              // 只保留匹配的子菜单
              menuCopy.subMenus = matchedSubMenus;
              return menuCopy;
            }
          }
          return null;
        })
        .filter((menu) => menu !== null); // 移除空菜单
    }

    const store = useStore();

    // 选中 菜单
    // const route = useRoute();
    const router = useRouter();
    function goPath(menu: any) {
      // closeDrawer();
      context.emit('close');
      context.emit('resetAutoShow');
      // show.value = false;
      // 添加进最近访问
      // console.log('before',store.state.app.recentMenuList)
      store.commit('app/addRecentMenuItem', menu);
      // console.log('after',store.state.app.recentMenuList)
      // 路由跳转
      router.push({
        name: menu.key,
        path: menu.key,
      });
    }

    return {
      show,
      formRef,
      formState,
      finish,
      open,
      close,
      paramFormRef,
      paramFormState,
      //
      handleAnchorClick,
      getAnchorContainer,
      searchMenu,
      searchMenuValue,
      goPath,
      menuList,
    };
  },
});
</script>

<style scoped>
::v-deep(.ant-radio-button-wrapper) {
  margin: 5px;
  text-align: center;
  text-overflow: ellipsis;
}

.ant-col-8 {
  padding-bottom: 8px;
}

::v-deep(.func-card .ant-card-body) {
  padding-top: 5px;
  padding-bottom: 5px;
}

.ant-card-meta-description {
  overflow: hidden;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-search-input {
  position: fixed;
  z-index: 1005;
  width: 650px;
  border-right-color: #fff;
  box-shadow: none;
}

::v-deep(.menu-search-input-line .ant-btn-icon-only) {
  display: none;
}

::v-deep(.menu-search-input-line .ant-input-wrapper .ant-input-affix-wrapper) {
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

::v-deep(.menu-search-input-line .ant-input-affix-wrapper-focused) {
  border-right-width: 0;
  border-color: #2a7dc9;
  outline: 0;
  box-shadow: none;
}

.ant-input-group .ant-input {
  width: 100%;
}

.CB-gkoLZz {
  border-bottom: 1px solid var(--cb-color-border-tertiary, #ebebeb);
}

body
  > div:nth-child(5)
  > div
  > div.ant-drawer-content-wrapper
  > div
  > div
  > div.ant-drawer-body
  > div
  > div.scrollbar__wrap.scrollbar__wrap--hidden-default
  > div
  > section
  > section
  > main
  > div:nth-child(1)
  > span
  > span {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  border: 1px solid transparent;
  box-sizing: border-box;
  height: 32px;
  font-size: 12px;
  transition: all 0.3s ease-out 0s;
  background-color: var(--cb-color-input-bg, #fff);
}

body
  > div:nth-child(5)
  > div
  > div.ant-drawer-content-wrapper
  > div
  > div
  > div.ant-drawer-body
  > div
  > div.scrollbar__wrap.scrollbar__wrap--hidden-default
  > div
  > section
  > section
  > main
  > div:nth-child(1)
  > span
  > span
  > span.ant-input-affix-wrapper {
  border: none;
  border-bottom: 1px solid #d9d9d9;
  border-radius: 0;
  box-shadow: none;
}

body
  > div:nth-child(5)
  > div
  > div.ant-drawer-content-wrapper
  > div
  > div
  > div.ant-drawer-body
  > div
  > div.scrollbar__wrap.scrollbar__wrap--hidden-default
  > div
  > section
  > section
  > main
  > div:nth-child(1)
  > span
  > span
  > span.ant-input-group-addon
  > button {
  display: none;
}

::v-deep(.menu-search-input-line .ant-input-group-addon) {
  background-color: white;
}

.menu-group-title {
  height: 32px;
  margin-top: 20px;
  padding: 0 8px;
  overflow: hidden;
  color: var(--cb-color-text-primary, #333);
  font-size: 12px;
  font-weight: 600;
  line-height: 32px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::-webkit-scrollbar {
  width: 0;
  height: 8px;
}

.ant-card-bordered {
  border: 0;
}

.CB-fLeXOC {
  display: -webkit-inline-box;
  flex: 1 1 0%;
  width: 70%;
  overflow: hidden;
  font-size: 12px;
  line-height: 30px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ant-anchor-link {
  padding-left: 8px;
}

.ant-card {
  border-radius: 2px;
}

.func-card {
  margin: 3px 0;
  padding: 6px 0;
}

.ant-card-hoverable:hover {
  background-color: #f1efef;
  box-shadow: none;
}

.menu-dir {
  padding-left: 18px;
}

.menu-dir:hover {
  border-radius: 2px;
  background-color: #f1efef;
}
</style>
