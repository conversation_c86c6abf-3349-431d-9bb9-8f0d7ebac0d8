<template>
  <a-drawer
    :destroy-on-close="false"
    :z-index="9999"
    :width="330"
    mask-closable
    :footer-style="{ textAlign: 'right' }"
    placement="right"
    :loading="true"
    @close="close"
  >
    <a-divider>导航栏主题</a-divider>
    <menu-type-picker />
  </a-drawer>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import MenuTypePicker from '@/views/layout/typePicker.vue';

export default defineComponent({
  name: 'SettingDrawer',
  components: {
    MenuTypePicker,
  },
  props: {
    title: {
      type: String,
      required: true,
    },
  },
  emits: ['resetAutoShow', 'close'],
  setup(props, context) {
    const close = () => {
      context.emit('close');
    };
    return {
      close,
    };
  },
});
</script>

<style scoped></style>
