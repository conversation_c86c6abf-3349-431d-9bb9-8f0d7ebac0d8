<template>
  <a-layout>
    <!--    <a-layout-header-->
    <!--      :style="{-->
    <!--        position: 'fixed',-->
    <!--        zIndex: 10000,-->
    <!--        width: '100%',-->
    <!--        maxHeight: '50px',-->
    <!--        display: 'flex',-->
    <!--        justifyContent: 'space-between',-->
    <!--        paddingLeft: '6px',-->
    <!--        paddingRight: '6px',-->
    <!--        alignItems: 'center',-->
    <!--        background: theme === 'light' ? '#fff' : '#001529',-->
    <!--        boxShadow: '0 2px 4px 0 rgba(0,0,0,0.16)',-->
    <!--      }"-->
    <!--    >-->
    <!--      <div style="display: flex; align-items: center">-->
    <!--        <div style="margin-left: 14px; margin-bottom: 10px" @click="goToHome">-->
    <!--          <img src="/src/assets/logo.png" alt="logo" />-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div style="display: flex; align-items: center">-->
    <!--        &lt;!&ndash; 右对齐的元素放在这里 &ndash;&gt;-->
    <!--        <div-->
    <!--          style="margin-right: 16px"-->
    <!--          :style="{ color: theme === 'light' ? 'gray' : '#fff' }"-->
    <!--          @click="toggleFullScreen"-->
    <!--        >-->
    <!--          <fullscreen-exit-outlined v-if="isFullScreen" />-->
    <!--          <fullscreen-outlined v-else />-->
    <!--        </div>-->
    <!--        <a-popover-->
    <!--          trigger="click"-->
    <!--          placement="bottomRight"-->
    <!--          :get-popup-container="(triggerNode: any) => triggerNode.parentNode"-->
    <!--        >-->
    <!--          <template #content>-->
    <!--            <div style="display: flex; flex-direction: column">-->
    <!--              <a-button-->
    <!--                style="margin-bottom: 8px"-->
    <!--                type="ghost"-->
    <!--                @click="openPermissionDrawer"-->
    <!--              >-->
    <!--                权限申请-->
    <!--              </a-button>-->
    <!--              <a-button-->
    <!--                style="margin-bottom: 8px"-->
    <!--                type="ghost"-->
    <!--                @click="setAccount"-->
    <!--              >-->
    <!--                账号设置-->
    <!--              </a-button>-->
    <!--              <a-button type="ghost" @click="logout">退出登录</a-button>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--          <div style="margin-right: 6px">-->
    <!--            <a-avatar-->
    <!--              :src="user.headpic"-->
    <!--              style="width: 30px; height: 30px; margin-right: 8px"-->
    <!--            />-->
    <!--            <span :style="{ color: theme === 'light' ? 'gray' : '#fff' }">-->
    <!--              {{ user.userName }}-->
    <!--            </span>-->
    <!--          </div>-->
    <!--        </a-popover>-->
    <!--        <setting-outlined-->
    <!--          :style="{-->
    <!--            marginLeft: '8px',-->
    <!--            marginRight: '8px',-->
    <!--            color: theme === 'light' ? 'gray' : '#fff',-->
    <!--          }"-->
    <!--          @click="openSettingDrawer"-->
    <!--        />-->
    <!--      </div>-->
    <!--    </a-layout-header>-->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :theme="theme"
      collapsible
      class="fixed-sider"
    >
      <div class="layout-sider">
        <a-menu
          v-for="(menu, index) in menuList"
          :key="index"
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          :theme="theme"
          mode="inline"
          style="border-inline-end: 0 !important"
          @click="onMenuClicked"
          @open-change="onOpenChange"
        >
          <a-menu-item v-if="menu.subMenus.length === 0" :key="menu.key">
            <component :is="menu.icon" />
            <span>{{ menu.title }}</span>
          </a-menu-item>
          <template v-else>
            <sub-menu :key="menu.key" :menu-info="menu" />
          </template>
        </a-menu>
      </div>
    </a-layout-sider>
    <a-layout-content
      :style="{
        padding: '0px',
        marginLeft: collapsed ? '80px' : '200px',
        background: '#fff',
        minHeight: 'calc(100vh - 58px)',
        maxHeight: 'calc(100vh - 60px)',
      }"
      class="main-content"
    >
      <!--                      <a-breadcrumb :style="{ margin: '16px 0',background: '#fff',minHeight: '20px' }">-->
      <!--                        <a-breadcrumb-item>Home</a-breadcrumb-item>-->
      <!--                        <a-breadcrumb-item>List</a-breadcrumb-item>-->
      <!--                        <a-breadcrumb-item>App</a-breadcrumb-item>-->
      <!--                      </a-breadcrumb>-->
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </a-layout-content>
    <!--    <a-layout-footer :style="{ textAlign: 'center' }">-->
    <!--      Ant Design ©2018 Created by Ant UED-->
    <!--    </a-layout-footer>-->
  </a-layout>
  <setting-drawer
    ref="settingDrawerRef"
    v-model:visible="showSettingDrawer"
    title="setting"
  />
  <!--  <request-drawer-->
  <!--    ref="requestPermissionDrawerRef"-->
  <!--    title="申请权限"-->
  <!--    style="margin-top: 50px"-->
  <!--  />-->
</template>
<script lang="ts">
import { openURL } from '@/utils';
import { appUtil } from '@/utils/app';
import {
  FullscreenExitOutlined,
  FullscreenOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  CloseOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue';
import { computed, defineComponent, ref, watchEffect } from 'vue';
import { useStore } from 'vuex';
import SubMenu from './subMenu.vue';
import MenuDrawer from '@/views/layout/drawer.vue';
import SettingDrawer from '@/views/layout/setting.vue';
// import RequestDrawer from '@/views/Permission/request-drawer.vue';

export default defineComponent({
  name: '',
  components: {
    // RequestDrawer,
    SettingDrawer,
    MenuDrawer,
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    SubMenu,
    CloseOutlined,
    SettingOutlined,
  },
  setup() {
    const store = useStore();

    const isFullScreen = ref<boolean>(document.fullscreenElement !== null);

    // 是否展开菜单栏
    let ca = localStorage.getItem('collapsed') === 'true';
    const collapsed = ref<boolean>(ca);

    // 主题
    let tm = localStorage.getItem('theme') || 'light';
    const theme = ref<string>(tm);

    function goToHome() {
      appUtil.goToHome();
    }

    // 添加防抖处理避免快速点击
    let menuClickTimer: NodeJS.Timeout | null = null;
    function onMenuClicked(item: any) {
      if (menuClickTimer) {
        clearTimeout(menuClickTimer);
      }
      menuClickTimer = setTimeout(() => {
        appUtil.goToPage(item.key);
        menuClickTimer = null;
      }, 100); // 100ms防抖
    }

    function toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    }

    addEventListener('fullscreenchange', () => {
      isFullScreen.value = document.fullscreenElement !== null;
    });

    function onOpenChange(openKeys: string[]) {
      store.commit('app/setOpenMenuKeys', openKeys);
    }

    const showSettingDrawer = ref<boolean>(false);
    const settingDrawerRef = ref();

    function openSettingDrawer() {
      showSettingDrawer.value = !showSettingDrawer.value;
    }

    watchEffect(() => {
      localStorage.setItem('collapsed', collapsed.value.toString());
    });

    const requestPermissionDrawerRef = ref();
    function openPermissionDrawer() {
      try {
        // 拿到当前url中的redirect值取路由表查询对应的id
        requestPermissionDrawerRef.value.open();
      } catch (e) {
        console.error(e);
      }
    }

    return {
      theme,
      collapsed,
      selectedKeys: computed({
        get: () => store.state.app.curMenuKeys,
        set: (val) => store.commit('app/selectedMenuKeys', val),
      }),
      openKeys: computed({
        get: () => store.state.app.openMenuKeys,
        set: (val) => store.commit('app/setOpenMenuKeys', val),
      }),
      menuList: computed(() => store.state.app.menuList),
      user: computed(() => store.state.user),
      logout: () => store.dispatch('user/logout'),
      setAccount: () => {
        const url = appUtil.getUrl('profile/settings');
        openURL(url);
      },
      goToHome,
      onMenuClicked,
      onOpenChange,
      toggleFullScreen,
      isFullScreen,
      openSettingDrawer,
      showSettingDrawer,
      settingDrawerRef,
      openPermissionDrawer,
      requestPermissionDrawerRef,
    };
  },
});
</script>

<style scoped>
.layout {
  height: 100vh;
  overflow: hidden;
}

.pilot-layout-sider {
  height: calc(100% - 52px);
  overflow-y: hidden;
  //margin-top: 52px;
  border-right: #dedcdc 1px solid;
}

/* 隐藏滚动条 */

.layout-sider::-webkit-scrollbar {
  width: 0;
}

.layout-sider:hover {
  overflow-y: auto;
}

.ant-layout-content {
  overflow-y: auto;
}

#components-layout-demo-custom-trigger .trigger {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo img {
  width: 80%;
  margin: 4px 0px;
  /*margin: 20px;*/
  /* animation: windowLoading-logo-img 2s infinite; */
}

.site-layout .site-layout-background {
  background: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: end;
  height: 50px;
  padding-right: 20px;
  padding-left: 20px;
  color: white;
  background: #fff;
}

.ant-layout-content .ant-card {
  min-height: calc(100vh - 74px);
}

.ant-layout {
  background: #fff !important;
}

::v-deep(.pilot-layout-sider-trigger) {
  border-right: #dedcdc 1px solid !important;
}

.pilot-card-bordered {
  border: 0;
}

::v-deep(.ant-menu-inline, .ant-menu-vertical, .ant-menu-vertical-left) {
  border-right-width: 0 !important;
}

.fixed-sider {
  position: fixed;
  height: 100vh;
  z-index: 1000;
}
/*table滚动条样式优化*/
::v-deep(.main-content::-webkit-scrollbar-track-piece) {
  -webkit-border-radius: 0;
  background-color: #f8f7f7;
}
::v-deep(.main-content::-webkit-scrollbar) {
  width: 6px;
  height: 10px;
}
::v-deep(.main-content::-webkit-scrollbar-thumb) {
  height: 50px;
  background-color: #cecdcd;
  -webkit-border-radius: 6px;
  outline-offset: -2px;
  filter: alpha(opacity = 50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}
::v-deep(.main-content::-webkit-scrollbar-thumb:hover) {
  height: 50px;
  background-color: #878987;
  -webkit-border-radius: 6px;
}
</style>
