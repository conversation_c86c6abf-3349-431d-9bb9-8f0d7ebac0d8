<template>
  <a-config-provider v-bind="getPopupContainer" prefix-cls="pilot">
    <router-view />
    <stagewise-toolbar v-if="showStagewise" :config="stagewiseConfig" />
  </a-config-provider>
</template>

<script lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import 'moment/dist/locale/zh-cn';
import { defineComponent } from 'vue';
import { StagewiseToolbar } from '@stagewise/toolbar-vue';
import VuePlugin from '@stagewise-plugins/vue';

export default defineComponent({
  name: 'App',
  components: {
    StagewiseToolbar,
  },
  data() {
    return {
      getPopupContainer: {
        locale: zhCN,
      },
      stagewiseConfig: {
        plugins: [VuePlugin],
      },
    };
  },
  computed: {
    showStagewise() {
      return import.meta.env.MODE === 'localhost';
    },
  },
  mounted() {
    // Stagewise工具栏会自动在开发模式下显示
  },
});
</script>

<style>
/* stylelint-disable string-quotes */

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
}

/* 确保 Stagewise 工具栏显示在最顶层 */
[class*='stagewise'] {
  z-index: 9999 !important;
}

.pilot-drawer {
  margin-top: 50px !important;
}
/*table滚动条样式优化*/
.pilot-table-body::-webkit-scrollbar-track-piece {
  -webkit-border-radius: 0;
  background-color: #f8f7f7;
}
.pilot-table-body::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
.pilot-table-body::-webkit-scrollbar-thumb {
  height: 50px;
  background-color: #cecdcd;
  -webkit-border-radius: 6px;
  outline-offset: -2px;
  filter: alpha(opacity = 50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}
.pilot-table-body::-webkit-scrollbar-thumb:hover {
  height: 50px;
  background-color: #878987;
  -webkit-border-radius: 6px;
}
</style>
