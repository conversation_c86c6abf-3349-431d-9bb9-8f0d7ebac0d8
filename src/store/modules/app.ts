import { appUtil } from '@/utils/app';
import type { ActionContext } from 'vuex';
import type { AllState } from '../index';
import { allMenus, Menu } from './menu';
import { getStore, setStore } from '@/utils/storage';
import { PermissionKey } from '@/types/permissionKey';

export type AppState = {
  menuList: Menu[];
  permissions: string[];
  curMenuKeys: string[];
  openMenuKeys: string[];
  // 最近访问的菜单
  recentMenuList: Menu[];
};

const state: AppState = {
  menuList: [],
  permissions: [],
  curMenuKeys: [],
  openMenuKeys: [],
  // 最近访问的菜单
  recentMenuList: [],
};

function permMenu(item: Menu, perms: string[]): Menu {
  const subMenus: Menu[] = [];
  if (item.subMenus && item.subMenus.length > 0) {
    item.subMenus.forEach((el) => {
      const sm = permMenu(el, perms);
      if (sm) {
        subMenus.push(sm);
      }
    });
  }
  if (subMenus.length > 0 || perms.includes(item.key)) {
    const nm = new Menu();
    nm.icon = item.icon;
    nm.key = item.key;
    nm.title = item.title;
    nm.subMenus = subMenus;
    return nm;
  }
  return null;
}

function openMenuKeys(item: Menu, key: string): string[] {
  const openKeys: string[] = [];
  if (item.subMenus && item.subMenus.length > 0) {
    item.subMenus.forEach((el) => {
      const sm = openMenuKeys(el, key);
      if (sm.length) {
        openKeys.push(...sm);
      }
    });
  }
  if (openKeys.length > 0 || item.key === key) {
    openKeys.push(item.key);
  }
  return openKeys;
}

const app = {
  namespaced: true,

  state,

  mutations: {
    updateMenulist(state: AppState, perms: string[]) {
      console.log('mutations updateMenulist');
      const menus: Menu[] = [];
      for (let i = 0; i < allMenus.length; i++) {
        const m = allMenus[i];
        const nm = permMenu(m, perms);
        if (nm) {
          menus.push(nm);
        }
      }
      state.menuList = menus;
    },

    updateOpenMenu(state: AppState, key: string) {
      state.curMenuKeys = [key];
      for (let i = 0; i < state.menuList.length; i++) {
        const m = state.menuList[i];
        const openKeys = openMenuKeys(m, key);
        if (openKeys.length > 0) {
          const keys = [...state.openMenuKeys, ...openKeys];
          keys.push(
            PermissionKey.Gateway,
            PermissionKey.System,
            PermissionKey.Governance,
            PermissionKey.Canary,
          ); // 保持持续集成和系统配置菜单始终展开
          state.openMenuKeys = Array.from(new Set(keys));
          break;
        }
      }
    },

    setOpenMenuKeys(state: AppState, openKeys: string[]) {
      state.openMenuKeys = openKeys;
    },

    selectedMenuKeys(state: AppState, key: string) {
      state.curMenuKeys = [key];
    },

    addRecentMenuItem(state: AppState, menu: Menu) {
      // remove record
      const recentMenuList = state.recentMenuList.filter(
        (item) => item.key !== menu.key,
      );
      // put to first
      recentMenuList.unshift(menu);
      // over max value remove it
      if (recentMenuList.length > 8) {
        recentMenuList.pop();
      }
      // save to store
      setStore(RECENT_MENU_LIST_KEY, recentMenuList);
      state.recentMenuList = recentMenuList;
    },
    // 清理最近访问的菜单
    cleanRecentMenuItem(state: AppState) {
      setStore(RECENT_MENU_LIST_KEY, []);
      state.recentMenuList = [];
    },
  },

  actions: {
    // 获取权限/菜单
    permissions(context: ActionContext<AppState, AllState>) {
      console.log('action permissions');
      return new Promise((resolve, reject) => {
        appUtil
          .getPermissions()
          .then((res: any) => {
            if (res.data && res.data.length > 0) {
              const perms: string[] = [];
              res.data.forEach((m: any) => {
                perms.push(m.code);
              });
              state.permissions = perms;
            } else {
              state.permissions = [];
            }
            context.commit('updateMenulist', state.permissions);
            resolve(state.permissions);
          })
          .catch((err) => {
            console.log(err);
            reject(err);
          });
      });
    },
    // 在 app.ts 的 actions 对象中添加以下代码
    getRecentMenuItem(context: ActionContext<AppState, AllState>) {
      // 检查localStorage中是否有最近访问的菜单
      context.state.recentMenuList = getStore(RECENT_MENU_LIST_KEY) || [];
      return context.state.recentMenuList;
    },
  },
};
const RECENT_MENU_LIST_KEY = 'RECENT_MENUS_KEY';
export default app;
