import { PermissionKey } from '@/types/permissionKey';
import {
  GlobalOutlined,
  HomeOutlined,
  BorderOuterOutlined,
  CopyrightCircleOutlined,
  // ApartmentOutlined,
  // AppstoreOutlined,
  // BarsOutlined,
  // ControlOutlined,
  // DeploymentUnitOutlined,
  // SettingOutlined,
} from '@ant-design/icons-vue';
import type { FunctionalComponent } from 'vue';

export class MenuItem {
  key: string;
  title: string;
}

export class Menu extends MenuItem {
  icon?: FunctionalComponent;
  subMenus?: Menu[];
}

export const allMenus: Menu[] = [
  {
    icon: HomeOutlined,
    key: PermissionKey.HOME,
    title: '系统首页',
    subMenus: [],
  },
  {
    icon: GlobalOutlined,
    key: PermissionKey.Gateway,
    title: '网关管理',
    subMenus: [
      {
        icon: CopyrightCircleOutlined,
        key: PermissionKey.Domain,
        title: '域名配置',
      },
      {
        icon: BorderOuterOutlined,
        key: PermissionKey.VirtualService,
        title: '路由配置',
      },
    ],
  },
  // {
  //   icon: ApartmentOutlined,
  //   key: PermissionKey.Canary,
  //   title: '灰度发布',
  //   subMenus: [
  //     {
  //       icon: DeploymentUnitOutlined,
  //       key: PermissionKey.Workflow,
  //       title: '发布管理',
  //     },
  //     {
  //       icon: BarsOutlined,
  //       key: PermissionKey.WorkflowExecution,
  //       title: '执行记录',
  //     },
  //   ],
  // },
  // {
  //   icon: ControlOutlined,
  //   key: PermissionKey.Governance,
  //   title: '服务治理',
  //   subMenus: [
  //     {
  //       icon: AppstoreOutlined,
  //       key: PermissionKey.Application,
  //       title: '应用列表',
  //     },
  //   ],
  // },
  // {
  //   icon: SettingOutlined,
  //   key: PermissionKey.System,
  //   title: '系统配置',
  //   subMenus: [
  //     {
  //       icon: DeploymentUnitOutlined,
  //       key: PermissionKey.Cluster,
  //       title: '集群管理',
  //     },
  //   ],
  // },
];
