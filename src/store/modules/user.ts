import { appUtil } from '@/utils/app';
import type { ActionContext } from 'vuex';
import type { AllState } from '../index';
import type { Menu } from './menu';

export type UserState = {
  utoken: string;
  userName: string;
  headpic: string;
  roles: string[];
  menus?: Menu[];
  email?: string;
  uid: number;
};

const state: UserState = {
  utoken: '',
  userName: '',
  headpic: '',
  roles: [],
  uid: 0,
  email: '',
};

const user = {
  namespaced: true,

  state,

  mutations: {
    setInfo(state: UserState, info: UserState) {
      const { userName, headpic, roles, email, uid } = info;
      state.userName = userName;
      state.headpic = headpic;
      state.roles = roles;
      state.email = email;
      state.uid = uid;
    },
    // 用户退出登录
    clearState(state: UserState) {
      state.userName = '';
      state.headpic = '';
      state.roles = [];
      state.uid = 0;
    },
  },

  actions: {
    // 获取用户信息
    userInfo(context: ActionContext<UserState, AllState>) {
      console.log('action userInfo');
      return new Promise((resolve, reject) => {
        appUtil
          .getUserInfo()
          .then((userInfo) => {
            context.commit('setInfo', userInfo);
            resolve(userInfo);
          })
          .catch((err) => {
            console.log(err);
            reject(err);
          });
      });
    },
    // 退出登录
    logout(context: ActionContext<UserState, AllState>) {
      return new Promise((resolve, reject) => {
        appUtil
          .logout()
          .then((e) => {
            context.commit('clearState');
            // 使用root: true选项提交app模块的cleanRecentMenuItem mutation
            context.commit('app/cleanRecentMenuItem', null, { root: true });
            resolve(e);
          })
          .catch((err) => {
            console.log(err);
            reject(err);
          })
          .finally(() => {
            appUtil.goToLogin();
          });
      });
    },
  },
};

export default user;
