export interface ResponseData<T = any> {
  code: number;
  message: string;
  data?: T;
}

export interface ResponseArray extends ResponseData {
  total: number;
  current: number;
  pageSize: number;
  data: [];
}

export const ResponseMessage = {
  '10000': '未知系统错误！',
  '10001': '数据库运行错误！',
  '10002': '数据库运行错误！',
  '10003': '请求参数不正确！',
  '20101': '用户登录状态失效！',
  '20102': '用户无权限！',
  '80101': '字段值已被占用！',
  // pilot
  '90535': '配置分支不存在',
  '90536': '流水线配置错误',
  '90537': '配置状态已变更',
};
