// import { request } from '@/utils/axios';
import { pilotRequest as request } from '@/utils/pilot';
// import { PageResponse } from '@/types/response';

// 定义通用分页响应类型
export interface PageResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 集群相关接口类型定义
export interface ClusterInfo {
  id: number;
  uuid: string;
  name: string;
  description?: string;
  apiServer: string;
  status: string;
  version?: string;
  nodeCount?: number;
  createdTime: string;
  updatedTime: string;
}

export interface NamespaceInfo {
  name: string;
  labels: Record<string, string>;
  status: string;
  createdTime: string;
}

export interface ContainerImage {
  containerName: string;
  image: string;
  repository: string;
  tag: string;
}

export interface DeploymentInfo {
  name: string;
  namespace: string;
  labels: Record<string, string>;
  replicas: number;
  readyReplicas: number;
  availableReplicas: number;
  createdTime: string;
  images: ContainerImage[];
}

export interface ServiceInfo {
  name: string;
  namespace: string;
  labels: Record<string, string>;
  clusterIP: string;
  ports: ServicePort[];
  createdTime: string;
}

export interface ServicePort {
  name: string;
  port: number;
  targetPort: string;
  protocol: string;
}

// 请求参数类型
export interface ListClustersParams {
  page: number;
  pageSize: number;
  name?: string;
  status?: string;
}

export interface NamespaceRequest {
  cluster: string;
  name?: string;
  page?: number;
  pageSize?: number;
}

export interface ResourceListRequest {
  cluster: string;
  namespace: string;
  name?: string;
  page?: number;
  pageSize?: number;
}

export interface ClusterSelectOption {
  label: string;
  value: string;
}

// 新增参数类型
export interface AddClusterParams {
  name: string;
  description?: string;
  apiServer: string;
  token?: string;
  kubeConfig?: string;
}

export interface UpdateClusterParams {
  uuid: string;
  name?: string;
  description?: string;
  apiServer?: string;
  token?: string;
  kubeConfig?: string;
}

export interface DeleteClusterParams {
  uuids: string[];
}

export interface InfoClusterParams {
  uuid: string;
}

export interface ListClusterForSelectParams {
  enable?: boolean;
}

// API 方法
export const clusterApi = {
  // 集群管理
  list: (params: ListClustersParams) => {
    return request.post<PageResponse<ClusterInfo>>(
      '/api/v1/cluster/list',
      params,
    );
  },

  info: (params: { cluster: string }) => {
    return request.post<ClusterInfo>('/api/v1/cluster/info', params);
  },

  add: (params: AddClusterParams) => {
    return request.post<ClusterInfo>('/api/v1/cluster/add', params);
  },

  update: (params: UpdateClusterParams) => {
    return request.put<ClusterInfo>('/api/v1/cluster/update', params);
  },

  delete: (params: DeleteClusterParams) => {
    return request.delete<void>('/api/v1/cluster/delete', { data: params });
  },

  getInfo: (params: InfoClusterParams) => {
    return request.post<ClusterInfo>('/api/v1/cluster/info', params);
  },

  listForSelect: (params: ListClusterForSelectParams) => {
    return request
      .post<PageResponse<ClusterInfo>>('/api/v1/cluster/list', {
        page: 1,
        pageSize: 100,
        ...params,
      })
      .then((response) => {
        // 转换为选择列表格式，保持与其他组件期望的格式兼容
        if (response.data && response.data.list) {
          const transformedList = response.data.list.map(
            (cluster: ClusterInfo) => ({
              label: cluster.name,
              value: cluster.uuid,
            }),
          );

          return {
            data: {
              list: transformedList,
              total: transformedList.length,
            },
          };
        }
        return {
          data: {
            list: [],
            total: 0,
          },
        };
      });
  },

  // 资源查询
  listNamespaces: (params: NamespaceRequest) => {
    return request.post<ClusterSelectOption[]>(
      '/api/v1/cluster/resources/namespaces',
      params,
    );
  },

  listDeployments: (params: ResourceListRequest) => {
    return request.post<PageResponse<DeploymentInfo>>(
      '/api/v1/cluster/resources/deployments',
      params,
    );
  },

  listServices: (params: ResourceListRequest) => {
    return request.post<PageResponse<ServiceInfo>>(
      '/api/v1/cluster/resources/services',
      params,
    );
  },
};

// 向后兼容的函数导出
export const listCluster = clusterApi.list;
export const addCluster = clusterApi.add;
export const updateCluster = clusterApi.update;
export const deleteCluster = clusterApi.delete;
export const infoCluster = clusterApi.getInfo;
export const listClusterForSelect = clusterApi.listForSelect;
