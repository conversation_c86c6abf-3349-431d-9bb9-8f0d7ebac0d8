import { pilotRequest as request } from '@/utils/pilot';

enum Api {
  ListNamespaces = '/api/v1/cluster/resources/namespaces',
  ListGateways = '/api/v1/gateway/gateways',
  ListServices = '/api/v1/gateway/services',

  GetVirtualService = '/api/v1/gateway/virtualservice/info',
  ListVirtualServices = '/api/v1/gateway/virtualservice/list',
  AddVirtualService = '/api/v1/gateway/virtualservice/add',
  UpdateVirtualService = '/api/v1/gateway/virtualservice/update',
  DeleteVirtualService = '/api/v1/gateway/virtualservice/delete',

  // VirtualService审批相关
  ReviewVirtualService = '/api/v1/gateway/virtualservice/review', // 新的统一审批接口
  DryRunVirtualService = '/api/v1/gateway/virtualservice/dryrun',

  ListDestinationRules = '/api/v1/gateway/destinationrules',
  GetDestinationRule = '/api/v1/gateway/destinationrule',
}

// 获取 Namespace 列表
export function getNamespaces(params: { cluster: string }) {
  return request.post(Api.ListNamespaces, params);
}

// 获取 K8s Services
export function getK8sServices(params: any) {
  return request.post(Api.ListServices, params);
}

// 获取服务列表（为了保持兼容性，保留这个函数）
export function getServices(params: any) {
  return request.post(Api.ListServices, params);
}

// 获取 VirtualServices
export function getVirtualServices(params: any) {
  return request.post(Api.ListVirtualServices, params);
}

// 获取 VirtualService 详情
export function getVirtualService(params: {
  cluster: string;
  namespace: string;
  name: string;
  online?: boolean;
  silent?: boolean;
  fallback?: boolean;
}) {
  return request.post(Api.GetVirtualService, params);
}

// 获取 DestinationRules
export function getDestinationRules(params: any) {
  return request.post(Api.ListDestinationRules, params);
}

// 获取 DestinationRule 详情
export function getDestinationRule(params: {
  cluster: string;
  namespace: string;
  name: string;
}) {
  return request.post(Api.GetDestinationRule, params);
}

// 获取 Gateways
export function getGateways(params: any) {
  return request.post(Api.ListGateways, params);
}

// 创建 VirtualService
export function createVirtualService(params: any) {
  return request.post(Api.AddVirtualService, params);
}

// 更新 VirtualService
export function updateVirtualService(params: any) {
  return request.put(Api.UpdateVirtualService, params);
}

// 删除 VirtualService
export function deleteVirtualService(params: {
  resources: Array<{
    cluster: string;
    namespace: string;
    name: string;
  }>;
  reason?: string;
}) {
  return request.delete(Api.DeleteVirtualService, { data: params });
}

// 获取 VirtualService 历史版本
export function getVirtualServiceHistory(params: {
  cluster: string;
  namespace: string;
  name: string;
  page?: number;
  pageSize?: number;
}) {
  return request.post('/api/v1/gateway/virtualservice/history', params);
}

// 统一审批VirtualService (推荐使用这个新接口)
export function reviewVirtualService(params: {
  uuid: string;
  // namespace: string;
  // name: string;
  action: 'approve' | 'reject'; // 操作类型：批准或拒绝
  comment: string;
  content?: string; // 修改后的YAML内容
}) {
  return request.post(Api.ReviewVirtualService, params);
}

export function dryRunVirtualService(params: {
  cluster: string;
  namespace: string;
  name: string;
  content: string;
}) {
  return request.post(Api.DryRunVirtualService, params);
}
