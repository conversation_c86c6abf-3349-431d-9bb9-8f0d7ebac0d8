import { pilotRequest as request } from '@/utils/pilot';

enum Api {
  // Domain Provider APIs
  ListDomainProviders = '/api/v1/gateway/domain/provider/list',

  // Domain APIs
  ListDomains = '/api/v1/gateway/domain/list',

  // Domain Record APIs
  ListDomainRecords = '/api/v1/gateway/domain/record/list',
  AddDomainRecord = '/api/v1/gateway/domain/record/add',
  InfoDomainRecord = '/api/v1/gateway/domain/record/info',
  UpdateDomainRecord = '/api/v1/gateway/domain/record/update',
  DeleteDomainRecord = '/api/v1/gateway/domain/record/delete',
  SyncDomainRecords = '/api/v1/gateway/domain/record/sync',
  ValidateDomainRecord = '/api/v1/gateway/domain/record/validate',

  // Domain Approval APIs
  ListDomainApprovals = '/api/v1/gateway/domain/approval/list',
  ApproveDomainRecord = '/api/v1/gateway/domain/approval/approve',
  RejectDomainRecord = '/api/v1/gateway/domain/approval/reject',

  // Domain History APIs
  ListDomainHistory = '/api/v1/gateway/domain/history/list',
  RollbackDomainRecord = '/api/v1/gateway/domain/history/rollback',
}

// ============ Domain Provider APIs ============

// Get domain provider list
export function getDomainProviders(params: any) {
  return request.post(Api.ListDomainProviders, params);
}

// ============ Domain APIs ============

// Get domain list from cloud provider
export function getDomains(params: any) {
  return request.post(Api.ListDomains, params);
}

// ============ Domain Record APIs ============

// Get domain record list
export function getDomainRecords(params: any) {
  return request.post(Api.ListDomainRecords, params);
}

// Add domain record
export function addDomainRecord(params: {
  domain: string;
  providerUuid: string;
  subdomain?: string;
  recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'NS';
  value: string;
  ttl?: number;
  priority?: number;
  proxied?: boolean; // DNS代理状态
  comment?: string;
  reason?: string;
}) {
  return request.post(Api.AddDomainRecord, params);
}

// Get domain record info
export function getDomainRecordInfo(params: any) {
  return request.post(Api.InfoDomainRecord, params);
}

// Update domain record
export function updateDomainRecord(params: {
  uuid?: string; // 可选，用于编辑pending记录
  version?: number;
  domain: string;
  providerUuid: string;
  subdomain?: string;
  recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'NS';
  value: string;
  ttl?: number;
  priority?: number;
  proxied?: boolean; // DNS代理状态
  comment?: string;
  reason?: string;
  prevRecord?: {
    subdomain: string;
    recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'NS';
    value: string;
    ttl: number;
    priority: number;
    proxied?: boolean; // DNS代理状态
  }; // 原记录信息，更新线上记录时需要
}) {
  return request.put(Api.UpdateDomainRecord, params);
}

// Delete domain record
export function deleteDomainRecord(params: {
  domain: string;
  providerUuid: string;
  subdomain?: string;
  recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'NS';
  comment?: string;
  reason: string;
  value: string;
}) {
  return request.delete(Api.DeleteDomainRecord, { data: params });
}

// Review domain record (approve/reject)
export function reviewDomainRecord(params: {
  uuid: string;
  action: 'approved' | 'rejected';
  comment: string;
}) {
  return request.post('/api/v1/gateway/domain/record/review', params);
}

// Sync domain records from provider
export function syncDomainRecords(params: { providerId: number }) {
  return request.post(Api.SyncDomainRecords, params);
}

// Validate domain record resolution
export function validateDomainRecord(params: {
  domain: string;
  subdomain?: string;
  recordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'NS';
  value: string;
}) {
  return request.post(Api.ValidateDomainRecord, params);
}

// ============ Domain Approval APIs ============

// Get domain approval list
export function getDomainApprovals(params: {
  status?: 'pending' | 'approved' | 'rejected';
  applicant?: string;
  approver?: string;
  page: number;
  pageSize: number;
}) {
  return request.post(Api.ListDomainApprovals, params);
}

// Approve domain record changes
export function approveDomainRecord(params: { uuid: string; comment: string }) {
  return request.post(Api.ApproveDomainRecord, params);
}

// Reject domain record changes
export function rejectDomainRecord(params: { uuid: string; comment: string }) {
  return request.post(Api.RejectDomainRecord, params);
}

// ============ Domain History APIs ============

// Get domain record history
export function getDomainHistory(params: {
  recordUuid: string;
  page: number;
  pageSize: number;
}) {
  return request.post(Api.ListDomainHistory, params);
}

// Rollback domain record to specific version
export function rollbackDomainRecord(params: {
  recordUuid: string;
  version: number;
  reason: string;
}) {
  return request.post(Api.RollbackDomainRecord, params);
}

// ============ Helper Functions ============

// Get provider type options
export const PROVIDER_TYPE_OPTIONS = [
  { label: '阿里云DNS', value: 'alibaba' },
  { label: 'Azure DNS', value: 'azure' },
  { label: 'Cloudflare', value: 'cloudflare' },
];

// Get provider status options
export const PROVIDER_STATUS_OPTIONS = [
  { label: '启用', value: 'active' },
  { label: '停用', value: 'inactive' },
];

// Get record type options
export const RECORD_TYPE_OPTIONS = [
  { label: 'A', value: 'A', description: 'IPv4地址记录' },
  { label: 'AAAA', value: 'AAAA', description: 'IPv6地址记录' },
  { label: 'CNAME', value: 'CNAME', description: '别名记录' },
  { label: 'MX', value: 'MX', description: '邮件交换记录' },
  { label: 'TXT', value: 'TXT', description: '文本记录' },
  { label: 'SRV', value: 'SRV', description: '服务记录' },
  { label: 'NS', value: 'NS', description: '域名服务器记录' },
];

// Get record status options
export const RECORD_STATUS_OPTIONS = [
  { label: '已启用', value: 'active' },
  { label: '待审批', value: 'pending' },
  // { label: '失败', value: 'failed' },
  // { label: '已删除', value: 'deleted' },
];

// Get approval status options
export const APPROVAL_STATUS_OPTIONS = [
  { label: '待审批', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' },
];

// Get operation type options
export const OPERATION_TYPE_OPTIONS = [
  { label: '创建', value: 'create' },
  { label: '更新', value: 'update' },
  { label: '删除', value: 'delete' },
];

// Get status color mapping
export const getStatusColor = (status: string) => {
  const statusColorMap: Record<string, string> = {
    active: 'green',
    pending: 'orange',
    failed: 'red',
    deleted: 'red',
    approved: 'green',
    rejected: 'red',
  };
  return statusColorMap[status] || 'default';
};

// Get status text mapping
export const getStatusText = (status: string) => {
  const statusTextMap: Record<string, string> = {
    active: '启用',
    pending: '待审批',
    failed: '失败',
    deleted: '已删除',
    approved: '已通过',
    rejected: '已拒绝',
    alibaba: '阿里云',
    azure: 'Azure',
    cloudflare: 'Cloudflare',
    create: '创建',
    update: '更新',
    delete: '删除',
  };
  return statusTextMap[status] || status;
};

// Get provider type text
export const getProviderTypeText = (type: string) => {
  const provider = PROVIDER_TYPE_OPTIONS.find((p) => p.value === type);
  return provider?.label || type;
};

// Get record type description
export const getRecordTypeDescription = (type: string) => {
  const recordType = RECORD_TYPE_OPTIONS.find((r) => r.value === type);
  return recordType?.description || type;
};

// Get record type color mapping
export const getRecordTypeColor = (type: string) => {
  const recordTypeColorMap: Record<string, string> = {
    'A': 'blue',
    'AAAA': 'purple',
    'CNAME': 'green',
    'MX': 'orange',
    'TXT': 'geekblue',
    'SRV': 'magenta',
    'NS': 'volcano',
  };
  return recordTypeColorMap[type] || 'default';
};
