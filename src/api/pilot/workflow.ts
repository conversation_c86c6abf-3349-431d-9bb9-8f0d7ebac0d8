// import { request } from '@/utils/axios';
// TODO: 本地引用
import { pilotRequest as request } from '@/utils/pilot';

// 工作流类型
export type WorkflowType = 'canary' | 'blue-green' | 'ab-test';

// 灰度服务配置
export interface CanaryService {
  serviceName: string;
  namespace: string;
  order: number;
  images: Record<string, string>;
  version?: string;
  envs?: Record<string, string>;
  resources?: Record<string, string>;
  dependencies?: string[];
  canaryReplicas: number;
  prodReplicas: number;
  trafficRatio: number;
  strategy?: string;
  routingConfig?: ServiceRoutingConfig;
  monitoringConfig?: ServiceMonitoringConfig;
  policy?: CanaryServicePolicy;
  chainConfig?: ChainPropagationConfig;
  isChainEntry?: boolean;
}

// 服务路由配置
export interface ServiceRoutingConfig {
  headerRouting?: {
    headerName: string;
    headerValue: string;
  };
  weightRouting?: {
    canaryWeight: number;
    stableWeight: number;
  };
  cookieRouting?: {
    cookieName: string;
    cookieValue: string;
  };
  strategy: 'header' | 'weight' | 'cookie';
}

// 服务监控配置
export interface ServiceMonitoringConfig {
  metrics: string[];
  thresholds: Array<{
    name: string;
    operator: string;
    value: number;
    unit: string;
  }>;
  duration: string;
  interval: string;
}

// 灰度服务策略
export interface CanaryServicePolicy {
  autoPromotion: boolean;
  promotionCriteria?: Array<{
    metric: string;
    threshold: number;
    operator: string;
  }>;
  rollbackCriteria?: Array<{
    metric: string;
    threshold: number;
    operator: string;
  }>;
  healthCheck?: {
    path: string;
    intervalSeconds: number;
    timeoutSeconds: number;
    successThreshold: number;
    failureThreshold: number;
    initialDelaySeconds: number;
  };
}

// 链路传播配置
export interface ChainPropagationConfig {
  enablePropagation: boolean;
  propagationHeader?: string;
  propagationValue?: string;
  downstreamServices?: string[];
  stickySession?: boolean;
  fallbackStrategy?: string;
}

// 全局配置
export interface CanaryGlobalConfig {
  globalHeaders?: Record<string, string>;
  globalMonitoring?: {
    enableTracing: boolean;
    traceHeaders?: Record<string, string>;
  };
  globalRollback?: WorkflowGlobalRollback;
  globalTimeout?: {
    stepTimeout: number;
    approvalTimeout: number;
    monitoringTimeout: number;
    rollbackTimeout: number;
  };
  globalNotification?: {
    enabled: boolean;
    channels: string[];
    events: string[];
    template?: string;
    users?: string[];
  };
  globalApproval?: GlobalApprovalConfig;
}

// 全链路灰度配置
export interface ChainCanaryConfig {
  enableChainCanary: boolean;
  propagationHeader?: string;
  propagationValue?: string;
  entryServices: string[];
  serviceDependencies?: Record<string, string[]>;
  trafficStrategy: 'weight' | 'header' | 'cookie';
  defaultTrafficRatio: number;
  serviceChainOrders?: Record<string, number>;
  fallbackStrategy?: string;
  stickySession?: boolean;
  chainValidation?: {
    enableValidation: boolean;
    validationTimeout: number;
    endToEndTests?: Array<{
      name: string;
      path: string;
      method: string;
      headers?: Record<string, string>;
      body?: string;
      expectedCode: number;
      timeout: number;
    }>;
    serviceHealthChecks?: Record<
      string,
      {
        path: string;
        intervalSeconds: number;
        timeoutSeconds: number;
        successThreshold: number;
        failureThreshold: number;
        initialDelaySeconds: number;
      }
    >;
  };
}

// 工作流步骤配置
export interface WorkflowStepConfig {
  name: string;
  type: string;
  order: number;
  serviceName?: string;
  config?: Record<string, any>;
  autoExecute: boolean;
  timeout: number;
  retryCount?: number;
}

// 审批配置
export interface ApprovalConfig {
  approvers: string[];
  conditionType: 'and' | 'or';
  timeout: number;
}

// 全局审批配置
export interface GlobalApprovalConfig {
  enabled: boolean;
  // 灰度发布审批 - 基于 PilotAdmin 或 WorkflowAdmin 权限
  enableCanaryApproval: boolean;
  // 生产上线审批 - 基于 PilotAdmin 权限
  enableProductionApproval: boolean;
}

// 监控配置
export interface WorkflowGlobalMonitoring {
  enabled: boolean;
  duration?: string;
  interval?: string;
  metrics?: string[];
  alertRules?: string[];
  dashboardUrl?: string;
}

// 回滚配置
export interface WorkflowGlobalRollback {
  enabled: boolean;
  autoRollback: boolean;
  rollbackTrigger?: string;
  thresholdConfig?: string;
}

// 创建工作流请求参数
export interface CreateWorkflowParams {
  name: string;
  description?: string;
  type: WorkflowType;
  namespace: string;
  clusterId: string;
  labels?: Record<string, string>;

  // 服务配置
  services: CanaryService[];
  globalConfig?: CanaryGlobalConfig;
  steps?: WorkflowStepConfig[];
  dependencies?: string[];
  canaryApprovals?: ApprovalConfig[];
  releaseApprovals?: ApprovalConfig[];
  monitoringConfig?: WorkflowGlobalMonitoring;
  rollbackConfig?: WorkflowGlobalRollback;

  // 全链路配置
  chainConfig?: ChainCanaryConfig;
}

// 工作流列表请求参数
export interface ListWorkflowParams {
  page: number;
  pageSize: number;
  name?: string;
  serviceName?: string;
  namespace?: string;
  clusterId?: string;
  status?: string;
  type?: string;
}

// 执行工作流请求参数
export interface ExecuteWorkflowParams {
  workflowId: number;
}

// 审批步骤请求参数
export interface ApproveStepParams {
  executionId: number;
  stepId: number;
  action: 'approve' | 'reject';
  comment?: string;
}

// 工作流响应
export interface WorkflowResponse {
  id: number;
  uuid: string;
  name: string;
  description?: string;
  type: WorkflowType;
  namespace: string;
  clusterId: string;
  status: string;
  version: number;
  creator: string;
  modifier?: string;
  gmtCreate: string;
  gmtModified: string;
  createTime?: string; // 兼容字段
  updateTime?: string; // 兼容字段
  steps?: any[];
  chainConfig?: ChainCanaryConfig; // 全链路灰度配置
}

// 工作流执行响应
export interface WorkflowExecutionResponse {
  id: number;
  workflowId: number;
  status: string;
  startTime: string;
  endTime?: string;
  executorId: string;
  executorName: string;
  triggerType: string;
  stepExecutions?: any[];
}

// 工作流执行列表请求参数
export interface ListWorkflowExecutionsParams {
  page: number;
  pageSize: number;
  status?: string;
  workflowName?: string;
  executor?: string;
  startTime?: string;
  endTime?: string;
  workflowId?: number;
}

// 删除工作流请求参数
export interface DeleteWorkflowParams {
  uuids: string[];
}

// 获取工作流类型响应
export interface WorkflowTypeInfo {
  type: WorkflowType;
  name: string;
  description: string;
  version: string;
  author: string;
  supported: boolean;
  tags: string[];
  features: string[];
}

// 获取配置模板请求参数
export interface GetConfigTemplateParams {
  type: WorkflowType;
}

// 配置模板响应
export interface ConfigTemplateResponse {
  type: WorkflowType;
  name: string;
  description: string;
  configSchema: {
    schema: Record<string, any>;
    defaultData: Record<string, any>;
    examples: Record<string, any>;
  };
  metadata: {
    name: string;
    description: string;
    version: string;
    author: string;
    tags: string[];
    features: string[];
  };
}

// 预览工作流模板请求参数
export interface PreviewTemplateParams {
  type: WorkflowType;
  services: Array<{
    name: string;
    images: Array<{
      name: string;
      repository: string;
      tag: string;
    }>;
    replicas: number;
    resources?: Record<string, any>;
    environment?: Record<string, string>;
    strategy?: string;
  }>;
}

// 工作流模板预览响应
export interface WorkflowTemplatePreviewResponse {
  steps: Array<{
    name: string;
    type: string;
    order: number;
    autoExecute: boolean;
    timeout: number;
    config: string;
    serviceName?: string;
    retryCount?: number;
  }>;
}

// 根据模板创建工作流请求参数
export interface CreateWorkflowFromTemplateParams {
  name: string;
  description?: string;
  type: WorkflowType;
  namespace: string;
  clusterId: string;
  labels?: Record<string, string>;
  config: Record<string, any>;
}

// 提交审批请求参数
export interface SubmitApprovalParams {
  uuid: string;
}

// 审批工作流请求参数
export interface ApproveWorkflowParams {
  uuid: string;
  action: 'approve' | 'reject';
}

// 工作流指标响应
export interface WorkflowMetricsResponse {
  workflowId: number;
  totalExecutions: number;
  successRate: number;
  failureRate: number;
  averageTime: number;
  runningCount: number;
}

// API 方法
export const workflowApi = {
  // 获取支持的工作流类型
  getTypes: () => {
    return request.get<{ types: WorkflowTypeInfo[] }>('/api/v1/workflow/types');
  },

  // 获取工作流配置模板
  getConfigTemplate: (params: GetConfigTemplateParams) => {
    return request.get<ConfigTemplateResponse>('/api/v1/workflow/template', {
      params,
    });
  },

  // 预览工作流模板
  previewTemplate: (params: PreviewTemplateParams) => {
    return request.post<WorkflowTemplatePreviewResponse>(
      '/api/v1/workflow/template/preview',
      params,
    );
  },

  // 根据模板创建工作流
  createFromTemplate: (params: CreateWorkflowFromTemplateParams) => {
    return request.post('/api/v1/workflow/template/create', params);
  },

  // 创建工作流 (自定义配置)
  create: (params: any) => {
    return request.post('/api/v1/workflow/create', params);
  },

  // 获取工作流列表
  list: (params: ListWorkflowParams) => {
    return request.post('/api/v1/workflow/list', params);
  },

  // 获取工作流详情
  getDetail: (uuid: string) => {
    return request.get(`/api/v1/workflow/${uuid}`);
  },

  // 删除工作流
  delete: (params: DeleteWorkflowParams) => {
    return request.post('/api/v1/workflow/delete', params);
  },

  // 提交审批
  submitApproval: (uuid: string) => {
    return request.post(`/api/v1/workflow/${uuid}/submit`);
  },

  // 审批工作流
  approveWorkflow: (params: ApproveWorkflowParams) => {
    return request.post(`/api/v1/workflow/${params.uuid}/approve`, {
      action: params.action,
    });
  },

  // 执行工作流
  execute: (params: ExecuteWorkflowParams) => {
    return request.post('/api/v1/workflow/execute', params);
  },

  // 获取工作流执行列表
  listExecutions: (params: ListWorkflowExecutionsParams) => {
    return request.post('/api/v1/workflow/executions/list', params);
  },

  // 暂停工作流执行
  pause: (executionId: number) => {
    return request.post(`/api/v1/workflow/execution/${executionId}/pause`);
  },

  // 恢复工作流执行
  resume: (executionId: number) => {
    return request.post(`/api/v1/workflow/execution/${executionId}/resume`);
  },

  // 取消工作流执行
  cancel: (executionId: number) => {
    return request.post(`/api/v1/workflow/execution/${executionId}/cancel`);
  },

  // 获取工作流执行状态
  getExecution: (executionId: number) => {
    return request.get(`/api/v1/workflow/execution/${executionId}`);
  },

  // 删除工作流执行记录
  deleteExecution: (executionId: number) => {
    return request.delete(`/api/v1/workflow/execution/${executionId}`);
  },

  // 审批步骤
  approveStep: (params: ApproveStepParams) => {
    return request.post('/api/v1/workflow/step/approve', params);
  },

  // 获取工作流指标
  getMetrics: (uuid: string) => {
    return request.get<WorkflowMetricsResponse>(
      `/api/v1/workflow/${uuid}/metrics`,
    );
  },
};
