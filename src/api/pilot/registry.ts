// import { request } from '@/utils/axios';
import { pilotRequest as request } from '@/utils/pilot';

// Registry类型
export type RegistryType = 'aliyun' | 'nexus' | 'harbor';

// 获取镜像标签列表请求参数
export interface GetRepositoryTagsParams {
  page: number;
  pageSize: number;
  registryType: RegistryType;
  namespaceName: string;
  repositoryName: string;
  keyword?: string;
}

// 获取仓库列表请求参数
export interface GetRepositoriesParams {
  page: number;
  pageSize: number;
  registryType: RegistryType;
  namespaceName?: string;
  keyword?: string;
}

// 镜像标签响应
export interface RepositoryTagsResponse {
  list: string[];
  total: number;
  page: number;
  pageSize: number;
}

// 仓库信息
export interface RepositoryInfo {
  name: string;
  namespace: string;
  fullName: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

// 仓库列表响应
export interface RepositoriesResponse {
  list: RepositoryInfo[];
  total: number;
  page: number;
  pageSize: number;
}

// Registry API
export const registryApi = {
  // 获取镜像标签列表
  getTags: (params: GetRepositoryTagsParams) => {
    return request.post<RepositoryTagsResponse>(
      '/api/v1/registry/tags',
      params,
    );
  },

  // 获取仓库列表（如果后端有这个接口）
  getRepositories: (params: GetRepositoriesParams) => {
    return request.post<RepositoriesResponse>(
      '/api/v1/registry/repositories',
      params,
    );
  },
};

// 常用的镜像仓库配置
export const commonRepositories = [
  {
    name: 'nginx',
    namespace: 'library',
    fullName: 'library/nginx',
    description: 'Official Nginx image',
  },
  {
    name: 'redis',
    namespace: 'library',
    fullName: 'library/redis',
    description: 'Official Redis image',
  },
  {
    name: 'mysql',
    namespace: 'library',
    fullName: 'library/mysql',
    description: 'Official MySQL image',
  },
  {
    name: 'postgres',
    namespace: 'library',
    fullName: 'library/postgres',
    description: 'Official PostgreSQL image',
  },
];

// 常用标签
export const commonTags = [
  'latest',
  'stable',
  'alpine',
  '1.20',
  '1.21',
  '8.0',
  '14',
  '16',
];
