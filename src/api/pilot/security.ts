import { pilotRequest as request } from '@/utils/pilot';

enum Api {
  // AuthorizationPolicy 相关接口
  ListAuthorizationPolicies = '/api/v1/security/authorization-policies',
  GetAuthorizationPolicy = '/api/v1/security/authorization-policy/get',
  CreateAuthorizationPolicy = '/api/v1/security/authorization-policy/create',
  UpdateAuthorizationPolicy = '/api/v1/security/authorization-policy/update',
  DeleteAuthorizationPolicy = '/api/v1/security/authorization-policy/delete',

  // 统一的安全规则接口
  ListSecurityRules = '/api/v1/security/rules',
  CreateSecurityRule = '/api/v1/security/rule/create',
  UpdateSecurityRule = '/api/v1/security/rule/update',
  DeleteSecurityRule = '/api/v1/security/rule/delete',

  // 兼容性：黑白名单相关接口
  ListBlacklistRules = '/api/v1/security/blacklist-rules',
  ListWhitelistRules = '/api/v1/security/whitelist-rules',
  CreateBlacklistRule = '/api/v1/security/blacklist-rule/create',
  CreateWhitelistRule = '/api/v1/security/whitelist-rule/create',
  UpdateBlacklistRule = '/api/v1/security/blacklist-rule/update',
  UpdateWhitelistRule = '/api/v1/security/whitelist-rule/update',
  DeleteBlacklistRule = '/api/v1/security/blacklist-rule/delete',
  DeleteWhitelistRule = '/api/v1/security/whitelist-rule/delete',

  // 安全策略模板
  ListSecurityTemplates = '/api/v1/security/templates',

  // YAML生成和应用
  ApplySecurityRuleToCluster = '/api/v1/security/apply-to-cluster',

  // 历史记录
  GetSecurityRuleHistory = '/api/v1/security/rule-history',
}

// AuthorizationPolicy 接口类型定义
export interface AuthorizationPolicyItem {
  name: string;
  namespace: string;
  cluster: string;
  spec: {
    selector?: {
      matchLabels: Record<string, string>;
    };
    rules?: Array<{
      from?: Array<{
        source?: {
          principals?: string[];
          requestPrincipals?: string[];
          namespaces?: string[];
          ipBlocks?: string[];
        };
      }>;
      to?: Array<{
        operation?: {
          methods?: string[];
          paths?: string[];
        };
      }>;
      when?: Array<{
        key: string;
        values: string[];
      }>;
    }>;
    action?: 'ALLOW' | 'DENY';
  };
  createdAt?: string;
  updatedAt?: string;
}

// 黑白名单规则类型定义
export interface SecurityRuleItem {
  id: string;
  name: string;
  namespace: string;
  cluster: string;
  type: 'blacklist' | 'whitelist' | 'ratelimit';
  rules: {
    ipBlocks?: string[];
    principals?: string[];
    namespaces?: string[];
    methods?: string[];
    paths?: string[];
    // 限流相关字段
    rateLimit?: {
      domain: string;
      descriptors: Array<{
        key: string;
        value: string;
        rate_limit: {
          requests_per_unit: number;
          unit: 'SECOND' | 'MINUTE' | 'HOUR' | 'DAY';
        };
      }>;
      failure_mode_deny: boolean;
      timeout: number;
    };
  };
  enabled: boolean;
  description?: string;
  // 审核状态
  auditStatus?: 'pending' | 'approved' | 'rejected' | 'applied';
  createdAt: string;
  updatedAt: string;
}

// 安全规则历史记录
export interface SecurityRuleHistoryItem {
  id: string;
  ruleId: string;
  version: number;
  operation: 'create' | 'update' | 'delete' | 'approve' | 'reject';
  operatorId: string;
  operatorName: string;
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  description?: string;
  createdAt: string;
}

// 获取 AuthorizationPolicy 列表
export function getAuthorizationPolicies(params: {
  cluster: string;
  namespace?: string;
  page?: number;
  pageSize?: number;
}) {
  return request.post(Api.ListAuthorizationPolicies, params);
}

// 获取 AuthorizationPolicy 详情
export function getAuthorizationPolicy(params: {
  cluster: string;
  namespace: string;
  name: string;
}) {
  return request.post(Api.GetAuthorizationPolicy, params);
}

// 创建 AuthorizationPolicy
export function createAuthorizationPolicy(params: AuthorizationPolicyItem) {
  return request.post(Api.CreateAuthorizationPolicy, params);
}

// 更新 AuthorizationPolicy
export function updateAuthorizationPolicy(params: AuthorizationPolicyItem) {
  return request.post(Api.UpdateAuthorizationPolicy, params);
}

// 删除 AuthorizationPolicy
export function deleteAuthorizationPolicy(params: {
  cluster: string;
  namespace: string;
  name: string;
}) {
  return request.post(Api.DeleteAuthorizationPolicy, params);
}

// 统一的安全规则接口
export function getSecurityRules(params: {
  cluster: string;
  namespace?: string;
  type?: 'blacklist' | 'whitelist' | 'ratelimit';
  page?: number;
  pageSize?: number;
}) {
  return request.post(Api.ListSecurityRules, params);
}

export function createSecurityRule(
  params: Omit<SecurityRuleItem, 'id' | 'createdAt' | 'updatedAt'>,
) {
  return request.post(Api.CreateSecurityRule, params);
}

export function updateSecurityRule(params: SecurityRuleItem) {
  // 构造后端期望的UpdateSecurityRuleRequest格式
  const updateRequest = {
    uuid: params.id, // 后端期望uuid字段
    name: params.name,
    namespace: params.namespace,
    cluster: params.cluster,
    type: params.type,
    rules: params.rules,
    enabled: params.enabled,
    description: params.description,
  };
  return request.put(Api.UpdateSecurityRule, updateRequest);
}

export function deleteSecurityRule(params: {
  cluster: string;
  namespace: string;
  id: string;
}) {
  return request.delete(Api.DeleteSecurityRule, { data: params });
}

// 获取黑名单规则列表
export function getBlacklistRules(params: {
  cluster: string;
  namespace?: string;
  page?: number;
  pageSize?: number;
}) {
  return request.post(Api.ListBlacklistRules, params);
}

// 获取白名单规则列表
export function getWhitelistRules(params: {
  cluster: string;
  namespace?: string;
  page?: number;
  pageSize?: number;
}) {
  return request.post(Api.ListWhitelistRules, params);
}

// 创建黑名单规则
export function createBlacklistRule(
  params: Omit<SecurityRuleItem, 'id' | 'createdAt' | 'updatedAt'>,
) {
  return request.post(Api.CreateBlacklistRule, params);
}

// 创建白名单规则
export function createWhitelistRule(
  params: Omit<SecurityRuleItem, 'id' | 'createdAt' | 'updatedAt'>,
) {
  return request.post(Api.CreateWhitelistRule, params);
}

// 更新黑名单规则
export function updateBlacklistRule(params: SecurityRuleItem) {
  return request.post(Api.UpdateBlacklistRule, params);
}

// 更新白名单规则
export function updateWhitelistRule(params: SecurityRuleItem) {
  return request.post(Api.UpdateWhitelistRule, params);
}

// 删除黑名单规则
export function deleteBlacklistRule(params: {
  cluster: string;
  namespace: string;
  id: string;
}) {
  return request.post(Api.DeleteBlacklistRule, params);
}

// 删除白名单规则
export function deleteWhitelistRule(params: {
  cluster: string;
  namespace: string;
  id: string;
}) {
  return request.post(Api.DeleteWhitelistRule, params);
}

// 获取安全策略模板
export function getSecurityTemplates(params: {
  type?: 'blacklist' | 'whitelist';
}) {
  return request.post(Api.ListSecurityTemplates, params);
}

// 应用安全规则到集群
export function applySecurityRuleToCluster(
  rule: SecurityRuleItem,
  yaml: string,
) {
  return request.post(Api.ApplySecurityRuleToCluster, {
    rule,
    yaml,
  });
}

// 获取安全规则历史记录
export function getSecurityRuleHistory(params: {
  ruleId: string;
  page?: number;
  pageSize?: number;
}) {
  return request.get(Api.GetSecurityRuleHistory, { params });
}
