import { pilotRequest as request } from '@/utils/pilot';

enum Api {
  // 应用接口监控相关
  ApplicationMetrics = '/api/v1/application/metrics',
  ApplicationInterfaces = '/api/v1/application/interfaces',
  ApplicationQPS = '/api/v1/application/qps',
  ApplicationRT = '/api/v1/application/rt',
  ApplicationOverview = '/api/v1/application/overview',
}

// 获取应用接口概览数据
export function getApplicationOverview(params: {
  serviceName: string;
  namespace?: string;
  cluster?: string;
  timeRange?: string;
}) {
  return request.post(Api.ApplicationOverview, params);
}

// 获取应用QPS数据
export function getApplicationQPS(params: {
  serviceName: string;
  namespace?: string;
  cluster?: string;
  startTime?: string;
  endTime?: string;
  interval?: string;
}) {
  return request.post(Api.ApplicationQPS, params);
}

// 获取应用RT数据
export function getApplicationRT(params: {
  serviceName: string;
  namespace?: string;
  cluster?: string;
  startTime?: string;
  endTime?: string;
  interval?: string;
}) {
  return request.post(Api.ApplicationRT, params);
}

// 获取应用接口列表
export function getApplicationInterfaces(params: {
  serviceName: string;
  namespace?: string;
  cluster?: string;
  page?: number;
  pageSize?: number;
}) {
  return request.post(Api.ApplicationInterfaces, params);
}

// 获取应用监控指标
export function getApplicationMetrics(params: {
  serviceName: string;
  namespace?: string;
  cluster?: string;
  metrics?: string[];
  timeRange?: string;
}) {
  return request.post(Api.ApplicationMetrics, params);
}
