import { pipelineRequest as defHttp } from '@/utils/pipeline';

enum Api {
  CreateStrategy = '/api/v1/grayscale/strategies',
  ListStrategies = '/api/v1/grayscale/strategies',
  GetStrategy = '/api/v1/grayscale/strategies/',
  UpdateStrategy = '/api/v1/grayscale/strategies/',
  DeleteStrategy = '/api/v1/grayscale/strategies/',
}

export const createGrayscaleStrategy = (params: any) =>
  defHttp.post(Api.CreateStrategy, params);

export const listGrayscaleStrategies = (params?: any) =>
  defHttp.get(Api.ListStrategies, { params });

export const getGrayscaleStrategy = (id: number) =>
  defHttp.get(Api.GetStrategy + id);

export const updateGrayscaleStrategy = (id: number, params: any) =>
  defHttp.put(Api.UpdateStrategy + id, params);

export const deleteGrayscaleStrategy = (id: number) =>
  defHttp.delete(Api.DeleteStrategy + id);
