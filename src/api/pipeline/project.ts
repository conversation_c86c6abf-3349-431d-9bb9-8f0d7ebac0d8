import { pipelineRequest as defHttp } from '@/utils/pipeline';
enum Api {
  AddProjectInfo = '/api/v1/project/info/add',
  InfoProjectInfo = '/api/v1/project/info/info',
  PageProjectInfo = '/api/v1/project/info/page',
  ListProjectInfo = '/api/v1/project/info/list',
  UpdateProjectInfo = '/api/v1/project/info/update',
  DeleteProjectInfo = '/api/v1/project/info/delete',
  RequestProjectInfo = '/api/v1/project/info/request',
  QueryUser = '/api/v1/system/user/query',
}

export const pageProjectInfo = (params: any) =>
  defHttp.post(Api.PageProjectInfo, params);

export const listProjectInfo = (params: any) =>
  defHttp.post(Api.ListProjectInfo, params);

export const deleteProjectInfo = (params: any) =>
  defHttp.delete(Api.DeleteProjectInfo, { data: params });

export const updateProjectInfo = (params: any) =>
  defHttp.put(Api.UpdateProjectInfo, params);

export const addProjectInfo = (params: any) =>
  defHttp.post(Api.AddProjectInfo, params);

export const infoProjectInfo = (params: any) =>
  defHttp.post(Api.InfoProjectInfo, params);

export const queryUser = (params: any) => defHttp.post(Api.QueryUser, params);

export const requestProjectInfo = (params: any) =>
  defHttp.post(Api.RequestProjectInfo, params);
