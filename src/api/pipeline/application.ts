import { pipelineRequest as defHttp } from '@/utils/pipeline';

enum Api {
  AddProjectApp = '/api/v1/project/app/add',
  ImportProjectApp = '/api/v1/project/app/import',
  InfoProjectApp = '/api/v1/project/app/info',
  InfoProjectAppByLoggerUUID = '/api/v1/project/app/info/',
  PageProjectApp = '/api/v1/project/app/page',
  ListProjectApp = '/api/v1/project/app/list',
  UpdateProjectApp = '/api/v1/project/app/update',
  DeleteProjectApp = '/api/v1/project/app/delete',
  ProjectAppTemplates = '/api/v1/project/app/templates',
  ProjectAppTemplatesRefresh = '/api/v1/project/app/templates/refresh',
  AppTemplatesParameters = '/api/v1/project/app/parameters',
  AppBranches = '/api/v1/project/app/branches',
  AppBranchesOrTags = '/api/v1/project/app/branchesOrTags',
  AppConfigHistory = '/api/v1/project/app/config/history',
  AppConfigVersion = '/api/v1/project/app/config/version',
  ApprovalAppConfig = '/api/v1/project/app/config/approval',
  ReviewAppConfig = '/api/v1/project/app/config/review',
  EditVersionAppConfig = '/api/v1/project/app/config/edit',
  EntryAppConfig = '/api/v1/project/app/config/entry',
  RequestJoinProjectApp = '/api/v1/project/app/request',
}

export const pageProjectApp = (params: any) =>
  defHttp.post(Api.PageProjectApp, params);

export const listProjectApp = (params: any) =>
  defHttp.post(Api.ListProjectApp, params);

export const deleteProjectApp = (params: any) =>
  defHttp.delete(Api.DeleteProjectApp, { data: params });

export const updateProjectApp = (params: any) =>
  defHttp.put(Api.UpdateProjectApp, params);

export const addProjectApp = (params: any) =>
  defHttp.post(Api.AddProjectApp, params, { responseType: 'blob' });

export const importProjectApp = (params: any) =>
  defHttp.post(Api.ImportProjectApp, params);

export const infoProjectApp = (params: any) =>
  defHttp.post(Api.InfoProjectApp, params);

export const projectAppTemplates = (params: any) =>
  defHttp.get(Api.ProjectAppTemplates, params);

export const projectAppTemplatesRefresh = () =>
  defHttp.get(Api.ProjectAppTemplatesRefresh);

export const appTemplatesParam = (params: any) =>
  defHttp.post(Api.AppTemplatesParameters, params);

export const appBranches = (params: any) =>
  defHttp.post(Api.AppBranches, params);

export const appBranchesOrTags = (params: any) =>
  defHttp.post(Api.AppBranchesOrTags, params);

export const getAppByLoggerUUID = (uuid: any) =>
  defHttp.get(Api.InfoProjectAppByLoggerUUID + uuid);

export const appConfigHistory = (params: any) =>
  defHttp.post(Api.AppConfigHistory, params);

export const appConfigVersion = (params: any) =>
  defHttp.post(Api.AppConfigVersion, params);

export const approvalAppConfig = (params: any) =>
  defHttp.post(Api.ApprovalAppConfig, params);

export const reviewAppConfig = (params: any) =>
  defHttp.post(Api.ReviewAppConfig, params);

export const editVersionAppConfig = (params: any) =>
  defHttp.post(Api.EditVersionAppConfig, params);

export const entryAppConfig = (params: any) =>
  defHttp.post(Api.EntryAppConfig, params);

export const requestJoinProjectApp = (params: any) =>
  defHttp.post(Api.RequestJoinProjectApp, params);
