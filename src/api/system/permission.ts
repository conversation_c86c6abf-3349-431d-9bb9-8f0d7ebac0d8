import type { ResponseArray, ResponseData } from '@/types/response';
import { request } from '@/utils/axios';
import type { AxiosResponse } from 'axios';

type ConfigType<T = ResponseData> = Promise<AxiosResponse<T>>;
const prefix = '/efficacy/v1';

// 获取应用列表
export const getPermissions = (): ConfigType<ResponseArray> => {
  return request({
    url: prefix + '/permissions?d=pilot',
    method: 'GET',
  });
};
