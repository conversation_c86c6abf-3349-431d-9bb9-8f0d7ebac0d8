import type { ResponseArray, ResponseData } from '@/types/response';
import { request } from '@/utils/axios';
import type { AxiosResponse } from 'axios';

type ConfigType<T = ResponseData> = Promise<AxiosResponse<T>>;
const prefix = '/efficacy/v1/admin';

// 获取应用列表
export const getDomains = (params: any): ConfigType<ResponseArray> => {
  return request({
    url: prefix + '/domains',
    method: 'GET',
    params,
  });
};
// 获取可选应用列表
export const getOptionalDomains = (): ConfigType<ResponseData> => {
  return request({
    url: prefix + '/domains/optional',
    method: 'GET',
  });
};
// 获取应用
export const getDomain = (id: number): ConfigType<ResponseData> => {
  return request({
    url: prefix + `/domain/${id}`,
    method: 'GET',
  });
};
// 添加应用
export const addDomain = (data: any): ConfigType<ResponseData> => {
  return request({
    url: prefix + '/domain',
    method: 'POST',
    data,
  });
};
// 编辑应用
export const editDomain = (id: number, data: any): ConfigType<ResponseData> => {
  return request({
    url: prefix + `/domain/${id}`,
    method: 'PUT',
    data,
  });
};
// 启用应用
export const enableDomain = (id: number): ConfigType<ResponseData> => {
  return request({
    url: prefix + `/domain/${id}/enable`,
    method: 'POST',
  });
};
// 禁用应用
export const disableDomain = (id: number): ConfigType<ResponseData> => {
  return request({
    url: prefix + `/domain/${id}/disable`,
    method: 'POST',
  });
};
// 删除应用
export const deleteDomain = (id: number): ConfigType<ResponseData> => {
  return request({
    url: prefix + `/domain/${id}`,
    method: 'DELETE',
  });
};
