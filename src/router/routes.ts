import { PermissionKey } from '@/types/permissionKey';
import Layout from '@/views/layout/index.vue';
import type { RouteRecordRaw } from 'vue-router';

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        name: PermissionKey.HOME,
        path: '/',
        component: () => import('@/views/index/index.vue'),
      },
    ],
  },
  {
    path: `/${PermissionKey.Governance}`,
    component: Layout,
    children: [
      {
        path: 'application',
        name: PermissionKey.Application,
        component: () => import('@/views/polit/application/index.vue'),
      },
      // {
      //   path: 'observability',
      //   name: PermissionKey.Observability,
      //   component: () => import('@/views/polit/observability/index.vue'),
      // },
      // {
      //   path: 'resource',
      //   name: PermissionKey.Resource,
      //   component: () =>
      //     import('@/views/polit/gateway/destinationrules/index.vue'),
      // },
    ],
  },
  {
    path: `/${PermissionKey.Gateway}`,
    component: Layout,
    children: [
      {
        path: PermissionKey.VirtualService,
        name: PermissionKey.VirtualService,
        component: () =>
          import('@/views/polit/gateway/virtualservice/index.vue'),
      },
      {
        path: PermissionKey.Domain,
        name: PermissionKey.Domain,
        component: () => import('@/views/polit/gateway/dns/index.vue'),
      },
    ],
  },
  {
    path: `/${PermissionKey.Canary}`,
    component: Layout,
    children: [
      {
        path: PermissionKey.Workflow,
        name: PermissionKey.Workflow,
        component: () => import('@/views/polit/workflow/index.vue'),
      },
      {
        path: PermissionKey.WorkflowExecution,
        name: PermissionKey.WorkflowExecution,
        component: () =>
          import('@/views/polit/workflow/components/WorkflowExecution.vue'),
      },
    ],
  },

  {
    path: `/${PermissionKey.System}`,
    component: Layout,
    children: [
      {
        path: 'cluster',
        name: PermissionKey.Cluster,
        component: () => import('@/views/polit/cluster/index.vue'),
      },
    ],
  },
];
