import store from '@/store';
import { PermissionKey } from '@/types/permissionKey';
import { appUtil } from '@/utils/app';
import type { Router } from 'vue-router';

const noAuthPages = [
  PermissionKey.LOGIN.toString(),
  PermissionKey.ERROR403.toString(),
  PermissionKey.ERROR500.toString(),
  PermissionKey.EXTERNAL_BASE_DATA.toString(),
  PermissionKey.FEI_SHU_REDIRECT.toString(),
];

// 权限验证
export const permission = (router: Router) => {
  // 添加路由导航状态管理，防止快速切换
  let isNavigating = false;

  router.beforeEach(async (to, from, next) => {
    // 如果正在导航中，则阻止新的导航
    if (isNavigating) {
      console.warn('Navigation already in progress, ignoring this navigation');
      return;
    }

    isNavigating = true;
    const toPath = to.name?.toString() || to.path.toString();
    if (noAuthPages.includes(toPath)) {
      isNavigating = false;
      next();
      return;
    }
    if (!appUtil.getUToken()) {
      isNavigating = false;
      appUtil.goToLogin();
      return;
    }
    if (store.state.user.userName.length === 0) {
      try {
        await store.dispatch('user/userInfo');
      } catch (error) {
        isNavigating = false;
        appUtil.goToLogin();
        return;
      }
    }
    if (store.state.app.permissions.length === 0) {
      await store.dispatch('app/permissions');
    }
    if (store.state.app.permissions.includes(toPath)) {
      isNavigating = false;
      next();
    } else {
      isNavigating = false;
      // next({ name: PermissionKey.ERROR403, query: { redirect: to.fullPath } });
      appUtil.goTo403(to.fullPath);
    }
  });

  router.afterEach((to) => {
    const toPath = to.name.toString();
    store.commit('app/updateOpenMenu', toPath);
  });
};
