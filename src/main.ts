import {
  Avatar,
  But<PERSON>,
  Card,
  DatePicker,
  Divider,
  Input,
  List,
  Menu,
  Modal,
  Table,
  message,
} from 'ant-design-vue';
// Register icon sprite
import 'virtual:svg-icons-register';
// https://www.antdv.com/docs/vue/getting-started-cn
// import 'ant-design-vue/es/message/style/css';
// import 'ant-design-vue/es/modal/style/css';
// 手动引入 ECharts 各模块来减小打包体积
import { Bar<PERSON>hart, LineChart, PieChart } from 'echarts/charts';
import {
  GridComponent,
  ToolboxComponent,
  TooltipComponent,
} from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
// 导入进度条插件
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
// 全局进度条的配置
NProgress.configure({
  easing: 'ease',
  showSpinner: false,
  parent: 'body', // 指定进度条的父容器
  speed: 500, // 递增进度条的速度
  trickleSpeed: 200, // 自动递增间隔
  minimum: 0.3, // 初始化时的最小百分比
});

use([
  CanvasRenderer,
  BarChart,
  PieChart,
  LineChart,
  GridComponent,
  TooltipComponent,
  ToolboxComponent,
]);

import {
  renderWithQiankun,
  qiankunWindow,
} from 'vite-plugin-qiankun/dist/helper';
import type { QiankunProps } from 'vite-plugin-qiankun/es/helper';
import { renderNavbar } from '@/utils/nav';

// @ts-ignore
let app: App<Element>;

const render = (container: HTMLElement) => {
  // @ts-ignore
  app = createApp(App);
  app.use(store);
  // app.use(router);
  app
    .use(Avatar)
    .use(Button)
    .use(Card)
    .use(DatePicker)
    .use(Divider)
    .use(Menu)
    .use(Table)
    .use(Input)
    .use(List)
    .use(Modal);
  app.config.globalProperties.$message = message;
  message.config({
    top: '120px',
  });
  app.use(router).mount(container ? container.querySelector('#app') : '#app');
};

const initQianKun = () => {
  renderWithQiankun({
    update(props: QiankunProps): void | Promise<void> {
      console.log('update props in main app', props);
      return undefined;
    },
    mount(props) {
      const { container } = props;
      render(container);
      renderNavbar();
    },
    bootstrap() {},
    unmount() {
      app.unmount();
    },
  });
};

// @ts-ignore
qiankunWindow.__POWERED_BY_QIANKUN__ ? initQianKun() : render();
