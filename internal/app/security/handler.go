package security

import (
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"strconv"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// 安全规则类型常量
const (
	RuleTypeBlacklist = "blacklist"
	RuleTypeWhitelist = "whitelist"
	RuleTypeRateLimit = "ratelimit"
)

// ListSecurityTemplates 获取安全策略模板列表
func ListSecurityTemplates(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req SecurityTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewSecurityService(ts, mysql.GetDB())
	list, err := svc.ListSecurityTemplates(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}

// GetSecurityRuleHistory 获取安全规则历史记录
func GetSecurityRuleHistory(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)

	// 从查询参数获取ruleId
	ruleID := c.Query("ruleId")
	if ruleID == "" {
		response.Err(c, response.RequestParamError("ruleId参数是必需的"))
		return
	}

	// 构造请求
	req := SecurityRuleHistoryRequest{
		RuleID: ruleID,
	}
	req.Page = 1
	req.PageSize = 10

	// 从查询参数获取分页信息
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}
	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			req.PageSize = ps
		}
	}

	svc := NewSecurityService(ts, mysql.GetDB())
	list, err := svc.GetSecurityRuleHistory(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}

// ListSecurityRules 统一的安全规则列表接口
func ListSecurityRules(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req SecurityRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewSecurityService(ts, mysql.GetDB())
	list, err := svc.listSecurityRules(req, req.Type) // 使用请求中的type参数
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}

// CreateSecurityRule 统一的创建安全规则接口
func CreateSecurityRule(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req CreateSecurityRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	operator := c.GetString(util.UserName)
	svc := NewSecurityService(ts, mysql.GetDB())

	var rule *SecurityRuleResponse
	var err error

	switch req.Type {
	case RuleTypeBlacklist:
		rule, err = svc.CreateBlacklistRule(req, operator)
	case RuleTypeWhitelist:
		rule, err = svc.CreateWhitelistRule(req, operator)
	case RuleTypeRateLimit:
		// 可以扩展支持限流规则创建
		rule, err = svc.createSecurityRule(req, operator)
	default:
		response.Err(c, response.RequestParamError("不支持的规则类型: "+req.Type))
		return
	}

	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, rule)
}

// UpdateSecurityRule 统一的更新安全规则接口
func UpdateSecurityRule(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateSecurityRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	operator := c.GetString(util.UserName)
	svc := NewSecurityService(ts, mysql.GetDB())

	var rule *SecurityRuleResponse
	var err error

	switch req.Type {
	case RuleTypeBlacklist:
		rule, err = svc.UpdateBlacklistRule(req, operator)
	case RuleTypeWhitelist:
		rule, err = svc.UpdateWhitelistRule(req, operator)
	case RuleTypeRateLimit:
		// 可以扩展支持限流规则更新
		rule, err = svc.updateSecurityRule(req, operator)
	default:
		response.Err(c, response.RequestParamError("不支持的规则类型: "+req.Type))
		return
	}

	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, rule)
}

// DeleteSecurityRule 统一的删除安全规则接口
func DeleteSecurityRule(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req DeleteSecurityRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	operator := c.GetString(util.UserName)
	svc := NewSecurityService(ts, mysql.GetDB())
	err := svc.deleteSecurityRule(req, operator)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}
