package security

import (
	"pilot-api/internal/pkg/request"
	"time"
)

// SecurityRuleRequest 安全规则基础请求
type SecurityRuleRequest struct {
	request.PageRequest
	Cluster   string `json:"cluster" binding:"required"` // 集群标识
	Namespace string `json:"namespace"`                  // 命名空间
	Type      string `json:"type"`                       // 规则类型：blacklist, whitelist, ratelimit
	Name      string `json:"name"`                       // 规则名称（用于搜索）
}

// CreateSecurityRuleRequest 创建安全规则请求
type CreateSecurityRuleRequest struct {
	Name        string           `json:"name" binding:"required"`
	Namespace   string           `json:"namespace" binding:"required"`
	Cluster     string           `json:"cluster" binding:"required"`
	Type        string           `json:"type" binding:"required"` // blacklist, whitelist, ratelimit
	Rules       SecurityRuleSpec `json:"rules" binding:"required"`
	Enabled     bool             `json:"enabled"`
	Description string           `json:"description"`
}

// UpdateSecurityRuleRequest 更新安全规则请求
type UpdateSecurityRuleRequest struct {
	request.UpdateRequest
	CreateSecurityRuleRequest
}

// SecurityRuleSpec 安全规则配置
type SecurityRuleSpec struct {
	IPBlocks   []string            `json:"ipBlocks,omitempty"`   // IP地址或CIDR
	Principals []string            `json:"principals,omitempty"` // 用户主体
	Namespaces []string            `json:"namespaces,omitempty"` // 命名空间
	Methods    []string            `json:"methods,omitempty"`    // HTTP方法
	Paths      []string            `json:"paths,omitempty"`      // 请求路径
	RateLimit  *RateLimitSpec      `json:"rateLimit,omitempty"`  // 限流配置
	Headers    map[string][]string `json:"headers,omitempty"`    // 请求头匹配
}

// RateLimitSpec 限流配置
type RateLimitSpec struct {
	Domain          string                `json:"domain"`
	Descriptors     []RateLimitDescriptor `json:"descriptors"`
	FailureModeDeny bool                  `json:"failure_mode_deny"`
	Timeout         int                   `json:"timeout"`
}

// RateLimitDescriptor 限流描述符
type RateLimitDescriptor struct {
	Key       string    `json:"key"`
	Value     string    `json:"value"`
	RateLimit RateLimit `json:"rate_limit"`
}

// RateLimit 限流配置
type RateLimit struct {
	RequestsPerUnit int    `json:"requests_per_unit"`
	Unit            string `json:"unit"` // SECOND, MINUTE, HOUR, DAY
}

// SecurityRuleResponse 安全规则响应
type SecurityRuleResponse struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Namespace   string           `json:"namespace"`
	Cluster     string           `json:"cluster"`
	Type        string           `json:"type"`
	Rules       SecurityRuleSpec `json:"rules"`
	Enabled     bool             `json:"enabled"`
	Description string           `json:"description"`
	AuditStatus string           `json:"auditStatus"` // pending, approved, rejected, applied
	CreatedAt   string           `json:"createdAt"`
	UpdatedAt   string           `json:"updatedAt"`
	Creator     string           `json:"creator"`
	Modifier    string           `json:"modifier"`
}

// AuthorizationPolicyRequest AuthorizationPolicy请求
type AuthorizationPolicyRequest struct {
	request.PageRequest
	Cluster   string `json:"cluster" binding:"required"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// CreateAuthorizationPolicyRequest 创建AuthorizationPolicy请求
type CreateAuthorizationPolicyRequest struct {
	Name      string                  `json:"name" binding:"required"`
	Namespace string                  `json:"namespace" binding:"required"`
	Cluster   string                  `json:"cluster" binding:"required"`
	Spec      AuthorizationPolicySpec `json:"spec" binding:"required"`
}

// AuthorizationPolicySpec AuthorizationPolicy规格
type AuthorizationPolicySpec struct {
	Selector *Selector           `json:"selector,omitempty"`
	Rules    []AuthorizationRule `json:"rules,omitempty"`
	Action   string              `json:"action,omitempty"` // ALLOW, DENY
}

// Selector 选择器
type Selector struct {
	MatchLabels map[string]string `json:"matchLabels,omitempty"`
}

// AuthorizationRule 授权规则
type AuthorizationRule struct {
	From []From      `json:"from,omitempty"`
	To   []To        `json:"to,omitempty"`
	When []Condition `json:"when,omitempty"`
}

// From 来源配置
type From struct {
	Source *Source `json:"source,omitempty"`
}

// Source 来源
type Source struct {
	Principals        []string `json:"principals,omitempty"`
	RequestPrincipals []string `json:"requestPrincipals,omitempty"`
	Namespaces        []string `json:"namespaces,omitempty"`
	IPBlocks          []string `json:"ipBlocks,omitempty"`
}

// To 目标配置
type To struct {
	Operation *Operation `json:"operation,omitempty"`
}

// Operation 操作
type Operation struct {
	Methods []string `json:"methods,omitempty"`
	Paths   []string `json:"paths,omitempty"`
}

// Condition 条件
type Condition struct {
	Key    string   `json:"key"`
	Values []string `json:"values"`
}

// AuthorizationPolicyResponse AuthorizationPolicy响应
type AuthorizationPolicyResponse struct {
	Name      string                  `json:"name"`
	Namespace string                  `json:"namespace"`
	Cluster   string                  `json:"cluster"`
	Spec      AuthorizationPolicySpec `json:"spec"`
	CreatedAt string                  `json:"createdAt"`
	UpdatedAt string                  `json:"updatedAt"`
}

// SecurityTemplateRequest 安全模板请求
type SecurityTemplateRequest struct {
	Type string `json:"type"` // blacklist, whitelist
}

// SecurityTemplateResponse 安全模板响应
type SecurityTemplateResponse struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Type        string           `json:"type"`
	Description string           `json:"description"`
	Template    SecurityRuleSpec `json:"template"`
}

// GenerateYamlRequest 生成YAML请求
type GenerateYamlRequest struct {
	Rule SecurityRuleResponse `json:"rule" binding:"required"`
}

// GenerateYamlResponse 生成YAML响应
type GenerateYamlResponse struct {
	Yaml string `json:"yaml"`
}

// ApplyRuleRequest 应用规则请求
type ApplyRuleRequest struct {
	Rule SecurityRuleResponse `json:"rule" binding:"required"`
}

// ApplyRuleToClusterRequest 应用规则到集群请求
type ApplyRuleToClusterRequest struct {
	Rule SecurityRuleResponse `json:"rule" binding:"required"`
	Yaml string               `json:"yaml" binding:"required"`
}

// SecurityRuleHistoryRequest 安全规则历史请求
type SecurityRuleHistoryRequest struct {
	request.PageRequest
	RuleID string `json:"ruleId" binding:"required"`
}

// SecurityRuleHistoryResponse 安全规则历史响应
type SecurityRuleHistoryResponse struct {
	ID        string    `json:"id"`
	RuleID    string    `json:"ruleId"`
	Version   int       `json:"version"`
	Action    string    `json:"action"` // create, update, delete, apply
	Changes   string    `json:"changes"`
	Operator  string    `json:"operator"`
	CreatedAt time.Time `json:"createdAt"`
}

// DeleteSecurityRuleRequest 删除安全规则请求
type DeleteSecurityRuleRequest struct {
	Cluster   string `json:"cluster" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	ID        string `json:"id" binding:"required"`
}
