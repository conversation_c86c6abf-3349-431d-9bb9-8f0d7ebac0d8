package security

import (
	"context"
	"fmt"
	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/response"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
	istio "istio.io/client-go/pkg/clientset/versioned"
	"k8s.io/client-go/kubernetes"
)

type SecurityService struct {
	*trace.Service
	ruleRepo     *repos.SecurityRuleRepo
	historyRepo  *repos.SecurityRuleHistoryRepo
	templateRepo *repos.SecurityTemplateRepo
}

func NewSecurityService(ts *trace.Service, dbEngine *gorm.DB) *SecurityService {
	svc := &SecurityService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.ruleRepo = repos.NewSecurityRuleRepo(dbEngine.WithContext(ctx))
	svc.historyRepo = repos.NewSecurityRuleHistoryRepo(dbEngine.WithContext(ctx))
	svc.templateRepo = repos.NewSecurityTemplateRepo(dbEngine.WithContext(ctx))
	return svc
}

// GetClusterClient 获取集群客户端
func (s *SecurityService) GetClusterClient(clusterID string) (kubernetes.Interface, istio.Interface, error) {
	clusterMgr := cluster.GetClusterManager()
	return clusterMgr.GetClusterClient(clusterID)
}

// ListBlacklistRules 获取黑名单规则列表
func (s *SecurityService) ListBlacklistRules(req SecurityRuleRequest) (*response.PageModel[*SecurityRuleResponse], error) {
	return s.listSecurityRules(req, "blacklist")
}

// ListWhitelistRules 获取白名单规则列表
func (s *SecurityService) ListWhitelistRules(req SecurityRuleRequest) (*response.PageModel[*SecurityRuleResponse], error) {
	return s.listSecurityRules(req, "whitelist")
}

// listSecurityRules 通用的规则列表查询方法
func (s *SecurityService) listSecurityRules(req SecurityRuleRequest, ruleType string) (*response.PageModel[*SecurityRuleResponse], error) {
	var rules []*models.SecurityRule
	var total int64
	var err error

	// 根据不同的查询条件调用不同的方法
	if req.Name != "" {
		// 按名称搜索
		rules, total, err = s.ruleRepo.SearchByName(req.Cluster, req.Name, req.Page, req.PageSize)
	} else if req.Type != "" {
		// 按类型查询
		rules, total, err = s.ruleRepo.GetByTypeAndCluster(req.Type, req.Cluster, req.Page, req.PageSize)
	} else {
		// 按集群和命名空间查询
		rules, total, err = s.ruleRepo.GetByClusterAndNamespace(req.Cluster, req.Namespace, req.Page, req.PageSize)
	}

	if err != nil {
		return nil, err
	}

	// 过滤指定类型的规则（如果ruleType为空，则返回所有规则）
	var filteredRules []*models.SecurityRule
	for _, rule := range rules {
		if ruleType == "" || rule.Type == ruleType {
			filteredRules = append(filteredRules, rule)
		}
	}

	// 转换为响应格式
	var list []*SecurityRuleResponse
	for _, rule := range filteredRules {
		resp, err := s.convertToResponse(rule)
		if err != nil {
			return nil, err
		}
		list = append(list, resp)
	}

	return &response.PageModel[*SecurityRuleResponse]{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// CreateBlacklistRule 创建黑名单规则
func (s *SecurityService) CreateBlacklistRule(req CreateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	req.Type = RuleTypeBlacklist
	return s.createSecurityRule(req, operator)
}

// CreateWhitelistRule 创建白名单规则
func (s *SecurityService) CreateWhitelistRule(req CreateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	req.Type = RuleTypeWhitelist
	return s.createSecurityRule(req, operator)
}

// createSecurityRule 通用的规则创建方法
func (s *SecurityService) createSecurityRule(req CreateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	// 检查规则名称是否已存在
	existingRule, err := s.ruleRepo.GetByClusterNamespaceAndName(req.Cluster, req.Namespace, req.Name)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if existingRule != nil {
		return nil, fmt.Errorf("安全规则 '%s' 在命名空间 '%s' 中已存在", req.Name, req.Namespace)
	}

	// 创建规则模型
	rule := &models.SecurityRule{
		Name:        req.Name,
		Namespace:   req.Namespace,
		Cluster:     req.Cluster,
		Type:        req.Type,
		Enabled:     req.Enabled,
		Description: req.Description,
		AuditStatus: "pending",
	}
	rule.Create(operator)

	// 设置规则配置
	rulesMap := map[string]interface{}{
		"ipBlocks":   req.Rules.IPBlocks,
		"principals": req.Rules.Principals,
		"namespaces": req.Rules.Namespaces,
		"methods":    req.Rules.Methods,
		"paths":      req.Rules.Paths,
		"headers":    req.Rules.Headers,
	}

	if req.Rules.RateLimit != nil {
		rulesMap["rateLimit"] = req.Rules.RateLimit
	}

	if err := rule.SetRules(rulesMap); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.ruleRepo.Create(rule); err != nil {
		return nil, err
	}

	// 创建历史记录
	if err := s.createHistory(rule.UUID, 1, "create", "创建安全规则", operator, rulesMap); err != nil {
		s.Logger.Error("创建历史记录失败: " + err.Error())
	}

	return s.convertToResponse(rule)
}

// UpdateBlacklistRule 更新黑名单规则
func (s *SecurityService) UpdateBlacklistRule(req UpdateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	req.Type = RuleTypeBlacklist
	return s.updateSecurityRule(req, operator)
}

// UpdateWhitelistRule 更新白名单规则
func (s *SecurityService) UpdateWhitelistRule(req UpdateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	req.Type = RuleTypeWhitelist
	return s.updateSecurityRule(req, operator)
}

// updateSecurityRule 通用的规则更新方法
func (s *SecurityService) updateSecurityRule(req UpdateSecurityRuleRequest, operator string) (*SecurityRuleResponse, error) {
	// 获取现有规则
	rule, err := s.ruleRepo.GetFirst(models.SecurityRule{BaseModel: models.BaseModel{UUID: req.UUID}})
	if err != nil {
		return nil, err
	}
	if rule == nil {
		return nil, gorm.ErrRecordNotFound
	}

	// 检查版本冲突
	if err = models.CheckVersionConflict(req.GmtModified, rule.GmtModified); err != nil {
		return nil, err
	}

	// 获取原始规则配置用于记录变更
	oldRules, _ := rule.GetRules()

	// 更新规则字段
	rule.Name = req.Name
	rule.Namespace = req.Namespace
	rule.Cluster = req.Cluster
	rule.Type = req.Type
	rule.Enabled = req.Enabled
	rule.Description = req.Description
	rule.Update(operator)

	// 设置新的规则配置
	rulesMap := map[string]interface{}{
		"ipBlocks":   req.Rules.IPBlocks,
		"principals": req.Rules.Principals,
		"namespaces": req.Rules.Namespaces,
		"methods":    req.Rules.Methods,
		"paths":      req.Rules.Paths,
		"headers":    req.Rules.Headers,
	}

	if req.Rules.RateLimit != nil {
		rulesMap["rateLimit"] = req.Rules.RateLimit
	}

	if err := rule.SetRules(rulesMap); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.ruleRepo.Save(rule); err != nil {
		return nil, err
	}

	// 获取最新版本号并创建历史记录
	version, _ := s.historyRepo.GetLatestVersionByRuleID(rule.UUID)
	changes := s.generateChanges(oldRules, rulesMap)
	if err := s.createHistory(rule.UUID, version+1, "update", changes, operator, rulesMap); err != nil {
		s.Logger.Error("创建历史记录失败: " + err.Error())
	}

	return s.convertToResponse(rule)
}

// DeleteBlacklistRule 删除黑名单规则
func (s *SecurityService) DeleteBlacklistRule(req DeleteSecurityRuleRequest, operator string) error {
	return s.deleteSecurityRule(req, operator)
}

// DeleteWhitelistRule 删除白名单规则
func (s *SecurityService) DeleteWhitelistRule(req DeleteSecurityRuleRequest, operator string) error {
	return s.deleteSecurityRule(req, operator)
}

// deleteSecurityRule 通用的规则删除方法
func (s *SecurityService) deleteSecurityRule(req DeleteSecurityRuleRequest, operator string) error {
	// 获取规则
	rule, err := s.ruleRepo.GetFirst(models.SecurityRule{BaseModel: models.BaseModel{UUID: req.ID}})
	if err != nil {
		return err
	}
	if rule == nil {
		return gorm.ErrRecordNotFound
	}

	// 获取规则配置用于历史记录
	rulesMap, _ := rule.GetRules()

	// 删除规则
	if err := s.ruleRepo.DeleteByID(rule.ID); err != nil {
		return err
	}

	// 获取最新版本号并创建历史记录
	version, _ := s.historyRepo.GetLatestVersionByRuleID(rule.UUID)
	if err := s.createHistory(rule.UUID, version+1, "delete", "删除安全规则", operator, rulesMap); err != nil {
		s.Logger.Error("创建历史记录失败: " + err.Error())
	}

	return nil
}

// GenerateSecurityYaml 生成安全规则的YAML配置
func (s *SecurityService) GenerateSecurityYaml(req GenerateYamlRequest) (*GenerateYamlResponse, error) {
	var yamlContent string
	var err error

	switch req.Rule.Type {
	case RuleTypeBlacklist, RuleTypeWhitelist:
		yamlContent = s.generateAuthorizationPolicyYaml(req.Rule)
	case RuleTypeRateLimit:
		yamlContent, err = s.generateRateLimitYaml(req.Rule)
	default:
		return nil, fmt.Errorf("不支持的规则类型: %s", req.Rule.Type)
	}

	if err != nil {
		return nil, err
	}

	return &GenerateYamlResponse{
		Yaml: yamlContent,
	}, nil
}

// ApplySecurityRuleToCluster 应用安全规则到集群
func (s *SecurityService) ApplySecurityRuleToCluster(req ApplyRuleToClusterRequest, operator string) error {
	// 获取集群客户端
	kubeClient, istioClient, err := s.GetClusterClient(req.Rule.Cluster)
	if err != nil {
		return fmt.Errorf("获取集群客户端失败: %w", err)
	}

	// 这里应该实现将YAML应用到集群的逻辑
	// 由于涉及到复杂的Kubernetes API操作，这里先返回成功
	_ = kubeClient
	_ = istioClient

	// 更新规则的审核状态为已应用
	rule, err := s.ruleRepo.GetFirst(models.SecurityRule{BaseModel: models.BaseModel{UUID: req.Rule.ID}})
	if err != nil {
		return err
	}
	if rule != nil {
		rule.AuditStatus = "applied"
		rule.Update(operator)
		if err := s.ruleRepo.Save(rule); err != nil {
			s.Logger.Error("更新规则状态失败: " + err.Error())
		}

		// 创建历史记录
		rulesMap, _ := rule.GetRules()
		version, _ := s.historyRepo.GetLatestVersionByRuleID(rule.UUID)
		if err := s.createHistory(rule.UUID, version+1, "apply", "应用规则到集群", operator, rulesMap); err != nil {
			s.Logger.Error("创建历史记录失败: " + err.Error())
		}
	}

	return nil
}

// GetSecurityRuleHistory 获取安全规则历史记录
func (s *SecurityService) GetSecurityRuleHistory(req SecurityRuleHistoryRequest) (*response.PageModel[*SecurityRuleHistoryResponse], error) {
	histories, total, err := s.historyRepo.GetByRuleID(req.RuleID, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	var list []*SecurityRuleHistoryResponse
	for _, history := range histories {
		resp := &SecurityRuleHistoryResponse{
			ID:        history.UUID,
			RuleID:    history.RuleID,
			Version:   history.Version,
			Action:    history.Action,
			Changes:   history.Changes,
			Operator:  history.Operator,
			CreatedAt: *history.GmtCreate,
		}
		list = append(list, resp)
	}

	return &response.PageModel[*SecurityRuleHistoryResponse]{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// ListSecurityTemplates 获取安全策略模板列表
func (s *SecurityService) ListSecurityTemplates(req SecurityTemplateRequest) ([]*SecurityTemplateResponse, error) {
	templates, err := s.templateRepo.GetByType(req.Type)
	if err != nil {
		return nil, err
	}

	var list []*SecurityTemplateResponse
	for _, template := range templates {
		templateMap, _ := template.GetTemplate()
		resp := &SecurityTemplateResponse{
			ID:          template.UUID,
			Name:        template.Name,
			Type:        template.Type,
			Description: template.Description,
			Template:    s.convertMapToSecurityRuleSpec(templateMap),
		}
		list = append(list, resp)
	}

	return list, nil
}

// 辅助方法

// convertToResponse 将模型转换为响应格式
func (s *SecurityService) convertToResponse(rule *models.SecurityRule) (*SecurityRuleResponse, error) {
	rulesMap, err := rule.GetRules()
	if err != nil {
		return nil, err
	}

	resp := &SecurityRuleResponse{
		ID:          rule.UUID,
		Name:        rule.Name,
		Namespace:   rule.Namespace,
		Cluster:     rule.Cluster,
		Type:        rule.Type,
		Rules:       s.convertMapToSecurityRuleSpec(rulesMap),
		Enabled:     rule.Enabled,
		Description: rule.Description,
		AuditStatus: rule.AuditStatus,
		CreatedAt:   rule.GmtCreate.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   rule.GmtModified.Format("2006-01-02T15:04:05Z"),
		Creator:     rule.Creator,
		Modifier:    rule.Modifier,
	}

	return resp, nil
}

// convertMapToSecurityRuleSpec 将map转换为SecurityRuleSpec
func (s *SecurityService) convertMapToSecurityRuleSpec(rulesMap map[string]interface{}) SecurityRuleSpec {
	spec := SecurityRuleSpec{}

	s.convertStringArrayFields(rulesMap, &spec)
	s.convertHeadersField(rulesMap, &spec)
	s.convertRateLimitField(rulesMap, &spec)

	return spec
}

// convertStringArrayFields 转换字符串数组字段
func (s *SecurityService) convertStringArrayFields(rulesMap map[string]interface{}, spec *SecurityRuleSpec) {
	stringArrayFields := map[string]*[]string{
		"ipBlocks":   &spec.IPBlocks,
		"principals": &spec.Principals,
		"namespaces": &spec.Namespaces,
		"methods":    &spec.Methods,
		"paths":      &spec.Paths,
	}

	for fieldName, fieldPtr := range stringArrayFields {
		if values, ok := rulesMap[fieldName].([]interface{}); ok {
			for _, value := range values {
				if strValue, ok := value.(string); ok {
					*fieldPtr = append(*fieldPtr, strValue)
				}
			}
		}
	}
}

// convertHeadersField 转换headers字段
func (s *SecurityService) convertHeadersField(rulesMap map[string]interface{}, spec *SecurityRuleSpec) {
	if headers, ok := rulesMap["headers"].(map[string]interface{}); ok {
		spec.Headers = make(map[string][]string)
		for key, values := range headers {
			if valueList, ok := values.([]interface{}); ok {
				var strValues []string
				for _, value := range valueList {
					if valueStr, ok := value.(string); ok {
						strValues = append(strValues, valueStr)
					}
				}
				spec.Headers[key] = strValues
			}
		}
	}
}

// convertRateLimitField 转换rateLimit字段
func (s *SecurityService) convertRateLimitField(rulesMap map[string]interface{}, spec *SecurityRuleSpec) {
	rateLimit, ok := rulesMap["rateLimit"].(map[string]interface{})
	if !ok {
		return
	}

	spec.RateLimit = &RateLimitSpec{}

	if domain, ok := rateLimit["domain"].(string); ok {
		spec.RateLimit.Domain = domain
	}
	if failureModeDeny, ok := rateLimit["failure_mode_deny"].(bool); ok {
		spec.RateLimit.FailureModeDeny = failureModeDeny
	}
	if timeout, ok := rateLimit["timeout"].(float64); ok {
		spec.RateLimit.Timeout = int(timeout)
	}

	s.convertRateLimitDescriptors(rateLimit, spec.RateLimit)
}

// convertRateLimitDescriptors 转换限流描述符
func (s *SecurityService) convertRateLimitDescriptors(rateLimit map[string]interface{}, rateLimitSpec *RateLimitSpec) {
	descriptors, ok := rateLimit["descriptors"].([]interface{})
	if !ok {
		return
	}

	for _, desc := range descriptors {
		descMap, ok := desc.(map[string]interface{})
		if !ok {
			continue
		}

		descriptor := RateLimitDescriptor{}
		if key, ok := descMap["key"].(string); ok {
			descriptor.Key = key
		}
		if value, ok := descMap["value"].(string); ok {
			descriptor.Value = value
		}

		if rateLimitMap, ok := descMap["rate_limit"].(map[string]interface{}); ok {
			if requestsPerUnit, ok := rateLimitMap["requests_per_unit"].(float64); ok {
				descriptor.RateLimit.RequestsPerUnit = int(requestsPerUnit)
			}
			if unit, ok := rateLimitMap["unit"].(string); ok {
				descriptor.RateLimit.Unit = unit
			}
		}

		rateLimitSpec.Descriptors = append(rateLimitSpec.Descriptors, descriptor)
	}
}

// createHistory 创建历史记录
func (s *SecurityService) createHistory(ruleID string, version int, action, changes, operator string, ruleData map[string]interface{}) error {
	history := &models.SecurityRuleHistory{
		RuleID:   ruleID,
		Version:  version,
		Action:   action,
		Changes:  changes,
		Operator: operator,
	}
	history.Create(operator)

	if err := history.SetRuleData(ruleData); err != nil {
		return err
	}

	return s.historyRepo.Create(history)
}

// generateChanges 生成变更描述
func (s *SecurityService) generateChanges(oldRules, newRules map[string]interface{}) string {
	// 这里应该实现详细的变更对比逻辑
	// 简化实现，返回基本的变更信息
	return "更新安全规则配置"
}

// generateAuthorizationPolicyYaml 生成AuthorizationPolicy的YAML
func (s *SecurityService) generateAuthorizationPolicyYaml(rule SecurityRuleResponse) string {
	action := "DENY"
	if rule.Type == RuleTypeWhitelist {
		action = "ALLOW"
	}

	yaml := fmt.Sprintf(`apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: %s
  namespace: %s
spec:
  action: %s`, rule.Name, rule.Namespace, action)

	// 添加规则配置
	if len(rule.Rules.IPBlocks) > 0 || len(rule.Rules.Principals) > 0 || len(rule.Rules.Methods) > 0 || len(rule.Rules.Paths) > 0 {
		yaml += "\n  rules:\n  - "

		// 添加来源配置
		if len(rule.Rules.IPBlocks) > 0 || len(rule.Rules.Principals) > 0 {
			yaml += "\n    from:\n    - source:"
			if len(rule.Rules.IPBlocks) > 0 {
				yaml += "\n        ipBlocks:"
				for _, ip := range rule.Rules.IPBlocks {
					yaml += fmt.Sprintf("\n        - %s", ip)
				}
			}
			if len(rule.Rules.Principals) > 0 {
				yaml += "\n        principals:"
				for _, principal := range rule.Rules.Principals {
					yaml += fmt.Sprintf("\n        - %s", principal)
				}
			}
		}

		// 添加目标配置
		if len(rule.Rules.Methods) > 0 || len(rule.Rules.Paths) > 0 {
			yaml += "\n    to:\n    - operation:"
			if len(rule.Rules.Methods) > 0 {
				yaml += "\n        methods:"
				for _, method := range rule.Rules.Methods {
					yaml += fmt.Sprintf("\n        - %s", method)
				}
			}
			if len(rule.Rules.Paths) > 0 {
				yaml += "\n        paths:"
				for _, path := range rule.Rules.Paths {
					yaml += fmt.Sprintf("\n        - %s", path)
				}
			}
		}
	}

	return yaml
}

// generateRateLimitYaml 生成限流的YAML配置
func (s *SecurityService) generateRateLimitYaml(rule SecurityRuleResponse) (string, error) {
	if rule.Rules.RateLimit == nil {
		return "", fmt.Errorf("限流规则配置不能为空")
	}

	yaml := fmt.Sprintf(`apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: %s-ratelimit
  namespace: %s
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.local_ratelimit
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
          value:
            stat_prefix: local_rate_limiter
            token_bucket:
              max_tokens: %d
              tokens_per_fill: %d
              fill_interval: 60s
            filter_enabled:
              runtime_key: local_rate_limit_enabled
              default_value:
                numerator: 100
                denominator: HUNDRED
            filter_enforced:
              runtime_key: local_rate_limit_enforced
              default_value:
                numerator: 100
                denominator: HUNDRED`,
		rule.Name, rule.Namespace,
		rule.Rules.RateLimit.Descriptors[0].RateLimit.RequestsPerUnit,
		rule.Rules.RateLimit.Descriptors[0].RateLimit.RequestsPerUnit)

	return yaml, nil
}
