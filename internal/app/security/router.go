package security

import (
	"pilot-api/internal/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterRouter(r *gin.Engine) {
	securityAPI := r.Group("/api/v1/security")
	{
		securityAPI.Use(middleware.AuthUToken())
		// 统一的安全规则接口
		securityAPI.POST("/rules", ListSecurityRules)
		securityAPI.POST("/rule/create", CreateSecurityRule)
		securityAPI.PUT("/rule/update", UpdateSecurityRule)
		securityAPI.DELETE("/rule/delete", DeleteSecurityRule)
		// 安全策略模板
		securityAPI.POST("/templates", ListSecurityTemplates)
		// 历史记录
		securityAPI.GET("/rule-history", GetSecurityRuleHistory)
	}
}
