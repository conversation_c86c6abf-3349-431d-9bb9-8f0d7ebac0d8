package dns

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/pkg/models"
)

// ============ Domain Provider Requests ============

// ListDomainProvidersRequest represents the request for listing domain providers
type ListDomainProvidersRequest struct {
	request.PageRequest
	Name   string                      `json:"name"`
	Type   models.DomainProviderType   `json:"type"`
	Status models.DomainProviderStatus `json:"status"`
}

// ListDomainsRequest represents the request for listing domains
type ListDomainsRequest struct {
	ProviderUUID string `json:"providerUuid" binding:"required"`
}

// AddDomainProviderRequest represents the request for adding a domain provider
type AddDomainProviderRequest struct {
	Name   string                      `json:"name" binding:"required"`
	Type   models.DomainProviderType   `json:"type" binding:"required"`
	Config string                      `json:"config" binding:"required"` // JSON string
	Status models.DomainProviderStatus `json:"status"`
}

// UpdateDomainProviderRequest represents the request for updating a domain provider
type UpdateDomainProviderRequest struct {
	request.UpdateRequest
	AddDomainProviderRequest
}

// InfoDomainProviderRequest represents the request for getting domain provider info
type InfoDomainProviderRequest struct {
	UUID string `json:"uuid" binding:"required"`
}

// DeleteDomainProviderRequest represents the request for deleting domain providers
type DeleteDomainProviderRequest struct {
	UUIDs []string `json:"uuids" binding:"required"`
}

// TestDomainProviderRequest represents the request for testing domain provider connection
type TestDomainProviderRequest struct {
	UUID string `json:"uuid" binding:"required"`
}

// ============ Domain Record Requests ============

// ListDomainRecordsRequest represents the request for listing domain records
type ListDomainRecordsRequest struct {
	request.PageRequest
	Domain       string                    `json:"domain"`
	ProviderUUID string                    `json:"providerUuid"`
	Subdomain    string                    `json:"subdomain"`
	RecordType   models.DomainRecordType   `json:"recordType"`
	Status       models.DomainRecordStatus `json:"status"`
	Value        string                    `json:"value"`
}

// AddDomainRecordRequest represents the request for adding a domain record
type AddDomainRecordRequest struct {
	Domain       string                  `json:"domain" binding:"required"`       // Domain name string
	ProviderUUID string                  `json:"providerUuid" binding:"required"` // Provider UUID
	Subdomain    string                  `json:"subdomain"`
	RecordType   models.DomainRecordType `json:"recordType" binding:"required"`
	Value        string                  `json:"value" binding:"required"`
	TTL          int                     `json:"ttl"`
	Priority     int                     `json:"priority"`
	Proxied      *bool                   `json:"proxied"` // Cloudflare DNS Proxy状态
	Comment      string                  `json:"comment"`
	Reason       string                  `json:"reason"` // Approval reason
}

// PrevRecord represents the previous record information for update operations
type PrevRecord struct {
	Subdomain  string                  `json:"subdomain"`
	RecordType models.DomainRecordType `json:"recordType"`
	Value      string                  `json:"value"`
	TTL        int                     `json:"ttl"`
	Priority   int                     `json:"priority"`
	Proxied    *bool                   `json:"proxied"` // Cloudflare DNS Proxy状态
}

// UpdateDomainRecordRequest represents the request for updating a domain record
type UpdateDomainRecordRequest struct {
	UUID string `json:"uuid"` // 可选，用于编辑pending记录
	AddDomainRecordRequest
	PrevRecord *PrevRecord `json:"prevRecord"` // 原记录信息，用于精确定位要更新的记录
}

// InfoDomainRecordRequest represents the request for getting domain record info
type InfoDomainRecordRequest struct {
	UUID string `json:"uuid" binding:"required"`
}

// DeleteDomainRecordRequest represents the request for deleting domain records
type DeleteDomainRecordRequest struct {
	Domain       string                  `json:"domain" binding:"required"`       // Domain name string
	ProviderUUID string                  `json:"providerUuid" binding:"required"` // Provider UUID
	Subdomain    string                  `json:"subdomain"`
	RecordType   models.DomainRecordType `json:"recordType" binding:"required"`
	Comment      string                  `json:"comment"`
	Reason       string                  `json:"reason" binding:"required"` // Deletion reason
	Value        string                  `json:"value" binding:"required"`
}

// SyncDomainRecordsRequest represents the request for syncing domain records
type SyncDomainRecordsRequest struct {
	ProviderID int `json:"providerId" binding:"required"`
}

// ValidateDomainRecordRequest represents the request for validating domain resolution
type ValidateDomainRecordRequest struct {
	Domain     string                  `json:"domain" binding:"required"`
	Subdomain  string                  `json:"subdomain"`
	RecordType models.DomainRecordType `json:"recordType" binding:"required"`
	Value      string                  `json:"value" binding:"required"`
}

// ============ Domain Approval Requests ============

// ListDomainApprovalsRequest represents the request for listing domain approvals
type ListDomainApprovalsRequest struct {
	request.PageRequest
	Status    models.ApprovalStatus `json:"status"`
	Applicant string                `json:"applicant"`
	Approver  string                `json:"approver"`
}

// ReviewDomainRecordRequest represents the request for reviewing (approve/reject) domain record
type ReviewDomainRecordRequest struct {
	UUID    string                `json:"uuid" binding:"required"`
	Action  models.ApprovalStatus `json:"action" binding:"required"` // "approve" or "reject"
	Comment string                `json:"comment" binding:"required"`
}

// ============ Domain History Requests ============

// ListDomainHistoryRequest represents the request for listing domain record history
type ListDomainHistoryRequest struct {
	request.PageRequest
	RecordUUID string `json:"recordUuid" binding:"required"`
}

// RollbackDomainRecordRequest represents the request for rolling back domain record
type RollbackDomainRecordRequest struct {
	RecordUUID string `json:"recordUuid" binding:"required"`
	Version    int    `json:"version" binding:"required"`
	Reason     string `json:"reason" binding:"required"`
}

// ============ Response Structures ============

// DomainProviderInfoResponse represents the response for domain provider info
type DomainProviderInfoResponse struct {
	UUID      string                      `json:"uuid"`
	Name      string                      `json:"name"`
	Type      models.DomainProviderType   `json:"type"`
	Status    models.DomainProviderStatus `json:"status"`
	Config    string                      `json:"config"` // Masked sensitive info
	Creator   string                      `json:"creator"`
	Modifier  string                      `json:"modifier"`
	CreatedAt models.Time                 `json:"createdAt"`
	UpdatedAt models.Time                 `json:"updatedAt"`
}

// DomainProviderListResponse represents the response for domain provider list
type DomainProviderListResponse struct {
	UUID      string                      `json:"uuid"`
	Name      string                      `json:"name"`
	Type      models.DomainProviderType   `json:"type"`
	Status    models.DomainProviderStatus `json:"status"`
	Creator   string                      `json:"creator"`
	CreatedAt models.Time                 `json:"createdAt"`
}

// DomainRecordInfoResponse represents the response for domain record info
type DomainRecordInfoResponse struct {
	UUID             string                    `json:"uuid"`
	Domain           string                    `json:"domain"`
	DomainUUID       string                    `json:"domainUUID"`
	FullDomain       string                    `json:"fullDomain"`
	Subdomain        string                    `json:"subdomain"`
	RecordType       models.DomainRecordType   `json:"recordType"`
	Value            string                    `json:"value"`
	TTL              int                       `json:"ttl"`
	Priority         int                       `json:"priority"`
	Proxied          *bool                     `json:"proxied"` // Cloudflare DNS Proxy状态
	ProviderID       int                       `json:"providerId"`
	ProviderName     string                    `json:"providerName"`
	ProviderRecordID string                    `json:"providerRecordId"`
	Status           models.DomainRecordStatus `json:"status"`
	Comment          string                    `json:"comment"`
	Version          int                       `json:"version"`
	Creator          string                    `json:"creator"`
	Modifier         string                    `json:"modifier"`
	CreatedAt        models.Time               `json:"createdAt"`
	UpdatedAt        models.Time               `json:"updatedAt"`
	Action           models.OperationType      `json:"action"`
}

// DomainRecordListResponse represents the response for domain record list
type DomainRecordListResponse struct {
	UUID         string                    `json:"uuid"`
	FullDomain   string                    `json:"fullDomain"` // domain + subdomain
	Domain       string                    `json:"domain"`
	Subdomain    string                    `json:"subdomain"`
	RecordType   models.DomainRecordType   `json:"recordType"`
	Value        string                    `json:"value"`
	TTL          int                       `json:"ttl"`
	Priority     int                       `json:"priority"`
	Proxied      *bool                     `json:"proxied"` // Cloudflare DNS Proxy状态
	ProviderName string                    `json:"providerName"`
	Status       models.DomainRecordStatus `json:"status"`
	Creator      string                    `json:"creator"`
	CreatedAt    models.Time               `json:"createdAt"`
	Action       models.OperationType      `json:"action"`
}

// DomainApprovalInfoResponse represents the response for domain approval info
type DomainApprovalInfoResponse struct {
	UUID           string                `json:"uuid"`
	RecordUUID     string                `json:"recordUuid"`
	Operation      models.OperationType  `json:"operation"`
	Status         models.ApprovalStatus `json:"status"`
	Applicant      string                `json:"applicant"`
	Approver       string                `json:"approver"`
	ApplyReason    string                `json:"applyReason"`
	ApproveComment string                `json:"approveComment"`
	BeforeValue    string                `json:"beforeValue"`
	AfterValue     string                `json:"afterValue"`
	CreatedAt      models.Time           `json:"createdAt"`
	ApprovedAt     *models.Time          `json:"approvedAt"`
}

// DomainHistoryResponse represents the response for domain record history
type DomainHistoryResponse struct {
	ID         int                     `json:"id"`
	Domain     string                  `json:"domain"`
	Subdomain  string                  `json:"subdomain"`
	RecordType models.DomainRecordType `json:"recordType"`
	Value      string                  `json:"value"`
	TTL        int                     `json:"ttl"`
	Priority   int                     `json:"priority"`
	Comment    string                  `json:"comment"`
	Version    int                     `json:"version"`
	Operation  models.OperationType    `json:"operation"`
	Operator   string                  `json:"operator"`
	CreatedAt  models.Time             `json:"createdAt"`
}

// TestDomainProviderResponse represents the response for testing domain provider
type TestDomainProviderResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ValidateDomainRecordResponse represents the response for validating domain record
type ValidateDomainRecordResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	ResolvedValue string `json:"resolvedValue,omitempty"`
	ResponseTime  int    `json:"responseTime,omitempty"` // milliseconds
}

// SyncDomainRecordsResponse represents the response for syncing domain records
type SyncDomainRecordsResponse struct {
	Success      bool   `json:"success"`
	SyncedCount  int    `json:"syncedCount"`
	CreatedCount int    `json:"createdCount"`
	UpdatedCount int    `json:"updatedCount"`
	FailedCount  int    `json:"failedCount"`
	Message      string `json:"message"`
}
