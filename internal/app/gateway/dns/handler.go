package dns

import (
	"pilot-api/internal/pkg/middleware"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"

	"github.com/gin-gonic/gin"
)

// InfoDomainProvider gets domain provider info by UUID
func InfoDomainProvider(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req InfoDomainProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	data, err := svc.InfoDomainProvider(req.UUID)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, data)
}

// ListDomainProviders gets domain provider list
func ListDomainProviders(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListDomainProvidersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	data, err := svc.ListDomainProviders(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, data)
}

// ListDomains gets domain list from cloud provider
func ListDomains(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListDomainsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	data, err := svc.ListDomains(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, data)
}

// AddDomainRecord adds a new domain record
func AddDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	err := svc.AddDomainRecord(req, c.GetString(util.Email))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// ListDomainRecords lists domain records with pagination and filters
func ListDomainRecords(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListDomainRecordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	data, err := svc.ListDomainRecords(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, data)
}

// InfoDomainRecord gets domain record info by UUID
func InfoDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req InfoDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	data, err := svc.InfoDomainRecord(req.UUID)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, data)
}

// UpdateDomainRecord updates a domain record
func UpdateDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	err := svc.UpdateDomainRecord(req, c.GetString(util.Email))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// DeleteDomainRecord deletes a domain record
func DeleteDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req DeleteDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	err := svc.DeleteDomainRecord(req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// ReviewDomainRecord reviews (approve/reject) a domain record
func ReviewDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ReviewDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	err := svc.ReviewDomainRecord(req, c.GetString(util.UserName),
		middleware.HasRole(c, middleware.DnsAdmin, middleware.PilotAdmin)) // must admin
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// ValidateDomainRecord validates domain record resolution
func ValidateDomainRecord(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ValidateDomainRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewDomainService(ts, mysql.GetDB())
	result, err := svc.ValidateDomainRecord(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, result)
}
