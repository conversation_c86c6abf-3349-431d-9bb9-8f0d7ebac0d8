package dns

import (
	"pilot-api/internal/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterRouter registers domain management routes
func RegisterRouter(r *gin.Engine) {
	domainAPI := r.Group("/api/v1/gateway/domain")
	{
		domainAPI.Use(middleware.AuthIsAdmin(), middleware.AuthUToken())

		// Domain Provider routes
		providerAPI := domainAPI.Group("/provider")
		{
			providerAPI.POST("/list", ListDomainProviders)
		}

		// Domain routes
		domainAPI.POST("/list", ListDomains)

		// Domain Record routes
		recordAPI := domainAPI.Group("/record")
		{
			recordAPI.POST("/add", AddDomainRecord)
			recordAPI.POST("/list", ListDomainRecords)
			recordAPI.POST("/info", InfoDomainRecord)
			recordAPI.PUT("/update", UpdateDomainRecord)
			recordAPI.DELETE("/delete", DeleteDomainRecord)
			recordAPI.POST("/validate", ValidateDomainRecord)
			recordAPI.POST("/review", ReviewDomainRecord)
		}
	}
}
