package dns

import (
	"context"
	"errors"
	"fmt"
	"net"
	"pilot-api/config"
	"pilot-api/internal/pkg/dns"
	"pilot-api/internal/pkg/notification"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

const (
	ValidateSuccess = "DNS解析成功，记录值匹配"
	ActionCreate    = "创建"
	ActionUpdate    = "更新"
	ActionDelete    = "删除"
)

// DomainService 重构后的域名服务
type DomainService struct {
	*trace.Service
	providerRepo    *repos.DomainProviderRepo
	recordRepo      *repos.DomainRecordRepo
	dnsFactory      *dns.ProviderFactory
	notificationSvc *notification.NotificationService
}

// NewDomainService 创建新的域名服务实例
func NewDomainService(ts *trace.Service, dbEngine *gorm.DB) *DomainService {
	svc := &DomainService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	db := dbEngine.WithContext(ctx)

	svc.providerRepo = repos.NewDomainProviderRepo(db)
	svc.recordRepo = repos.NewDomainRecordRepo(db)
	svc.dnsFactory = dns.NewProviderFactory()

	// 初始化通知服务
	nc := config.Items().Notify
	svc.notificationSvc = notification.
		NewNotificationService(nc.AppID, nc.AppSecret, nc.GatewayChatID)

	return svc
}

// InfoDomainProvider gets domain provider info by UUID
func (svc *DomainService) InfoDomainProvider(uuid string) (*DomainProviderInfoResponse, error) {
	provider, err := svc.providerRepo.GetByUUID(uuid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("provider not found")
		}
		log.ErrorE("Failed to get domain provider", err)
		return nil, fmt.Errorf("failed to get provider: %v", err)
	}

	var resp DomainProviderInfoResponse
	err = copier.Copy(&resp, provider)
	if err != nil {
		log.ErrorE("Failed to copy provider data", err)
		return nil, fmt.Errorf("failed to process provider data: %v", err)
	}

	// Mask sensitive config information
	resp.Config = util.Hide

	return &resp, nil
}

// ListDomainProviders 获取服务商列表
func (svc *DomainService) ListDomainProviders(req ListDomainProvidersRequest) (*response.PageModel[DomainProviderInfoResponse], error) {
	page := 1
	pageSize := 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.PageSize > 0 {
		pageSize = req.PageSize
	}

	providers, err := svc.providerRepo.GetPageWithFilter(
		page, pageSize, req.Name, req.Type, req.Status,
	)
	if err != nil {
		log.ErrorE("Failed to get domain providers", err)
		return nil, fmt.Errorf("failed to get providers: %v", err)
	}

	// Convert to response format
	var responseList []DomainProviderInfoResponse
	for _, provider := range providers.List {
		var resp DomainProviderInfoResponse
		err = copier.Copy(&resp, &provider)
		if err != nil {
			log.ErrorE("Failed to copy provider data", err)
			continue
		}
		// Mask sensitive config information
		resp.Config = util.Hide
		responseList = append(responseList, resp)
	}

	return &response.PageModel[DomainProviderInfoResponse]{
		List:     responseList,
		Total:    providers.Total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// ListDomains 从云平台获取域名列表
func (svc *DomainService) ListDomains(req ListDomainsRequest) ([]dns.DomainInfo, error) {
	// 获取服务商
	provider, err := svc.providerRepo.GetByUUID(req.ProviderUUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("provider not found")
		}
		log.ErrorE("Failed to get domain provider", err)
		return nil, fmt.Errorf("failed to get provider: %v", err)
	}

	if provider.Status != models.ProviderStatusActive {
		return nil, fmt.Errorf("provider is not active")
	}

	// 创建DNS提供者实例
	dnsProvider, err := svc.dnsFactory.CreateProviderFromModel(provider)
	if err != nil {
		log.ErrorE("Failed to create DNS provider instance", err)
		return nil, fmt.Errorf("failed to create provider instance: %v", err)
	}

	// 从云平台获取域名列表
	ctx := svc.AttachTrace(context.Background())
	domains, err := dnsProvider.ListDomains(ctx)
	if err != nil {
		log.ErrorE("Failed to list domains from provider", err)
		return nil, fmt.Errorf("failed to list domains: %v", err)
	}

	return domains, nil
}

// AddDomainRecord 添加域名记录
func (svc *DomainService) AddDomainRecord(req AddDomainRecordRequest, ops string) error {
	// 验证服务商
	provider, err := svc.providerRepo.GetByUUID(req.ProviderUUID)
	if err != nil {
		return err
	}
	// 验证服务商状态
	if provider.Status != models.ProviderStatusActive {
		return fmt.Errorf("provider is not active")
	}
	// 创建变更记录
	record := &models.DomainRecord{
		Domain:       req.Domain,
		ProviderUUID: req.ProviderUUID,
		Subdomain:    req.Subdomain,
		RecordType:   req.RecordType,
		Value:        req.Value,
		TTL:          req.TTL,
		Priority:     req.Priority,
		Proxied:      req.Proxied,
		Comment:      req.Comment,
		Reason:       req.Reason,
		Status:       models.RecordStatusPending,
		Action:       models.OperationTypeCreate,
	}

	// 设置默认TTL
	if record.TTL == 0 {
		record.TTL = 600
	}

	record.Create(ops)

	if err = svc.recordRepo.Create(record); err != nil {
		log.ErrorE("Failed to create domain record", err)
		return fmt.Errorf("failed to create record: %v", err)
	}

	// 发送提交变更通知到群
	approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s",
		config.Items().Notify.FrontendHost, config.Items().Notify.DomainRecordRoute, record.UUID)
	if err = svc.notificationSvc.SendDomainRecordSubmitNotification(req.Domain, req.Subdomain,
		string(req.RecordType), req.Value, "create", ops, approvalURL, req.Reason,
	); err != nil {
		log.ErrorE("发送DNS记录新增变更通知失败", err)
	}

	log.Printf("Domain record create request created for approval: %s.%s %s",
		req.Subdomain, req.Domain, req.RecordType)
	return nil
}

// InfoDomainRecord gets domain record info by UUID
func (svc *DomainService) InfoDomainRecord(uuid string) (*DomainRecordInfoResponse, error) {
	// 直接从数据库查询记录信息
	record, err := svc.recordRepo.GetByUUID(uuid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("record not found")
		}
		log.ErrorE("Failed to get domain record", err)
		return nil, fmt.Errorf("failed to get record: %v", err)
	}

	var resp DomainRecordInfoResponse
	err = copier.Copy(&resp, record)
	if err != nil {
		log.ErrorE("Failed to copy record data", err)
		return nil, fmt.Errorf("failed to process record data: %v", err)
	}

	// 设置域名信息 - 现在直接从record中获取
	resp.Domain = record.Domain
	if record.Subdomain != "" {
		resp.FullDomain = record.Subdomain + "." + record.Domain
	} else {
		resp.FullDomain = record.Domain
	}

	return &resp, nil
}

// ListDomainRecords 获取域名记录列表
func (svc *DomainService) ListDomainRecords(req ListDomainRecordsRequest) (*response.PageModel[DomainRecordListResponse], error) {
	// 如果状态是online，从云平台查询；否则从数据库查询
	if req.Status == models.RecordStatusActive {
		return svc.listRecordsFromCloud(req)
	}
	return svc.listRecordsFromDatabase(req)
}

// listRecordsFromCloud 从云平台查询记录
func (svc *DomainService) listRecordsFromCloud(req ListDomainRecordsRequest) (*response.PageModel[DomainRecordListResponse], error) {
	if req.ProviderUUID == "" {
		return nil, fmt.Errorf("providerUUID is required for online query")
	}

	// 获取服务商
	provider, err := svc.providerRepo.GetByUUID(req.ProviderUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider: %v", err)
	}

	// 创建DNS提供者实例
	dnsProvider, err := svc.dnsFactory.CreateProviderFromModel(provider)
	if err != nil {
		return nil, fmt.Errorf("failed to create provider instance: %v", err)
	}

	// 查询记录
	ctx := svc.AttachTrace(context.Background())
	filters := &dns.RecordFilters{
		Subdomain:  req.Subdomain,
		RecordType: req.RecordType,
		Value:      req.Value,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}

	cloudPageModel, err := dnsProvider.ListRecords(ctx, req.Domain, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list records from cloud: %v", err)
	}

	// 转换为响应格式
	var responseList []DomainRecordListResponse
	for _, record := range cloudPageModel.List {
		resp := DomainRecordListResponse{
			UUID:         record.UUID,
			FullDomain:   record.FullDomain,
			Domain:       record.Domain,
			Subdomain:    record.Subdomain,
			RecordType:   record.RecordType,
			Value:        record.Value,
			TTL:          record.TTL,
			Priority:     record.Priority,
			Proxied:      record.Proxied,
			ProviderName: record.ProviderName,
			Status:       record.Status,
			Creator:      record.Creator,
			CreatedAt:    record.CreatedAt,
		}
		responseList = append(responseList, resp)
	}

	return &response.PageModel[DomainRecordListResponse]{
		List:     responseList,
		Total:    cloudPageModel.Total,
		Page:     cloudPageModel.Page,
		PageSize: cloudPageModel.PageSize,
	}, nil
}

// listRecordsFromDatabase 从数据库查询记录
func (svc *DomainService) listRecordsFromDatabase(req ListDomainRecordsRequest) (*response.PageModel[DomainRecordListResponse], error) {
	pageModel, err := svc.recordRepo.GetPageWithFilter(
		req.Page, req.PageSize,
		req.Domain, req.Subdomain, req.Value, req.RecordType, req.Status, req.ProviderUUID)
	if err != nil {
		log.ErrorE("Failed to list domain records", err)
		return nil, fmt.Errorf("failed to list records: %v", err)
	}

	// 转换为响应格式
	var responseList []DomainRecordListResponse
	for _, record := range pageModel.List {
		var resp DomainRecordListResponse
		err := copier.Copy(&resp, &record)
		if err != nil {
			log.ErrorE("Failed to copy record data", err)
			continue
		}

		// 设置域名信息
		resp.Domain = record.Domain
		if record.Subdomain != "" {
			resp.FullDomain = record.Subdomain + "." + record.Domain
		} else {
			resp.FullDomain = record.Domain
		}

		responseList = append(responseList, resp)
	}

	return &response.PageModel[DomainRecordListResponse]{
		List:     responseList,
		Total:    pageModel.Total,
		Page:     pageModel.Page,
		PageSize: pageModel.PageSize,
	}, nil
}

// DeleteDomainRecord 删除域名记录
func (svc *DomainService) DeleteDomainRecord(req DeleteDomainRecordRequest, ops string) error {
	// 验证服务商
	provider, err := svc.providerRepo.GetByUUID(req.ProviderUUID)
	if err != nil {
		return err
	}

	if provider.Status != models.ProviderStatusActive {
		return fmt.Errorf("provider is not active")
	}

	record := &models.DomainRecord{
		Domain:       req.Domain,
		ProviderUUID: req.ProviderUUID,
		Subdomain:    req.Subdomain,
		RecordType:   req.RecordType,
		Comment:      req.Comment,
		Reason:       req.Reason,
		Value:        req.Value,
		Status:       models.RecordStatusPending,
		Action:       models.OperationTypeDelete,
	}

	record.Create(ops)
	err = svc.recordRepo.Create(record)
	if err != nil {
		log.ErrorE("Failed to create delete domain record", err)
		return fmt.Errorf("failed to create delete record: %v", err)
	}

	// 发送提交变更通知到群
	approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s", config.Items().Notify.FrontendHost,
		config.Items().Notify.DomainRecordRoute, record.UUID)
	if err = svc.notificationSvc.SendDomainRecordSubmitNotification(req.Domain, req.Subdomain,
		string(req.RecordType), "", "delete", ops, approvalURL, req.Reason); err != nil {
		log.ErrorE("发送DNS记录删除变更通知失败", err)
	}

	log.Printf("Domain record delete request created for approval: %s.%s %s", req.Subdomain, req.Domain, req.RecordType)
	return nil
}

// UpdateDomainRecord updates a domain record
func (svc *DomainService) UpdateDomainRecord(req UpdateDomainRecordRequest, ops string) error {
	// 验证服务商
	provider, err := svc.providerRepo.GetByUUID(req.ProviderUUID)
	if err != nil {
		return err
	}

	if provider.Status != models.ProviderStatusActive {
		return fmt.Errorf("provider is not active")
	}

	// 创建新的update记录（针对线上记录的更新申请）
	record := &models.DomainRecord{
		Domain:       req.Domain,
		ProviderUUID: req.ProviderUUID,
		Subdomain:    req.Subdomain,
		RecordType:   req.RecordType,
		Value:        req.Value,
		TTL:          req.TTL,
		Priority:     req.Priority,
		Proxied:      req.Proxied,
		Comment:      req.Comment,
		Reason:       req.Reason,
		Status:       models.RecordStatusPending,
		Action:       models.OperationTypeUpdate,
	}

	// 如果有原记录信息，保存为JSON
	if req.PrevRecord != nil {
		record.PrevRecord = util.StructToString(req.PrevRecord)
	}

	// 设置默认TTL
	if record.TTL == 0 {
		record.TTL = 600
	}

	record.Create(ops)

	if err = svc.recordRepo.Create(record); err != nil {
		log.ErrorE("Failed to create update domain record", err)
		return fmt.Errorf("failed to create update record: %v", err)
	}

	// 发送提交变更通知到群
	approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s",
		config.Items().Notify.FrontendHost, config.Items().Notify.DomainRecordRoute, record.UUID)

	if err = svc.notificationSvc.SendDomainRecordSubmitNotification(req.Domain, req.Subdomain, string(req.RecordType),
		req.Value, "update", ops, approvalURL, req.Reason); err != nil {
		log.ErrorE("发送DNS记录更新变更通知失败", err)
	}

	log.Printf("Domain record update request created for approval: %s.%s %s", req.Subdomain, req.Domain, req.RecordType)
	return nil
}

// ValidateDomainRecord validates domain record resolution
func (svc *DomainService) ValidateDomainRecord(req ValidateDomainRecordRequest) (*ValidateDomainRecordResponse, error) {
	// 构建完整域名
	fullDomain := req.Domain
	if req.Subdomain != "" && req.Subdomain != "@" {
		fullDomain = req.Subdomain + "." + req.Domain
	}

	// 根据记录类型进行DNS查询验证
	var result string
	var success bool

	switch req.RecordType {
	case models.RecordTypeA:
		// 验证A记录
		success, result = svc.validateARecord(fullDomain, req.Value)
	case models.RecordTypeAAAA:
		// 验证AAAA记录
		success, result = svc.validateAAAARecord(fullDomain, req.Value)
	case models.RecordTypeCNAME:
		// 验证CNAME记录
		success, result = svc.validateCNAMERecord(fullDomain, req.Value)
	case models.RecordTypeMX:
		// 验证MX记录
		success, result = svc.validateMXRecord(fullDomain, req.Value)
	case models.RecordTypeTXT:
		// 验证TXT记录
		success, result = svc.validateTXTRecord(fullDomain, req.Value)
	default:
		return &ValidateDomainRecordResponse{
			Success: false,
			Message: fmt.Sprintf("暂不支持 %s 类型记录的验证", req.RecordType),
		}, nil
	}

	return &ValidateDomainRecordResponse{
		Success: success,
		Message: result,
	}, nil
}

// ReviewDomainRecord reviews (approve/reject) a domain record
func (svc *DomainService) ReviewDomainRecord(req ReviewDomainRecordRequest, ops string, isAdmin interface{}) error {
	// 查找要审批的记录
	record, err := svc.recordRepo.GetByUUID(req.UUID)
	if err != nil {
		return err
	}

	// 只能审批pending状态的记录
	if record.Status != models.RecordStatusPending {
		return fmt.Errorf("只有pending状态的记录可以被审批")
	}

	// 获取服务商信息
	provider, err := svc.providerRepo.GetByUUID(record.ProviderUUID)
	if err != nil {
		return fmt.Errorf("failed to get provider: %v", err)
	}

	switch req.Action {
	case models.ApprovalStatusApproved:
		return svc.approveDomainRecord(record, provider, req, ops)
	case models.ApprovalStatusRejected:
		return svc.rejectDomainRecord(record, req, ops)
	default:
		return fmt.Errorf("无效的审批操作: %s", req.Action)
	}
}

// DNS validation helper methods

// validateARecord 验证A记录
func (svc *DomainService) validateARecord(domain, expectedValue string) (bool, string) {
	ips, err := net.LookupIP(domain)
	if err != nil {
		return false, fmt.Sprintf("DNS解析失败: %v", err)
	}

	for _, ip := range ips {
		if ip.To4() != nil && ip.String() == expectedValue {
			return true, ValidateSuccess
		}
	}

	var actualIPs []string
	for _, ip := range ips {
		if ip.To4() != nil {
			actualIPs = append(actualIPs, ip.String())
		}
	}

	if len(actualIPs) == 0 {
		return false, "未找到A记录"
	}

	return false, fmt.Sprintf("记录值不匹配，期望: %s，实际: %s", expectedValue, strings.Join(actualIPs, ", "))
}

// validateAAAARecord 验证AAAA记录
func (svc *DomainService) validateAAAARecord(domain, expectedValue string) (bool, string) {
	ips, err := net.LookupIP(domain)
	if err != nil {
		return false, fmt.Sprintf("DNS解析失败: %v", err)
	}

	for _, ip := range ips {
		if ip.To4() == nil && ip.String() == expectedValue {
			return true, ValidateSuccess
		}
	}

	var actualIPs []string
	for _, ip := range ips {
		if ip.To4() == nil {
			actualIPs = append(actualIPs, ip.String())
		}
	}

	if len(actualIPs) == 0 {
		return false, "未找到AAAA记录"
	}

	return false, fmt.Sprintf("记录值不匹配，期望: %s，实际: %s", expectedValue, strings.Join(actualIPs, ", "))
}

// validateCNAMERecord 验证CNAME记录
func (svc *DomainService) validateCNAMERecord(domain, expectedValue string) (bool, string) {
	cname, err := net.LookupCNAME(domain)
	if err != nil {
		return false, fmt.Sprintf("DNS解析失败: %v", err)
	}

	// 去除末尾的点
	cname = strings.TrimSuffix(cname, ".")
	expectedValue = strings.TrimSuffix(expectedValue, ".")

	if cname == expectedValue {
		return true, ValidateSuccess
	}

	return false, fmt.Sprintf("记录值不匹配，期望: %s，实际: %s", expectedValue, cname)
}

// validateMXRecord 验证MX记录
func (svc *DomainService) validateMXRecord(domain, expectedValue string) (bool, string) {
	mxRecords, err := net.LookupMX(domain)
	if err != nil {
		return false, fmt.Sprintf("DNS解析失败: %v", err)
	}

	expectedValue = strings.TrimSuffix(expectedValue, ".")

	for _, mx := range mxRecords {
		host := strings.TrimSuffix(mx.Host, ".")
		if host == expectedValue {
			return true, ValidateSuccess
		}
	}

	var actualMXs []string
	for _, mx := range mxRecords {
		host := strings.TrimSuffix(mx.Host, ".")
		actualMXs = append(actualMXs, fmt.Sprintf("%s (优先级: %d)", host, mx.Pref))
	}

	if len(actualMXs) == 0 {
		return false, "未找到MX记录"
	}

	return false, fmt.Sprintf("记录值不匹配，期望: %s，实际: %s", expectedValue, strings.Join(actualMXs, ", "))
}

// validateTXTRecord 验证TXT记录
func (svc *DomainService) validateTXTRecord(domain, expectedValue string) (bool, string) {
	txtRecords, err := net.LookupTXT(domain)
	if err != nil {
		return false, fmt.Sprintf("DNS解析失败: %v", err)
	}

	for _, txt := range txtRecords {
		if txt == expectedValue {
			return true, ValidateSuccess
		}
	}

	if len(txtRecords) == 0 {
		return false, "未找到TXT记录"
	}

	return false, fmt.Sprintf("记录值不匹配，期望: %s，实际: %s", expectedValue, strings.Join(txtRecords, ", "))
}

// approveDomainRecord 批准域名记录
func (svc *DomainService) approveDomainRecord(record *models.DomainRecord, provider *models.DomainProvider, req ReviewDomainRecordRequest, ops string) error {
	// 创建DNS提供者实例
	dnsProvider, err := svc.dnsFactory.CreateProviderFromModel(provider)
	if err != nil {
		log.ErrorE("Failed to create DNS provider instance", err)
		return fmt.Errorf("failed to create provider instance: %v", err)
	}

	ctx := svc.AttachTrace(context.Background())

	// 根据操作类型执行相应的DNS操作
	switch record.Action {
	case models.OperationTypeCreate:
		// 创建DNS记录
		err = dnsProvider.CreateRecord(ctx, record.Domain, record)
		if err != nil {
			log.ErrorE("Failed to create DNS record on provider", err)
			return fmt.Errorf("failed to create DNS record: %v", err)
		}
		log.Printf("Created DNS record: %s.%s %s", record.Subdomain, record.Domain, record.RecordType)

	case models.OperationTypeUpdate:
		// 更新DNS记录
		err = dnsProvider.UpdateRecord(ctx, record.Domain, record)
		if err != nil {
			log.ErrorE("Failed to update DNS record on provider", err)
			return fmt.Errorf("failed to update DNS record: %v", err)
		}
		log.Printf("Updated DNS record: %s.%s %s", record.Subdomain, record.Domain, record.RecordType)

	case models.OperationTypeDelete:
		// 删除DNS记录
		err = dnsProvider.DeleteRecord(ctx, record.Domain, record)
		if err != nil {
			log.ErrorE("Failed to delete DNS record on provider", err)
			return fmt.Errorf("failed to delete DNS record: %v", err)
		}
		log.Printf("Deleted DNS record: %s.%s %s", record.Subdomain, record.Domain, record.RecordType)
	}

	// 更新记录状态为active
	record.Status = models.RecordStatusActive
	record.Update(ops)

	// 发送审批通过通知
	svc.sendApprovalNotification(record, "通过", ops, req.Comment)

	log.Printf("Domain record approved and applied: %s.%s %s", record.Subdomain, record.Domain, record.RecordType)

	// 保存记录状态更新
	return svc.recordRepo.Save(record)
}

// rejectDomainRecord 拒绝域名记录
func (svc *DomainService) rejectDomainRecord(record *models.DomainRecord, req ReviewDomainRecordRequest, ops string) error {
	// 拒绝操作
	record.Status = models.RecordStatusFailed
	record.Update(ops)

	// 发送审批拒绝通知
	svc.sendApprovalNotification(record, "拒绝", ops, req.Comment)

	log.Printf("Domain record rejected: %s.%s %s, reason: %s", record.Subdomain, record.Domain, record.RecordType, req.Comment)

	// 保存记录状态更新
	return svc.recordRepo.Save(record)
}

// sendApprovalNotification 发送审批通知
func (svc *DomainService) sendApprovalNotification(record *models.DomainRecord, action, ops, comment string) {
	if svc.notificationSvc != nil {
		actionText := ActionCreate
		switch record.Action {
		case models.OperationTypeUpdate:
			actionText = ActionUpdate
		case models.OperationTypeDelete:
			actionText = ActionDelete
		}

		if err := svc.notificationSvc.SendDomainRecordApprovalResultNotification(
			record.Domain, record.Subdomain, string(record.RecordType), record.Value,
			actionText, action, ops, comment, record.Creator,
		); err != nil {
			log.ErrorE("发送DNS记录审批结果通知失败", err)
		}
	}
}
