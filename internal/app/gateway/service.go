package gateway

import (
	"context"
	"time"

	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/repos"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type GatewayService struct {
	*trace.Service
	repo *repos.GatewayRepo
	mgr  *cluster.ClusterManager
}

func NewGatewayService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *GatewayService {
	svc := &GatewayService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.repo = repos.NewGatewayRepo(dbEngine.WithContext(ctx))
	svc.mgr = cluster.GetClusterManager()

	return svc
}

func (s *GatewayService) ListGateways(r ListRequest) (*response.PageModel[*GatewayInfo], error) {
	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(r.Cluster)
	if err != nil {
		log.ErrorE("failed to get cluster client", err)
		return nil, err
	}

	pm := &response.PageModel[*GatewayInfo]{}

	gateways, err := istioClient.NetworkingV1alpha3().Gateways(r.Namespace).List(context.TODO(), v1.ListOptions{})
	if err != nil {
		return pm, err
	}

	var gatewayInfos []*GatewayInfo
	for _, item := range gateways.Items {
		// 过滤条件
		if r.Name != "" && !util.Contains(item.Name, r.Name) {
			continue
		}

		var servers []GatewayServer
		for _, server := range item.Spec.Servers {
			servers = append(servers, GatewayServer{
				Port: GatewayPort{
					Number:   server.Port.Number,
					Name:     server.Port.Name,
					Protocol: server.Port.Protocol,
				},
				Hosts: server.Hosts,
			})
		}

		gatewayInfos = append(gatewayInfos, &GatewayInfo{
			Name:        item.Name,
			Namespace:   item.Namespace,
			Labels:      item.Labels,
			Servers:     servers,
			CreatedTime: item.CreationTimestamp.Format(time.RFC3339),
		})
	}

	// 分页处理
	total := int64(len(gatewayInfos))
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	if start >= len(gatewayInfos) {
		pm.List = []*GatewayInfo{}
	} else {
		if end > len(gatewayInfos) {
			end = len(gatewayInfos)
		}
		pm.List = gatewayInfos[start:end]
	}

	return pm, nil
}

func (s *GatewayService) ListServices(r ListRequest) (*response.PageModel[*ServiceInfo], error) {
	// 获取集群客户端
	kubeClient, _, err := s.mgr.GetClusterClient(r.Cluster)
	if err != nil {
		log.ErrorE("failed to get cluster client", err)
		return nil, err
	}

	pm := &response.PageModel[*ServiceInfo]{}

	services, err := kubeClient.CoreV1().Services(r.Namespace).List(context.TODO(), v1.ListOptions{})
	if err != nil {
		return pm, err
	}

	var serviceInfos []*ServiceInfo
	for _, item := range services.Items {
		// 过滤条件
		if r.Name != "" && !util.Contains(item.Name, r.Name) {
			continue
		}

		var ports []ServicePort
		for _, port := range item.Spec.Ports {
			ports = append(ports, ServicePort{
				Name:       port.Name,
				Port:       port.Port,
				TargetPort: port.TargetPort.String(),
				Protocol:   string(port.Protocol),
			})
		}

		serviceInfos = append(serviceInfos, &ServiceInfo{
			Name:        item.Name,
			Namespace:   item.Namespace,
			Labels:      item.Labels,
			ClusterIP:   item.Spec.ClusterIP,
			Ports:       ports,
			CreatedTime: item.CreationTimestamp.Format(time.RFC3339),
		})
	}

	// 分页处理
	total := int64(len(serviceInfos))
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	if start >= len(serviceInfos) {
		pm.List = []*ServiceInfo{}
	} else {
		if end > len(serviceInfos) {
			end = len(serviceInfos)
		}
		pm.List = serviceInfos[start:end]
	}

	return pm, nil
}
