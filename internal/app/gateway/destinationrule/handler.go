package gateway

import (
	"pilot-api/internal/pkg/response"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

func ListDestinationRules(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewDestinationRuleService(ts, mysql.GetDB(), redis.GetClient())
	list, err := svc.ListDestinationRules(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}

func GetDestinationRule(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req GetRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewDestinationRuleService(ts, mysql.GetDB(), redis.GetClient())
	detail, err := svc.GetDestinationRule(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, detail)
}
