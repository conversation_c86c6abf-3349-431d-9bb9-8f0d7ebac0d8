package gateway

import (
	"context"
	"time"

	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/repos"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type DestinationRuleService struct {
	*trace.Service
	repo *repos.GatewayRepo
	mgr  *cluster.ClusterManager
}

func NewDestinationRuleService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *DestinationRuleService {
	svc := &DestinationRuleService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.repo = repos.NewGatewayRepo(dbEngine.WithContext(ctx))
	svc.mgr = cluster.GetClusterManager()

	return svc
}

func (s *DestinationRuleService) ListDestinationRules(r ListRequest) (*response.PageModel[*DestinationRuleInfo], error) {
	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(r.Cluster)
	if err != nil {
		log.ErrorE("failed to get cluster client", err)
		return nil, err
	}

	pm := &response.PageModel[*DestinationRuleInfo]{}

	destinationRules, err := istioClient.NetworkingV1alpha3().DestinationRules(r.Namespace).List(context.TODO(), v1.ListOptions{})
	if err != nil {
		return pm, err
	}

	var destinationRuleInfos []*DestinationRuleInfo
	for _, item := range destinationRules.Items {
		// 过滤条件
		if r.Name != "" && !util.Contains(item.Name, r.Name) {
			continue
		}

		destinationRuleInfos = append(destinationRuleInfos, &DestinationRuleInfo{
			Name:        item.Name,
			Namespace:   item.Namespace,
			Labels:      item.Labels,
			Host:        item.Spec.Host,
			CreatedTime: item.CreationTimestamp.Format(time.RFC3339),
		})
	}

	// 分页处理
	total := int64(len(destinationRuleInfos))
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	if start >= len(destinationRuleInfos) {
		pm.List = []*DestinationRuleInfo{}
	} else {
		if end > len(destinationRuleInfos) {
			end = len(destinationRuleInfos)
		}
		pm.List = destinationRuleInfos[start:end]
	}

	return pm, nil
}

func (s *DestinationRuleService) GetDestinationRule(r GetRequest) (any, error) {
	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(r.Cluster)
	if err != nil {
		log.ErrorE("failed to get cluster client", err)
		return nil, err
	}

	destinationRule, err := istioClient.NetworkingV1alpha3().DestinationRules(r.Namespace).Get(context.TODO(), r.Name, v1.GetOptions{})
	if err != nil {
		return nil, err
	}

	return destinationRule, nil
}
