package gateway

import (
	"pilot-api/internal/pkg/request"
)

// ListRequest represents the request for listing resources
type ListRequest struct {
	request.PageRequest
	Cluster   string `json:"cluster" binding:"required"` // 集群标识
	Namespace string `json:"namespace"`                  // 命名空间
	Name      string `json:"name"`                       // 资源名称
}

// NamespaceRequest represents the request for listing namespaces
type NamespaceRequest struct {
	Cluster string `json:"cluster" binding:"required"` // 集群标识
}

// GetRequest represents the request for getting a specific resource
type GetRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
}

// NamespaceInfo represents Kubernetes Namespace information
type NamespaceInfo struct {
	Name        string            `json:"name"`
	Labels      map[string]string `json:"labels"`
	Status      string            `json:"status"`
	CreatedTime string            `json:"createdTime"`
}

// ServiceInfo represents Kubernetes Service information
type ServiceInfo struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels"`
	ClusterIP   string            `json:"clusterIP"`
	Ports       []ServicePort     `json:"ports"`
	CreatedTime string            `json:"createdTime"`
}

// ServicePort represents service port information
type ServicePort struct {
	Name       string `json:"name"`
	Port       int32  `json:"port"`
	TargetPort string `json:"targetPort"`
	Protocol   string `json:"protocol"`
}

// DeploymentInfo represents Kubernetes Deployment information
type DeploymentInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Labels            map[string]string `json:"labels"`
	Replicas          int32             `json:"replicas"`
	ReadyReplicas     int32             `json:"readyReplicas"`
	AvailableReplicas int32             `json:"availableReplicas"`
	CreatedTime       string            `json:"createdTime"`
}

// DestinationRuleInfo represents Istio DestinationRule information
type DestinationRuleInfo struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels"`
	Host        string            `json:"host"`
	CreatedTime string            `json:"createdTime"`
}

// GatewayInfo represents Istio Gateway information
type GatewayInfo struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels"`
	Servers     []GatewayServer   `json:"servers"`
	CreatedTime string            `json:"createdTime"`
}

// GatewayServer represents gateway server configuration
type GatewayServer struct {
	Port  GatewayPort `json:"port"`
	Hosts []string    `json:"hosts"`
}

// GatewayPort represents gateway port configuration
type GatewayPort struct {
	Number   uint32 `json:"number"`
	Name     string `json:"name"`
	Protocol string `json:"protocol"`
}
