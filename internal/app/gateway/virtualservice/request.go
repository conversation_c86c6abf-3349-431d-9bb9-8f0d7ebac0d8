package virtualservice

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/pkg/models"
)

// VirtualServiceInfo represents Istio VirtualService information
type VirtualServiceInfo struct {
	UUID        string                      `json:"uuid,omitempty"` // 数据库记录UUID
	Name        string                      `json:"name"`
	Namespace   string                      `json:"namespace"`
	Labels      map[string]string           `json:"labels"`
	Hosts       []string                    `json:"hosts"`
	Gateways    []string                    `json:"gateways"`
	CreatedTime string                      `json:"createdTime"`
	Status      models.VirtualServiceStatus `json:"status"` // 状态：pending, deployed, failed
	Action      string                      `json:"action"` // 操作类型：create, update, delete
	Reason      string                      `json:"reason"` // 申请理由
}

// ListRequest represents the request for listing resources
type ListRequest struct {
	request.PageRequest
	Cluster   string                      `json:"cluster" binding:"required"` // 集群标识
	Namespace string                      `json:"namespace"`                  // 命名空间
	Name      string                      `json:"name"`                       // 资源名称
	Status    models.VirtualServiceStatus `json:"status"`                     // 状态：pending, deployed, failed
}

// GetRequest represents the request for getting a specific resource
type GetRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Online    bool   `json:"online"`                       // 是否获取线上数据
	// 如果找不到本地数据是否查询线上数据
	Fallback bool `json:"fallback"`
	// 静默模式，不抛出异常
	Silent bool `json:"silent"`
}

type AddVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Content   string `json:"content" binding:"required"`   // 内容
	Reason    string `json:"reason"`                       // 申请理由
}

type UpdateVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Content   string `json:"content" binding:"required"`   // 内容
	Reason    string `json:"reason"`                       // 申请理由
}

type SaveVirtualServiceEditorRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Content   string `json:"content" binding:"required"`   // 内容
}

// VirtualServiceHistoryRequest 获取VirtualService历史版本请求
type VirtualServiceHistoryRequest struct {
	request.PageRequest
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
}

// ApproveVirtualServiceRequest 审批VirtualService请求
type ApproveVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Comment   string `json:"comment"`                      // 审批意见
	Content   string `json:"content"`                      // 修改后的YAML内容
}

// RejectVirtualServiceRequest 拒绝VirtualService请求
type RejectVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Comment   string `json:"comment"`                      // 拒绝原因
	Content   string `json:"content"`                      // 修改后的YAML内容
}

type VirtualServiceReviewAction string

const (
	ApproveVirtualService VirtualServiceReviewAction = "approve"
	RejectVirtualService  VirtualServiceReviewAction = "reject"
)

// ReviewVirtualServiceRequest 审批VirtualService请求（合并批准和拒绝）
type ReviewVirtualServiceRequest struct {
	UUID    string                     `json:"uuid" binding:"required"`   // 变更记录UUID
	Action  VirtualServiceReviewAction `json:"action" binding:"required"` // 操作类型：approve(批准) 或 reject(拒绝)
	Comment string                     `json:"comment"`                   // 审批意见
	Content string                     `json:"content"`                   // 修改后的YAML内容
}

// DryRunVirtualServiceRequest VirtualService Dry Run请求
type DryRunVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Content   string `json:"content" binding:"required"`   // YAML内容
}

// RollbackVirtualServiceRequest 回滚VirtualService请求
type RollbackVirtualServiceRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
	Version   int    `json:"version" binding:"required"`   // 回滚版本
	Comment   string `json:"comment"`                      // 回滚原因
}

// VirtualServiceApprovalInfo VirtualService审批信息
type VirtualServiceApprovalInfo struct {
	UUID        string                      `json:"uuid"`
	Name        string                      `json:"name"`
	Namespace   string                      `json:"namespace"`
	Cluster     string                      `json:"cluster"`
	Status      models.VirtualServiceStatus `json:"status"`
	Content     string                      `json:"content"`
	Applicant   string                      `json:"applicant"`
	ApplyTime   string                      `json:"applyTime"`
	Approver    string                      `json:"approver,omitempty"`
	ApproveTime string                      `json:"approveTime,omitempty"`
	Comment     string                      `json:"comment,omitempty"`
	Diff        string                      `json:"diff,omitempty"`
}

// DeleteVirtualServiceRequest 删除VirtualService请求
type DeleteVirtualServiceRequest struct {
	// 要删除的资源列表
	Resources []VirtualServiceResource `json:"resources" binding:"required,dive"`
	// 删除原因
	Reason string `json:"reason"`
}

// VirtualServiceResource 资源标识符
type VirtualServiceResource struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
}

type VirtualServiceEditorResponse struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Cluster   string `json:"cluster"`
	Content   string `json:"content"`
}
