package virtualservice

import (
	"pilot-api/internal/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterRouter 注册VirtualService相关路由
func RegisterRouter(r *gin.Engine) {
	gatewayAPI := r.Group("/api/v1/gateway")
	{
		gatewayAPI.Use(middleware.AuthIsAdmin(), middleware.AuthUToken())
		// VirtualService
		gatewayAPI.POST("/virtualservice/info", GetVirtualService)
		gatewayAPI.POST("/virtualservice/list", ListVirtualServices)
		gatewayAPI.POST("/virtualservice/add", AddVirtualService)
		gatewayAPI.PUT("/virtualservice/update", UpdateVirtualService)
		gatewayAPI.DELETE("/virtualservice/delete", DeleteVirtualService)
		gatewayAPI.POST("/virtualservice/history", GetVirtualServiceHistory)
		// VirtualService审批相关
		gatewayAPI.POST("/virtualservice/review", ReviewVirtualService) // 审批
		gatewayAPI.POST("/virtualservice/dryrun", DryRunVirtualService) // DryRun
	}
}
