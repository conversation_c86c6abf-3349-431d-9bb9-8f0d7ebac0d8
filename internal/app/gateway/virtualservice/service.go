package virtualservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"pilot-api/config"

	"pilot-api/internal/pkg/util"
	"strings"
	"time"

	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/notification"
	"pilot-api/internal/pkg/response"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	istiov1 "istio.io/client-go/pkg/apis/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	k8syaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/dynamic"
	"sigs.k8s.io/yaml"
)

type VirtualService struct {
	*trace.Service
	virtualServiceRepo        *repos.VirtualServiceRepo
	virtualServiceHistoryRepo *repos.VirtualServiceHistoryRepo
	mgr                       *cluster.ClusterManager
	notificationSvc           *notification.NotificationService
	redisClient               *redis.Client
}

func NewVirtualService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *VirtualService {
	svc := &VirtualService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.redisClient = redisClient
	svc.virtualServiceRepo = repos.NewVirtualServiceRepo(dbEngine.WithContext(ctx))
	svc.virtualServiceHistoryRepo = repos.NewVirtualServiceHistoryRepo(dbEngine.WithContext(ctx))
	svc.mgr = cluster.GetClusterManager()

	// 初始化通知服务
	nc := config.Items().Notify
	svc.notificationSvc = notification.NewNotificationService(nc.AppID, nc.AppSecret, nc.GatewayChatID)

	return svc
}

func (s *VirtualService) ListVirtualServices(r ListRequest) (*response.PageModel[*VirtualServiceInfo], error) {
	// 如果状态是active，从K8s查询线上状态
	if r.Status == models.Active {
		return s.listVirtualServicesFromCluster(r)
	}
	// 从数据库查询变更记录
	return s.listVirtualServicesFromDatabase(r)
}

// listVirtualServicesFromDatabase 从数据库查询变更记录
func (s *VirtualService) listVirtualServicesFromDatabase(r ListRequest) (*response.PageModel[*VirtualServiceInfo], error) {
	pm := &response.PageModel[*VirtualServiceInfo]{}

	// 从数据库查询变更记录
	cond := &models.VirtualService{
		Cluster: r.Cluster,
	}
	if r.Namespace != "" {
		cond.Namespace = r.Namespace
	}
	if r.Status != "" {
		cond.Status = r.Status
	}

	// 使用带名称过滤的查询
	virtualServices, err := s.virtualServiceRepo.GetPageWithNameFilter(cond, r.Name, "gmt_create DESC", r.Page, r.PageSize)
	if err != nil {
		return pm, err
	}

	// 获取总数
	total, err := s.virtualServiceRepo.CountWithFilter(cond, r.Name)
	if err != nil {
		return pm, err
	}

	var virtualServiceInfos []*VirtualServiceInfo
	for _, item := range virtualServices {
		// 解析hosts和gateways
		var hosts, gateways []string
		if item.Hosts != "" {
			if err = json.Unmarshal([]byte(item.Hosts), &hosts); err != nil {
				log.ErrorE("Failed to unmarshal hosts", err)
			}
		}
		if item.Gateways != "" {
			if err = json.Unmarshal([]byte(item.Gateways), &gateways); err != nil {
				log.ErrorE("Failed to unmarshal gateways", err)
			}
		}
		// 添加到结果列表
		virtualServiceInfos = append(virtualServiceInfos, &VirtualServiceInfo{
			UUID:        item.UUID,
			Name:        item.Name,
			Namespace:   item.Namespace,
			Labels:      make(map[string]string),
			Hosts:       hosts,
			Gateways:    gateways,
			Status:      item.Status,
			Action:      string(item.Action), // 添加操作类型
			Reason:      item.Reason,         // 添加申请理由
			CreatedTime: item.GmtCreate.Format(time.RFC3339),
		})
	}

	pm.List = virtualServiceInfos
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	return pm, nil
}

// listVirtualServicesFromCluster 从K8s集群查询线上状态
func (s *VirtualService) listVirtualServicesFromCluster(r ListRequest) (*response.PageModel[*VirtualServiceInfo], error) {
	pm := &response.PageModel[*VirtualServiceInfo]{}

	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(r.Cluster)
	if err != nil {
		return pm, fmt.Errorf("获取集群客户端失败: %w", err)
	}

	// 从K8s查询VirtualService
	var vsList *istiov1.VirtualServiceList
	vsList, err = istioClient.NetworkingV1().VirtualServices(r.Namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return pm, fmt.Errorf("查询VirtualService失败: %w", err)
	}

	var virtualServiceInfos []*VirtualServiceInfo
	for _, vs := range vsList.Items {
		// 名称过滤
		if r.Name != "" && !strings.Contains(vs.Name, r.Name) {
			continue
		}
		virtualServiceInfos = append(virtualServiceInfos, &VirtualServiceInfo{
			Name:        vs.Name,
			Namespace:   vs.Namespace,
			Labels:      vs.Labels,
			Hosts:       vs.Spec.Hosts,
			Gateways:    vs.Spec.Gateways,
			Status:      models.Active, // 线上状态都是active
			CreatedTime: vs.CreationTimestamp.Format(time.RFC3339),
		})
	}

	// 简单分页
	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	total := int64(len(virtualServiceInfos))

	if start > len(virtualServiceInfos) {
		virtualServiceInfos = []*VirtualServiceInfo{}
	} else if end > len(virtualServiceInfos) {
		virtualServiceInfos = virtualServiceInfos[start:]
	} else {
		virtualServiceInfos = virtualServiceInfos[start:end]
	}

	pm.List = virtualServiceInfos
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	return pm, nil
}

func (s *VirtualService) GetVirtualService(r GetRequest) (vs *models.VirtualService, err error) {
	// default value
	defaultValue := &models.VirtualService{}
	// fallback
	defer func() {
		// 如果是记录不存在，判断是否需要去线上查询(修改的时候)
		if errors.Is(err, gorm.ErrRecordNotFound) && r.Fallback {
			vs, _ = s.GetOnlineVirtualService(r)
		}
		// 解决新增的时候对比问题
		vs = util.Coalesce(vs, defaultValue)
		// 静默模式下不抛出异常
		if r.Silent {
			err = nil
		}
	}()
	// 如果请求的是线上版本，则从集群获取
	if r.Online {
		vs, err = s.GetOnlineVirtualService(r)
		if err != nil {
			return nil, err
		}
		return vs, nil
	}
	// 从数据库获取申请记录
	virtualService, err := s.virtualServiceRepo.GetFirst(models.VirtualService{
		Name:      r.Name,
		Cluster:   r.Cluster,
		Namespace: r.Namespace,
	})
	if err != nil {
		return nil, err
	}
	return virtualService, nil
}

func (s *VirtualService) ApplyYAML(cluster, namespace, yamlStr string) error {
	_, istioClient, err := s.mgr.GetClusterClient(cluster)
	if err != nil {
		return errors.New("集群连接失败: " + err.Error())
	}
	var vs istiov1.VirtualService
	if err = yaml.Unmarshal([]byte(yamlStr), &vs); err != nil {
		return errors.New("YAML 解析失败: " + err.Error())
	}
	if vs.Namespace != namespace {
		return errors.New("YAML namespace 与参数不一致")
	}
	api := istioClient.NetworkingV1().VirtualServices(namespace)
	_, err = api.Create(context.TODO(), &vs, metav1.CreateOptions{})
	if err != nil && apierrors.IsAlreadyExists(err) {
		// 获取现有资源以获取resourceVersion
		existing, getErr := api.Get(context.TODO(), vs.Name, metav1.GetOptions{})
		if getErr != nil {
			return fmt.Errorf("获取现有VirtualService失败: %w", getErr)
		}
		// 设置resourceVersion
		vs.ResourceVersion = existing.ResourceVersion
		_, err = api.Update(context.TODO(), &vs, metav1.UpdateOptions{})
	}
	return err
}

// ApplyYAMLV2 直接使用 YAML 字符串进行 Server-Side Apply
// 因为envoy默认会进行重试，如果不显示指定retry就会导致走默认的策略，所以这里使用k8s原生的apply进行实现
func (s *VirtualService) ApplyYAMLV2(cluster, namespace, yamlStr string) error {
	// 获取集群配置
	kubeConfig, err := s.mgr.GetClusterConfig(cluster)
	if err != nil {
		return errors.New("获取集群配置失败: " + err.Error())
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(kubeConfig)
	if err != nil {
		return errors.New("创建动态客户端失败: " + err.Error())
	}

	// 直接将 YAML 转换为 unstructured
	unstructuredObj := &unstructured.Unstructured{}
	if err = yaml.Unmarshal([]byte(yamlStr), &unstructuredObj.Object); err != nil {
		return errors.New("YAML 解析失败: " + err.Error())
	}

	// 验证 namespace
	if unstructuredObj.GetNamespace() != namespace {
		return errors.New("YAML namespace 与参数不一致")
	}

	// 设置 VirtualService 的 GVR
	gvr := schema.GroupVersionResource{
		Version:  "v1",
		Resource: "virtualservices",
		Group:    "networking.istio.io",
	}

	// 使用 Server-Side Apply
	fieldManager := "pilot-api"
	_, err = dynamicClient.Resource(gvr).Namespace(namespace).Apply(
		context.TODO(), unstructuredObj.GetName(),
		unstructuredObj, metav1.ApplyOptions{
			FieldManager: fieldManager, Force: true, // 强制应用，覆盖冲突字段
		},
	)

	if err != nil {
		return fmt.Errorf("Server-Side Apply 失败: %w", err)
	}

	log.Info("VirtualService Server-Side Apply 成功", log.Any("name", unstructuredObj.GetName()),
		log.Any("namespace", namespace), log.Any("cluster", cluster))

	return nil
}

// Add 创建VirtualService
func (s *VirtualService) Add(req AddVirtualServiceRequest, ops string) error {
	// 解析YAML内容
	var vs istiov1.VirtualService
	if err := k8syaml.Unmarshal([]byte(req.Content), &vs); err != nil {
		return errors.New("YAML解析失败: " + err.Error())
	}

	// 验证必要字段
	if vs.Name == "" {
		return errors.New("VirtualService名称不能为空")
	}
	if vs.Namespace == "" {
		return errors.New("命名空间不能为空")
	}

	// 创建VirtualService变更记录
	virtualService := &models.VirtualService{
		Name:      vs.Name,
		Namespace: vs.Namespace,
		Cluster:   req.Cluster,
		Content:   req.Content,
		Status:    models.Pending,
		Action:    models.OperationTypeCreate,
		Reason:    req.Reason,
	}

	// 提取hosts和gateways信息用于查询
	if len(vs.Spec.Hosts) > 0 {
		hostsJSON, _ := json.Marshal(vs.Spec.Hosts)
		virtualService.Hosts = string(hostsJSON)
	}
	if len(vs.Spec.Gateways) > 0 {
		gatewaysJSON, _ := json.Marshal(vs.Spec.Gateways)
		virtualService.Gateways = string(gatewaysJSON)
	}

	// 设置创建信息
	virtualService.Create(ops)

	// 保存到数据库
	if err := s.virtualServiceRepo.Create(virtualService); err != nil {
		return err
	}

	// 发送提交变更通知到群
	approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s",
		config.Items().Notify.FrontendHost, config.Items().Notify.VirtualServiceRoute, virtualService.UUID)
	if err := s.notificationSvc.SendVirtualServiceSubmitNotification(
		virtualService.Name, virtualService.Namespace, virtualService.Cluster, ops, req.Content, approvalURL,
	); err != nil {
		log.ErrorE("发送VirtualService提交变更通知失败", err)
	}

	return nil
}

// Update 更新VirtualService
func (s *VirtualService) Update(req UpdateVirtualServiceRequest, ops string) error {
	// 解析YAML内容
	var vs istiov1.VirtualService
	if err := k8syaml.Unmarshal([]byte(req.Content), &vs); err != nil {
		return errors.New("YAML解析失败: " + err.Error())
	}

	// 验证必要字段
	if vs.Name == "" {
		return errors.New("VirtualService名称不能为空")
	}
	if vs.Namespace == "" {
		return errors.New("命名空间不能为空")
	}

	// 查询数据库, 检查记录是否存在
	virtualService, err := s.virtualServiceRepo.GetFirst(models.VirtualService{
		Name: vs.Name, Namespace: vs.Namespace, Cluster: req.Cluster,
	})

	isNewRecord := false
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		// 记录不存在，创建新记录
		virtualService = &models.VirtualService{
			Name:      vs.Name,
			Namespace: vs.Namespace,
			Cluster:   req.Cluster,
		}
		virtualService.Create(ops)
		isNewRecord = true
	}

	// 更新VirtualService变更记录
	virtualService.Content = req.Content
	virtualService.Reason = req.Reason
	virtualService.Status = models.Pending
	virtualService.Action = models.OperationTypeUpdate

	// 提取hosts和gateways信息用于查询
	if len(vs.Spec.Hosts) > 0 {
		hostsJSON, _ := json.Marshal(vs.Spec.Hosts)
		virtualService.Hosts = string(hostsJSON)
	}
	if len(vs.Spec.Gateways) > 0 {
		gatewaysJSON, _ := json.Marshal(vs.Spec.Gateways)
		virtualService.Gateways = string(gatewaysJSON)
	}

	// 根据是否为新记录选择不同的保存方法
	if isNewRecord {
		// 新记录使用Create方法
		if err = s.virtualServiceRepo.Create(virtualService); err != nil {
			return err
		}
	} else {
		// 已存在记录，设置更新信息并使用Update方法
		virtualService.Update(ops)
		if err = s.virtualServiceRepo.Update(virtualService); err != nil {
			return err
		}
	}

	// 发送提交变更通知到群
	approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s",
		config.Items().Notify.FrontendHost, config.Items().Notify.VirtualServiceRoute, virtualService.UUID)
	if err = s.notificationSvc.SendVirtualServiceSubmitNotification(
		vs.Name, vs.Namespace, req.Cluster, ops, req.Content, approvalURL,
	); err != nil {
		log.ErrorE("发送VirtualService提交变更通知失败", err)
	}

	return nil
}

// Delete 删除VirtualService - 所有用户都需要通过审批流程
func (s *VirtualService) Delete(req DeleteVirtualServiceRequest, ops string) error {
	// 遍历所有要删除的资源
	for _, resource := range req.Resources {
		// 先尝试从本地数据库查找记录
		vs, err := s.virtualServiceRepo.GetFirst(models.VirtualService{
			Name:      resource.Name,
			Namespace: resource.Namespace,
			Cluster:   resource.Cluster,
		})
		// 如果本地没有记录，从集群查询资源是否存在
		if err != nil || vs == nil {
			clusterVS, clusterErr := s.getVirtualServiceFromCluster(resource.Cluster, resource.Namespace, resource.Name)
			if clusterErr != nil {
				if apierrors.IsNotFound(clusterErr) {
					log.Info("VirtualService在集群中不存在，跳过删除", log.Any("name", resource.Name),
						log.Any("namespace", resource.Namespace), log.Any("cluster", resource.Cluster))
					continue
				}
				return fmt.Errorf("查询集群VirtualService失败: %w", clusterErr)
			}
			// 为外部资源创建临时记录
			vs, err = s.createVirtualServiceFromClusterResource(clusterVS, resource.Cluster, req.Reason, ops)
			if err != nil {
				return fmt.Errorf("创建外部资源记录失败: %w", err)
			}
		}

		// 所有用户都走审批流程：创建或更新删除申请记录
		if vs.Action != models.OperationTypeDelete || vs.Status != models.Pending {
			// 先创建历史备份
			if err = s.createVirtualServiceHistory(vs, models.OperationTypeDelete, ops, fmt.Sprintf("申请删除：%s", req.Reason)); err != nil {
				log.Warn("创建删除申请历史记录失败", log.Any("error", err.Error()))
			}

			// 更新为删除申请状态
			vs.Status = models.Pending
			vs.Action = models.OperationTypeDelete
			vs.Reason = req.Reason
			vs.Update(ops)

			if err = s.virtualServiceRepo.Update(vs); err != nil {
				return fmt.Errorf("更新删除申请失败: %w", err)
			}
		}

		// 发送删除申请通知到群
		approvalURL := fmt.Sprintf("%s%s?approval=true&uuid=%s",
			config.Items().Notify.FrontendHost, config.Items().Notify.VirtualServiceRoute,
			vs.UUID)

		if err = s.notificationSvc.SendVirtualServiceSubmitNotification(vs.Name, vs.Namespace, vs.Cluster, ops,
			"申请删除VirtualService", approvalURL,
		); err != nil {
			log.ErrorE("发送删除申请通知失败", err)
		}
		log.Info("普通用户申请删除VirtualService", log.Any("name", vs.Name),
			log.Any("namespace", vs.Namespace), log.Any("cluster", vs.Cluster))
	}

	return nil
}

// deleteVirtualServiceFromCluster 从集群中删除VirtualService
func (s *VirtualService) deleteVirtualServiceFromCluster(vs *models.VirtualService) error {
	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(vs.Cluster)
	if err != nil {
		return fmt.Errorf("获取集群客户端失败: %w", err)
	}
	// 从集群删除VirtualService
	if err = istioClient.NetworkingV1().VirtualServices(vs.Namespace).
		Delete(context.TODO(), vs.Name, metav1.DeleteOptions{}); err != nil {
		// 如果资源不存在，认为是删除成功
		if apierrors.IsNotFound(err) {
			log.Info("VirtualService在集群中不存在，视为删除成功", log.Any("name", vs.Name), log.Any("namespace", vs.Namespace))
			return nil
		}
		return fmt.Errorf("从集群删除VirtualService失败: %w", err)
	}
	return nil
}

// getVirtualServiceFromCluster 从集群中获取VirtualService资源
func (s *VirtualService) getVirtualServiceFromCluster(cluster, namespace, name string) (*istiov1.VirtualService, error) {
	// 获取集群客户端
	_, istioClient, err := s.mgr.GetClusterClient(cluster)
	if err != nil {
		return nil, fmt.Errorf("获取集群客户端失败: %w", err)
	}

	// 从集群获取VirtualService
	vs, err := istioClient.NetworkingV1().VirtualServices(namespace).
		Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	return vs, nil
}

// createVirtualServiceFromClusterResource 根据集群资源创建本地VirtualService记录
func (s *VirtualService) createVirtualServiceFromClusterResource(clusterVS *istiov1.VirtualService, cluster, reason, ops string) (*models.VirtualService, error) {
	// 将集群资源转换为YAML
	yamlContent, err := yaml.Marshal(clusterVS)
	if err != nil {
		return nil, fmt.Errorf("转换VirtualService为YAML失败: %w", err)
	}

	// 创建本地记录
	vs := &models.VirtualService{
		Name:            clusterVS.Name,
		Namespace:       clusterVS.Namespace,
		Cluster:         cluster,
		Content:         string(yamlContent),
		Status:          models.Pending,
		Action:          models.OperationTypeDelete,
		Reason:          reason,
		ResourceVersion: clusterVS.ResourceVersion,
		Hosts:           strings.Join(clusterVS.Spec.Hosts, ","),
		Gateways:        strings.Join(clusterVS.Spec.Gateways, ","),
	}

	vs.Create(ops)

	if err = s.virtualServiceRepo.Create(vs); err != nil {
		return nil, err
	}

	return vs, nil
}

// createVirtualServiceHistory 创建VirtualService历史记录
func (s *VirtualService) createVirtualServiceHistory(vs *models.VirtualService, action models.OperationType, operator string, comment string) error {
	// 获取下一个版本号
	nextVersion, err := s.virtualServiceRepo.GetNextVersion(vs.Cluster, vs.Namespace, vs.Name)
	if err != nil {
		return fmt.Errorf("获取下一个版本号失败: %w", err)
	}

	// 创建历史记录
	history := &models.VirtualServiceHistory{
		Name:               vs.Name,
		Namespace:          vs.Namespace,
		Cluster:            vs.Cluster,
		Gateways:           vs.Gateways,
		Hosts:              vs.Hosts,
		Content:            vs.Content,
		ResourceVersion:    vs.ResourceVersion,
		VirtualServiceUUID: vs.UUID,
		Version:            nextVersion,
		Action:             action,
		Status:             vs.Status,
		Operator:           operator,
		Comment:            comment,
	}
	history.Create(operator)

	// 保存历史记录
	return s.virtualServiceRepo.CreateHistory(history)
}

// GetVirtualServiceHistory 获取VirtualService历史版本
func (s *VirtualService) GetVirtualServiceHistory(r VirtualServiceHistoryRequest) (*response.PageModel[*VirtualServiceHistoryResponse], error) {
	pm := &response.PageModel[*VirtualServiceHistoryResponse]{}

	// 首先获取当前的VirtualService记录
	virtualService, err := s.virtualServiceRepo.GetFirst(models.VirtualService{
		Name:      r.Name,
		Namespace: r.Namespace,
		Cluster:   r.Cluster,
	})
	if err != nil {
		return pm, err
	}

	// 查询历史版本记录
	cond := &models.VirtualServiceHistory{
		VirtualServiceUUID: virtualService.UUID,
	}

	histories, err := s.virtualServiceRepo.GetHistoryPage(cond, "version DESC", r.Page, r.PageSize)
	if err != nil {
		return pm, err
	}

	// 获取总数
	total, err := s.virtualServiceRepo.CountHistory(cond)
	if err != nil {
		return pm, err
	}

	var historyResponses []*VirtualServiceHistoryResponse
	for _, history := range histories {
		historyResponses = append(historyResponses, &VirtualServiceHistoryResponse{
			ID:                 history.UUID,
			UUID:               history.UUID,
			VirtualServiceUUID: history.VirtualServiceUUID,
			Name:               history.Name,
			Namespace:          history.Namespace,
			Cluster:            history.Cluster,
			Content:            history.Content,
			ResourceVersion:    history.ResourceVersion,
			Creator:            history.Creator,
			Modifier:           history.Modifier,
			CreatedTime:        history.GmtCreate.Format("2006-01-02T15:04:05Z07:00"),
			ModifiedTime:       history.GmtModified.Format("2006-01-02T15:04:05Z07:00"),
		})
	}

	pm.List = historyResponses
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	return pm, nil
}

// ApproveVirtualService 审批通过VirtualService
func (s *VirtualService) ApproveVirtualService(uuid, content, comment, reviewer string, admin bool) error {
	// 检查管理员权限
	if !admin {
		return response.PermissionDeniedError("")
	}

	// 查找VirtualService变更记录
	vs, err := s.virtualServiceRepo.GetFirst(models.VirtualService{
		BaseModel: models.BaseModel{UUID: uuid},
	})
	if err != nil {
		return fmt.Errorf("VirtualService变更记录不存在: %w", err)
	}

	if vs.Status != models.Pending {
		return fmt.Errorf("只有pending状态的VirtualService可以被审批")
	}

	// 如果有修改后的内容，更新数据库中的内容
	if content != "" {
		vs.Content = content
	}

	// 根据Action执行相应的K8s操作
	_, istioClient, err := s.mgr.GetClusterClient(vs.Cluster)
	if err != nil {
		return fmt.Errorf("获取集群客户端失败: %w", err)
	}

	// 先查询线上版本做一个历史记录
	onlineVS, _ := s.GetOnlineVirtualService(GetRequest{
		Cluster:   vs.Cluster,
		Namespace: vs.Namespace,
		Name:      vs.Name,
	})

	contentToApply := vs.Content
	if content != "" {
		contentToApply = content
	}

	switch vs.Action {
	case models.OperationTypeCreate:
		// 创建操作
		if err = s.ApplyYAMLV2(vs.Cluster, vs.Namespace, contentToApply); err != nil {
			return fmt.Errorf("创建VirtualService失败: %w", err)
		}
		log.Printf("Created VirtualService: %s.%s in cluster %s", vs.Name, vs.Namespace, vs.Cluster)

	case models.OperationTypeUpdate:
		// 更新操作
		if err = s.ApplyYAMLV2(vs.Cluster, vs.Namespace, contentToApply); err != nil {
			return fmt.Errorf("更新VirtualService失败: %w", err)
		}
		log.Printf("Updated VirtualService: %s.%s in cluster %s", vs.Name, vs.Namespace, vs.Cluster)

	case models.OperationTypeDelete:
		// 删除操作
		if err = istioClient.NetworkingV1().VirtualServices(vs.Namespace).
			Delete(context.TODO(), vs.Name, metav1.DeleteOptions{}); err != nil {
			if !apierrors.IsNotFound(err) {
				return fmt.Errorf("删除VirtualService失败: %w", err)
			}
		}
		log.Printf("Deleted VirtualService: %s.%s in cluster %s", vs.Name, vs.Namespace, vs.Cluster)
	}

	// 更新记录状态为active
	vs.Status = models.Active
	vs.Comment = comment
	vs.Update(reviewer)

	// 如果有修改后的内容，更新数据库中的内容
	if content != "" {
		vs.Content = content
	}

	// 发送审批通过通知
	s.sendApprovalNotification(vs, string(ApproveVirtualService), reviewer, comment)

	// 发送审批结果通知给申请人
	if err = s.notificationSvc.SendVirtualServiceApprovalResultNotification(
		vs.Name, vs.Namespace, vs.Cluster, string(ApproveVirtualService), reviewer, comment, vs.Creator,
	); err != nil {
		log.ErrorE("发送VirtualService审批结果通知失败", err)
	}

	// 保存审批前的历史记录
	if onlineVS != nil {
		// 获取下一个版本号
		nextVersion, err := s.virtualServiceRepo.GetNextVersion(onlineVS.Cluster, onlineVS.Namespace, onlineVS.Name)
		if err != nil {
			log.Warn("获取历史版本号失败", log.Any("error", err.Error()))
			nextVersion = 1
		}

		history := &models.VirtualServiceHistory{
			Name:               onlineVS.Name,
			Namespace:          onlineVS.Namespace,
			Cluster:            onlineVS.Cluster,
			Gateways:           onlineVS.Gateways,
			Hosts:              onlineVS.Hosts,
			Content:            onlineVS.Content,
			ResourceVersion:    onlineVS.ResourceVersion,
			VirtualServiceUUID: vs.UUID,
			Version:            nextVersion,
			Action:             vs.Action,
			Status:             onlineVS.Status,
			Operator:           reviewer,
			Comment:            fmt.Sprintf("审批%s操作前的备份：%s", string(vs.Action), comment),
		}
		history.Create(reviewer)
		if err = s.virtualServiceRepo.CreateHistory(history); err != nil {
			log.Warn("保存历史记录失败", log.Any("error", err.Error()))
		}
	}

	return s.virtualServiceRepo.Update(vs)
}

// Review 统一审批VirtualService（合并批准和拒绝逻辑）
func (s *VirtualService) Review(req ReviewVirtualServiceRequest, reviewer string, admin bool) error {

	// 调用独立的审批通过方法
	if req.Action == ApproveVirtualService {
		return s.ApproveVirtualService(req.UUID, req.Content, req.Comment, reviewer, admin)
	}

	// 查找VirtualService变更记录
	vs, err := s.virtualServiceRepo.GetFirst(models.VirtualService{BaseModel: models.BaseModel{UUID: req.UUID}})
	if err != nil {
		return fmt.Errorf("VirtualService变更记录不存在: %w", err)
	}

	if vs.Status != models.Pending {
		return fmt.Errorf("只有pending状态的VirtualService可以被审批")
	}

	// 如果有修改后的内容，更新数据库中的内容
	if req.Content != "" {
		vs.Content = req.Content
	}

	vs.Status = models.Rejected
	vs.Comment = req.Comment
	vs.Update(reviewer)

	// 发送审批拒绝通知
	s.sendApprovalNotification(vs, string(RejectVirtualService), reviewer, req.Comment)

	// 发送审批结果通知给申请人
	if s.notificationSvc != nil {
		if err = s.notificationSvc.SendVirtualServiceApprovalResultNotification(vs.Name, vs.Namespace, vs.Cluster,
			"rejected", reviewer, req.Comment, vs.Creator); err != nil {
			log.ErrorE("发送VirtualService审批结果通知失败", err)
		}
	}

	return s.virtualServiceRepo.Update(vs)
}

// DryRun VirtualService Dry Run
func (s *VirtualService) DryRun(req DryRunVirtualServiceRequest) (*DryRunVirtualServiceResponse, error) {
	// 解析YAML内容
	var vs istiov1.VirtualService
	if err := k8syaml.Unmarshal([]byte(req.Content), &vs); err != nil {
		return &DryRunVirtualServiceResponse{
			Valid:   false,
			Message: "YAML格式错误: " + err.Error(),
		}, nil
	}

	// 验证必要字段
	if vs.Name == "" {
		return &DryRunVirtualServiceResponse{
			Valid:   false,
			Message: "VirtualService名称不能为空",
		}, nil
	}
	if vs.Namespace == "" {
		return &DryRunVirtualServiceResponse{
			Valid:   false,
			Message: "命名空间不能为空",
		}, nil
	}

	// 获取线上资源进行对比
	_, istioClient, err := s.mgr.GetClusterClient(req.Cluster)
	if err != nil {
		return &DryRunVirtualServiceResponse{
			Valid:   false,
			Message: "获取集群客户端失败: " + err.Error(),
		}, nil
	}

	onlineVS, err := istioClient.NetworkingV1().VirtualServices(req.Namespace).Get(context.TODO(), req.Name, metav1.GetOptions{})
	var diff string
	var warnings []string
	var oldContent string

	if err == nil {
		// 线上资源存在，生成差异对比
		onlineContent, _ := yaml.Marshal(onlineVS)
		oldContent = string(onlineContent)
		diff = s.generateDiff(oldContent, req.Content)
		//warnings = append(warnings, "线上已存在同名VirtualService")
	} else {
		// 线上资源不存在，这是新增
		diff = req.Content
		//warnings = append(warnings, "这是新增的VirtualService配置")
	}

	return &DryRunVirtualServiceResponse{
		Valid:      true,
		Message:    "Dry Run 验证通过",
		Diff:       diff,
		Warnings:   warnings,
		OldContent: oldContent,
	}, nil
}

// GetOnlineVirtualService 获取线上VirtualService
func (s *VirtualService) GetOnlineVirtualService(r GetRequest) (*models.VirtualService, error) {
	// 获取集群配置
	config, err := s.mgr.GetClusterConfig(r.Cluster)
	if err != nil {
		return nil, fmt.Errorf("获取集群配置失败: %w", err)
	}

	// 创建动态客户端，直接获取原始YAML避免omitempty问题
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 设置 VirtualService 的 GVR
	gvr := schema.GroupVersionResource{
		Version:  "v1",
		Resource: "virtualservices",
		Group:    "networking.istio.io",
	}

	// 获取线上VirtualService
	unstructuredVS, err := dynamicClient.Resource(gvr).Namespace(r.Namespace).Get(context.TODO(), r.Name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取线上VirtualService失败: %w", err)
	}

	// 确保设置正确的 GVK (避免某些情况下缺失)
	unstructuredVS.SetGroupVersionKind(schema.GroupVersionKind{
		Version: "v1",
		Kind:    "VirtualService",
		Group:   "networking.istio.io",
	})

	// 转换为YAML格式 - 这样可以保留所有字段，包括值为0的字段
	content, err := yaml.Marshal(unstructuredVS.Object)
	if err != nil {
		return nil, fmt.Errorf("序列化VirtualService失败: %w", err)
	}

	// 准备返回数据
	return &models.VirtualService{
		Name:      unstructuredVS.GetName(),
		Namespace: unstructuredVS.GetNamespace(),
		Cluster:   r.Cluster,
		Content:   string(content),
		Status:    "active",
	}, nil
}

// generateDiff 生成差异对比
func (s *VirtualService) generateDiff(oldContent, newContent string) string {
	// 这里可以使用更高级的diff库生成差异对比
	// 简化实现，直接返回新内容
	if oldContent == "" {
		return newContent
	}

	// 简单的差异对比实现
	// 在实际项目中可以使用github.com/sergi/go-diff等库
	return fmt.Sprintf("--- 线上版本\n+++ 申请版本\n%s", newContent)
}

// sendApprovalNotification 发送审批通知
func (s *VirtualService) sendApprovalNotification(vs *models.VirtualService, action, approver, comment string) {
	// 记录日志
	log.Info(fmt.Sprintf("VirtualService审批通知: %s %s by %s, comment: %s",
		vs.Name, action, approver, comment))
}
