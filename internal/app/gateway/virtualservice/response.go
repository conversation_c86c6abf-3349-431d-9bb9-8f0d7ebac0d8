package virtualservice

// DryRunVirtualServiceResponse VirtualService Dry Run响应
type DryRunVirtualServiceResponse struct {
	Valid      bool     `json:"valid"`      // 是否有效
	Message    string   `json:"message"`    // 消息
	Diff       string   `json:"diff"`       // 差异对比
	Warnings   []string `json:"warnings"`   // 警告信息
	OldContent string   `json:"oldContent"` // 线上版本内容
}

// VirtualServiceHistoryResponse VirtualService历史版本响应
type VirtualServiceHistoryResponse struct {
	ID                 string `json:"id"`                 // 历史记录ID
	UUID               string `json:"uuid"`               // 历史记录UUID
	Version            int    `json:"version"`            // 版本号
	VirtualServiceUUID string `json:"virtualServiceUUID"` // VirtualService UUID
	Name               string `json:"name"`               // VirtualService名称
	Namespace          string `json:"namespace"`          // 命名空间
	Cluster            string `json:"cluster"`            // 集群
	Content            string `json:"content"`            // YAML内容
	Status             string `json:"status"`             // 状态
	ResourceVersion    string `json:"resourceVersion"`    // Kubernetes资源版本
	Creator            string `json:"creator"`            // 创建者
	Modifier           string `json:"modifier"`           // 修改者
	CreatedTime        string `json:"createdTime"`        // 创建时间
	ModifiedTime       string `json:"modifiedTime"`       // 修改时间
}
