package virtualservice

import (
	"pilot-api/internal/pkg/middleware"

	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	stdErrors "github.com/pkg/errors"
	"gorm.io/gorm"
)

func GetVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req GetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	detail, err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).GetVirtualService(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, detail)
}

func ListVirtualServices(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).ListVirtualServices(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}

func AddVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddVirtualServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	if err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).
		Add(req, c.GetString(util.Email)); err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, nil)
}

func UpdateVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateVirtualServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).Update(req, c.GetString(util.Email))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, nil)
}

func DeleteVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req DeleteVirtualServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).
		Delete(req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, nil)
}

// GetVirtualServiceHistory 获取VirtualService历史版本
func GetVirtualServiceHistory(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req VirtualServiceHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).GetVirtualServiceHistory(req)
	if err != nil && !stdErrors.Is(err, gorm.ErrRecordNotFound) {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}

// ReviewVirtualService 统一审批VirtualService（合并批准和拒绝）
func ReviewVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ReviewVirtualServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).
		Review(req, c.GetString(util.UserName),
			middleware.HasRole(c, middleware.PilotAdmin, middleware.RouteAdmin))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, nil)
}

// DryRunVirtualService VirtualService Dry Run
func DryRunVirtualService(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req DryRunVirtualServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	result, err := NewVirtualService(ts, mysql.GetDB(), redis.GetClient()).DryRun(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, result)
}
