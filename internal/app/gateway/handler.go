package gateway

import (
	"pilot-api/internal/pkg/response"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

func ListGateways(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewGatewayService(ts, mysql.GetDB(), redis.GetClient())
	list, err := svc.ListGateways(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}

func ListServices(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ListRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewGatewayService(ts, mysql.GetDB(), redis.GetClient())
	list, err := svc.ListServices(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}
