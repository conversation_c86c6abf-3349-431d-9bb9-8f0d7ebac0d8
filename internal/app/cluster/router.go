package cluster

import (
	"pilot-api/internal/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterRouter(r *gin.Engine) {
	clusterAPI := r.Group("/api/v1/cluster")
	{
		clusterAPI.Use(middleware.AuthUToken())
		clusterAPI.POST("/add", Add)         // 添加集群
		clusterAPI.POST("/list", List)       // 获取集群列表
		clusterAPI.POST("/info", Info)       // 获取集群信息
		clusterAPI.PUT("/update", Update)    // 更新集群
		clusterAPI.DELETE("/delete", Delete) // 删除集群

	}
	resourceAPI := clusterAPI.Group("/resources")
	{
		resourceAPI.POST("/namespaces", ListNamespaces) // 下拉框格式
		resourceAPI.POST("/deployments", ListDeployments)
	}
}
