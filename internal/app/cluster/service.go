package cluster

import (
	"context"
	"fmt"
	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.makeblock.com/makeblock-go/log"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	istio "istio.io/client-go/pkg/clientset/versioned"
	"k8s.io/client-go/kubernetes"
)

type ClusterService struct {
	*trace.Service
	repo *repos.ClusterRepo
}

func NewClusterService(ts *trace.Service, dbEngine *gorm.DB) *ClusterService {
	svc := &ClusterService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.repo = repos.NewClusterRepo(dbEngine.WithContext(ctx))
	return svc
}

func (s *ClusterService) Info(uuid string) (*InfoResponse, error) {
	m, err := s.repo.GetFirst(models.Cluster{BaseModel: models.BaseModel{UUID: uuid}})
	if err != nil {
		return nil, err
	}
	if m == nil {
		return nil, gorm.ErrRecordNotFound
	}
	s.decryptKubeConfig(m)
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *ClusterService) Add(r AddRequest, ops string) (*models.Cluster, error) {
	var m models.Cluster
	err := copier.Copy(&m, &r)
	if err != nil {
		return nil, err
	}
	m.Create(ops)
	s.encryptKubeConfig(&m)
	err = s.repo.Create(&m)
	if err != nil {
		return nil, err
	}

	if m.Enable != nil && *m.Enable {
		clusterMgr := cluster.GetClusterManager()
		_, _, err = clusterMgr.GetClusterClient(m.UUID)
		if err != nil {
			log.ErrorE("Failed to preload cluster client after add", err)
		} else {
			log.Printf("Added new cluster and preloaded cache: %s", m.UUID)
		}
	} else {
		log.Printf("Added new cluster (disabled, cache not preloaded): %s", m.UUID)
	}
	return &m, nil
}

func (s *ClusterService) Delete(ids []string) error {
	err := s.repo.DeleteInColumn(nil, "uuid", ids, true)
	if err != nil {
		return err
	}
	clusterMgr := cluster.GetClusterManager()
	clusterMgr.InvalidateClusterCache(ids...)
	log.Printf("Deleted clusters and invalidated cache: %v", ids)
	return nil
}

func (s *ClusterService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	m, err := s.repo.GetFirst(models.Cluster{BaseModel: models.BaseModel{UUID: r.UUID}})
	if err != nil {
		return 0, err
	}
	if m == nil {
		return 0, gorm.ErrRecordNotFound
	}
	if err = models.CheckVersionConflict(r.GmtModified, m.GmtModified); err != nil {
		return 0, err
	}
	if r.KubeConfig == util.Hide {
		r.KubeConfig = m.KubeConfig
	}
	err = copier.Copy(m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	s.encryptKubeConfig(m)
	err = s.repo.Save(m)
	if err != nil {
		return 0, err
	}
	clusterMgr := cluster.GetClusterManager()
	if r.KubeConfig != util.Hide {
		err = clusterMgr.RefreshClusterCache(r.UUID)
		if err != nil {
			log.ErrorE("Failed to refresh cluster cache after update", err)
		}
	} else {
		clusterMgr.InvalidateClusterCache(r.UUID)
	}
	log.Printf("Updated cluster and refreshed cache: %s", r.UUID)
	return rowsAffected, nil
}

func (s *ClusterService) List(r ListRequest) (*response.PageModel[*InfoResponse], error) {
	cond := models.Cluster{
		Enable: r.Enable,
	}
	clusters, err := s.repo.GetPage(&cond, "gmt_create DESC", r.Page, r.PageSize)
	if err != nil {
		return nil, err
	}
	total, err := s.repo.Count()
	if err != nil {
		return nil, err
	}

	var list []*InfoResponse
	for _, cluster := range clusters {
		s.decryptKubeConfig(cluster)
		var info InfoResponse
		err = copier.Copy(&info, cluster)
		if err != nil {
			return nil, err
		}
		info.KubeConfig = util.Hide
		list = append(list, &info)
	}

	return &response.PageModel[*InfoResponse]{
		List:     list,
		Total:    total,
		Page:     r.Page,
		PageSize: r.PageSize,
	}, nil
}

// GetClusterClient 获取集群客户端
func (s *ClusterService) GetClusterClient(clusterID string) (kubernetes.Interface, istio.Interface, error) {
	clusterMgr := cluster.GetClusterManager()
	return clusterMgr.GetClusterClient(clusterID)
}

// 加密kubeconfig
func (s *ClusterService) encryptKubeConfig(c *models.Cluster) {
	var err error
	c.KubeConfig, err = util.DefaultEncrypt(c.KubeConfig)
	if err != nil {
		log.ErrorE("encrypt kube config failed", err)
	}
}

// 解密kubeconfig
func (s *ClusterService) decryptKubeConfig(c *models.Cluster) {
	var err error
	c.KubeConfig, err = util.DefaultDecrypt(c.KubeConfig)
	if err != nil {
		log.ErrorE("decrypt kube config failed", err)
	}
}

// ListNamespaces 列出命名空间
func (s *ClusterService) ListNamespaces(r NamespaceRequest) (*response.PageModel[*NamespaceInfo], error) {
	// 获取集群客户端
	kubeClient, _, err := s.GetClusterClient(r.Cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster client: %w", err)
	}

	pm := &response.PageModel[*NamespaceInfo]{}

	namespaces, err := kubeClient.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return pm, err
	}

	var namespaceInfos []*NamespaceInfo
	for _, item := range namespaces.Items {
		// 过滤条件：如果提供了名称参数，则进行模糊搜索
		if r.Name != "" && !containsString(item.Name, r.Name) {
			continue
		}

		namespaceInfos = append(namespaceInfos, &NamespaceInfo{
			Name:        item.Name,
			Labels:      item.Labels,
			Status:      string(item.Status.Phase),
			CreatedTime: item.CreationTimestamp.Format("2006-01-02T15:04:05Z"),
		})
	}

	// 分页处理
	r.Page = util.Coalesce(r.Page, 1)
	r.PageSize = util.Coalesce(r.PageSize, models.MaxPageSize)
	total := int64(len(namespaceInfos))
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	if start >= len(namespaceInfos) {
		pm.List = []*NamespaceInfo{}
	} else {
		if end > len(namespaceInfos) {
			end = len(namespaceInfos)
		}
		pm.List = namespaceInfos[start:end]
	}

	return pm, nil
}

// ListDeployments 列出部署
func (s *ClusterService) ListDeployments(r ResourceListRequest) (*response.PageModel[*DeploymentInfo], error) {
	// 获取集群客户端
	kubeClient, _, err := s.GetClusterClient(r.Cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster client: %w", err)
	}

	pm := &response.PageModel[*DeploymentInfo]{}

	deployments, err := kubeClient.AppsV1().Deployments(r.Namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return pm, err
	}

	var deploymentInfos []*DeploymentInfo
	for _, item := range deployments.Items {
		// 过滤条件
		if r.Name != "" && !containsString(item.Name, r.Name) {
			continue
		}

		deploymentInfos = append(deploymentInfos, &DeploymentInfo{
			Name:              item.Name,
			Namespace:         item.Namespace,
			Labels:            item.Labels,
			Replicas:          *item.Spec.Replicas,
			ReadyReplicas:     item.Status.ReadyReplicas,
			AvailableReplicas: item.Status.AvailableReplicas,
			CreatedTime:       item.CreationTimestamp.Format("2006-01-02T15:04:05Z"),
		})
	}

	// 分页处理
	total := int64(len(deploymentInfos))
	pm.Total = total
	pm.Page = r.Page
	pm.PageSize = r.PageSize

	start := (r.Page - 1) * r.PageSize
	end := start + r.PageSize
	if start >= len(deploymentInfos) {
		pm.List = []*DeploymentInfo{}
	} else {
		if end > len(deploymentInfos) {
			end = len(deploymentInfos)
		}
		pm.List = deploymentInfos[start:end]
	}

	return pm, nil
}

// containsString 检查字符串是否包含子字符串（替代util.Contains）
func containsString(s, substr string) bool {
	return len(substr) == 0 || (len(s) >= len(substr) &&
		func() bool {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
			return false
		}())
}
