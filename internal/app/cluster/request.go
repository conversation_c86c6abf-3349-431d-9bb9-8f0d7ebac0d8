package cluster

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/pkg/models"
	"time"
)

type ListRequest struct {
	request.PageRequest
	Name   string `json:"name"`   //  名称
	Enable *bool  `json:"enable"` //  是否启用
}

type AddRequest struct {
	Name        string `gorm:"type:varchar(255);not null;uniqueIndex" json:"name" binding:"required"`
	KubeConfig  string `gorm:"type:text;not null" json:"kubeConfig" binding:"required"`
	Description string `gorm:"type:varchar(1024)" json:"description"`
	Enable      *bool  `gorm:"type:bool;default:false" json:"enable"`
}

type UpdateRequest struct {
	request.UpdateRequest
	AddRequest
}

type InfoResponse struct {
	Uuid        string      `json:"uuid"` //  uuid
	Name        string      `json:"name"` //  名称
	KubeConfig  string      `gorm:"type:text;not null" json:"kubeConfig"`
	Enable      bool        `gorm:"type:bool;default:false" json:"enable"`
	Description string      `gorm:"type:varchar(1024)" json:"description"`
	Creator     string      `json:"creator"`     //  创建人
	Modifier    string      `json:"modifier"`    //  修改人
	GmtCreate   models.Time `json:"gmtCreate"`   //  创建时间
	GmtModified time.Time   `json:"gmtModified"` //  修改时间
}

// 以下是从resource模块合并的请求结构体

// ListRequest represents the request for listing resources
type ResourceListRequest struct {
	request.PageRequest
	Cluster   string `json:"cluster" binding:"required"` // 集群标识
	Namespace string `json:"namespace"`                  // 命名空间
	Name      string `json:"name"`                       // 资源名称
}

// NamespaceRequest represents the request for listing namespaces
type NamespaceRequest struct {
	request.PageRequest
	Cluster string `json:"cluster" binding:"required"` // 集群标识
	Name    string `json:"name"`                       // 命名空间名称（用于搜索）
}

// GetRequest represents the request for getting a specific resource
type GetRequest struct {
	Cluster   string `json:"cluster" binding:"required"`   // 集群标识
	Namespace string `json:"namespace" binding:"required"` // 命名空间
	Name      string `json:"name" binding:"required"`      // 资源名称
}

// NamespaceInfo represents Kubernetes Namespace information
type NamespaceInfo struct {
	Name        string            `json:"name"`
	Labels      map[string]string `json:"labels"`
	Status      string            `json:"status"`
	CreatedTime string            `json:"createdTime"`
}

// ServiceInfo represents Kubernetes Service information
type ServiceInfo struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels"`
	ClusterIP   string            `json:"clusterIP"`
	Ports       []ServicePort     `json:"ports"`
	CreatedTime string            `json:"createdTime"`
}

// ServicePort represents service port information
type ServicePort struct {
	Name       string `json:"name"`
	Port       int32  `json:"port"`
	TargetPort string `json:"targetPort"`
	Protocol   string `json:"protocol"`
}

// DeploymentInfo represents Kubernetes Deployment information
type DeploymentInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Labels            map[string]string `json:"labels"`
	Replicas          int32             `json:"replicas"`
	ReadyReplicas     int32             `json:"readyReplicas"`
	AvailableReplicas int32             `json:"availableReplicas"`
	CreatedTime       string            `json:"createdTime"`
}
