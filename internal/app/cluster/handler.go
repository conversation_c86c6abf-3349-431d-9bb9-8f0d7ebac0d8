package cluster

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

// Info 获取集群信息
func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewClusterService(ts, mysql.GetDB())
	info, err := svc.Info(req.UUID)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	var ret InfoResponse
	err = copier.Copy(&ret, &info)
	if err != nil {
		response.ErrorResponse(c, err)
	}
	ret.KubeConfig = util.Hide
	response.OK(c, ret)
}

// Add 添加集群
func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewClusterService(ts, mysql.GetDB())
	info, err := svc.Add(req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

// Update 更新集群
func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewClusterService(ts, mysql.GetDB())
	info, err := svc.Update(req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

// List 获取集群列表
func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewClusterService(ts, mysql.GetDB())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, list)
}

func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewClusterService(ts, mysql.GetDB())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// ListNamespaces 列出命名空间
func ListNamespaces(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req NamespaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := NewClusterService(ts, mysql.GetDB())
	list, err := svc.ListNamespaces(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	// 转换为下拉框格式
	var ret []response.SelectOption
	for _, item := range list.List {
		ret = append(ret, response.SelectOption{
			Label: item.Name,
			Value: item.Name,
		})
	}
	response.OK(c, ret)
}

// ListDeployments 列出部署
func ListDeployments(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ResourceListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewClusterService(ts, mysql.GetDB())
	list, err := svc.ListDeployments(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, list)
}
