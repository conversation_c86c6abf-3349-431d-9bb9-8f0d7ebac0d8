package app

import (
	"pilot-api/internal/app/cluster"
	"pilot-api/internal/app/docs"
	"pilot-api/internal/app/gateway"
	"pilot-api/internal/app/gateway/dns"
	"pilot-api/internal/app/gateway/virtualservice"
	"pilot-api/internal/app/healthz"
	"pilot-api/internal/app/registry"
	"pilot-api/internal/app/security"
	"pilot-api/internal/app/workflow"

	"github.com/gin-gonic/gin"
)

func registerRouters(router *gin.Engine) {
	healthz.RegisterRouters(router)
	docs.RegisterRouters(router)
	gateway.RegisterRouter(router)
	virtualservice.RegisterRouter(router)
	dns.RegisterRouter(router)
	cluster.RegisterRouter(router)
	workflow.RegisterRouter(router)
	security.RegisterRouter(router)
	registry.RegisterRouter(router)
}
