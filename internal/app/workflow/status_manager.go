package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/pkg/models"
	"strings"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

const (
	HealthStatusHealthy   = "healthy"
	HealthStatusUnhealthy = "unhealthy"
)

// WorkflowStatusManager 工作流状态管理器
type WorkflowStatusManager struct {
	*trace.Service
	db *gorm.DB
}

// NewWorkflowStatusManager 创建工作流状态管理器
func NewWorkflowStatusManager(ts *trace.Service, db *gorm.DB) *WorkflowStatusManager {
	return &WorkflowStatusManager{
		Service: trace.IfNil(ts),
		db:      db,
	}
}

// WorkflowStateTransition 工作流状态转换
type WorkflowStateTransition struct {
	From      models.WorkflowExecutionStatus `json:"from"`
	To        models.WorkflowExecutionStatus `json:"to"`
	Timestamp time.Time                      `json:"timestamp"`
	Operator  string                         `json:"operator"`
	Reason    string                         `json:"reason"`
	Context   map[string]any                 `json:"context"`
}

// ValidateStatusTransition 验证状态转换是否合法
func (m *WorkflowStatusManager) ValidateStatusTransition(from, to models.WorkflowExecutionStatus) error {
	// 定义合法的状态转换规则
	validTransitions := map[models.WorkflowExecutionStatus][]models.WorkflowExecutionStatus{
		models.WorkflowExecutionStatusPending: {
			models.WorkflowExecutionStatusRunning,
			models.WorkflowExecutionStatusCanceled,
		},
		models.WorkflowExecutionStatusRunning: {
			models.WorkflowExecutionStatusPaused,
			models.WorkflowExecutionStatusCompleted,
			models.WorkflowExecutionStatusFailed,
			models.WorkflowExecutionStatusCanceled,
		},
		models.WorkflowExecutionStatusPaused: {
			models.WorkflowExecutionStatusRunning,
			models.WorkflowExecutionStatusCanceled,
		},
		models.WorkflowExecutionStatusCompleted: {
			// 完成状态是终态，不能转换到其他状态
		},
		models.WorkflowExecutionStatusFailed: {
			models.WorkflowExecutionStatusRunning, // 允许重试
		},
		models.WorkflowExecutionStatusCanceled: {
			models.WorkflowExecutionStatusRunning, // 允许重新启动
		},
	}

	allowedStates, exists := validTransitions[from]
	if !exists {
		return fmt.Errorf("unknown source status: %s", from)
	}

	for _, allowed := range allowedStates {
		if allowed == to {
			return nil
		}
	}

	return fmt.Errorf("invalid status transition from %s to %s", from, to)
}

// TransitionWorkflowStatus 执行工作流状态转换
func (m *WorkflowStatusManager) TransitionWorkflowStatus(
	ctx context.Context,
	executionID int64,
	newStatus models.WorkflowExecutionStatus,
	operator string,
	reason string,
	context map[string]any,
) error {
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 获取当前执行记录
		var execution models.WorkflowExecution
		if err := tx.Where("id = ?", executionID).First(&execution).Error; err != nil {
			return fmt.Errorf("failed to get workflow execution: %w", err)
		}

		// 验证状态转换
		if err := m.ValidateStatusTransition(execution.Status, newStatus); err != nil {
			return fmt.Errorf("status transition validation failed: %w", err)
		}

		// 记录状态转换历史
		transition := WorkflowStateTransition{
			From:      execution.Status,
			To:        newStatus,
			Timestamp: time.Now(),
			Operator:  operator,
			Reason:    reason,
			Context:   context,
		}

		// 更新执行状态
		oldStatus := execution.Status
		execution.Status = newStatus

		// 根据新状态设置时间字段
		switch newStatus {
		case models.WorkflowExecutionStatusRunning:
			if oldStatus == models.WorkflowExecutionStatusPending {
				execution.Start()
			} else if oldStatus == models.WorkflowExecutionStatusPaused {
				execution.Resume()
			}
		case models.WorkflowExecutionStatusCompleted:
			execution.Complete()
		case models.WorkflowExecutionStatusFailed:
			execution.Fail(reason)
		case models.WorkflowExecutionStatusCanceled:
			execution.Cancel()
		case models.WorkflowExecutionStatusPaused:
			execution.Pause()
		}

		// 将状态转换历史添加到日志中
		transitionJSON, _ := json.Marshal(transition)
		if execution.Logs != "" {
			execution.Logs += "\n" + string(transitionJSON)
		} else {
			execution.Logs = string(transitionJSON)
		}

		// 保存更新
		if err := tx.Save(&execution).Error; err != nil {
			return fmt.Errorf("failed to update workflow execution: %w", err)
		}

		log.Info(fmt.Sprintf("workflow execution %d status changed from %s to %s by %s",
			executionID, oldStatus, newStatus, operator))

		return nil
	})
}

// GetWorkflowStatusHistory 获取工作流状态历史
func (m *WorkflowStatusManager) GetWorkflowStatusHistory(executionID int64) ([]WorkflowStateTransition, error) {
	var execution models.WorkflowExecution
	if err := m.db.Where("id = ?", executionID).First(&execution).Error; err != nil {
		return nil, fmt.Errorf("failed to get workflow execution: %w", err)
	}

	var transitions []WorkflowStateTransition
	if execution.Logs != "" {
		// 解析日志中的状态转换记录
		logLines := strings.Split(execution.Logs, "\n")
		for _, line := range logLines {
			var transition WorkflowStateTransition
			if err := json.Unmarshal([]byte(line), &transition); err == nil {
				transitions = append(transitions, transition)
			}
		}
	}

	return transitions, nil
}

// RecoverFailedWorkflow 恢复失败的工作流
func (m *WorkflowStatusManager) RecoverFailedWorkflow(
	ctx context.Context,
	executionID int64,
	operator string,
	recoveryStrategy string,
) error {
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 获取失败的执行记录
		var execution models.WorkflowExecution
		if err := tx.Where("id = ?", executionID).First(&execution).Error; err != nil {
			return fmt.Errorf("failed to get workflow execution: %w", err)
		}

		if execution.Status != models.WorkflowExecutionStatusFailed {
			return fmt.Errorf("only failed workflows can be recovered")
		}

		// 根据恢复策略执行不同的恢复逻辑
		switch recoveryStrategy {
		case "retry_failed_step":
			return m.retryFailedStep(ctx, tx, &execution, operator)
		case "restart_from_beginning":
			return m.restartFromBeginning(ctx, tx, &execution, operator)
		case "continue_from_checkpoint":
			return m.continueFromCheckpoint(ctx, tx, &execution, operator)
		default:
			return fmt.Errorf("unknown recovery strategy: %s", recoveryStrategy)
		}
	})
}

// retryFailedStep 重试失败的步骤
func (m *WorkflowStatusManager) retryFailedStep(
	ctx context.Context,
	tx *gorm.DB,
	execution *models.WorkflowExecution,
	operator string,
) error {
	// 查找失败的步骤
	var failedSteps []models.StepExecution
	if err := tx.Where("execution_id = ? AND status = ?",
		execution.ID, models.StepStatusFailed).Find(&failedSteps).Error; err != nil {
		return fmt.Errorf("failed to find failed steps: %w", err)
	}

	// 重置失败步骤的状态
	for _, step := range failedSteps {
		step.Status = models.StepStatusPending
		step.ErrorMessage = ""
		step.RetryCount++

		if err := tx.Save(&step).Error; err != nil {
			return fmt.Errorf("failed to reset step status: %w", err)
		}
	}

	// 转换工作流状态为运行中
	return m.TransitionWorkflowStatus(ctx, execution.ID,
		models.WorkflowExecutionStatusRunning, operator,
		"retry failed steps", map[string]any{
			"strategy":           "retry_failed_step",
			"failed_steps_count": len(failedSteps),
		})
}

// restartFromBeginning 从头开始重启
func (m *WorkflowStatusManager) restartFromBeginning(
	ctx context.Context,
	tx *gorm.DB,
	execution *models.WorkflowExecution,
	operator string,
) error {
	// 重置所有步骤状态
	if err := tx.Model(&models.StepExecution{}).
		Where("execution_id = ?", execution.ID).
		Updates(map[string]any{
			"status":        models.StepStatusPending,
			"error_message": "",
			"start_time":    nil,
			"end_time":      nil,
			"output":        "",
		}).Error; err != nil {
		return fmt.Errorf("failed to reset step executions: %w", err)
	}

	// 重置服务状态
	if err := execution.UpdateServiceStatus("all", models.ServiceExecutionStatus{
		DeploymentStatus: "pending",
		HealthStatus:     "unknown",
		LastUpdated:      time.Now().Format(time.RFC3339),
	}); err != nil {
		return fmt.Errorf("failed to reset service status: %w", err)
	}

	// 转换工作流状态为运行中
	return m.TransitionWorkflowStatus(ctx, execution.ID,
		models.WorkflowExecutionStatusRunning, operator,
		"restart from beginning", map[string]any{
			"strategy": "restart_from_beginning",
		})
}

// continueFromCheckpoint 从检查点继续
func (m *WorkflowStatusManager) continueFromCheckpoint(
	ctx context.Context,
	tx *gorm.DB,
	execution *models.WorkflowExecution,
	operator string,
) error {
	// 查找最后一个成功的步骤作为检查点
	var lastSuccessStep models.StepExecution
	if err := tx.Where("execution_id = ? AND status = ?",
		execution.ID, models.StepStatusCompleted).
		Order("end_time DESC").First(&lastSuccessStep).Error; err != nil {
		// 如果没有成功的步骤，从头开始
		return m.restartFromBeginning(ctx, tx, execution, operator)
	}

	// 重置检查点之后的步骤
	if err := tx.Model(&models.StepExecution{}).
		Where("execution_id = ? AND start_time > ?",
			execution.ID, lastSuccessStep.EndTime).
		Updates(map[string]any{
			"status":        models.StepStatusPending,
			"error_message": "",
			"start_time":    nil,
			"end_time":      nil,
			"output":        "",
		}).Error; err != nil {
		return fmt.Errorf("failed to reset steps after checkpoint: %w", err)
	}

	// 转换工作流状态为运行中
	return m.TransitionWorkflowStatus(ctx, execution.ID,
		models.WorkflowExecutionStatusRunning, operator,
		"continue from checkpoint", map[string]any{
			"strategy":        "continue_from_checkpoint",
			"checkpoint_step": lastSuccessStep.StepID,
		})
}

// HealthCheckWorkflow 工作流健康检查
func (m *WorkflowStatusManager) HealthCheckWorkflow(executionID int64) (*WorkflowHealthStatus, error) {
	var execution models.WorkflowExecution
	if err := m.db.Where("id = ?", executionID).First(&execution).Error; err != nil {
		return nil, fmt.Errorf("failed to get workflow execution: %w", err)
	}

	// 获取步骤执行状态
	var stepExecutions []models.StepExecution
	if err := m.db.Where("execution_id = ?", executionID).Find(&stepExecutions).Error; err != nil {
		return nil, fmt.Errorf("failed to get step executions: %w", err)
	}

	// 获取服务状态
	serviceStatuses, err := execution.GetServiceStatuses()
	if err != nil {
		return nil, fmt.Errorf("failed to get service statuses: %w", err)
	}

	health := &WorkflowHealthStatus{
		ExecutionID:     executionID,
		OverallStatus:   execution.Status,
		HealthScore:     m.calculateHealthScore(execution, stepExecutions, serviceStatuses),
		LastChecked:     time.Now(),
		Issues:          []string{},
		Recommendations: []string{},
	}

	// 分析健康状况
	m.analyzeWorkflowHealth(health, execution, stepExecutions, serviceStatuses)

	return health, nil
}

// WorkflowHealthStatus 工作流健康状态
type WorkflowHealthStatus struct {
	ExecutionID     int64                          `json:"executionId"`
	OverallStatus   models.WorkflowExecutionStatus `json:"overallStatus"`
	HealthScore     float64                        `json:"healthScore"` // 0-1之间的健康分数
	LastChecked     time.Time                      `json:"lastChecked"`
	Issues          []string                       `json:"issues"`          // 发现的问题
	Recommendations []string                       `json:"recommendations"` // 建议的操作
	StepHealth      map[int64]string               `json:"stepHealth"`      // 步骤健康状态
	ServiceHealth   map[string]string              `json:"serviceHealth"`   // 服务健康状态
}

// calculateHealthScore 计算健康分数
func (m *WorkflowStatusManager) calculateHealthScore(
	_ models.WorkflowExecution,
	stepExecutions []models.StepExecution,
	serviceStatuses []models.ServiceExecutionStatus,
) float64 {
	if len(stepExecutions) == 0 {
		return 1.0 // 没有步骤时认为是健康的
	}

	score := 0.0

	// 基于步骤状态计算分数
	for _, step := range stepExecutions {
		switch step.Status {
		case models.StepStatusCompleted:
			score += 1.0
		case models.StepStatusRunning:
			score += 0.5
		case models.StepStatusFailed:
			score += 0.0
		default:
			score += 0.3
		}
	}

	stepScore := score / float64(len(stepExecutions))

	// 基于服务状态调整分数
	serviceScore := 1.0
	if len(serviceStatuses) > 0 {
		healthyServices := 0
		for _, service := range serviceStatuses {
			if service.HealthStatus == HealthStatusHealthy {
				healthyServices++
			}
		}
		serviceScore = float64(healthyServices) / float64(len(serviceStatuses))
	}

	// 综合分数（步骤分数占70%，服务分数占30%）
	return stepScore*0.7 + serviceScore*0.3
}

// analyzeWorkflowHealth 分析工作流健康状况
func (m *WorkflowStatusManager) analyzeWorkflowHealth(
	health *WorkflowHealthStatus,
	_ models.WorkflowExecution,
	stepExecutions []models.StepExecution,
	serviceStatuses []models.ServiceExecutionStatus,
) {
	health.StepHealth = make(map[int64]string)
	health.ServiceHealth = make(map[string]string)

	// 分析步骤健康状况
	for _, step := range stepExecutions {
		switch step.Status {
		case models.StepStatusFailed:
			health.Issues = append(health.Issues, fmt.Sprintf("Step %d failed: %s", step.StepID, step.ErrorMessage))
			health.Recommendations = append(health.Recommendations, fmt.Sprintf("Consider retrying step %d", step.StepID))
			health.StepHealth[step.StepID] = HealthStatusUnhealthy
		case models.StepStatusRunning:
			// 检查是否超时
			if time.Since(step.StartTime) > 30*time.Minute {
				health.Issues = append(health.Issues, fmt.Sprintf("Step %d has been running for over 30 minutes", step.StepID))
				health.Recommendations = append(health.Recommendations, fmt.Sprintf("Check if step %d is stuck", step.StepID))
				health.StepHealth[step.StepID] = "warning"
			} else {
				health.StepHealth[step.StepID] = HealthStatusHealthy
			}
		case models.StepStatusCompleted:
			health.StepHealth[step.StepID] = HealthStatusHealthy
		default:
			health.StepHealth[step.StepID] = "pending"
		}
	}

	// 分析服务健康状况
	for _, service := range serviceStatuses {
		switch service.HealthStatus {
		case HealthStatusUnhealthy:
			health.Issues = append(health.Issues, fmt.Sprintf("Service %s is unhealthy", service.ServiceName))
			health.Recommendations = append(health.Recommendations, fmt.Sprintf("Check service %s logs", service.ServiceName))
			health.ServiceHealth[service.ServiceName] = HealthStatusUnhealthy
		case HealthStatusHealthy:
			health.ServiceHealth[service.ServiceName] = HealthStatusHealthy
		default:
			health.ServiceHealth[service.ServiceName] = "unknown"
		}
	}

	// 基于总体健康分数提供建议
	if health.HealthScore < 0.3 {
		health.Recommendations = append(health.Recommendations, "Consider stopping and investigating the workflow")
	} else if health.HealthScore < 0.7 {
		health.Recommendations = append(health.Recommendations, "Monitor the workflow closely for potential issues")
	}
}
