# 工作流配置源系统

## 概述

配置源系统允许从不同的数据源（如canary配置、A/B测试配置等）创建工作流，实现配置的复用和标准化。

## 架构设计

### 核心接口

```go
type ConfigSourceProvider interface {
    GetSourceType() string
    LoadFromDatabase(ctx context.Context, db *gorm.DB, sourceID string) (interface{}, error)
    ValidateConfig(config interface{}) error
    ConvertToWorkflowConfig(config interface{}, req CreateWorkflowRequest) (*WorkflowConfigData, error)
}
```

### 支持的配置源类型

1. **canary** - 灰度发布配置
2. **abtest** - A/B测试配置

## 使用方法

### 1. 获取支持的配置源类型

```bash
GET /api/v1/workflow/config-sources
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "supportedSources": [
      {
        "type": "canary",
        "name": "灰度发布配置",
        "description": "从已有的灰度发布配置创建工作流"
      },
      {
        "type": "abtest",
        "name": "A/B测试配置",
        "description": "从A/B测试配置创建工作流"
      }
    ]
  }
}
```

### 2. 从配置源创建工作流

```bash
POST /api/v1/workflow/create-from-source
```

请求示例：
```json
{
  "name": "灰度发布工作流",
  "description": "基于canary配置的工作流",
  "type": "canary",
  "namespace": "default",
  "clusterId": "cluster-1",
  "config": {
    "sourceType": "canary",
    "sourceId": "canary-uuid-123"
  }
}
```

## 实现示例

### 1. Canary配置源提供者

```go
type CanaryConfigSourceProvider struct{}

func (c *CanaryConfigSourceProvider) GetSourceType() string {
    return "canary"
}

func (c *CanaryConfigSourceProvider) LoadFromDatabase(ctx context.Context, db *gorm.DB, sourceID string) (interface{}, error) {
    var canary models.Canary
    if err := db.Where("uuid = ? AND deleted = ?", sourceID, false).First(&canary).Error; err != nil {
        return nil, fmt.Errorf("canary configuration not found: %s", sourceID)
    }
    return &canary, nil
}

func (c *CanaryConfigSourceProvider) ConvertToWorkflowConfig(config interface{}, req CreateWorkflowRequest) (*WorkflowConfigData, error) {
    canary := config.(*models.Canary)

    // 转换canary配置为工作流配置
    return &WorkflowConfigData{
        Services: convertCanaryServices(canary.Services),
        Steps: buildCanarySteps(canary),
        // ... 其他配置
    }, nil
}
```

### 2. 注册配置源提供者

```go
func NewConfigSourceManager() *ConfigSourceManager {
    manager := &ConfigSourceManager{
        providers: make(map[string]ConfigSourceProvider),
    }

    // 注册配置源提供者
    manager.RegisterProvider(NewCanaryConfigSourceProvider())
    manager.RegisterProvider(NewABTestConfigSourceProvider())

    return manager
}
```

## 工作流程

1. **配置源注册** - 系统启动时注册所有配置源提供者
2. **加载配置** - 根据sourceType和sourceID从数据库加载原始配置
3. **配置验证** - 验证加载的配置是否有效
4. **配置转换** - 将原始配置转换为标准的工作流配置格式
5. **工作流创建** - 使用转换后的配置创建工作流和步骤

## 扩展新的配置源

要添加新的配置源类型，需要：

1. 实现 `ConfigSourceProvider` 接口
2. 在 `ConfigSourceManager` 中注册新的提供者
3. 添加相应的数据库模型和表结构

### 示例：添加蓝绿部署配置源

```go
type BlueGreenConfigSourceProvider struct{}

func (b *BlueGreenConfigSourceProvider) GetSourceType() string {
    return "bluegreen"
}

func (b *BlueGreenConfigSourceProvider) LoadFromDatabase(ctx context.Context, db *gorm.DB, sourceID string) (interface{}, error) {
    // 从数据库加载蓝绿部署配置
    var blueGreenConfig models.BlueGreenConfig
    // ... 实现加载逻辑
    return &blueGreenConfig, nil
}

// ... 实现其他接口方法
```

## 优势

1. **配置复用** - 避免重复配置，提高一致性
2. **标准化** - 统一的配置格式和验证规则
3. **可扩展** - 易于添加新的配置源类型
4. **解耦** - 配置源与工作流逻辑分离
5. **验证** - 内置配置验证机制

## 注意事项

1. 确保配置源ID的唯一性和有效性
2. 配置转换时需要处理版本兼容性
3. 数据库查询需要考虑性能和安全性
4. 配置验证应该覆盖所有必要字段
