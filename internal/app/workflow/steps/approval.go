package steps

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/pkg/models"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// ApprovalHandler 审批步骤处理器
// 支持发布审核和生产部署审核两种类型
type ApprovalHandler struct {
	*BaseStepHandler
}

func NewApprovalHandler(ts *trace.Service, db *gorm.DB) *ApprovalHandler {
	return &ApprovalHandler{
		BaseStepHandler: NewBaseStepHandler(ts, db),
	}
}

func (h *ApprovalHandler) Execute(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	if stepConfig.ApprovalConfig == nil {
		return fmt.Errorf("approval config is required")
	}

	config := stepConfig.ApprovalConfig

	// 确定审批类型
	approvalType := h.determineApprovalType(step.Name)
	log.Info(fmt.Sprintf("executing %s approval step: %s, approvers: %s",
		approvalType, step.Name, strings.Join(config.Approvers, ",")))

	// 发送审批通知
	h.sendApprovalNotification(config, approvalType, step.Name)

	// 如果是自动执行，直接通过
	if step.AutoExecute {
		execution.Output = fmt.Sprintf(`{"status": "auto_approved", "type": "%s"}`, approvalType)

		// 发送自动审批完成通知
		h.sendApprovalCompletedNotification(config, approvalType, step.Name, "auto_approved")
		return nil
	}

	// 服务级审批处理
	if len(config.ServiceApprovals) > 0 {
		h.processServiceApprovals(ctx, config.ServiceApprovals, approvalType)
	}

	// 否则等待人工审批
	execution.Status = models.StepStatusWaiting
	execution.Output = fmt.Sprintf(`{"status": "waiting_approval", "type": "%s"}`, approvalType)

	return nil
}

// determineApprovalType 根据步骤名称确定审批类型
func (h *ApprovalHandler) determineApprovalType(stepName string) string {
	stepNameLower := strings.ToLower(stepName)
	if strings.Contains(stepNameLower, "发布") || strings.Contains(stepNameLower, "release") {
		return "release_approval"
	}
	if strings.Contains(stepNameLower, "生产") || strings.Contains(stepNameLower, "production") {
		return "production_approval"
	}
	return "general_approval"
}

// sendApprovalNotification 发送审批通知
func (h *ApprovalHandler) sendApprovalNotification(config *models.ApprovalConfig, approvalType, stepName string) {
	// 构建通知消息
	message := fmt.Sprintf("【%s】需要您的审批\n步骤: %s\n审批人: %s",
		approvalType, stepName, strings.Join(config.Approvers, ", "))

	// 发送通知给审批人
	for _, approver := range config.Approvers {
		h.sendNotificationToUser(approver, message, "approval_request")
	}

	// 服务级审批通知
	for _, serviceApproval := range config.ServiceApprovals {
		serviceMessage := fmt.Sprintf("【服务审批】%s 需要审批\n服务: %s\n必需: %t",
			approvalType, serviceApproval.ServiceName, serviceApproval.Required)

		// 这里可以根据服务配置发送通知
		log.Info(fmt.Sprintf("service approval notification: %s", serviceMessage))
	}
}

// sendApprovalCompletedNotification 发送审批完成通知
func (h *ApprovalHandler) sendApprovalCompletedNotification(config *models.ApprovalConfig, approvalType, stepName, result string) {
	message := fmt.Sprintf("【%s】审批已完成\n步骤: %s\n结果: %s",
		approvalType, stepName, result)

	// 发送通知给所有相关人员
	for _, approver := range config.Approvers {
		h.sendNotificationToUser(approver, message, "approval_completed")
	}
}

// sendNotificationToUser 发送通知给用户
func (h *ApprovalHandler) sendNotificationToUser(user, message, notificationType string) {
	// 这里应该调用实际的通知发送逻辑
	// 可以是邮件、短信、企业微信、钉钉等
	log.Info(fmt.Sprintf("sending %s notification to %s: %s", notificationType, user, message))
}

func (h *ApprovalHandler) processServiceApprovals(_ context.Context, serviceApprovals []models.ServiceApproval, approvalType string) {
	// 处理服务级审批
	for _, approval := range serviceApprovals {
		if approval.Required {
			log.Info(fmt.Sprintf("processing required %s approval for service: %s", approvalType, approval.ServiceName))
			// 这里应该发送服务级审批通知
			h.sendServiceApprovalNotification(approval, approvalType)
		}
	}
}

// sendServiceApprovalNotification 发送服务级审批通知
func (h *ApprovalHandler) sendServiceApprovalNotification(approval models.ServiceApproval, approvalType string) {
	message := fmt.Sprintf("【服务审批】%s\n服务: %s\n必需: %t",
		approvalType, approval.ServiceName, approval.Required)

	log.Info(fmt.Sprintf("service approval notification: %s", message))
	// 这里可以根据服务的负责人发送通知
}

func (h *ApprovalHandler) Validate(step *models.WorkflowStep) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("invalid step config: %w", err)
	}

	if stepConfig.ApprovalConfig == nil {
		return fmt.Errorf("approval config is required")
	}

	if len(stepConfig.ApprovalConfig.Approvers) == 0 && len(stepConfig.ApprovalConfig.ServiceApprovals) == 0 {
		return fmt.Errorf("approvers are required")
	}

	return nil
}

func (h *ApprovalHandler) GetStatus(ctx context.Context, execution *models.StepExecution) (models.StepStatus, error) {
	return execution.Status, nil
}

func (h *ApprovalHandler) Cancel(ctx context.Context, execution *models.StepExecution) error {
	log.Info(fmt.Sprintf("canceling approval step: %d", execution.StepID))
	return nil
}

// ApproveStep 审批步骤（供外部调用）
func (h *ApprovalHandler) ApproveStep(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep, approver string, approved bool, comment string) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	approvalType := h.determineApprovalType(step.Name)

	if approved {
		execution.Status = models.StepStatusCompleted
		execution.Output = fmt.Sprintf(`{"status": "approved", "type": "%s", "approver": "%s", "comment": "%s"}`,
			approvalType, approver, comment)

		// 发送审批通过通知
		h.sendApprovalCompletedNotification(stepConfig.ApprovalConfig, approvalType, step.Name, "approved")
	} else {
		execution.Status = models.StepStatusFailed
		execution.Output = fmt.Sprintf(`{"status": "rejected", "type": "%s", "approver": "%s", "comment": "%s"}`,
			approvalType, approver, comment)

		// 发送审批拒绝通知
		h.sendApprovalCompletedNotification(stepConfig.ApprovalConfig, approvalType, step.Name, "rejected")
	}

	return nil
}
