package steps

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/istio"
	"pilot-api/internal/pkg/types"
	"pilot-api/pkg/models"
	"strings"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
)

/*
DeploymentHandler 实现了完整的 Kubernetes 部署功能，包括：

1. 灰度部署 (Canary Deployment)
   - 创建带有 -canary 后缀的部署
   - 设置较少的副本数进行灰度测试
   - 自动创建对应的 Service
   - 集成 Istio 进行流量分割

2. 生产部署 (Production Deployment)
   - 创建或更新主要的生产部署
   - 设置完整的副本数
   - 支持滚动更新策略
   - 100% 流量切换到新版本

3. 标准部署 (Standard Deployment)
   - 创建标准的 Kubernetes 部署
   - 适用于非灰度发布场景
   - 支持资源配置和环境变量

4. 健康检查 (Health Check)
   - 检查 Deployment 状态
   - 验证 Pod 就绪状态
   - 确保容器健康运行

5. 集成功能
   - 与 ClusterManager 集成，支持多集群部署
   - 与 Istio 集成，实现流量管理
   - 支持资源配置（CPU、内存）
   - 支持环境变量配置
   - 自动创建和更新 Kubernetes Service

使用示例：
```go
handler := NewDeploymentHandler(traceService, db)
config := &models.DeploymentConfig{
    ServiceName: "my-service",
    Image: "my-app:v1.0.0",
    Replicas: 3,
    CanaryReplicas: 1,
    Resources: map[string]string{
        "cpu_request": "100m",
        "memory_request": "128Mi",
        "cpu_limit": "500m",
        "memory_limit": "512Mi",
    },
    Environment: map[string]string{
        "CLUSTER_ID": "cluster-1",
        "NAMESPACE": "default",
    },
}

// 部署灰度版本
err := handler.deployCanaryVersion(ctx, config)
```
*/

// DeploymentHandler 部署步骤处理器
// 支持灰度部署和生产部署，包含流量分割功能
type DeploymentHandler struct {
	*BaseStepHandler
	istioManager   *istio.ClientManager
	clusterManager *cluster.ClusterManager
}

// NewDeploymentHandler 创建部署步骤处理器
func NewDeploymentHandler(ts *trace.Service, db *gorm.DB) *DeploymentHandler {
	return &DeploymentHandler{
		BaseStepHandler: NewBaseStepHandler(ts, db),
		istioManager:    istio.NewClientManager(),
		clusterManager:  cluster.GetClusterManager(),
	}
}

func (h *DeploymentHandler) Execute(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	if stepConfig.DeploymentConfig == nil {
		return fmt.Errorf("deployment config is required")
	}

	config := stepConfig.DeploymentConfig

	// 从canary配置中获取最新的镜像配置
	if err := h.updateImagesFromCanary(ctx, execution, config); err != nil {
		log.ErrorE("failed to update images from canary config", err)
		// 不阻塞执行，使用原始配置继续
	}

	// 确定部署类型
	deploymentType := h.determineDeploymentType(step.Name, config.Strategy)
	log.Info(fmt.Sprintf("executing %s deployment step: %s, service: %s, image: %s",
		deploymentType, step.Name, config.ServiceName, h.getMainImage(config.Images)))

	// 发送部署开始通知
	if err := h.sendDeploymentNotification(ctx, config, deploymentType, step.Name, "started"); err != nil {
		log.ErrorE("failed to send deployment start notification", err)
	}

	var deploymentResult *DeploymentResult
	var err error

	// 根据部署类型执行不同的部署逻辑
	switch deploymentType {
	case DeploymentTypeCanary:
		deploymentResult, err = h.deployCanary(ctx, config)
	case DeploymentTypeProduction:
		deploymentResult, err = h.deployProduction(ctx, config)
	default:
		deploymentResult, err = h.deployStandard(ctx, config)
	}

	if err != nil {
		// 发送部署失败通知
		if notifyErr := h.sendDeploymentNotification(ctx, config, deploymentType, step.Name, "failed"); notifyErr != nil {
			log.ErrorE("failed to send deployment failure notification", notifyErr)
		}
		return fmt.Errorf("deployment failed: %w", err)
	}

	// 健康检查
	if config.HealthCheck != nil {
		if err := h.performHealthCheck(ctx, config.HealthCheck, config.ServiceName); err != nil {
			// 发送健康检查失败通知
			if notifyErr := h.sendDeploymentNotification(ctx, config, deploymentType, step.Name, "health_check_failed"); notifyErr != nil {
				log.ErrorE("failed to send health check failure notification", notifyErr)
			}
			return fmt.Errorf("health check failed: %w", err)
		}
	}

	// 构建执行结果
	execution.Output = h.buildDeploymentOutput(deploymentResult, config, deploymentType)

	// 发送部署成功通知
	if err := h.sendDeploymentNotification(ctx, config, deploymentType, step.Name, "completed"); err != nil {
		log.ErrorE("failed to send deployment completion notification", err)
	}

	return nil
}

// DeploymentResult 部署结果
type DeploymentResult struct {
	Status            string            `json:"status"`
	ServiceName       string            `json:"serviceName"`
	Images            map[string]string `json:"images"` // 多镜像支持
	Replicas          int32             `json:"replicas"`
	Strategy          string            `json:"strategy"`
	CanaryReplicas    int32             `json:"canaryReplicas,omitempty"`
	TrafficSplit      *TrafficSplitInfo `json:"trafficSplit,omitempty"`
	HealthCheckPassed bool              `json:"healthCheckPassed"`
	Endpoints         []string          `json:"endpoints,omitempty"`
}

// TrafficSplitInfo 流量分割信息
type TrafficSplitInfo struct {
	Strategy     string `json:"strategy"`
	CanaryWeight int    `json:"canaryWeight,omitempty"`
	StableWeight int    `json:"stableWeight,omitempty"`
	Configured   bool   `json:"configured"`
}

// determineDeploymentType 根据步骤名称和策略确定部署类型
func (h *DeploymentHandler) determineDeploymentType(stepName, strategy string) string {
	stepNameLower := strings.ToLower(stepName)

	// 根据步骤名称判断
	if strings.Contains(stepNameLower, "灰度") || strings.Contains(stepNameLower, "canary") {
		return DeploymentTypeCanary
	}
	if strings.Contains(stepNameLower, "生产") || strings.Contains(stepNameLower, "production") {
		return DeploymentTypeProduction
	}

	// 根据策略判断
	if strategy == StrategyCanary {
		return DeploymentTypeCanary
	}

	return DeploymentTypeStandard
}

// deployCanary 执行灰度部署
func (h *DeploymentHandler) deployCanary(ctx context.Context, config *models.DeploymentConfig) (*DeploymentResult, error) {
	log.Info(fmt.Sprintf("executing canary deployment for service: %s, canary replicas: %d",
		config.ServiceName, config.CanaryReplicas))

	// 部署灰度版本
	if err := h.deployCanaryVersion(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to deploy canary version: %w", err)
	}

	// 配置流量分割
	trafficSplit, err := h.configCanaryTraffic(ctx, config)
	if err != nil {
		log.ErrorE("failed to configure canary traffic split", err)
		// 不阻塞部署，但记录错误
		trafficSplit = &TrafficSplitInfo{
			Strategy:   "canary",
			Configured: false,
		}
	}

	return &DeploymentResult{
		Status:            StatusDeployed,
		ServiceName:       config.ServiceName,
		Images:            config.Images,
		Replicas:          config.Replicas,
		Strategy:          StrategyCanary,
		CanaryReplicas:    config.CanaryReplicas,
		TrafficSplit:      trafficSplit,
		HealthCheckPassed: true,
	}, nil
}

// deployProduction 执行生产部署
func (h *DeploymentHandler) deployProduction(ctx context.Context, config *models.DeploymentConfig) (*DeploymentResult, error) {
	log.Info(fmt.Sprintf("executing production deployment for service: %s, replicas: %d",
		config.ServiceName, config.Replicas))

	// 部署生产版本
	if err := h.deployProductionVersion(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to deploy production version: %w", err)
	}

	// 配置生产流量（100%切换到新版本）
	trafficSplit, err := h.configProdTraffic(ctx, config)
	if err != nil {
		log.ErrorE("failed to configure production traffic split", err)
		trafficSplit = &TrafficSplitInfo{
			Strategy:   "production",
			Configured: false,
		}
	}

	return &DeploymentResult{
		Status:            StatusDeployed,
		ServiceName:       config.ServiceName,
		Images:            config.Images,
		Replicas:          config.Replicas,
		Strategy:          StrategyProduction,
		TrafficSplit:      trafficSplit,
		HealthCheckPassed: true,
	}, nil
}

// deployStandard 执行标准部署
func (h *DeploymentHandler) deployStandard(ctx context.Context, config *models.DeploymentConfig) (*DeploymentResult, error) {
	log.Info(fmt.Sprintf("executing standard deployment for service: %s, replicas: %d",
		config.ServiceName, config.Replicas))

	// 部署标准版本
	if err := h.deployStandardVersion(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to deploy standard version: %w", err)
	}

	return &DeploymentResult{
		Status:            StatusDeployed,
		ServiceName:       config.ServiceName,
		Images:            config.Images,
		Replicas:          config.Replicas,
		Strategy:          StrategyStandard,
		HealthCheckPassed: true,
	}, nil
}

// configCanaryTraffic 配置灰度流量分割
func (h *DeploymentHandler) configCanaryTraffic(ctx context.Context, config *models.DeploymentConfig) (*TrafficSplitInfo, error) {
	log.Info(fmt.Sprintf("configuring canary traffic split for service: %s", config.ServiceName))

	// 从环境变量获取namespace和clusterID
	namespace := h.getNamespaceFromConfig(config)
	clusterID := h.getClusterIDFromConfig(config)

	// 灰度发布模式
	canaryServices, err := h.getCanaryServices(ctx, config)
	if err != nil {
		log.ErrorE("failed to get canary services", err)
		return &TrafficSplitInfo{
			Strategy:   "canary",
			Configured: false,
		}, nil
	}

	// 配置灰度流量传播
	return h.configCanary(ctx, canaryServices, namespace, clusterID, config.ServiceName)
}

// configCanary 配置灰度流量
func (h *DeploymentHandler) configCanary(
	ctx context.Context,
	canaryServices []types.CanaryService,
	namespace, clusterID, currentServiceName string,
) (*TrafficSplitInfo, error) {
	log.Info(fmt.Sprintf("configuring canary traffic for service: %s", currentServiceName))

	// 查找当前服务在链路中的配置
	var currentService *types.CanaryService
	for i := range canaryServices {
		if canaryServices[i].ServiceName == currentServiceName {
			currentService = &canaryServices[i]
			break
		}
	}

	if currentService == nil {
		return nil, fmt.Errorf("current service %s not found in canary configuration", currentServiceName)
	}

	// 设置路由配置
	if currentService.RoutingConfig == nil {
		// 默认使用权重路由
		currentService.RoutingConfig = &types.ServiceRoutingConfig{
			Strategy: "weight",
			WeightRouting: &types.WeightRoutingConfig{
				CanaryWeight: currentService.TrafficRatio,
				StableWeight: 100 - currentService.TrafficRatio,
			},
		}
	}

	// 应用灰度Istio配置
	if clusterID != "" {
		if err := h.istioManager.ApplyChainIstioConfigs(ctx, canaryServices, namespace, clusterID); err != nil {
			return nil, fmt.Errorf("failed to apply canary Istio configs: %w", err)
		}
	}

	return &TrafficSplitInfo{
		Strategy:     "canary",
		CanaryWeight: int(currentService.TrafficRatio),
		StableWeight: int(100 - currentService.TrafficRatio),
		Configured:   true,
	}, nil
}

// configProdTraffic 配置生产流量分割
func (h *DeploymentHandler) configProdTraffic(ctx context.Context, config *models.DeploymentConfig) (*TrafficSplitInfo, error) {
	log.Info(fmt.Sprintf("configuring production traffic split for service: %s", config.ServiceName))

	// 从环境变量获取namespace和clusterID
	namespace := h.getNamespaceFromConfig(config)
	clusterID := h.getClusterIDFromConfig(config)

	// 检查是否为全链路部署
	if h.isCanaryDeployment(ctx, config) {
		// 全链路生产部署：清理所有灰度配置，100%切换到生产版本
		canaryServices, err := h.getCanaryServices(ctx, config)
		if err != nil {
			log.ErrorE("failed to get canary services, fallback to single service mode", err)
			return h.configureSingleServiceProductionTraffic(ctx, config, namespace, clusterID)
		}

		return h.configureCanaryProductionTraffic(ctx, canaryServices, namespace, clusterID)
	}

	// 单服务生产部署
	return h.configureSingleServiceProductionTraffic(ctx, config, namespace, clusterID)
}

// configureSingleServiceProductionTraffic 配置单服务生产流量
func (h *DeploymentHandler) configureSingleServiceProductionTraffic(ctx context.Context, config *models.DeploymentConfig, namespace, clusterID string) (*TrafficSplitInfo, error) {
	// 构建CanaryService来应用Istio配置（100%切换到新版本）
	canaryService := &types.CanaryService{
		ServiceName: config.ServiceName,
	}

	// 设置路由配置（100%切换到新版本）
	canaryService.RoutingConfig = &types.ServiceRoutingConfig{
		Strategy: "weight",
		WeightRouting: &types.WeightRoutingConfig{
			CanaryWeight: 100,
			StableWeight: 0,
		},
	}

	if clusterID != "" {
		if err := h.istioManager.ApplyServiceIstioConfigs(ctx, canaryService, namespace, clusterID); err != nil {
			return nil, fmt.Errorf("failed to apply Istio configs: %w", err)
		}
	}

	return &TrafficSplitInfo{
		Strategy:     "production",
		CanaryWeight: 100,
		StableWeight: 0,
		Configured:   true,
	}, nil
}

// configureCanaryProductionTraffic 配置灰度生产流量
func (h *DeploymentHandler) configureCanaryProductionTraffic(ctx context.Context, canaryServices []types.CanaryService, namespace, clusterID string) (*TrafficSplitInfo, error) {
	log.Info("configuring canary production traffic - switching 100% to production")

	// 清理所有灰度配置
	if clusterID != "" {
		if err := h.istioManager.CleanupChainIstioConfigs(ctx, canaryServices, namespace, clusterID); err != nil {
			log.ErrorE("failed to cleanup canary Istio configs", err)
			// 不阻塞生产部署，但记录错误
		}
	}

	return &TrafficSplitInfo{
		Strategy:     "canary-production",
		CanaryWeight: 100,
		StableWeight: 0,
		Configured:   true,
	}, nil
}

// isCanaryDeployment 判断是否为灰度部署
func (h *DeploymentHandler) isCanaryDeployment(_ context.Context, config *models.DeploymentConfig) bool {
	// 只支持灰度发布，始终返回true
	return true
}

// getCanaryServices 获取灰度服务配置
func (h *DeploymentHandler) getCanaryServices(_ context.Context, config *models.DeploymentConfig) ([]types.CanaryService, error) {
	// 这里应该从工作流上下文、数据库或配置中获取灰度服务配置
	// 为了演示，这里返回一个示例配置

	// 简单验证
	if config == nil {
		return nil, fmt.Errorf("deployment config cannot be nil")
	}

	canaryServices := []types.CanaryService{
		{
			ServiceName:  "service-a",
			Namespace:    h.getNamespaceFromConfig(config),
			Order:        0,
			TrafficRatio: 20, // 20%灰度流量
		},
		{
			ServiceName:  "service-b",
			Namespace:    h.getNamespaceFromConfig(config),
			Order:        1,
			Dependencies: []string{"service-a"},
		},
		{
			ServiceName:  "service-c",
			Namespace:    h.getNamespaceFromConfig(config),
			Order:        2,
			Dependencies: []string{"service-b"},
		},
	}

	return canaryServices, nil
}

func (h *DeploymentHandler) deployCanaryVersion(ctx context.Context, config *models.DeploymentConfig) error {
	log.Info(fmt.Sprintf("deploying canary version for service: %s, image: %s, replicas: %d",
		config.ServiceName, h.getMainImage(config.Images), config.CanaryReplicas))

	// 获取集群客户端
	clusterID := h.getClusterIDFromConfig(config)
	if clusterID == "" {
		return fmt.Errorf("cluster ID is required for deployment")
	}

	kubeClient, _, err := h.clusterManager.GetClusterClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster client: %w", err)
	}

	namespace := h.getNamespaceFromConfig(config)

	// 创建灰度部署
	deployment := h.buildCanaryDeployment(config, namespace)

	// 检查部署是否已存在
	existingDeployment, err := kubeClient.AppsV1().Deployments(namespace).Get(ctx, deployment.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的部署
			_, err = kubeClient.AppsV1().Deployments(namespace).Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("failed to create canary deployment: %w", err)
			}
			log.Info(fmt.Sprintf("created canary deployment: %s", deployment.Name))
		} else {
			return fmt.Errorf("failed to check existing deployment: %w", err)
		}
	} else {
		// 更新现有部署
		existingDeployment.Spec = deployment.Spec
		_, err = kubeClient.AppsV1().Deployments(namespace).Update(ctx, existingDeployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update canary deployment: %w", err)
		}
		log.Info(fmt.Sprintf("updated canary deployment: %s", deployment.Name))
	}

	// 等待部署就绪
	if err := h.waitForDeploymentReady(ctx, kubeClient, namespace, deployment.Name, 5*time.Minute); err != nil {
		return fmt.Errorf("canary deployment not ready: %w", err)
	}

	// 创建或更新服务
	service := h.buildService(config, namespace, "canary")
	if err := h.createOrUpdateService(ctx, kubeClient, service); err != nil {
		return fmt.Errorf("failed to create/update canary service: %w", err)
	}

	return nil
}

func (h *DeploymentHandler) deployProductionVersion(ctx context.Context, config *models.DeploymentConfig) error {
	log.Info(fmt.Sprintf("deploying production version for service: %s, image: %s, replicas: %d",
		config.ServiceName, h.getMainImage(config.Images), config.Replicas))

	// 获取集群客户端
	clusterID := h.getClusterIDFromConfig(config)
	if clusterID == "" {
		return fmt.Errorf("cluster ID is required for deployment")
	}

	kubeClient, _, err := h.clusterManager.GetClusterClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster client: %w", err)
	}

	namespace := h.getNamespaceFromConfig(config)

	// 创建生产部署
	deployment := h.buildProductionDeployment(config, namespace)

	// 检查部署是否已存在
	existingDeployment, err := kubeClient.AppsV1().Deployments(namespace).Get(ctx, deployment.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的部署
			_, err = kubeClient.AppsV1().Deployments(namespace).Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("failed to create production deployment: %w", err)
			}
			log.Info(fmt.Sprintf("created production deployment: %s", deployment.Name))
		} else {
			return fmt.Errorf("failed to check existing deployment: %w", err)
		}
	} else {
		// 更新现有部署
		existingDeployment.Spec = deployment.Spec
		_, err = kubeClient.AppsV1().Deployments(namespace).Update(ctx, existingDeployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update production deployment: %w", err)
		}
		log.Info(fmt.Sprintf("updated production deployment: %s", deployment.Name))
	}

	// 等待部署就绪
	if err := h.waitForDeploymentReady(ctx, kubeClient, namespace, deployment.Name, 10*time.Minute); err != nil {
		return fmt.Errorf("production deployment not ready: %w", err)
	}

	// 创建或更新服务
	service := h.buildService(config, namespace, "stable")
	if err := h.createOrUpdateService(ctx, kubeClient, service); err != nil {
		return fmt.Errorf("failed to create/update production service: %w", err)
	}

	return nil
}

func (h *DeploymentHandler) deployStandardVersion(ctx context.Context, config *models.DeploymentConfig) error {
	log.Info(fmt.Sprintf("deploying standard version for service: %s, image: %s, replicas: %d",
		config.ServiceName, h.getMainImage(config.Images), config.Replicas))

	// 获取集群客户端
	clusterID := h.getClusterIDFromConfig(config)
	if clusterID == "" {
		return fmt.Errorf("cluster ID is required for deployment")
	}

	kubeClient, _, err := h.clusterManager.GetClusterClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster client: %w", err)
	}

	namespace := h.getNamespaceFromConfig(config)

	// 创建标准部署
	deployment := h.buildStandardDeployment(config, namespace)

	// 检查部署是否已存在
	existingDeployment, err := kubeClient.AppsV1().Deployments(namespace).Get(ctx, deployment.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的部署
			_, err = kubeClient.AppsV1().Deployments(namespace).Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("failed to create standard deployment: %w", err)
			}
			log.Info(fmt.Sprintf("created standard deployment: %s", deployment.Name))
		} else {
			return fmt.Errorf("failed to check existing deployment: %w", err)
		}
	} else {
		// 更新现有部署
		existingDeployment.Spec = deployment.Spec
		_, err = kubeClient.AppsV1().Deployments(namespace).Update(ctx, existingDeployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update standard deployment: %w", err)
		}
		log.Info(fmt.Sprintf("updated standard deployment: %s", deployment.Name))
	}

	// 等待部署就绪
	if err := h.waitForDeploymentReady(ctx, kubeClient, namespace, deployment.Name, 10*time.Minute); err != nil {
		return fmt.Errorf("standard deployment not ready: %w", err)
	}

	// 创建或更新服务
	service := h.buildService(config, namespace, "stable")
	if err := h.createOrUpdateService(ctx, kubeClient, service); err != nil {
		return fmt.Errorf("failed to create/update standard service: %w", err)
	}

	return nil
}

func (h *DeploymentHandler) performHealthCheck(ctx context.Context, healthCheck *models.WorkflowServiceHealthCheck, serviceName string) error {
	log.Info(fmt.Sprintf("performing health check for service: %s, path: %s", serviceName, healthCheck.Path))

	// 获取集群客户端
	clusterID := h.getClusterIDFromEnvironment()
	if clusterID == "" {
		return fmt.Errorf("cluster ID is required for health check")
	}

	kubeClient, _, err := h.clusterManager.GetClusterClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster client: %w", err)
	}

	namespace := h.getNamespaceFromEnvironment()

	// 检查部署状态
	deploymentName := serviceName + "-canary"
	deployment, err := kubeClient.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get deployment %s: %w", deploymentName, err)
	}

	// 检查副本是否就绪
	if deployment.Status.ReadyReplicas != *deployment.Spec.Replicas {
		return fmt.Errorf("deployment %s not ready: %d/%d replicas ready",
			deploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
	}

	// 检查Pod状态
	pods, err := kubeClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s,version=canary", serviceName),
	})
	if err != nil {
		return fmt.Errorf("failed to list pods: %w", err)
	}

	for _, pod := range pods.Items {
		if pod.Status.Phase != corev1.PodRunning {
			return fmt.Errorf("pod %s is not running: %s", pod.Name, pod.Status.Phase)
		}

		// 检查容器就绪状态
		for _, containerStatus := range pod.Status.ContainerStatuses {
			if !containerStatus.Ready {
				return fmt.Errorf("container %s in pod %s is not ready", containerStatus.Name, pod.Name)
			}
		}
	}

	log.Info(fmt.Sprintf("health check passed for service: %s", serviceName))
	return nil
}

// getNamespaceFromConfig 从配置中获取namespace
func (h *DeploymentHandler) getNamespaceFromConfig(config *models.DeploymentConfig) string {
	if config.Environment != nil {
		if ns, ok := config.Environment["NAMESPACE"]; ok {
			return ns
		}
	}
	return DefaultNamespace
}

// getClusterIDFromConfig 从配置中获取clusterID
func (h *DeploymentHandler) getClusterIDFromConfig(config *models.DeploymentConfig) string {
	if config.Environment != nil {
		if cid, ok := config.Environment["CLUSTER_ID"]; ok {
			return cid
		}
	}
	return ""
}

// buildDeploymentOutput 构建部署输出
func (h *DeploymentHandler) buildDeploymentOutput(result *DeploymentResult, _ *models.DeploymentConfig, deploymentType string) string {
	output, _ := json.Marshal(map[string]any{
		"status":            result.Status,
		"deploymentType":    deploymentType,
		"serviceName":       result.ServiceName,
		"images":            result.Images,
		"replicas":          result.Replicas,
		"strategy":          result.Strategy,
		"canaryReplicas":    result.CanaryReplicas,
		"trafficSplit":      result.TrafficSplit,
		"healthCheckPassed": result.HealthCheckPassed,
		"endpoints":         result.Endpoints,
	})
	return string(output)
}

// sendDeploymentNotification 发送部署通知
func (h *DeploymentHandler) sendDeploymentNotification(ctx context.Context, config *models.DeploymentConfig, deploymentType, stepName, status string) error {
	var message string

	switch status {
	case "started":
		message = fmt.Sprintf("【%s】部署开始\n步骤: %s\n服务: %s\n镜像: %s",
			deploymentType, stepName, config.ServiceName, h.getMainImage(config.Images))
	case "completed":
		message = fmt.Sprintf("【%s】部署完成\n步骤: %s\n服务: %s\n镜像: %s\n副本数: %d",
			deploymentType, stepName, config.ServiceName, h.getMainImage(config.Images), config.Replicas)
	case "failed":
		message = fmt.Sprintf("【%s】部署失败\n步骤: %s\n服务: %s\n镜像: %s",
			deploymentType, stepName, config.ServiceName, h.getMainImage(config.Images))
	case "health_check_failed":
		message = fmt.Sprintf("【%s】健康检查失败\n步骤: %s\n服务: %s",
			deploymentType, stepName, config.ServiceName)
	default:
		message = fmt.Sprintf("【%s】状态更新\n步骤: %s\n服务: %s\n状态: %s",
			deploymentType, stepName, config.ServiceName, status)
	}

	// 发送通知
	log.Info(fmt.Sprintf("sending deployment notification: %s", message))

	// 这里应该调用实际的通知发送逻辑
	return h.sendNotificationToUsers(ctx, message, "deployment_notification")
}

// sendNotificationToUsers 发送通知给用户
func (h *DeploymentHandler) sendNotificationToUsers(_ context.Context, message, notificationType string) error {
	// 这里应该调用实际的通知发送逻辑
	// 可以是邮件、短信、企业微信、钉钉等
	log.Info(fmt.Sprintf("sending %s notification: %s", notificationType, message))
	return nil
}

func (h *DeploymentHandler) Validate(step *models.WorkflowStep) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("invalid step config: %w", err)
	}

	if stepConfig.DeploymentConfig == nil {
		return fmt.Errorf("deployment config is required")
	}

	config := stepConfig.DeploymentConfig
	if len(config.Images) == 0 {
		return fmt.Errorf("images are required")
	}

	if config.ServiceName == "" {
		return fmt.Errorf("service name is required")
	}

	if config.Replicas <= 0 {
		return fmt.Errorf("replicas must be greater than 0")
	}

	// 如果是灰度部署，验证灰度副本数
	if config.Strategy == StrategyCanary && config.CanaryReplicas <= 0 {
		return fmt.Errorf("canary replicas must be greater than 0 for canary strategy")
	}

	return nil
}

func (h *DeploymentHandler) GetStatus(ctx context.Context, execution *models.StepExecution) (models.StepStatus, error) {
	// 检查部署状态
	return execution.Status, nil
}

func (h *DeploymentHandler) Cancel(ctx context.Context, execution *models.StepExecution) error {
	// 取消部署
	log.Info(fmt.Sprintf("canceling deployment step: %d", execution.StepID))
	return nil
}

// buildCanaryDeployment 构建灰度部署
func (h *DeploymentHandler) buildCanaryDeployment(config *models.DeploymentConfig, namespace string) *appsv1.Deployment {
	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      config.ServiceName + "-canary",
			Namespace: namespace,
			Labels: map[string]string{
				"app":                          config.ServiceName,
				"version":                      "canary",
				"app.kubernetes.io/name":       config.ServiceName,
				"app.kubernetes.io/version":    "canary",
				"app.kubernetes.io/component":  "canary",
				"app.kubernetes.io/managed-by": "pilot-api",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &config.CanaryReplicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":     config.ServiceName,
					"version": "canary",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":     config.ServiceName,
						"version": "canary",
					},
				},
				Spec: corev1.PodSpec{
					Containers: h.buildContainers(config),
				},
			},
		},
	}
}

// buildProductionDeployment 构建生产部署
func (h *DeploymentHandler) buildProductionDeployment(config *models.DeploymentConfig, namespace string) *appsv1.Deployment {
	replicas := config.Replicas
	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      config.ServiceName,
			Namespace: namespace,
			Labels: map[string]string{
				"app":                          config.ServiceName,
				"version":                      "stable",
				"app.kubernetes.io/name":       config.ServiceName,
				"app.kubernetes.io/version":    "stable",
				"app.kubernetes.io/component":  "production",
				"app.kubernetes.io/managed-by": "pilot-api",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":     config.ServiceName,
					"version": "stable",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":     config.ServiceName,
						"version": "stable",
					},
				},
				Spec: corev1.PodSpec{
					Containers: h.buildContainers(config),
				},
			},
		},
	}
}

// buildStandardDeployment 构建标准部署
func (h *DeploymentHandler) buildStandardDeployment(config *models.DeploymentConfig, namespace string) *appsv1.Deployment {
	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      config.ServiceName,
			Namespace: namespace,
			Labels: map[string]string{
				"app":                          config.ServiceName,
				"version":                      "stable",
				"app.kubernetes.io/name":       config.ServiceName,
				"app.kubernetes.io/version":    "stable",
				"app.kubernetes.io/component":  "standard",
				"app.kubernetes.io/managed-by": "pilot-api",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &config.Replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":     config.ServiceName,
					"version": "stable",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":     config.ServiceName,
						"version": "stable",
					},
				},
				Spec: corev1.PodSpec{
					Containers: h.buildContainers(config),
				},
			},
		},
	}
}

// buildService 构建Kubernetes服务
func (h *DeploymentHandler) buildService(config *models.DeploymentConfig, namespace, version string) *corev1.Service {
	serviceName := config.ServiceName
	if version == "canary" {
		serviceName = config.ServiceName + "-canary"
	}

	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceName,
			Namespace: namespace,
			Labels: map[string]string{
				"app":                          config.ServiceName,
				"version":                      version,
				"app.kubernetes.io/name":       config.ServiceName,
				"app.kubernetes.io/version":    version,
				"app.kubernetes.io/managed-by": "pilot-api",
			},
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app":     config.ServiceName,
				"version": version,
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "http",
					Port:       80,
					TargetPort: intstr.FromInt(8080),
					Protocol:   corev1.ProtocolTCP,
				},
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
}

// createOrUpdateService 创建或更新服务
func (h *DeploymentHandler) createOrUpdateService(ctx context.Context, kubeClient kubernetes.Interface, service *corev1.Service) error {
	existingService, err := kubeClient.CoreV1().Services(service.Namespace).Get(ctx, service.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新服务
			_, err = kubeClient.CoreV1().Services(service.Namespace).Create(ctx, service, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("failed to create service: %w", err)
			}
			log.Info(fmt.Sprintf("created service: %s", service.Name))
		} else {
			return fmt.Errorf("failed to check existing service: %w", err)
		}
	} else {
		// 更新现有服务
		existingService.Spec.Selector = service.Spec.Selector
		existingService.Spec.Ports = service.Spec.Ports
		existingService.Labels = service.Labels
		_, err = kubeClient.CoreV1().Services(service.Namespace).Update(ctx, existingService, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update service: %w", err)
		}
		log.Info(fmt.Sprintf("updated service: %s", service.Name))
	}
	return nil
}

// waitForDeploymentReady 等待部署就绪
func (h *DeploymentHandler) waitForDeploymentReady(ctx context.Context, kubeClient kubernetes.Interface, namespace, deploymentName string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		deployment, err := kubeClient.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("failed to get deployment: %w", err)
		}

		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas &&
			deployment.Status.UpdatedReplicas == *deployment.Spec.Replicas {
			log.Info(fmt.Sprintf("deployment %s is ready", deploymentName))
			return nil
		}

		log.Info(fmt.Sprintf("waiting for deployment %s to be ready: %d/%d replicas ready",
			deploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas))

		time.Sleep(10 * time.Second)
	}

	return fmt.Errorf("deployment %s not ready within timeout", deploymentName)
}

// buildResourceRequirements 构建资源需求
func (h *DeploymentHandler) buildResourceRequirements(resources map[string]string) corev1.ResourceRequirements {
	requirements := corev1.ResourceRequirements{
		Requests: make(corev1.ResourceList),
		Limits:   make(corev1.ResourceList),
	}

	// 设置默认资源
	requirements.Requests[corev1.ResourceCPU] = resource.MustParse("100m")
	requirements.Requests[corev1.ResourceMemory] = resource.MustParse("128Mi")
	requirements.Limits[corev1.ResourceCPU] = resource.MustParse("500m")
	requirements.Limits[corev1.ResourceMemory] = resource.MustParse("512Mi")

	// 从配置中覆盖资源
	if resources != nil {
		if cpuRequest, ok := resources["cpu_request"]; ok {
			requirements.Requests[corev1.ResourceCPU] = resource.MustParse(cpuRequest)
		}
		if memoryRequest, ok := resources["memory_request"]; ok {
			requirements.Requests[corev1.ResourceMemory] = resource.MustParse(memoryRequest)
		}
		if cpuLimit, ok := resources["cpu_limit"]; ok {
			requirements.Limits[corev1.ResourceCPU] = resource.MustParse(cpuLimit)
		}
		if memoryLimit, ok := resources["memory_limit"]; ok {
			requirements.Limits[corev1.ResourceMemory] = resource.MustParse(memoryLimit)
		}
	}

	return requirements
}

// buildEnvironmentVariables 构建环境变量
func (h *DeploymentHandler) buildEnvironmentVariables(envVars map[string]string) []corev1.EnvVar {
	var envs []corev1.EnvVar

	for key, value := range envVars {
		envs = append(envs, corev1.EnvVar{
			Name:  key,
			Value: value,
		})
	}

	return envs
}

// buildContainers 构建容器列表，支持多镜像部署
func (h *DeploymentHandler) buildContainers(config *models.DeploymentConfig) []corev1.Container {
	var containers []corev1.Container

	// 使用多镜像配置
	for containerName, image := range config.Images {
		container := corev1.Container{
			Name:  containerName,
			Image: image,
			Ports: []corev1.ContainerPort{
				{
					ContainerPort: 8080,
					Name:          "http",
				},
			},
			Resources: h.buildResourceRequirements(config.Resources),
			Env:       h.buildEnvironmentVariables(config.Environment),
		}
		containers = append(containers, container)
	}

	return containers
}

// getClusterIDFromEnvironment 从环境变量获取集群ID
func (h *DeploymentHandler) getClusterIDFromEnvironment() string {
	// 这里可以从全局配置或环境变量中获取
	// 暂时返回空字符串，实际使用时需要根据具体情况实现
	return ""
}

// getNamespaceFromEnvironment 从环境变量获取命名空间
func (h *DeploymentHandler) getNamespaceFromEnvironment() string {
	// 这里可以从全局配置或环境变量中获取
	// 暂时返回默认命名空间
	return "default"
}

// getMainImage 获取主镜像（用于日志和展示）
func (h *DeploymentHandler) getMainImage(images map[string]string) string {
	if len(images) == 0 {
		return ""
	}
	// 返回第一个镜像作为主镜像
	for _, image := range images {
		return image
	}
	return ""
}

// updateImagesFromCanary 从canary配置中更新镜像配置
func (h *DeploymentHandler) updateImagesFromCanary(ctx context.Context, execution *models.StepExecution, config *models.DeploymentConfig) error {
	// 获取workflow执行信息
	var workflowExecution models.WorkflowExecution
	if err := h.db.WithContext(ctx).Where("id = ?", execution.ExecutionID).First(&workflowExecution).Error; err != nil {
		return fmt.Errorf("failed to get workflow execution: %w", err)
	}

	// 获取workflow信息
	var workflow models.Workflow
	if err := h.db.WithContext(ctx).Where("id = ?", workflowExecution.WorkflowID).First(&workflow).Error; err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}

	// 如果workflow没有关联的canary配置，直接返回
	if workflow.Services == "" {
		return nil
	}

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("failed to parse services config: %w", err)
	}

	// 查找当前服务的配置
	var currentService *models.WorkflowServiceConfig
	for _, service := range services {
		if service.Name == config.ServiceName {
			currentService = &service
			break
		}
	}

	if currentService == nil {
		return fmt.Errorf("service %s not found in workflow services", config.ServiceName)
	}

	// 直接使用workflow中的配置更新部署配置
	log.Info(fmt.Sprintf("using workflow config for service %s", config.ServiceName))

	// 更新镜像配置
	if len(currentService.Images) > 0 {
		config.Images = currentService.Images
	}

	// 更新其他配置
	if currentService.Replicas > 0 {
		config.Replicas = currentService.Replicas
	}
	if len(currentService.Environment) > 0 {
		config.Environment = currentService.Environment
	}
	if len(currentService.Resources) > 0 {
		config.Resources = currentService.Resources
	}

	log.Info(fmt.Sprintf("updated deployment config for service %s with workflow config", config.ServiceName))
	return nil
}
