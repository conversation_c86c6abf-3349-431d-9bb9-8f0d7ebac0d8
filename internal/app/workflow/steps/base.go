package steps

import (
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// 清理类型常量
const (
	CleanupTypeCanary   = "canary_cleanup"
	CleanupTypeFull     = "full_cleanup"
	CleanupTypeIstio    = "istio_cleanup"
	CleanupTypeStandard = "standard_cleanup"
)

// 部署类型常量
const (
	DeploymentTypeCanary     = "canary_deployment"
	DeploymentTypeProduction = "production_deployment"
	DeploymentTypeStandard   = "standard_deployment"
)

// 部署策略常量
const (
	StrategyCanary     = "canary"
	StrategyProduction = "production"
	StrategyStandard   = "standard"
)

// 状态常量
const (
	StatusStarted   = "started"
	StatusCompleted = "completed"
	StatusFailed    = "failed"
	StatusDeployed  = "deployed"
)

// 默认值常量
const (
	DefaultNamespace = "default"
	DefaultService   = "default"
)

// BaseStepHandler 基础步骤处理器
type BaseStepHandler struct {
	*trace.Service
	db *gorm.DB
}

// NewBaseStepHandler 创建基础步骤处理器
func NewBaseStepHandler(ts *trace.Service, db *gorm.DB) *BaseStepHandler {
	return &BaseStepHandler{
		Service: trace.IfNil(ts),
		db:      db,
	}
}
