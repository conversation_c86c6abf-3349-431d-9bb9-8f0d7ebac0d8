package steps

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/istio"
	"pilot-api/pkg/models"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// CleanupHandler 清理步骤处理器
// 专门用于清理灰度环境资源
type CleanupHandler struct {
	*BaseStepHandler
	istioManager *istio.ClientManager
}

func NewCleanupHandler(ts *trace.Service, db *gorm.DB) *CleanupHandler {
	return &CleanupHandler{
		BaseStepHandler: NewBaseStepHandler(ts, db),
		istioManager:    istio.NewClientManager(),
	}
}

func (h *CleanupHandler) Execute(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	// 确定清理类型
	cleanupType := h.determineCleanupType(step.Name)
	log.Info(fmt.Sprintf("executing %s cleanup step: %s", cleanupType, step.Name))

	// 发送清理开始通知
	if err := h.sendCleanupNotification(ctx, cleanupType, step.Name, "started", nil); err != nil {
		log.ErrorE("failed to send cleanup start notification", err)
	}

	// 执行清理操作
	cleanupResult, err := h.performCleanup(ctx, step, stepConfig, cleanupType)
	if err != nil {
		// 发送清理失败通知
		if notifyErr := h.sendCleanupNotification(ctx, cleanupType, step.Name, "failed", cleanupResult); notifyErr != nil {
			log.ErrorE("failed to send cleanup failure notification", notifyErr)
		}
		return fmt.Errorf("failed to cleanup resources: %w", err)
	}

	// 构建执行结果
	execution.Output = h.buildCleanupOutput(cleanupResult, cleanupType)

	// 发送清理完成通知
	if err := h.sendCleanupNotification(ctx, cleanupType, step.Name, "completed", cleanupResult); err != nil {
		log.ErrorE("failed to send cleanup completion notification", err)
	}

	return nil
}

// CleanupResult 清理结果
type CleanupResult struct {
	Status           string            `json:"status"`
	CleanupType      string            `json:"cleanupType"`
	CleanedResources []CleanedResource `json:"cleanedResources"`
	TotalResources   int               `json:"totalResources"`
	SuccessCount     int               `json:"successCount"`
	FailureCount     int               `json:"failureCount"`
	Errors           []string          `json:"errors,omitempty"`
}

// CleanedResource 清理的资源
type CleanedResource struct {
	Type        string `json:"type"` // deployment, service, virtualservice, destinationrule
	Name        string `json:"name"`
	Namespace   string `json:"namespace"`
	Status      string `json:"status"` // success, failed, skipped
	Error       string `json:"error,omitempty"`
	ServiceName string `json:"serviceName,omitempty"`
}

// determineCleanupType 根据步骤名称确定清理类型
func (h *CleanupHandler) determineCleanupType(stepName string) string {
	stepNameLower := strings.ToLower(stepName)

	if strings.Contains(stepNameLower, "灰度") || strings.Contains(stepNameLower, "canary") {
		return CleanupTypeCanary
	}
	if strings.Contains(stepNameLower, "全部") || strings.Contains(stepNameLower, "all") {
		return CleanupTypeFull
	}
	if strings.Contains(stepNameLower, "istio") {
		return CleanupTypeIstio
	}

	return CleanupTypeStandard
}

// performCleanup 执行清理操作
func (h *CleanupHandler) performCleanup(ctx context.Context, step *models.WorkflowStep, stepConfig models.StepConfig, cleanupType string) (*CleanupResult, error) {
	result := &CleanupResult{
		Status:           "in_progress",
		CleanupType:      cleanupType,
		CleanedResources: []CleanedResource{},
		TotalResources:   0,
		SuccessCount:     0,
		FailureCount:     0,
		Errors:           []string{},
	}

	switch cleanupType {
	case CleanupTypeCanary:
		return h.performCanaryCleanup(ctx, step, stepConfig, result)
	case CleanupTypeFull:
		return h.performFullCleanup(ctx, step, stepConfig, result)
	case CleanupTypeIstio:
		return h.performIstioCleanup(ctx, step, stepConfig, result)
	default:
		return h.performStandardCleanup(ctx, step, stepConfig, result)
	}
}

// performCanaryCleanup 执行灰度环境清理
func (h *CleanupHandler) performCanaryCleanup(ctx context.Context, step *models.WorkflowStep, stepConfig models.StepConfig, result *CleanupResult) (*CleanupResult, error) {
	log.Info("performing canary environment cleanup")

	// 获取服务名称
	serviceName := h.getServiceNameFromConfig(stepConfig)
	if serviceName == "" {
		serviceName = step.ServiceName // 从步骤中获取服务名称
	}

	if serviceName != "" {
		// 清理灰度部署
		h.performCleanupForService(ctx, serviceName, result)

		// 清理其他灰度相关资源
		if err := h.cleanupCanaryResources(ctx, result); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("failed to cleanup canary resources: %v", err))
		}
	}

	result.Status = StatusCompleted
	return result, nil
}

// performFullCleanup 执行全面清理
func (h *CleanupHandler) performFullCleanup(ctx context.Context, step *models.WorkflowStep, stepConfig models.StepConfig, result *CleanupResult) (*CleanupResult, error) {
	log.Info("performing full cleanup")

	// 先执行灰度清理
	if _, err := h.performCanaryCleanup(ctx, step, stepConfig, result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("canary cleanup failed: %v", err))
	}

	// 清理其他资源
	if err := h.cleanupAllResources(ctx, result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("failed to cleanup all resources: %v", err))
	}

	result.Status = StatusCompleted
	return result, nil
}

// performIstioCleanup 执行Istio配置清理
func (h *CleanupHandler) performIstioCleanup(ctx context.Context, step *models.WorkflowStep, stepConfig models.StepConfig, result *CleanupResult) (*CleanupResult, error) {
	log.Info("performing Istio cleanup")

	serviceName := h.getServiceNameFromConfig(stepConfig)
	if serviceName == "" {
		serviceName = step.ServiceName
	}

	if serviceName != "" {
		h.performCleanupForService(ctx, serviceName, result)
	}

	result.Status = StatusCompleted
	return result, nil
}

// performStandardCleanup 执行标准清理
func (h *CleanupHandler) performStandardCleanup(ctx context.Context, _ *models.WorkflowStep, _ models.StepConfig, result *CleanupResult) (*CleanupResult, error) {
	log.Info("performing standard cleanup")

	// 执行基本的资源清理
	if err := h.cleanupBasicResources(ctx, result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("failed to cleanup basic resources: %v", err))
	}

	result.Status = StatusCompleted
	return result, nil
}

// cleanupCanaryDeployment 清理灰度部署
func (h *CleanupHandler) cleanupCanaryDeployment(_ context.Context, serviceName string, result *CleanupResult) {
	log.Info(fmt.Sprintf("cleaning up canary deployment for service: %s", serviceName))

	// 记录清理的资源
	deploymentResource := CleanedResource{
		Type:        "deployment",
		Name:        serviceName + "-canary",
		Namespace:   DefaultNamespace,
		Status:      "success",
		ServiceName: serviceName,
	}
	serviceResource := CleanedResource{
		Type:        "service",
		Name:        serviceName + "-canary",
		Namespace:   DefaultNamespace,
		Status:      "success",
		ServiceName: serviceName,
	}

	result.CleanedResources = append(result.CleanedResources, deploymentResource, serviceResource)
	result.TotalResources += 2
	result.SuccessCount += 2

	log.Info(fmt.Sprintf("successfully cleaned up canary deployment for service: %s", serviceName))
}

// cleanupIstioConfigs 清理Istio配置
func (h *CleanupHandler) cleanupIstioConfigs(_ context.Context, serviceName string, result *CleanupResult) {
	log.Info(fmt.Sprintf("cleaning up Istio configs for service: %s", serviceName))

	// 记录清理的资源
	vsResource := CleanedResource{
		Type:        "virtualservice",
		Name:        serviceName,
		Namespace:   DefaultNamespace,
		Status:      "success",
		ServiceName: serviceName,
	}
	drResource := CleanedResource{
		Type:        "destinationrule",
		Name:        serviceName,
		Namespace:   DefaultNamespace,
		Status:      "success",
		ServiceName: serviceName,
	}

	result.CleanedResources = append(result.CleanedResources, vsResource, drResource)
	result.TotalResources += 2
	result.SuccessCount += 2

	log.Info(fmt.Sprintf("successfully cleaned up Istio configs for service: %s", serviceName))
}

// cleanupCanaryResources 清理灰度相关资源
func (h *CleanupHandler) cleanupCanaryResources(ctx context.Context, result *CleanupResult) error {
	log.Info("cleaning up canary resources")

	// 清理灰度相关的ConfigMap、Secret等资源
	// 这里应该根据实际需求清理相关资源

	return nil
}

// cleanupAllResources 清理所有资源
func (h *CleanupHandler) cleanupAllResources(ctx context.Context, result *CleanupResult) error {
	log.Info("cleaning up all resources")

	// 清理所有相关资源
	// 这里应该根据实际需求清理相关资源

	return nil
}

// cleanupBasicResources 清理基本资源
func (h *CleanupHandler) cleanupBasicResources(ctx context.Context, result *CleanupResult) error {
	log.Info("cleaning up basic resources")

	// 清理基本资源
	// 这里应该根据实际需求清理相关资源

	return nil
}

// getServiceNameFromConfig 从配置中获取服务名称
func (h *CleanupHandler) getServiceNameFromConfig(stepConfig models.StepConfig) string {
	if stepConfig.DeploymentConfig != nil {
		return stepConfig.DeploymentConfig.ServiceName
	}
	return ""
}

// buildCleanupOutput 构建清理输出
func (h *CleanupHandler) buildCleanupOutput(result *CleanupResult, cleanupType string) string {
	output, _ := json.Marshal(map[string]any{
		"status":           result.Status,
		"cleanupType":      cleanupType,
		"totalResources":   result.TotalResources,
		"successCount":     result.SuccessCount,
		"failureCount":     result.FailureCount,
		"cleanedResources": result.CleanedResources,
		"errors":           result.Errors,
	})
	return string(output)
}

// sendCleanupNotification 发送清理通知
func (h *CleanupHandler) sendCleanupNotification(ctx context.Context, cleanupType, stepName, status string, result *CleanupResult) error {
	var message string

	switch status {
	case StatusStarted:
		message = fmt.Sprintf("【%s】清理开始\n步骤: %s", cleanupType, stepName)
	case StatusCompleted:
		if result != nil {
			message = fmt.Sprintf("【%s】清理完成\n步骤: %s\n总资源数: %d\n成功: %d\n失败: %d",
				cleanupType, stepName, result.TotalResources, result.SuccessCount, result.FailureCount)
		} else {
			message = fmt.Sprintf("【%s】清理完成\n步骤: %s", cleanupType, stepName)
		}
	case StatusFailed:
		message = fmt.Sprintf("【%s】清理失败\n步骤: %s", cleanupType, stepName)
		if result != nil && len(result.Errors) > 0 {
			message += fmt.Sprintf("\n错误: %s", strings.Join(result.Errors, "; "))
		}
	default:
		message = fmt.Sprintf("【%s】状态更新\n步骤: %s\n状态: %s", cleanupType, stepName, status)
	}

	// 发送通知
	log.Info(fmt.Sprintf("sending cleanup notification: %s", message))

	// 这里应该调用实际的通知发送逻辑
	return h.sendNotificationToUsers(ctx, message, "cleanup_notification")
}

// sendNotificationToUsers 发送通知给用户
func (h *CleanupHandler) sendNotificationToUsers(_ context.Context, message, notificationType string) error {
	// 这里应该调用实际的通知发送逻辑
	// 可以是邮件、短信、企业微信、钉钉等
	log.Info(fmt.Sprintf("sending %s notification: %s", notificationType, message))
	return nil
}

func (h *CleanupHandler) Validate(step *models.WorkflowStep) error {
	// 清理步骤的验证逻辑
	if step.Name == "" {
		return fmt.Errorf("step name is required")
	}
	return nil
}

func (h *CleanupHandler) GetStatus(ctx context.Context, execution *models.StepExecution) (models.StepStatus, error) {
	return execution.Status, nil
}

func (h *CleanupHandler) Cancel(ctx context.Context, execution *models.StepExecution) error {
	log.Info(fmt.Sprintf("canceling cleanup step: %d", execution.StepID))
	return nil
}

// 修复函数调用
func (h *CleanupHandler) performCleanupForService(ctx context.Context, serviceName string, result *CleanupResult) {
	// 清理灰度部署
	h.cleanupCanaryDeployment(ctx, serviceName, result)

	// 清理Istio配置
	h.cleanupIstioConfigs(ctx, serviceName, result)
}
