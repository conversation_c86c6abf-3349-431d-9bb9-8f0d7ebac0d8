# 工作流步骤重构说明

## 概述

本次重构将原有的8个步骤文件简化为4个核心步骤，专门用于支持全链路灰度发布工作流。重构后的步骤更加聚焦，每个步骤都集成了通知功能。

## 重构后的步骤架构

### 1. 基础步骤 (base.go)
- **功能**: 提供所有步骤的基础结构和共享功能
- **变更**: 保持不变
- **用途**: 作为其他步骤的基类

### 2. 审批步骤 (approval.go)
- **功能**: 支持发布审核和生产部署审核
- **增强功能**:
  - 自动识别审批类型（发布审核/生产部署审核）
  - 集成通知功能（审批请求、审批完成通知）
  - 支持服务级审批
  - 提供外部审批接口 `ApproveStep`
- **适用场景**:
  - 发布审核：灰度发布前的审批
  - 生产部署审核：生产环境部署前的审批

### 3. 部署步骤 (deployment.go)
- **功能**: 支持灰度部署和生产部署，包含流量分割功能
- **整合功能**:
  - 原有的部署功能
  - 流量分割功能（来自 traffic_split.go）
  - Istio配置管理
- **增强功能**:
  - 自动识别部署类型（灰度部署/生产部署/标准部署）
  - 集成通知功能（部署开始、完成、失败通知）
  - 支持健康检查
  - 自动配置流量分割
- **适用场景**:
  - 灰度发布部署：部署灰度版本并配置流量分割
  - 生产环境部署：部署生产版本并切换流量

### 4. 清理步骤 (cleanup.go)
- **功能**: 专门用于清理灰度环境资源
- **增强功能**:
  - 自动识别清理类型（灰度清理/全面清理/Istio清理）
  - 集成通知功能（清理开始、完成、失败通知）
  - 支持多种资源清理（Deployment、Istio配置等）
  - 详细的清理结果报告
- **适用场景**:
  - 清理灰度环境：清理灰度部署和相关Istio配置

## 删除的步骤

以下步骤已被删除，其功能已整合到其他步骤中：

1. **traffic_split.go** - 流量分割功能已整合到 `deployment.go` 中
2. **rollback.go** - 简化的工作流不需要独立的回滚步骤
3. **user_group.go** - 简化的工作流不需要用户组绑定功能
4. **notification.go** - 通知功能已集成到每个步骤中

## 全链路灰度发布工作流

重构后的步骤支持以下完整的灰度发布流程：

```
1. 发布审核 (ApprovalHandler)
   ├── 发送审批通知
   ├── 等待审批
   └── 发送审批结果通知

2. 灰度发布部署 (DeploymentHandler)
   ├── 发送部署开始通知
   ├── 部署灰度版本
   ├── 配置流量分割 (50%/50%)
   ├── 健康检查
   └── 发送部署完成通知

3. 生产部署审核 (ApprovalHandler)
   ├── 发送审批通知
   ├── 等待审批
   └── 发送审批结果通知

4. 生产环境部署 (DeploymentHandler)
   ├── 发送部署开始通知
   ├── 部署生产版本
   ├── 配置流量切换 (100%/0%)
   ├── 健康检查
   └── 发送部署完成通知

5. 清理灰度环境 (CleanupHandler)
   ├── 发送清理开始通知
   ├── 清理灰度Deployment
   ├── 清理Istio配置
   ├── 清理相关资源
   └── 发送清理完成通知
```

## 通知集成

每个步骤都集成了通知功能，支持：
- 步骤开始通知
- 步骤完成通知
- 步骤失败通知
- 状态变更通知

通知可以通过多种渠道发送：
- 邮件
- 短信
- 企业微信
- 钉钉
- Webhook

## 配置示例

### 审批步骤配置
```json
{
  "approvalConfig": {
    "approvers": ["user1", "user2"],
    "conditionType": "and",
    "timeout": 3600,
    "serviceApprovals": [
      {
        "serviceName": "my-service",
        "approvers": ["service-owner"],
        "required": true
      }
    ]
  }
}
```

### 部署步骤配置
```json
{
  "deploymentConfig": {
    "serviceName": "my-service",
    "image": "my-service:v1.2.0",
    "replicas": 3,
    "canaryReplicas": 1,
    "strategy": "canary",
    "environment": {
      "NAMESPACE": "production",
      "CLUSTER_ID": "cluster-1"
    },
    "healthCheck": {
      "path": "/health",
      "port": 8080,
      "initialDelaySeconds": 30,
      "periodSeconds": 10
    }
  }
}
```

### 清理步骤配置
```json
{
  "deploymentConfig": {
    "serviceName": "my-service",
    "environment": {
      "NAMESPACE": "production",
      "CLUSTER_ID": "cluster-1"
    }
  }
}
```

## 使用指南

1. **创建工作流**: 使用重构后的步骤类型创建工作流
2. **配置步骤**: 根据需要配置每个步骤的参数
3. **执行工作流**: 工作流引擎会自动执行各个步骤
4. **监控通知**: 通过集成的通知功能监控执行状态

## 兼容性说明

- 保持了与现有模型结构的兼容性
- 保持了与现有工作流引擎的兼容性
- 新增的功能都是向后兼容的扩展
