package workflow

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/pkg/models"

	"gorm.io/gorm"
)

// ListWorkflowRequest 工作流列表请求
type ListWorkflowRequest struct {
	request.PageRequest
	Name        string `json:"name"`
	ServiceName string `json:"serviceName"`
	Namespace   string `json:"namespace"`
	ClusterID   string `json:"clusterId"`
	Status      string `json:"status"`
	Type        string `json:"type"` // 工作流类型
}

// ExecuteWorkflowRequest 执行工作流请求
type ExecuteWorkflowRequest struct {
	WorkflowID int64 `json:"workflowId" binding:"required"`
}

// ApproveStepRequest 审批步骤请求
type ApproveStepRequest struct {
	ExecutionID int64  `json:"executionId" binding:"required"`
	StepID      int64  `json:"stepId" binding:"required"`
	Action      string `json:"action" binding:"required,oneof=approve reject"`
	Comment     string `json:"comment"`
}

// SimplifiedCreateWorkflowRequest 创建工作流请求
type CreateWorkflowRequest struct {
	Name        string              `json:"name" binding:"required"`      // 工作流名称
	Description string              `json:"description,omitempty"`        // 工作流描述
	Type        models.WorkflowType `json:"type" binding:"required"`      // 工作流类型
	Namespace   string              `json:"namespace" binding:"required"` // 命名空间
	ClusterID   string              `json:"clusterId" binding:"required"` // 集群ID
	Labels      map[string]string   `json:"labels,omitempty"`             // 标签

	Config map[string]any `json:"config" binding:"required"` // 配置参数
}

// 实现接口方法
func (r *CreateWorkflowRequest) GetName() string {
	return r.Name
}

func (r *CreateWorkflowRequest) GetDescription() string {
	return r.Description
}

func (r *CreateWorkflowRequest) GetNamespace() string {
	return r.Namespace
}

func (r *CreateWorkflowRequest) GetClusterID() string {
	return r.ClusterID
}

func (r *CreateWorkflowRequest) GetLabels() map[string]string {
	if r.Labels == nil {
		return make(map[string]string)
	}
	return r.Labels
}

func (r *CreateWorkflowRequest) GetConfig() map[string]any {
	return r.Config
}

func (r *CreateWorkflowRequest) GetType() models.WorkflowType {
	return r.Type
}

// GetDB 获取数据库实例
func (r *CreateWorkflowRequest) GetDB() *gorm.DB {
	return nil
}

// ListWorkflowExecutionsRequest 获取工作流执行列表请求
type ListWorkflowExecutionsRequest struct {
	request.PageRequest
	Status       string `json:"status"`       // 执行状态
	WorkflowName string `json:"workflowName"` // 工作流名称
	Executor     string `json:"executor"`     // 执行人
	StartTime    string `json:"startTime"`    // 开始时间
	EndTime      string `json:"endTime"`      // 结束时间
	WorkflowID   *int64 `json:"workflowId"`   // 工作流ID（可选，用于筛选特定工作流的执行）
}

// GetWorkflowTemplateRequest 获取工作流模板请求
type GetWorkflowTemplateRequest struct {
	Type     models.WorkflowType            `json:"type" binding:"required"`     // 工作流类型
	Services []models.WorkflowServiceConfig `json:"services" binding:"required"` // 服务配置（必需，用于生成步骤）
}

// WorkflowConfigTemplateRequest 获取工作流配置模板请求
type WorkflowConfigTemplateRequest struct {
	Type models.WorkflowType `json:"type" binding:"required"` // 工作流类型
}

// WorkflowConfigForm 工作流配置表单
type WorkflowConfigForm struct {
	Sections []ConfigSection `json:"sections"` // 配置区块
}

// ConfigSection 配置区块
type ConfigSection struct {
	Title       string        `json:"title"`       // 区块标题
	Description string        `json:"description"` // 区块描述
	Fields      []ConfigField `json:"fields"`      // 配置字段
}

// ConfigField 配置字段
type ConfigField struct {
	Key          string      `json:"key"`                    // 字段键名
	Label        string      `json:"label"`                  // 字段标签
	Type         string      `json:"type"`                   // 字段类型：text, number, select, boolean, array, object
	Required     bool        `json:"required"`               // 是否必填
	Default      any         `json:"default,omitempty"`      // 默认值
	Options      []Option    `json:"options,omitempty"`      // 选项（select类型）
	Validation   *Validation `json:"validation,omitempty"`   // 验证规则
	Description  string      `json:"description,omitempty"`  // 字段描述
	Placeholder  string      `json:"placeholder,omitempty"`  // 占位符
	Dependencies []string    `json:"dependencies,omitempty"` // 依赖字段
}

// Option 选项
type Option struct {
	Label string `json:"label"` // 选项标签
	Value any    `json:"value"` // 选项值
}

// Validation 验证规则
type Validation struct {
	Min    *float64 `json:"min,omitempty"`    // 最小值
	Max    *float64 `json:"max,omitempty"`    // 最大值
	MinLen *int     `json:"minLen,omitempty"` // 最小长度
	MaxLen *int     `json:"maxLen,omitempty"` // 最大长度
	Regex  string   `json:"regex,omitempty"`  // 正则表达式
}
