# Workflow Application Layer

## 概述

这是工作流系统的应用层实现，包含具体的业务逻辑和策略实现。它基于 `internal/pkg/workflow` 提供的抽象接口构建。

## 目录结构

```
internal/app/workflow/
├── workflow.go           # 应用层工作流引擎（包含业务逻辑）
├── service.go           # 工作流服务层
├── handler.go           # HTTP 处理器
├── router.go            # 路由定义
├── request.go           # 请求结构体
├── strategy/            # 工作流策略实现
│   ├── canary_strategy.go   # 灰度发布策略
│   └── abtest_strategy.go   # A/B测试策略
├── steps/               # 步骤处理器实现
│   ├── base.go          # 基础步骤处理器
│   ├── deployment.go    # 部署步骤处理器
│   ├── approval.go      # 审批步骤处理器
│   └── cleanup.go       # 清理步骤处理器
└── config/              # 配置管理系统
    ├── interfaces.go    # 配置接口定义
    ├── manager.go       # 配置源管理器
    ├── canary_provider.go   # Canary配置提供者
    └── abtest_provider.go   # A/B测试配置提供者
```

## 核心组件

### 1. WorkflowEngine (`workflow.go`)

应用层工作流引擎，继承了 `pkg/workflow` 的抽象引擎，并添加了具体的业务逻辑：

- **ExecuteCanaryWorkflow**: 灰度发布工作流执行逻辑
- **ExecuteSequentialWorkflow**: 顺序工作流执行逻辑
- 其他业务相关的辅助方法

**优化后的特点**：
- 移除了重复的回滚逻辑（已移至strategy层）
- 简化了执行逻辑，专注于业务流程编排
- 使用基础引擎的通用方法，避免重复实现

### 2. 策略实现 (`strategy/`)

#### CanaryStrategy
- 实现灰度发布工作流策略
- 使用简化的接口避免循环导入
- 包含完整的配置构建和回滚逻辑
- 委托具体业务逻辑给应用层引擎

#### ABTestStrategy
- 实现A/B测试工作流策略
- 独立的A/B测试执行逻辑

### 3. 步骤处理器 (`steps/`)

实现具体的步骤执行逻辑：
- **DeploymentHandler**: 处理部署步骤（包含流量分割功能）
- **ApprovalHandler**: 处理审批步骤
- **CleanupHandler**: 处理清理步骤

### 4. 服务层 (`service.go`)

提供工作流相关的业务服务：
- 工作流CRUD操作
- 工作流执行控制
- 策略注册和管理
- 配置转换辅助方法

**优化后的特点**：
- 移除了大量未使用的配置构建方法
- 保留了必要的配置转换逻辑
- 统一使用strategy中的方法

### 5. 配置管理系统 (`config/`)

统一的配置源管理系统：
- **ConfigSourceManager**: 配置源管理器
- **ConfigSourceProvider**: 配置源提供者接口
- **CanaryConfigSourceProvider**: Canary配置提供者
- **ABTestConfigSourceProvider**: A/B测试配置提供者

**优化后的特点**：
- 简化了配置转换逻辑
- 使用统一的错误处理
- 避免了与strategy层的重复实现

## 架构特点

### 1. 避免循环导入

通过定义简化的接口 `WorkflowEngineInterface` 来避免策略包和工作流包之间的循环导入：

```go
// strategy/canary_strategy.go
type WorkflowEngineInterface interface {
    ExecuteCanaryWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error
}
```

**优化**：移除了不必要的接口方法，只保留真正需要的方法。

### 2. 职责分离

- **抽象层** (`pkg/workflow`): 提供通用框架和接口
- **应用层** (`app/workflow`): 实现具体业务逻辑
- **策略层** (`app/workflow/strategy`): 实现不同的工作流策略
- **步骤层** (`app/workflow/steps`): 实现具体的步骤处理器
- **配置层** (`app/workflow/config`): 管理配置源和转换

### 3. 组合模式

应用层引擎通过组合抽象引擎来获得基础功能，同时添加自己的业务逻辑：

```go
type WorkflowEngine struct {
    *workflowpkg.WorkflowEngine  // 继承抽象引擎
}
```

### 4. 统一错误处理

所有组件都使用pkg/workflow/errors.go中定义的统一错误类型：

```go
if err != nil {
    return fmt.Errorf("%w: %v", workflowpkg.ErrConfigValidationFailed, err)
}
```

## 使用方式

### 1. 创建工作流服务

```go
service := NewWorkflowService(traceService, db)
```

### 2. 执行工作流

```go
execution, err := service.ExecuteWorkflow(req, operator)
```

### 3. 添加新的工作流策略

1. 在 `strategy/` 目录下创建新的策略文件
2. 实现 `workflowpkg.WorkflowStrategy` 接口
3. 在 `service.go` 中注册新策略

### 4. 添加新的步骤类型

1. 在 `steps/` 目录下创建新的步骤处理器
2. 实现 `workflowpkg.StepHandler` 接口
3. 在 `workflow.go` 中注册新的步骤处理器

### 5. 添加新的配置源

1. 在 `config/` 目录下实现 `ConfigSourceProvider` 接口
2. 在 `ConfigSourceManager` 中注册新的配置源提供者
3. 实现配置加载、验证和转换逻辑

## 优化成果

### 1. 消除重复代码
- 移除了service.go中8个未使用的配置构建方法
- 整合了两个WorkflowEngine中的重复方法
- 简化了config/canary_provider.go中的步骤生成逻辑

### 2. 简化接口
- WorkflowEngineInterface只保留1个必要方法（原来有2个）
- 移除了不必要的回滚接口

### 3. 统一错误处理
- 所有错误类型都使用pkg/workflow/errors.go中的定义
- 配置管理器使用统一的错误包装机制
- 支持错误类型检查和链式错误处理

### 4. 清理未使用类型
- 为未实现的StepType和WorkflowType添加了注释说明
- 保留了类型定义以支持未来扩展

### 5. 改进文档
- 更新了README文档，明确各层职责
- 添加了使用指南和扩展指南
- 说明了优化后的架构特点

## 优势

1. **清晰的架构分层**: 抽象层和应用层职责明确
2. **避免重复代码**: 消除了冗余实现，提高了代码质量
3. **统一错误处理**: 错误类型统一，便于调试和监控
4. **可维护性**: 职责分离使得代码更容易维护和扩展
5. **可测试性**: 各层可以独立测试，提高了测试覆盖率
6. **扩展性**: 支持插件式的扩展机制，便于添加新功能
