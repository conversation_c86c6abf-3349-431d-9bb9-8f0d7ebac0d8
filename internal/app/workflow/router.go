package workflow

import (
	// "pilot-api/internal/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterRouter(r *gin.Engine) {
	workflowAPI := r.Group("/api/v1/workflow")
	{
		// workflowAPI.Use(middleware.AuthUToken())

		// 新的模板化工作流API
		workflowAPI.GET("/types", ListWorkflowTypes)                     // 获取支持的工作流类型(已验证)
		workflowAPI.GET("/template", GetWorkflowConfigTemplate)          // 获取工作流配置模板(已验证)
		workflowAPI.POST("/template/create", CreateWorkflowFromTemplate) // 根据模板创建工作流(已验证流程，后续需要验证逻辑)
		workflowAPI.POST("/template/preview", GetWorkflowTemplate)       // 获取工作流策略模板，用于预览工作流配置(已验证)

		// 工作流管理
		workflowAPI.POST("/list", ListWorkflows)    // 获取工作流列表(已验证)
		workflowAPI.POST("/delete", DeleteWorkflow) // 删除工作流(已验证)(已验证)

		// 工作流执行
		workflowAPI.POST("/execute", ExecuteWorkflow)                          // 执行工作流
		workflowAPI.POST("/executions/list", ListWorkflowExecutions)           // 获取执行列表
		workflowAPI.POST("/execution/:executionId/pause", PauseWorkflow)       // 暂停工作流
		workflowAPI.POST("/execution/:executionId/resume", ResumeWorkflow)     // 恢复工作流
		workflowAPI.POST("/execution/:executionId/cancel", CancelWorkflow)     // 取消工作流
		workflowAPI.GET("/execution/:executionId", GetWorkflowExecution)       // 获取执行状态
		workflowAPI.DELETE("/execution/:executionId", DeleteWorkflowExecution) // 删除执行记录

		// 步骤审批
		workflowAPI.POST("/step/approve", ApproveStep) // 审批步骤

		// 特定工作流的操作（使用uuid参数，放在最后避免冲突）
		workflowAPI.GET("/:uuid", GetWorkflow)                // 获取工作流详情
		workflowAPI.POST("/:uuid/submit", SubmitForApproval)  // 提交审批
		workflowAPI.POST("/:uuid/approve", ApproveWorkflow)   // 审批工作流
		workflowAPI.GET("/:uuid/metrics", GetWorkflowMetrics) // 获取工作流指标
	}
}
