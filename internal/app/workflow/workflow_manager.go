package workflow

import (
	"fmt"
	"pilot-api/internal/app/workflow/steps"
	"pilot-api/internal/app/workflow/strategy"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"
	"sync"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// WorkflowManager 工作流管理器，统一管理全局注册中心和服务实例
type WorkflowManager struct {
	registry *workflowpkg.WorkflowEngineRegistry
	db       *gorm.DB
}

var (
	globalManager *WorkflowManager
	managerOnce   sync.Once
	registryOnce  sync.Once
)

// InitWorkflowManager 初始化全局工作流管理器 - 在程序启动时调用
func InitWorkflowManager(db *gorm.DB) error {
	var initErr error

	managerOnce.Do(func() {
		log.Info("Initializing global workflow manager...")

		globalManager = &WorkflowManager{
			db: db,
		}

		if err := globalManager.initRegistry(); err != nil {
			initErr = fmt.Errorf("failed to initialize registry: %w", err)
			return
		}

		log.Info("Global workflow manager initialized successfully")
	})

	return initErr
}

// initRegistry 初始化注册中心
func (wm *WorkflowManager) initRegistry() error {
	var initErr error

	registryOnce.Do(func() {
		log.Info("Initializing workflow engine registry...")

		wm.registry = workflowpkg.NewWorkflowEngineRegistry()

		ts := &trace.Service{}

		wm.registerStepHandlers(ts)

		if err := wm.registerWorkflowStrategies(ts); err != nil {
			initErr = fmt.Errorf("failed to register workflow strategies: %w", err)
			return
		}

		log.Info("Workflow engine registry initialized successfully",
			log.Any("stepTypes", wm.getRegisteredStepTypes()),
			log.Any("workflowTypes", wm.getRegisteredWorkflowTypes()))
	})

	return initErr
}

// registerStepHandlers 注册所有步骤处理器
func (wm *WorkflowManager) registerStepHandlers(ts *trace.Service) {
	stepRegistry := wm.registry.GetStepHandlerRegistry()

	deploymentHandler := steps.NewDeploymentHandler(ts, wm.db)
	stepRegistry.RegisterStepHandler(models.StepTypeDeployment, deploymentHandler)
	log.Info("Registered deployment step handler")

	approvalHandler := steps.NewApprovalHandler(ts, wm.db)
	stepRegistry.RegisterStepHandler(models.StepTypeApproval, approvalHandler)
	log.Info("Registered approval step handler")

	cleanupHandler := steps.NewCleanupHandler(ts, wm.db)
	stepRegistry.RegisterStepHandler(models.StepTypeCleanup, cleanupHandler)
	log.Info("Registered cleanup step handler")
}

// registerWorkflowStrategies 注册所有工作流策略
func (wm *WorkflowManager) registerWorkflowStrategies(ts *trace.Service) error {
	strategyRegistry := wm.registry.GetStrategyRegistry()

	tempEngine := workflowpkg.NewWorkflowEngine(ts, wm.db)

	canaryFactory := strategy.NewCanaryStrategyFactory(ts, wm.db)
	if err := strategyRegistry.RegisterStrategyFactory(canaryFactory); err != nil {
		return fmt.Errorf("failed to register canary strategy factory: %w", err)
	}
	log.Info("Registered canary strategy factory")

	blueGreenFactory := strategy.NewBlueGreenStrategyFactory(ts, wm.db, wm.registry, tempEngine)
	if err := strategyRegistry.RegisterStrategyFactory(blueGreenFactory); err != nil {
		return fmt.Errorf("failed to register blue-green strategy factory: %w", err)
	}
	log.Info("Registered blue-green strategy factory")

	abTestFactory := strategy.NewABTestStrategyFactory(ts, wm.db, wm.registry, tempEngine)
	if err := strategyRegistry.RegisterStrategyFactory(abTestFactory); err != nil {
		return fmt.Errorf("failed to register ab-test strategy factory: %w", err)
	}
	log.Info("Registered ab-test strategy factory")

	return nil
}

// GetWorkflowManager 获取全局工作流管理器
func GetWorkflowManager() *WorkflowManager {
	if globalManager == nil {
		panic("global workflow manager not initialized, call InitWorkflowManager first")
	}
	return globalManager
}

// GetRegistry 获取注册中心
func (wm *WorkflowManager) GetRegistry() *workflowpkg.WorkflowEngineRegistry {
	if wm.registry == nil {
		panic("registry not initialized")
	}
	return wm.registry
}

// GetService 获取带有特定trace的service实例
func (wm *WorkflowManager) GetService(ts *trace.Service) *WorkflowService {
	// 创建工作流引擎，使用注册中心
	engine := workflowpkg.NewWorkflowEngineWithRegistry(trace.IfNil(ts), wm.db, wm.registry)

	// 返回service实例
	return &WorkflowService{
		Service:               trace.IfNil(ts),
		WorkflowEngine:        engine,
		repo:                  repos.NewWorkflowRepo(wm.db),
		workflowExecutionRepo: repos.NewWorkflowExecutionRepo(wm.db),
		stepExecutionRepo:     repos.NewStepExecutionRepo(wm.db),
	}
}

// getRegisteredStepTypes 获取已注册的步骤类型列表
func (wm *WorkflowManager) getRegisteredStepTypes() []models.StepType {
	handlers := wm.registry.GetAllStepHandlers()
	stepTypes := make([]models.StepType, 0, len(handlers))
	for stepType := range handlers {
		stepTypes = append(stepTypes, stepType)
	}
	return stepTypes
}

// getRegisteredWorkflowTypes 获取已注册的工作流类型列表
func (wm *WorkflowManager) getRegisteredWorkflowTypes() []models.WorkflowType {
	strategies := wm.registry.GetAllWorkflowStrategies()
	workflowTypes := make([]models.WorkflowType, 0, len(strategies))
	for workflowType := range strategies {
		workflowTypes = append(workflowTypes, workflowType)
	}
	return workflowTypes
}

// IsInitialized 检查工作流管理器是否已初始化
func IsInitialized() bool {
	return globalManager != nil && globalManager.registry != nil
}

// ValidateRegistration 验证注册是否完整
func ValidateRegistration() error {
	if globalManager == nil {
		return fmt.Errorf("global workflow manager not initialized")
	}

	if globalManager.registry == nil {
		return fmt.Errorf("registry not initialized")
	}

	// 验证步骤处理器
	stepTypes := globalManager.getRegisteredStepTypes()
	if len(stepTypes) == 0 {
		return fmt.Errorf("no step handlers registered")
	}

	// 验证工作流策略
	workflowTypes := globalManager.getRegisteredWorkflowTypes()
	if len(workflowTypes) == 0 {
		return fmt.Errorf("no workflow strategies registered")
	}

	log.Info("Registration validation passed",
		log.Any("stepTypes", stepTypes),
		log.Any("workflowTypes", workflowTypes))

	return nil
}
