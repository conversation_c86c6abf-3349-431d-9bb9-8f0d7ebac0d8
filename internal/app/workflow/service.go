package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/response"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

const (
	StrategyCanary    = "canary"
	StrategyBlueGreen = "blueGreen"
	StrategyABTest    = "abTest"
	TrueString        = "true"

	// JSON Schema 类型常量
	TypeString  = "string"
	TypeNumber  = "number"
	TypeBoolean = "boolean"
	TypeArray   = "array"
	TypeObject  = "object"
)

type WorkflowService struct {
	*trace.Service
	*workflowpkg.WorkflowEngine
	repo                  *repos.WorkflowRepo
	workflowExecutionRepo *repos.WorkflowExecutionRepo
	stepExecutionRepo     *repos.StepExecutionRepo
}

func NewWorkflowService(ts *trace.Service, db *gorm.DB) *WorkflowService {
	// 确保工作流管理器已初始化
	if !IsInitialized() {
		panic("workflow manager not initialized, call InitWorkflowManager first in main.go")
	}

	return GetWorkflowManager().GetService(ts)
}

// GetEngineRegistry 获取工作流引擎注册中心
func (s *WorkflowService) GetEngineRegistry() *workflowpkg.WorkflowEngineRegistry {
	return s.WorkflowEngine.GetRegistry()
}

// GetWorkflow 获取工作流详情
func (s *WorkflowService) GetWorkflow(uuid string) (*WorkflowResponse, error) {
	// 通过UUID查询工作流
	workflows, err := s.repo.GetListByWhere(fmt.Sprintf("uuid = '%s'", uuid))
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}
	if len(workflows) == 0 {
		return nil, fmt.Errorf("workflow not found")
	}

	workflow := *workflows[0]

	// 通过WorkflowID查询Steps - 这里暂时继续使用WorkflowEngine的DB
	var steps []models.WorkflowStep
	if err := s.WorkflowEngine.GetDB().Where("workflow_id = ?", workflow.ID).Find(&steps).Error; err != nil {
		return nil, fmt.Errorf("failed to get workflow steps: %w", err)
	}

	return &WorkflowResponse{
		Workflow: workflow,
		Steps:    steps,
	}, nil
}

// GetWorkflowStrategyTemplate 获取工作流策略的默认步骤模板
func (s *WorkflowService) GetWorkflowStrategyTemplate(req GetWorkflowTemplateRequest, operator string) (*CreateWorkflowFromTemplateResponse, error) {
	// 获取策略工厂
	strategyFactory, err := s.GetEngineRegistry().GetStrategyFactory(req.Type)
	if err != nil {
		return nil, fmt.Errorf("unsupported workflow type: %s", req.Type)
	}

	// 创建策略实例
	strategy := strategyFactory.CreateStrategy()

	// 创建临时工作流用于生成步骤
	tempWorkflow := &models.Workflow{
		Type: req.Type,
	}

	// 配置服务信息
	servicesJSON, err := json.Marshal(req.Services)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal services: %w", err)
	}
	tempWorkflow.Services = string(servicesJSON)

	// 获取默认步骤
	defaultSteps := strategy.GetDefaultSteps(tempWorkflow)

	return &CreateWorkflowFromTemplateResponse{
		Steps: defaultSteps,
	}, nil
}

// DeleteWorkflow 删除工作流（级联删除所有关联数据）
func (s *WorkflowService) DeleteWorkflow(uuid string, operator string) error {
	var workflows []*models.Workflow
	if err := s.WorkflowEngine.GetDB().Where("uuid = ?", uuid).Find(&workflows).Error; err != nil {
		log.Error("failed to query workflow", log.Any("uuid", uuid), log.Any("error", err))
		return fmt.Errorf("failed to get workflow: %w", err)
	}
	if len(workflows) == 0 {
		log.Warn("workflow not found", log.Any("uuid", uuid))
		return fmt.Errorf("workflow not found")
	}

	workflow := workflows[0]
	isDraftStatus := workflow.Status == models.WorkflowTemplateStatusDraft

	// 使用事务确保数据一致性
	tx := s.WorkflowEngine.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var executions []models.WorkflowExecution
	if err := tx.Where("workflow_id = ?", workflow.ID).Find(&executions).Error; err != nil {
		log.Error("failed to get workflow executions", log.Any("error", err))
		tx.Rollback()
		return err
	}

	for _, execution := range executions {
		log.Info("deleting step executions", log.Any("execution_id", execution.ID))
		if isDraftStatus {
			if err := tx.Unscoped().Model(&models.StepExecution{}).Where("execution_id = ?", execution.ID).Delete(nil).Error; err != nil {
				log.Error("failed to hard delete step executions for execution", log.Any("execution_id", execution.ID), log.Any("error", err))
				tx.Rollback()
				return err
			}
		} else {
			if err := tx.Model(&models.StepExecution{}).Where("execution_id = ?", execution.ID).Delete(nil).Error; err != nil {
				log.Error("failed to soft delete step executions for execution", log.Any("execution_id", execution.ID), log.Any("error", err))
				tx.Rollback()
				return err
			}
		}
	}

	log.Info("deleting workflow executions", log.Any("workflow_id", workflow.ID))
	if isDraftStatus {
		if err := tx.Unscoped().Model(&models.WorkflowExecution{}).Where("workflow_id = ?", workflow.ID).Delete(nil).Error; err != nil {
			log.Error("failed to hard delete workflow executions", log.Any("error", err))
			tx.Rollback()
			return err
		}
	} else {
		if err := tx.Model(&models.WorkflowExecution{}).Where("workflow_id = ?", workflow.ID).Delete(nil).Error; err != nil {
			log.Error("failed to soft delete workflow executions", log.Any("error", err))
			tx.Rollback()
			return err
		}
	}

	log.Info("deleting workflow steps", log.Any("workflow_id", workflow.ID))
	if isDraftStatus {
		if err := tx.Unscoped().Model(&models.WorkflowStep{}).Where("workflow_id = ?", workflow.ID).Delete(nil).Error; err != nil {
			log.Error("failed to hard delete workflow steps", log.Any("error", err))
			tx.Rollback()
			return err
		}
	} else {
		if err := tx.Model(&models.WorkflowStep{}).Where("workflow_id = ?", workflow.ID).Delete(nil).Error; err != nil {
			log.Error("failed to soft delete workflow steps", log.Any("error", err))
			tx.Rollback()
			return err
		}
	}

	log.Info("deleting workflow", log.Any("workflow_id", workflow.ID))
	if isDraftStatus {
		if err := tx.Unscoped().Delete(workflow).Error; err != nil {
			log.Error("failed to hard delete workflow", log.Any("error", err))
			tx.Rollback()
			return err
		}
	} else {
		if err := tx.Delete(workflow).Error; err != nil {
			log.Error("failed to soft delete workflow", log.Any("error", err))
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		log.Error("failed to commit transaction", log.Any("error", err), log.Any("workflow id", workflow.ID))
		return err
	}

	log.Info("workflow deleted successfully",
		log.Any("workflow_id", workflow.ID),
		log.Any("uuid", uuid),
		log.Any("is_hard_delete", isDraftStatus),
		log.Any("operator", operator))

	return nil
}

// ListWorkflows 获取工作流列表
func (s *WorkflowService) ListWorkflows(req ListWorkflowRequest) (*response.PageModel[*models.Workflow], error) {
	query := s.WorkflowEngine.GetDB().Model(&models.Workflow{})

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.ServiceName != "" {
		query = query.Where("service_name = ?", req.ServiceName)
	}
	if req.Namespace != "" {
		query = query.Where("namespace = ?", req.Namespace)
	}
	if req.ClusterID != "" {
		query = query.Where("cluster_id = ?", req.ClusterID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count workflows: %w", err)
	}

	// 获取分页数据
	var workflows []*models.Workflow
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("gmt_create DESC").Find(&workflows).Error; err != nil {
		return nil, fmt.Errorf("failed to get workflows: %w", err)
	}

	return &response.PageModel[*models.Workflow]{
		List:     workflows,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// ExecuteWorkflow 执行工作流
func (s *WorkflowService) ExecuteWorkflow(req ExecuteWorkflowRequest, operator string) (*models.WorkflowExecution, error) {
	ctx := context.Background()
	execution, err := s.WorkflowEngine.ExecuteWorkflow(ctx, req.WorkflowID, operator, operator)
	if err != nil {
		return nil, fmt.Errorf("failed to execute workflow: %w", err)
	}

	return execution, nil
}

// PauseWorkflow 暂停工作流
func (s *WorkflowService) PauseWorkflow(executionID int64, operator string) error {
	ctx := context.Background()
	return s.WorkflowEngine.PauseWorkflow(ctx, executionID)
}

// ResumeWorkflow 恢复工作流
func (s *WorkflowService) ResumeWorkflow(executionID int64, operator string) error {
	ctx := context.Background()
	return s.WorkflowEngine.ResumeWorkflow(ctx, executionID)
}

// CancelWorkflow 取消工作流
func (s *WorkflowService) CancelWorkflow(executionID int64, operator string) error {
	ctx := context.Background()
	return s.WorkflowEngine.CancelWorkflow(ctx, executionID)
}

// GetWorkflowExecution 获取工作流执行状态
func (s *WorkflowService) GetWorkflowExecution(executionID int64) (*WorkflowExecutionResponse, error) {
	execution, err := s.workflowExecutionRepo.GetByID(executionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}
	if execution == nil {
		return nil, fmt.Errorf("execution not found")
	}

	stepExecutions, err := s.stepExecutionRepo.GetList(models.StepExecution{ExecutionID: executionID})
	if err != nil {
		return nil, fmt.Errorf("failed to get step executions: %w", err)
	}

	var stepExecutionList []models.StepExecution
	for _, se := range stepExecutions {
		stepExecutionList = append(stepExecutionList, *se)
	}

	return &WorkflowExecutionResponse{
		WorkflowExecution: *execution,
		StepExecutions:    stepExecutionList,
	}, nil
}

// ApproveStep 审批步骤
func (s *WorkflowService) ApproveStep(req ApproveStepRequest, operator string) error {
	stepExecution, err := s.stepExecutionRepo.GetByID(req.StepID)
	if err != nil {
		return fmt.Errorf("failed to get step execution: %w", err)
	}
	if stepExecution == nil {
		return fmt.Errorf("step execution not found")
	}

	// 检查步骤状态
	if stepExecution.Status != models.StepStatusPending {
		return fmt.Errorf("step is not pending approval")
	}

	// 更新审批状态
	if req.Action == "approve" {
		stepExecution.Status = models.StepStatusApproved
	} else {
		stepExecution.Status = models.StepStatusRejected
	}

	// 更新审批信息
	stepExecution.Modifier = operator
	stepExecution.Remark = req.Comment

	if err := s.stepExecutionRepo.Save(stepExecution); err != nil {
		return fmt.Errorf("failed to update step execution: %w", err)
	}

	return nil
}

// GetWorkflowMetrics 获取工作流指标
func (s *WorkflowService) GetWorkflowMetrics(workflowID int64) (*WorkflowMetricsResponse, error) {
	metrics := &WorkflowMetricsResponse{
		WorkflowID: workflowID,
	}

	// 获取总执行次数
	if err := s.WorkflowEngine.GetDB().Model(&models.WorkflowExecution{}).Where("workflow_id = ?", workflowID).Count(&metrics.TotalExecutions).Error; err != nil {
		return nil, fmt.Errorf("failed to count total executions: %w", err)
	}

	// 获取成功执行次数
	var successCount int64
	if err := s.WorkflowEngine.GetDB().Model(&models.WorkflowExecution{}).Where("workflow_id = ? AND status = ?",
		workflowID, models.WorkflowExecutionStatusCompleted).Count(&successCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count successful executions: %w", err)
	}

	// 获取失败执行次数
	var failureCount int64
	if err := s.WorkflowEngine.GetDB().Model(&models.WorkflowExecution{}).Where("workflow_id = ? AND status = ?",
		workflowID, models.WorkflowExecutionStatusFailed).Count(&failureCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed executions: %w", err)
	}

	// 计算成功率和失败率
	if metrics.TotalExecutions > 0 {
		metrics.SuccessRate = float64(successCount) / float64(metrics.TotalExecutions) * 100
		metrics.FailureRate = float64(failureCount) / float64(metrics.TotalExecutions) * 100
	}

	// 计算平均执行时间
	type AvgTime struct {
		AvgDuration float64
	}
	var avgTime AvgTime
	if err := s.WorkflowEngine.GetDB().Raw(`
		SELECT AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avg_duration
		FROM workflow_execution 
		WHERE workflow_id = ? AND end_time IS NOT NULL
	`, workflowID).Scan(&avgTime).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average execution time: %w", err)
	}
	metrics.AverageTime = avgTime.AvgDuration

	// 获取正在运行的执行数
	if err := s.WorkflowEngine.GetDB().Model(&models.WorkflowExecution{}).Where("workflow_id = ? AND status = ?",
		workflowID, models.WorkflowExecutionStatusRunning).Count(&metrics.RunningCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count running executions: %w", err)
	}

	return metrics, nil
}

// SubmitForApproval 提交审批
func (s *WorkflowService) SubmitForApproval(uuid string, operator string) error {
	workflows, err := s.repo.GetListByWhere(fmt.Sprintf("uuid = '%s'", uuid))
	if err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}
	if len(workflows) == 0 {
		return fmt.Errorf("workflow not found")
	}

	workflow := workflows[0]

	if workflow.Status != models.WorkflowTemplateStatusDraft {
		return fmt.Errorf("only draft workflows can be submitted for approval")
	}

	workflow.SubmitForApproval()
	workflow.Update(operator)

	if err := s.repo.Save(workflow); err != nil {
		return fmt.Errorf("failed to submit workflow for approval: %w", err)
	}

	return nil
}

// ApproveWorkflow 审批工作流
func (s *WorkflowService) ApproveWorkflow(uuid string, action string, operator string) error {
	workflows, err := s.repo.GetListByWhere(fmt.Sprintf("uuid = '%s'", uuid))
	if err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}
	if len(workflows) == 0 {
		return fmt.Errorf("workflow not found")
	}

	workflow := workflows[0]

	if workflow.Status != models.WorkflowTemplateStatusPending {
		return fmt.Errorf("only pending workflows can be approved or rejected")
	}

	if action == "approve" {
		workflow.Approve()
	} else {
		workflow.Reject()
	}
	workflow.Update(operator)

	if err := s.repo.Save(workflow); err != nil {
		return fmt.Errorf("failed to approve workflow: %w", err)
	}

	return nil
}

// ListWorkflowExecutions 获取工作流执行列表
func (s *WorkflowService) ListWorkflowExecutions(req ListWorkflowExecutionsRequest) (*response.PageModel[*models.WorkflowExecution], error) {
	query := s.WorkflowEngine.GetDB().Model(&models.WorkflowExecution{})

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.WorkflowName != "" {
		// 需要联表查询
		query = query.Joins("JOIN workflow ON workflow_execution.workflow_id = workflow.id").
			Where("workflow.name LIKE ?", "%"+req.WorkflowName+"%")
	}
	if req.Executor != "" {
		query = query.Where("executor_name LIKE ?", "%"+req.Executor+"%")
	}
	if req.StartTime != "" {
		query = query.Where("start_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("start_time <= ?", req.EndTime)
	}
	if req.WorkflowID != nil {
		query = query.Where("workflow_id = ?", *req.WorkflowID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count executions: %w", err)
	}

	// 获取分页数据
	var executions []*models.WorkflowExecution
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("start_time DESC").Find(&executions).Error; err != nil {
		return nil, fmt.Errorf("failed to get executions: %w", err)
	}

	return &response.PageModel[*models.WorkflowExecution]{
		List:     executions,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// DeleteWorkflowExecution 删除工作流执行记录
func (s *WorkflowService) DeleteWorkflowExecution(executionID int64, operator string) error {
	execution, err := s.workflowExecutionRepo.GetByID(executionID)
	if err != nil {
		return fmt.Errorf("failed to get execution: %w", err)
	}
	if execution == nil {
		return fmt.Errorf("execution not found")
	}

	// 只有已完成、失败或取消的执行才能删除
	if !execution.IsFinished() {
		return fmt.Errorf("only finished executions can be deleted")
	}

	// 删除相关的步骤执行记录
	stepExecutionCond := &models.StepExecution{ExecutionID: executionID}
	if err := s.stepExecutionRepo.DeleteWith(stepExecutionCond); err != nil {
		return fmt.Errorf("failed to delete step executions: %w", err)
	}

	// 删除执行记录
	if err := s.workflowExecutionRepo.DeleteByID(executionID); err != nil {
		return fmt.Errorf("failed to delete execution: %w", err)
	}

	return nil
}

// GetWorkflowTemplate 获取工作流模板
func (s *WorkflowService) GetWorkflowTemplate(req GetWorkflowTemplateRequest) (*CreateWorkflowFromTemplateResponse, error) {
	return s.GetWorkflowStrategyTemplate(req, "") // Assuming operator is not needed for this call
}

// GetWorkflowConfigTemplate 获取工作流配置模板
func (s *WorkflowService) GetWorkflowConfigTemplate(workflowType models.WorkflowType) (*WorkflowConfigTemplateResponse, error) {
	strategy, err := s.GetEngineRegistry().GetWorkflowStrategy(workflowType)
	if err != nil {
		return nil, fmt.Errorf("unsupported workflow type: %s", workflowType)
	}

	// 获取配置模板
	template := strategy.GetConfigTemplate()
	if template == nil {
		return nil, fmt.Errorf("no config template available for workflow type: %s", workflowType)
	}

	// 转换为响应格式
	return &WorkflowConfigTemplateResponse{
		Type:         workflowType,
		Name:         template.Name,
		Description:  template.Description,
		ConfigSchema: s.convertToResponseConfigSchema(template.ConfigForm),
		Metadata:     template.Metadata,
	}, nil
}

// convertToResponseConfigSchema 转换配置Schema格式
func (s *WorkflowService) convertToResponseConfigSchema(form workflowpkg.WorkflowConfigForm) WorkflowConfigSchema {
	sections := make([]ConfigSection, len(form.Sections))
	for i, section := range form.Sections {
		fields := make([]ConfigField, len(section.Fields))
		for j, field := range section.Fields {
			configField := ConfigField{
				Key:          field.Key,
				Label:        field.Label,
				Type:         field.Type,
				Required:     field.Required,
				Default:      field.Default,
				Description:  field.Description,
				Placeholder:  field.Placeholder,
				Dependencies: field.Dependencies,
			}

			// 转换选项
			if len(field.Options) > 0 {
				options := make([]Option, len(field.Options))
				for k, option := range field.Options {
					options[k] = Option{
						Label: option.Label,
						Value: option.Value,
					}
				}
				configField.Options = options
			}

			// 转换验证规则
			if field.Validation != nil {
				configField.Validation = &Validation{
					Min:    field.Validation.Min,
					Max:    field.Validation.Max,
					MinLen: field.Validation.MinLen,
					MaxLen: field.Validation.MaxLen,
					Regex:  field.Validation.Regex,
				}
			}

			fields[j] = configField
		}

		sections[i] = ConfigSection{
			Title:       section.Title,
			Description: section.Description,
			Fields:      fields,
		}
	}

	// 转换为JSON Schema格式
	schema := map[string]any{
		"type":       "object",
		"properties": make(map[string]any),
		"required":   []string{},
	}

	defaultData := make(map[string]any)
	properties := schema["properties"].(map[string]any)
	required := []string{}

	for _, section := range sections {
		for _, field := range section.Fields {
			// 构建字段schema
			fieldSchema := map[string]any{
				"title":       field.Label,
				"description": field.Description,
			}

			// 设置字段类型
			switch field.Type {
			case "text":
				fieldSchema["type"] = TypeString
			case "number":
				fieldSchema["type"] = TypeNumber
			case "boolean":
				fieldSchema["type"] = TypeBoolean
			case "array":
				fieldSchema["type"] = TypeArray
			case "object":
				fieldSchema["type"] = TypeObject
			case "select":
				fieldSchema["type"] = TypeString
				if len(field.Options) > 0 {
					enum := make([]any, len(field.Options))
					for i, option := range field.Options {
						enum[i] = option.Value
					}
					fieldSchema["enum"] = enum
				}
			}

			properties[field.Key] = fieldSchema

			// 设置默认值
			if field.Default != nil {
				defaultData[field.Key] = field.Default
			}

			// 添加必填字段
			if field.Required {
				required = append(required, field.Key)
			}
		}
	}

	schema["required"] = required

	return WorkflowConfigSchema{
		Schema:      schema,
		DefaultData: defaultData,
		Examples:    defaultData, // 示例数据基于默认值
	}
}

// CreateWorkflowFromTemplate 根据模板创建工作流
func (s *WorkflowService) CreateWorkflowFromTemplate(req *CreateWorkflowRequest, userName string) (*WorkflowResponse, error) {
	if req.Type == "" {
		return nil, fmt.Errorf("workflow type is required")
	}

	strategy, err := s.GetEngineRegistry().GetWorkflowStrategy(req.Type)
	if err != nil {
		log.Error("unsupported workflow type", log.Any("workflowType", req.Type), log.Any("error", err))
		return nil, err
	}

	workflow := &models.Workflow{
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Namespace:   req.Namespace,
		ClusterID:   req.ClusterID,
		Status:      models.WorkflowTemplateStatusDraft,
	}

	workflow.Create(userName)

	if configErr := strategy.ConfigureWorkflow(workflow, req); configErr != nil {
		log.Error("failed to configure workflow", log.Any("workflow", workflow), log.Any("error", configErr))
		return nil, configErr
	}

	steps, err := strategy.GenStepsFromRequest(workflow)
	if err != nil {
		log.Error("failed to generate steps", log.Any("workflow", workflow), log.Any("error", err))
		return nil, err
	}

	tx := s.WorkflowEngine.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(workflow).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create workflow: %w", err)
	}

	for i := range steps {
		steps[i].Create(userName)
		steps[i].WorkflowID = workflow.ID
		if err := tx.Create(&steps[i]).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to create step: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &WorkflowResponse{
		Workflow: *workflow,
		Steps:    steps,
	}, nil
}
