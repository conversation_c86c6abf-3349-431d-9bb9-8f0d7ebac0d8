package strategy

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/app/workflow/config"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// 部署状态常量
const (
	DeploymentStatusFailed   = "failed"
	DeploymentStatusDeployed = "deployed"
	HealthStatusHealthy      = "healthy"
	HealthStatusUnhealthy    = "unhealthy"
	PhaseValidation          = "validation"
	PhaseBlueCleanup         = "blue-cleanup"
)

// BlueGreenStrategy 蓝绿部署策略
type BlueGreenStrategy struct {
	workflowpkg.BaseWorkflowStrategy
	*trace.Service
	db       *gorm.DB
	registry *workflowpkg.WorkflowEngineRegistry
	engine   *workflowpkg.WorkflowEngine
}

// NewBlueGreenStrategy 创建蓝绿部署策略
func NewBlueGreenStrategy(ts *trace.Service, db *gorm.DB, registry *workflowpkg.WorkflowEngineRegistry, engine *workflowpkg.WorkflowEngine) *BlueGreenStrategy {
	return &BlueGreenStrategy{
		BaseWorkflowStrategy: *workflowpkg.NewBaseWorkflowStrategy("BlueGreenStrategy"),
		Service:              trace.IfNil(ts),
		db:                   db,
		registry:             registry,
		engine:               engine,
	}
}

// ValidateWorkflow 验证蓝绿部署工作流配置
func (s *BlueGreenStrategy) ValidateWorkflow(workflow *models.Workflow) error {
	// 基础验证
	if err := s.BaseWorkflowStrategy.ValidateWorkflow(workflow); err != nil {
		return err
	}

	// 验证蓝绿部署特定配置
	if workflow.Services == "" {
		return fmt.Errorf("blue-green workflow requires services configuration")
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("invalid services configuration: %w", err)
	}

	if len(services) == 0 {
		return fmt.Errorf("blue-green workflow requires at least one service")
	}

	// 验证每个服务的配置
	for _, service := range services {
		if service.Name == "" {
			return fmt.Errorf("service name is required")
		}
		if len(service.Images) == 0 {
			return fmt.Errorf("service %s requires image configuration", service.Name)
		}
		if service.Replicas <= 0 {
			return fmt.Errorf("service %s requires positive replicas", service.Name)
		}
	}

	return nil
}

// ExecuteWorkflow 执行蓝绿部署工作流
func (s *BlueGreenStrategy) ExecuteWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing blue-green workflow: %s", workflow.Name))

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if workflow.Services != "" {
		if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
			return fmt.Errorf("failed to parse services config: %w", err)
		}
	}

	// 按服务分组步骤
	serviceSteps := s.engine.GroupStepsByService(steps)

	// 按服务顺序执行蓝绿部署流程
	for _, service := range services {
		log.Info(fmt.Sprintf("executing blue-green steps for service: %s", service.Name))

		if stepsForService, exists := serviceSteps[service.Name]; exists {
			// 执行该服务的蓝绿部署步骤
			if err := s.executeBlueGreenServiceSteps(ctx, execution, stepsForService, service); err != nil {
				return fmt.Errorf("blue-green service %s execution failed: %w", service.Name, err)
			}
		}
	}

	return nil
}

// executeBlueGreenServiceSteps 执行蓝绿服务相关步骤
func (s *BlueGreenStrategy) executeBlueGreenServiceSteps(
	ctx context.Context,
	execution *models.WorkflowExecution,
	steps []models.WorkflowStep,
	service models.WorkflowServiceConfig,
) error {
	// 开始事务，确保服务级别的原子性操作
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 记录服务开始执行
	serviceStatus := models.ServiceExecutionStatus{
		ServiceName:      service.Name,
		Namespace:        execution.Context,
		DeploymentStatus: "pending",
		HealthStatus:     "unknown",
		LastUpdated:      time.Now().Format(time.RFC3339),
	}

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update service execution status: %w", err)
	}

	if err := tx.Save(execution).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save execution status: %w", err)
	}

	// 蓝绿部署特定逻辑：顺序执行每个阶段
	phases := s.groupStepsByBlueGreenPhase(steps)

	for _, phase := range []string{"green-deploy", "validation", "traffic-switch", "blue-cleanup"} {
		if phaseSteps, exists := phases[phase]; exists {
			log.Info(fmt.Sprintf("executing blue-green phase: %s for service: %s", phase, service.Name))

			// 创建阶段保存点
			savepoint := fmt.Sprintf("sp_%s_%s", service.Name, phase)
			if err := tx.Exec(fmt.Sprintf("SAVEPOINT %s", savepoint)).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to create savepoint: %w", err)
			}

			var phaseErr error
			for _, step := range phaseSteps {
				if err := s.executeBlueGreenStepWithTransaction(ctx, tx, execution, &step); err != nil {
					phaseErr = err
					break
				}
			}

			if phaseErr != nil {
				// 回滚到阶段保存点
				if rollbackErr := tx.Exec(fmt.Sprintf("ROLLBACK TO SAVEPOINT %s", savepoint)).Error; rollbackErr != nil {
					tx.Rollback()
					return fmt.Errorf("failed to rollback to savepoint: %w, original error: %w", rollbackErr, phaseErr)
				}

				// 更新服务状态为失败
				serviceStatus.DeploymentStatus = DeploymentStatusFailed
				serviceStatus.ErrorMessage = phaseErr.Error()
				serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

				if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err == nil {
					tx.Save(execution)
				}

				tx.Rollback()
				return phaseErr
			}

			// 释放保存点
			if err := tx.Exec(fmt.Sprintf("RELEASE SAVEPOINT %s", savepoint)).Error; err != nil {
				log.ErrorE(fmt.Sprintf("failed to release savepoint %s", savepoint), err)
			}
		}
	}

	// 更新服务状态为成功
	serviceStatus.DeploymentStatus = DeploymentStatusDeployed
	serviceStatus.HealthStatus = HealthStatusHealthy
	serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update final service status: %w", err)
	}

	if err := tx.Save(execution).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save final execution status: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// executeBlueGreenStepWithTransaction 在事务中执行蓝绿步骤
func (s *BlueGreenStrategy) executeBlueGreenStepWithTransaction(ctx context.Context, tx *gorm.DB, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	// 检查执行条件
	if !s.engine.CheckExecutionCondition(ctx, execution, step) {
		log.Info(fmt.Sprintf("blue-green step execution condition not met: stepId=%d, stepName=%s", step.ID, step.Name))
		return nil
	}

	// 检查依赖
	if !s.engine.CheckDependencies(ctx, execution, step) {
		log.Info(fmt.Sprintf("blue-green step dependencies not satisfied: stepId=%d, stepName=%s", step.ID, step.Name))
		return nil
	}

	// 获取步骤处理器
	handler, err := s.registry.GetStepHandler(step.Type)
	if err != nil {
		return err
	}

	// 创建步骤执行记录（在事务中）
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
		RetryCount:   0,
	}

	// 在事务中创建记录
	if createErr := tx.Create(stepExecution).Error; createErr != nil {
		return fmt.Errorf("failed to create step execution record: %w", createErr)
	}

	// 开始执行步骤
	stepExecution.Status = models.StepStatusRunning
	if updateErr := tx.Save(stepExecution).Error; updateErr != nil {
		return fmt.Errorf("failed to update step status to running: %w", updateErr)
	}

	// 执行步骤
	err = handler.Execute(ctx, stepExecution, step)

	// 更新步骤执行状态（在事务中）
	if err != nil {
		stepExecution.Status = models.StepStatusFailed
		stepExecution.ErrorMessage = err.Error()
		now := time.Now()
		stepExecution.EndTime = &now
	} else {
		stepExecution.Status = models.StepStatusCompleted
		now := time.Now()
		stepExecution.EndTime = &now
	}

	// 保存最终状态（在事务中）
	if saveErr := tx.Save(stepExecution).Error; saveErr != nil {
		return fmt.Errorf("failed to save step execution status: %w", saveErr)
	}

	return err
}

// groupStepsByBlueGreenPhase 按蓝绿部署阶段分组步骤
func (s *BlueGreenStrategy) groupStepsByBlueGreenPhase(steps []models.WorkflowStep) map[string][]models.WorkflowStep {
	phases := make(map[string][]models.WorkflowStep)

	for _, step := range steps {
		var phase string

		// 根据步骤类型和名称确定阶段
		switch step.Type {
		case models.StepTypeDeployment:
			if step.Order <= 2 {
				phase = "green-deploy"
			} else {
				phase = PhaseBlueCleanup
			}
		case models.StepTypeMonitoring:
			phase = PhaseValidation
		case models.StepTypeTrafficSplit:
			phase = "traffic-switch"
		case models.StepTypeApproval:
			phase = PhaseValidation
		case models.StepTypeCleanup:
			phase = PhaseBlueCleanup
		default:
			phase = PhaseValidation
		}

		phases[phase] = append(phases[phase], step)
	}

	return phases
}

// GetDefaultSteps 获取蓝绿部署默认步骤模板
func (s *BlueGreenStrategy) GetDefaultSteps(workflow *models.Workflow) []models.WorkflowStep {
	var steps []models.WorkflowStep

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if workflow.Services != "" {
		if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
			return steps
		}
	}

	stepOrder := 1

	// 为每个服务生成默认步骤
	for _, service := range services {
		// 1. 绿色环境部署步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Deploy %s Green Environment", service.Name),
			Type:         models.StepTypeDeployment,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      600, // 10分钟
			Config:       s.buildGreenDeploymentConfig(service),
		})
		stepOrder++

		// 2. 绿色环境验证步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Validate %s Green Environment", service.Name),
			Type:         models.StepTypeMonitoring,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      1800, // 30分钟
			Config:       s.buildValidationConfig(service),
		})
		stepOrder++

		// 3. 审批步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Approve %s Traffic Switch", service.Name),
			Type:         models.StepTypeApproval,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  false,
			Timeout:      3600, // 1小时
			Config:       s.buildApprovalConfig(service),
		})
		stepOrder++

		// 4. 流量切换步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Switch %s Traffic to Green", service.Name),
			Type:         models.StepTypeTrafficSplit,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      300, // 5分钟
			Config:       s.buildTrafficSwitchConfig(service),
		})
		stepOrder++

		// 5. 蓝色环境清理步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Cleanup %s Blue Environment", service.Name),
			Type:         models.StepTypeCleanup,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      300, // 5分钟
			Config:       s.buildBlueCleanupConfig(service),
		})
		stepOrder++
	}

	return steps
}

// CanRollback 判断是否可以回滚
func (s *BlueGreenStrategy) CanRollback(execution *models.WorkflowExecution) bool {
	// 蓝绿部署支持快速回滚
	return execution.IsFailed() || execution.IsRunning() || execution.IsCompleted()
}

// ExecuteRollback 执行回滚
func (s *BlueGreenStrategy) ExecuteRollback(ctx context.Context, execution *models.WorkflowExecution) error {
	log.Info(fmt.Sprintf("executing blue-green rollback for execution: %d", execution.ID))

	// 获取工作流信息
	var workflow models.Workflow
	if err := s.db.WithContext(ctx).First(&workflow, execution.WorkflowID).Error; err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("failed to parse services config: %w", err)
	}

	// 为每个服务执行回滚（切换回蓝色环境）
	for _, service := range services {
		if err := s.performBlueGreenRollback(ctx, execution, service); err != nil {
			return fmt.Errorf("failed to rollback service %s: %w", service.Name, err)
		}
	}

	return nil
}

// performBlueGreenRollback 执行蓝绿回滚
func (s *BlueGreenStrategy) performBlueGreenRollback(ctx context.Context, execution *models.WorkflowExecution, service models.WorkflowServiceConfig) error {
	log.Info(fmt.Sprintf("performing blue-green rollback for service: %s", service.Name))

	// 创建回滚步骤配置（切换回蓝色环境）
	config := map[string]any{
		"serviceName":    service.Name,
		"rollbackTarget": "blue",
		"strategy":       "immediate",
		"preserveGreen":  true, // 保留绿色环境以便再次尝试
	}

	configJSON, _ := json.Marshal(config)

	// 创建回滚步骤
	rollbackStep := &models.WorkflowStep{
		WorkflowID:  execution.WorkflowID,
		Name:        fmt.Sprintf("Rollback %s to Blue", service.Name),
		Type:        models.StepTypeRollback,
		ServiceName: service.Name,
		Config:      string(configJSON),
	}

	// 获取步骤处理器并执行
	handler, err := s.registry.GetStepHandler(models.StepTypeRollback)
	if err != nil {
		return fmt.Errorf("failed to get rollback handler: %w", err)
	}

	// 创建步骤执行记录
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       rollbackStep.ID,
		Status:       models.StepStatusRunning,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
	}

	return handler.Execute(ctx, stepExecution, rollbackStep)
}

// 构建配置的辅助方法
func (s *BlueGreenStrategy) buildGreenDeploymentConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"serviceName":    service.Name,
		"images":         service.Images,
		"replicas":       service.Replicas,
		"environment":    "green",
		"strategy":       "blue-green",
		"resources":      service.Resources,
		"env":            service.Environment,
		"parallelDeploy": false, // 蓝绿部署不并行
	}

	configJSON, _ := json.Marshal(config)
	return string(configJSON)
}

func (s *BlueGreenStrategy) buildValidationConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"serviceName":    service.Name,
		"environment":    "green",
		"validationType": "comprehensive",
		"metrics":        []string{"health", "performance", "errors"},
		"duration":       "30m",
		"criteria": map[string]any{
			"healthCheck":  "passing",
			"errorRate":    "<1%",
			"responseTime": "<100ms",
		},
	}

	configJSON, _ := json.Marshal(config)
	return string(configJSON)
}

func (s *BlueGreenStrategy) buildApprovalConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"serviceName": service.Name,
		"approvers":   []string{"admin", "ops", "qa"},
		"timeout":     "1h",
		"description": fmt.Sprintf("Approve traffic switch for %s from blue to green environment", service.Name),
	}

	configJSON, _ := json.Marshal(config)
	return string(configJSON)
}

func (s *BlueGreenStrategy) buildTrafficSwitchConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"serviceName":     service.Name,
		"fromEnvironment": "blue",
		"toEnvironment":   "green",
		"switchType":      "immediate",
		"weightStrategy":  "all-or-nothing",
		"verifyAfter":     true,
	}

	configJSON, _ := json.Marshal(config)
	return string(configJSON)
}

func (s *BlueGreenStrategy) buildBlueCleanupConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"serviceName":        service.Name,
		"cleanupEnvironment": "blue",
		"preservePeriod":     "24h", // 保留24小时用于紧急回滚
		"cleanupType":        "graceful",
		"resources":          []string{"deployment", "service", "configmap"},
	}

	configJSON, _ := json.Marshal(config)
	return string(configJSON)
}

// BlueGreenStrategyFactory 蓝绿部署策略工厂
type BlueGreenStrategyFactory struct {
	traceService *trace.Service
	db           *gorm.DB
	registry     *workflowpkg.WorkflowEngineRegistry
	engine       *workflowpkg.WorkflowEngine
}

// NewBlueGreenStrategyFactory 创建蓝绿部署策略工厂
func NewBlueGreenStrategyFactory(ts *trace.Service, db *gorm.DB, registry *workflowpkg.WorkflowEngineRegistry, engine *workflowpkg.WorkflowEngine) *BlueGreenStrategyFactory {
	return &BlueGreenStrategyFactory{
		traceService: ts,
		db:           db,
		registry:     registry,
		engine:       engine,
	}
}

// CreateStrategy 创建蓝绿部署策略实例
func (f *BlueGreenStrategyFactory) CreateStrategy() workflowpkg.WorkflowStrategy {
	return NewBlueGreenStrategy(f.traceService, f.db, f.registry, f.engine)
}

// GetSupportedType 获取支持的工作流类型
func (f *BlueGreenStrategyFactory) GetSupportedType() models.WorkflowType {
	return models.WorkflowTypeBlueGreen
}

// GetMetadata 获取策略元数据
func (f *BlueGreenStrategyFactory) GetMetadata() workflowpkg.StrategyMetadata {
	return workflowpkg.StrategyMetadata{
		Name:        "Blue-Green Deployment Strategy",
		Description: "Zero-downtime deployment strategy using two identical production environments (blue and green)",
		Version:     "1.0.0",
		Author:      "Makeblock DevOps Team",
		Tags:        []string{"deployment", "blue-green", "zero-downtime", "rollback"},
		Features: []string{
			"zero-downtime-deployment",
			"instant-rollback",
			"environment-isolation",
			"comprehensive-validation",
			"approval-gates",
			"multi-service-support",
			"transaction-consistency",
		},
	}
}

// ConfigureWorkflow 配置蓝绿部署工作流（实现新接口）
func (s *BlueGreenStrategy) ConfigureWorkflow(workflow *models.Workflow, request any) error {
	// 蓝绿部署策略的配置逻辑
	// 这里可以根据需要实现蓝绿部署特定的配置
	return nil
}

// GenStepsFromRequest 从请求生成蓝绿部署工作流步骤（实现新接口）
func (s *BlueGreenStrategy) GenStepsFromRequest(workflow *models.Workflow) ([]models.WorkflowStep, error) {
	// 使用现有的 GetDefaultSteps 方法
	steps := s.GetDefaultSteps(workflow)
	return steps, nil
}

// GetConfigTemplate 获取蓝绿部署配置模板
func (s *BlueGreenStrategy) GetConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return config.GetBlueGreenConfigTemplate()
}
