package strategy

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/app/workflow/config"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// ABTestStrategy A/B测试策略
type ABTestStrategy struct {
	workflowpkg.BaseWorkflowStrategy
	*trace.Service
	db       *gorm.DB
	registry *workflowpkg.WorkflowEngineRegistry
	engine   *workflowpkg.WorkflowEngine
}

// NewABTestStrategy 创建A/B测试策略
func NewABTestStrategy(ts *trace.Service, db *gorm.DB, registry *workflowpkg.WorkflowEngineRegistry, engine *workflowpkg.WorkflowEngine) *ABTestStrategy {
	return &ABTestStrategy{
		BaseWorkflowStrategy: *workflowpkg.NewBaseWorkflowStrategy("ABTestStrategy"),
		Service:              trace.IfNil(ts),
		db:                   db,
		registry:             registry,
		engine:               engine,
	}
}

// ValidateWorkflow 验证A/B测试工作流配置
func (s *ABTestStrategy) ValidateWorkflow(workflow *models.Workflow) error {
	// 基础验证
	if err := s.BaseWorkflowStrategy.ValidateWorkflow(workflow); err != nil {
		return err
	}

	// 验证A/B测试特定配置
	if workflow.Services == "" {
		return fmt.Errorf("A/B test workflow requires services configuration")
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("invalid services configuration: %w", err)
	}

	if len(services) == 0 {
		return fmt.Errorf("A/B test workflow requires at least one service")
	}

	return nil
}

// ExecuteWorkflow 执行A/B测试工作流
func (s *ABTestStrategy) ExecuteWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error {
	log.Info(fmt.Sprintf("starting A/B test workflow execution: %d", execution.ID))

	// 验证工作流
	if err := s.ValidateWorkflow(workflow); err != nil {
		return fmt.Errorf("workflow validation failed: %w", err)
	}

	// 执行A/B测试工作流步骤
	return s.executeABTestSteps(ctx, execution, steps)
}

// executeABTestSteps 执行A/B测试步骤
func (s *ABTestStrategy) executeABTestSteps(ctx context.Context, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	for _, step := range steps {
		if err := s.executeStep(ctx, execution, &step); err != nil {
			return fmt.Errorf("failed to execute step %s: %w", step.Name, err)
		}
	}

	return nil
}

// executeStep 执行单个步骤
func (s *ABTestStrategy) executeStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test step: %s", step.Name))

	// 创建步骤执行记录
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusRunning,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
	}

	if err := s.db.WithContext(ctx).Create(stepExecution).Error; err != nil {
		return fmt.Errorf("failed to create step execution: %w", err)
	}

	// 根据步骤类型执行相应操作
	var err error
	switch step.Type {
	case models.StepTypeDeployment:
		err = s.executeABTestDeployment(ctx, stepExecution, step)
	case models.StepTypeTrafficSplit:
		err = s.executeABTestTrafficSplit(stepExecution, step)
	case models.StepTypeMonitoring:
		err = s.executeABTestMonitoring(stepExecution, step)
	case models.StepTypeApproval:
		err = s.executeABTestApproval(ctx, stepExecution, step)
	case models.StepTypeCleanup:
		err = s.executeABTestCleanup(ctx, stepExecution, step)
	default:
		err = fmt.Errorf("unsupported step type: %s", step.Type)
	}

	// 更新步骤执行状态
	now := time.Now()
	if err != nil {
		stepExecution.Status = models.StepStatusFailed
		stepExecution.ErrorMessage = err.Error()
	} else {
		stepExecution.Status = models.StepStatusCompleted
	}
	stepExecution.EndTime = &now

	if updateErr := s.db.WithContext(ctx).Save(stepExecution).Error; updateErr != nil {
		log.ErrorE("failed to update step execution status", updateErr)
	}

	return err
}

// executeABTestDeployment 执行A/B测试部署
func (s *ABTestStrategy) executeABTestDeployment(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test deployment for step: %s", step.Name))

	// 解析步骤配置
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	if stepConfig.DeploymentConfig == nil {
		return fmt.Errorf("deployment config is required for deployment step")
	}

	// 使用部署处理器执行部署
	deploymentHandler, err := s.registry.GetStepHandler(models.StepTypeDeployment)
	if err != nil {
		return fmt.Errorf("deployment handler not found: %w", err)
	}

	return deploymentHandler.Execute(ctx, execution, step)
}

// executeABTestTrafficSplit 执行A/B测试流量分割
func (s *ABTestStrategy) executeABTestTrafficSplit(execution *models.StepExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test traffic split for step: %s", step.Name))

	// 解析步骤配置
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	if stepConfig.TrafficConfig == nil {
		return fmt.Errorf("traffic config is required for traffic split step")
	}

	// 这里可以实现A/B测试特定的流量分割逻辑
	// 暂时使用基础的流量分割实现
	execution.Output = s.buildABTestTrafficOutput(stepConfig.TrafficConfig)

	return nil
}

// executeABTestMonitoring 执行A/B测试监控
func (s *ABTestStrategy) executeABTestMonitoring(execution *models.StepExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test monitoring for step: %s", step.Name))

	// 解析步骤配置
	var stepConfig models.StepConfig
	if err := json.Unmarshal([]byte(step.Config), &stepConfig); err != nil {
		return fmt.Errorf("failed to parse step config: %w", err)
	}

	// A/B测试监控逻辑
	// 这里可以实现指标收集、效果评估等
	execution.Output = s.buildABTestMonitoringOutput(step.ServiceName)

	return nil
}

// executeABTestApproval 执行A/B测试审批
func (s *ABTestStrategy) executeABTestApproval(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test approval for step: %s", step.Name))

	// 使用审批处理器
	approvalHandler, err := s.registry.GetStepHandler(models.StepTypeApproval)
	if err != nil {
		return fmt.Errorf("approval handler not found: %w", err)
	}

	return approvalHandler.Execute(ctx, execution, step)
}

// executeABTestCleanup 执行A/B测试清理
func (s *ABTestStrategy) executeABTestCleanup(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing A/B test cleanup for step: %s", step.Name))

	// 使用清理处理器
	cleanupHandler, err := s.registry.GetStepHandler(models.StepTypeCleanup)
	if err != nil {
		return fmt.Errorf("cleanup handler not found: %w", err)
	}

	return cleanupHandler.Execute(ctx, execution, step)
}

// buildABTestTrafficOutput 构建A/B测试流量输出
func (s *ABTestStrategy) buildABTestTrafficOutput(trafficConfig *models.TrafficConfig) string {
	output := map[string]any{
		"type":        "ab-test-traffic",
		"serviceName": trafficConfig.ServiceName,
		"strategy":    trafficConfig.Strategy,
		"weights":     trafficConfig.Weights,
		"timestamp":   time.Now().Unix(),
	}

	outputBytes, _ := json.Marshal(output)
	return string(outputBytes)
}

// buildABTestMonitoringOutput 构建A/B测试监控输出
func (s *ABTestStrategy) buildABTestMonitoringOutput(serviceName string) string {
	output := map[string]any{
		"type":        "ab-test-monitoring",
		"serviceName": serviceName,
		"metrics": map[string]any{
			"conversionRate": 0.0,
			"clickRate":      0.0,
			"responseTime":   0.0,
		},
		"timestamp": time.Now().Unix(),
	}

	outputBytes, _ := json.Marshal(output)
	return string(outputBytes)
}

// GetDefaultSteps 获取A/B测试的默认步骤
func (s *ABTestStrategy) GetDefaultSteps(workflow *models.Workflow) []models.WorkflowStep {
	var steps []models.WorkflowStep

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if workflow.Services != "" {
		if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
			return steps
		}
	}

	stepOrder := 1

	// 为每个服务生成默认步骤
	for _, service := range services {
		// 1. A版本部署步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Deploy %s Version A", service.Name),
			Type:         models.StepTypeDeployment,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      600, // 10分钟
			Config:       s.buildABTestDeploymentConfig(service, "versionA"),
		})
		stepOrder++

		// 2. B版本部署步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Deploy %s Version B", service.Name),
			Type:         models.StepTypeDeployment,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      600, // 10分钟
			Config:       s.buildABTestDeploymentConfig(service, "versionB"),
		})
		stepOrder++

		// 3. A/B测试流量分割步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Configure %s A/B Test Traffic", service.Name),
			Type:         models.StepTypeTrafficSplit,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      300, // 5分钟
			Config:       s.buildABTestTrafficConfig(service),
		})
		stepOrder++

		// 4. A/B测试监控步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Monitor %s A/B Test", service.Name),
			Type:         models.StepTypeMonitoring,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      7200, // 2小时
			Config:       s.buildABTestMonitoringConfig(service),
		})
		stepOrder++

		// 5. 决策审批步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Approve %s A/B Test Decision", service.Name),
			Type:         models.StepTypeApproval,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  false,
			Timeout:      3600, // 1小时
			Config:       s.buildABTestApprovalConfig(),
		})
		stepOrder++

		// 6. 清理步骤
		steps = append(steps, models.WorkflowStep{
			WorkflowID:   workflow.ID,
			Name:         fmt.Sprintf("Cleanup %s A/B Test", service.Name),
			Type:         models.StepTypeCleanup,
			Order:        stepOrder,
			ServiceName:  service.Name,
			ServiceOrder: service.Order,
			AutoExecute:  true,
			Timeout:      300, // 5分钟
			Config:       s.buildABTestCleanupConfig(service),
		})
		stepOrder++
	}

	return steps
}

// buildABTestDeploymentConfig 构建A/B测试部署配置
func (s *ABTestStrategy) buildABTestDeploymentConfig(service models.WorkflowServiceConfig, version string) string {
	config := models.DeploymentConfig{
		ServiceName: service.Name,
		Images:      service.Images,
		Version:     version,
		Replicas:    service.Replicas,
		Environment: service.Environment,
		Resources:   service.Resources,
		Strategy:    "ab-test",
	}

	stepConfig := models.StepConfig{
		DeploymentConfig: &config,
	}

	configBytes, _ := json.Marshal(stepConfig)
	return string(configBytes)
}

// buildABTestTrafficConfig 构建A/B测试流量配置
func (s *ABTestStrategy) buildABTestTrafficConfig(service models.WorkflowServiceConfig) string {
	config := models.TrafficConfig{
		ServiceName: service.Name,
		Strategy:    "ab-test",
		Weights: []models.TrafficWeight{
			{Version: "versionA", Weight: 50},
			{Version: "versionB", Weight: 50},
		},
	}

	stepConfig := models.StepConfig{
		TrafficConfig: &config,
	}

	configBytes, _ := json.Marshal(stepConfig)
	return string(configBytes)
}

// buildABTestMonitoringConfig 构建A/B测试监控配置
func (s *ABTestStrategy) buildABTestMonitoringConfig(service models.WorkflowServiceConfig) string {
	config := models.MonitorConfig{
		ServiceName: service.Name,
		Duration:    "2h",
		Metrics:     []string{"conversion_rate", "click_rate", "response_time", "error_rate"},
		Thresholds: []models.MonitorThreshold{
			{Name: "conversion_rate", Operator: ">=", Value: 0.05, Unit: "%"},
			{Name: "response_time", Operator: "<=", Value: 200, Unit: "ms"},
		},
	}

	stepConfig := models.StepConfig{
		MonitorConfig: &config,
	}

	configBytes, _ := json.Marshal(stepConfig)
	return string(configBytes)
}

// buildABTestApprovalConfig 构建A/B测试审批配置
func (s *ABTestStrategy) buildABTestApprovalConfig() string {
	config := models.ApprovalConfig{
		Approvers:     []string{"product-manager", "data-analyst"},
		ConditionType: "and",
		Timeout:       3600, // 1小时
	}

	stepConfig := models.StepConfig{
		ApprovalConfig: &config,
	}

	configBytes, _ := json.Marshal(stepConfig)
	return string(configBytes)
}

// buildABTestCleanupConfig 构建A/B测试清理配置
func (s *ABTestStrategy) buildABTestCleanupConfig(service models.WorkflowServiceConfig) string {
	config := map[string]any{
		"cleanupType":     "ab_test_cleanup",
		"serviceName":     service.Name,
		"cleanupVersions": []string{"versionA", "versionB"},
		"keepWinner":      true,
	}

	_ = models.StepConfig{
		Description: "Clean up A/B test resources",
	}

	// 将清理配置放在stepConfig中
	stepConfigMap := map[string]any{
		"cleanupConfig": config,
	}

	configBytes, _ := json.Marshal(stepConfigMap)
	return string(configBytes)
}

// ConfigureWorkflow 配置A/B测试工作流（实现新接口）
func (s *ABTestStrategy) ConfigureWorkflow(workflow *models.Workflow, request any) error {
	// A/B测试策略的配置逻辑
	// 这里可以根据需要实现A/B测试特定的配置
	return nil
}

// GenStepsFromRequest 从请求生成A/B测试工作流步骤（实现新接口）
func (s *ABTestStrategy) GenStepsFromRequest(workflow *models.Workflow) ([]models.WorkflowStep, error) {
	// 使用现有的 GetDefaultSteps 方法
	steps := s.GetDefaultSteps(workflow)
	return steps, nil
}

// ABTestStrategyFactory A/B测试策略工厂
type ABTestStrategyFactory struct {
	traceService *trace.Service
	db           *gorm.DB
	registry     *workflowpkg.WorkflowEngineRegistry
	engine       *workflowpkg.WorkflowEngine
}

// NewABTestStrategyFactory 创建A/B测试策略工厂
func NewABTestStrategyFactory(ts *trace.Service, db *gorm.DB, registry *workflowpkg.WorkflowEngineRegistry, engine *workflowpkg.WorkflowEngine) *ABTestStrategyFactory {
	return &ABTestStrategyFactory{
		traceService: ts,
		db:           db,
		registry:     registry,
		engine:       engine,
	}
}

// CreateStrategy 创建A/B测试策略实例
func (f *ABTestStrategyFactory) CreateStrategy() workflowpkg.WorkflowStrategy {
	return NewABTestStrategy(f.traceService, f.db, f.registry, f.engine)
}

// GetSupportedType 获取支持的工作流类型
func (f *ABTestStrategyFactory) GetSupportedType() models.WorkflowType {
	return models.WorkflowTypeABTest
}

// GetMetadata 获取策略元数据
func (f *ABTestStrategyFactory) GetMetadata() workflowpkg.StrategyMetadata {
	return workflowpkg.StrategyMetadata{
		Name:        "A/B Test Strategy",
		Description: "Statistical testing strategy for comparing different versions of features or services",
		Version:     "1.0.0",
		Author:      "Makeblock DevOps Team",
		Tags:        []string{"testing", "ab-test", "statistical", "experiment"},
		Features: []string{
			"statistical-testing",
			"traffic-splitting",
			"metric-collection",
			"confidence-analysis",
			"automatic-winner-selection",
			"multi-variant-support",
			"experiment-management",
			"data-driven-decisions",
		},
	}
}

// GetConfigTemplate 获取A/B测试配置模板
func (s *ABTestStrategy) GetConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return config.GetABTestConfigTemplate()
}
