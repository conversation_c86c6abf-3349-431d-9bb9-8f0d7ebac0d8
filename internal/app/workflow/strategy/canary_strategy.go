package strategy

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/app/workflow/config"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// WorkflowStepConfig 类型别名，避免导入循环
type WorkflowStepConfig struct {
	Name        string          `json:"name" binding:"required"` // 步骤名称
	Type        models.StepType `json:"type" binding:"required"` // 步骤类型
	Order       int             `json:"order"`                   // 执行顺序
	Config      map[string]any  `json:"config,omitempty"`        // 步骤配置
	DependsOn   []string        `json:"dependsOn,omitempty"`     // 依赖步骤
	Approvers   []string        `json:"approvers,omitempty"`     // 审批人
	AutoExecute bool            `json:"autoExecute"`             // 是否自动执行
	Timeout     int             `json:"timeout,omitempty"`       // 超时时间(秒)
	RetryCount  int             `json:"retryCount,omitempty"`    // 重试次数
	ServiceName string          `json:"serviceName,omitempty"`   // 关联服务名
	Condition   string          `json:"condition,omitempty"`     // 执行条件
}

const (
	// CanaryVersionLabel 灰度版本标签
	CanaryVersionLabel = "canary"
)

// CanaryStrategyFactory 灰度发布策略工厂
type CanaryStrategyFactory struct {
	traceService *trace.Service
	db           *gorm.DB
}

// NewCanaryStrategyFactory 创建灰度发布策略工厂
func NewCanaryStrategyFactory(ts *trace.Service, db *gorm.DB) *CanaryStrategyFactory {
	return &CanaryStrategyFactory{
		traceService: ts,
		db:           db,
	}
}

// CreateStrategy 创建灰度发布策略实例
func (f *CanaryStrategyFactory) CreateStrategy() workflowpkg.WorkflowStrategy {
	return NewCanaryStrategy(f.traceService, f.db)
}

// GetSupportedType 获取支持的工作流类型
func (f *CanaryStrategyFactory) GetSupportedType() models.WorkflowType {
	return models.WorkflowTypeCanary
}

// GetMetadata 获取策略元数据
func (f *CanaryStrategyFactory) GetMetadata() workflowpkg.StrategyMetadata {
	return workflowpkg.StrategyMetadata{
		Name:        "Canary Deployment Strategy",
		Description: "Advanced canary deployment strategy that supports both single service and multi-service scenarios with propagation capabilities",
		Version:     "1.0.0",
		Author:      "Makeblock DevOps Team",
		Tags:        []string{"deployment", "canary", "traffic-splitting", "rollback"},
		Features: []string{
			"traffic-propagation",
			"end-to-end-canary",
			"traffic-splitting",
			"health-monitoring",
			"automatic-rollback",
			"approval-gates",
			"multi-service-support",
			"parallel-execution",
			"dependency-management",
		},
	}
}

// CanaryStrategy 灰度发布策略
type CanaryStrategy struct {
	workflowpkg.BaseWorkflowStrategy
	*trace.Service
	db                    *gorm.DB
	registry              *workflowpkg.WorkflowEngineRegistry
	engine                *workflowpkg.WorkflowEngine
	workflowRepo          *repos.WorkflowRepo
	workflowExecutionRepo *repos.WorkflowExecutionRepo
	stepExecutionRepo     *repos.StepExecutionRepo
}

// NewCanaryStrategy 创建灰度发布策略
func NewCanaryStrategy(ts *trace.Service, db *gorm.DB) *CanaryStrategy {
	return &CanaryStrategy{
		Service:               trace.IfNil(ts),
		db:                    db,
		workflowRepo:          repos.NewWorkflowRepo(db),
		workflowExecutionRepo: repos.NewWorkflowExecutionRepo(db),
		stepExecutionRepo:     repos.NewStepExecutionRepo(db),
	}
}

// ValidateWorkflow 验证灰度发布工作流配置
func (s *CanaryStrategy) ValidateWorkflow(workflow *models.Workflow) error {
	// 验证灰度发布特定配置
	if workflow.Services == "" {
		return fmt.Errorf("canary workflow requires services configuration")
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("invalid services configuration: %w", err)
	}

	if len(services) == 0 {
		return fmt.Errorf("canary workflow requires at least one service")
	}

	// 验证每个服务的配置
	for _, service := range services {
		if service.Name == "" {
			return fmt.Errorf("service name is required")
		}
		if len(service.Images) == 0 {
			return fmt.Errorf("service %s requires image configuration", service.Name)
		}
		if service.Replicas <= 0 {
			return fmt.Errorf("service %s requires positive replicas", service.Name)
		}
	}

	// 验证全局配置
	if workflow.GlobalConfig != "" {
		var globalConfig models.WorkflowGlobalConfig
		if err := json.Unmarshal([]byte(workflow.GlobalConfig), &globalConfig); err != nil {
			return fmt.Errorf("invalid global configuration: %w", err)
		}
	}

	return nil
}

// ExecuteWorkflow 执行灰度发布工作流
func (s *CanaryStrategy) ExecuteWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error {
	log.Info(fmt.Sprintf("executing canary workflow: %s", workflow.Name))

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if workflow.Services != "" {
		if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
			log.Error("failed to parse services config during execution", log.Any("error", err))
			return err
		}
	}

	// 解析全局配置
	var globalConfig *models.WorkflowGlobalConfig
	if workflow.GlobalConfig != "" {
		if err := json.Unmarshal([]byte(workflow.GlobalConfig), &globalConfig); err != nil {
			log.Error("failed to parse global config during execution", log.Any("error", err))
			return err
		}
	}

	// 按服务分组步骤
	serviceSteps := s.engine.GroupStepsByService(steps)

	// 按服务顺序执行灰度发布流程
	for _, service := range services {
		log.Info(fmt.Sprintf("executing canary steps for service: %s", service.Name))

		if stepsForService, exists := serviceSteps[service.Name]; exists {
			// 执行该服务的灰度发布步骤
			if err := s.executeCanaryServiceSteps(ctx, execution, stepsForService, service); err != nil {
				log.Error("canary service execution failed", log.Any("service", service.Name), log.Any("error", err))
				return err
			}
		}
	}

	return nil
}

// executeCanaryServiceSteps 执行灰度服务相关步骤
func (s *CanaryStrategy) executeCanaryServiceSteps(
	ctx context.Context,
	execution *models.WorkflowExecution,
	steps []models.WorkflowStep,
	service models.WorkflowServiceConfig,
) error {
	// 开始事务，确保服务级别的原子性操作
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 记录服务开始执行
	serviceStatus := models.ServiceExecutionStatus{
		ServiceName:      service.Name,
		Namespace:        execution.Context, // 从执行上下文获取namespace
		DeploymentStatus: "pending",
		HealthStatus:     "unknown",
		LastUpdated:      time.Now().Format(time.RFC3339),
	}

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		log.Error("failed to update service execution status", log.Any("service", service.Name), log.Any("error", err))
		tx.Rollback()
		return err
	}

	// 使用事务repo保存execution
	txExecutionRepo := s.workflowExecutionRepo.WithTx(tx)
	if err := txExecutionRepo.Save(execution); err != nil {
		log.Error("failed to save execution status", log.Any("error", err))
		tx.Rollback()
		return err
	}

	// 按并行组分组步骤
	parallelGroups := s.engine.GroupStepsByParallelGroup(steps)

	for groupName, groupSteps := range parallelGroups {
		log.Info(fmt.Sprintf("executing canary parallel group: %s for service: %s", groupName, service.Name))

		// 创建事务保存点
		savepoint := fmt.Sprintf("sp_%s_%s", service.Name, groupName)
		if err := tx.Exec(fmt.Sprintf("SAVEPOINT %s", savepoint)).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create savepoint: %w", err)
		}

		var groupErr error
		if groupName == "" {
			// 顺序执行灰度步骤
			for _, step := range groupSteps {
				if err := s.executeCanaryStepWithTransaction(ctx, tx, execution, &step); err != nil {
					groupErr = err
					break
				}

				// 检查是否需要暂停（灰度发布特定逻辑）
				if s.shouldPauseAfterCanaryStep(&step) {
					log.Info(fmt.Sprintf("pausing canary workflow after step: %s", step.Name))
					// 提交事务并返回
					if err := tx.Commit().Error; err != nil {
						return fmt.Errorf("failed to commit transaction: %w", err)
					}
					return nil
				}
			}
		} else {
			// 并行执行 - 需要特殊处理事务
			groupErr = s.executeParallelStepsWithTransaction(ctx, tx, execution, groupSteps)
		}

		if groupErr != nil {
			// 回滚到保存点
			if rollbackErr := tx.Exec(fmt.Sprintf("ROLLBACK TO SAVEPOINT %s", savepoint)).Error; rollbackErr != nil {
				tx.Rollback()
				return fmt.Errorf("failed to rollback to savepoint: %w, original error: %w", rollbackErr, groupErr)
			}

			// 更新服务状态为失败
			serviceStatus.DeploymentStatus = "failed"
			serviceStatus.ErrorMessage = groupErr.Error()
			serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

			if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err == nil {
				if saveErr := txExecutionRepo.Save(execution); saveErr != nil {
					log.ErrorE("failed to save execution after updating service status", saveErr)
				}
			}

			tx.Rollback()
			return groupErr
		}

		// 释放保存点
		if err := tx.Exec(fmt.Sprintf("RELEASE SAVEPOINT %s", savepoint)).Error; err != nil {
			log.ErrorE(fmt.Sprintf("failed to release savepoint %s", savepoint), err)
		}
	}

	// 更新服务状态为成功
	serviceStatus.DeploymentStatus = "deployed"
	serviceStatus.HealthStatus = "healthy"
	serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update final service status: %w", err)
	}

	if err := txExecutionRepo.Save(execution); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save final execution status: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// executeCanaryStepWithTransaction 在事务中执行灰度步骤
func (s *CanaryStrategy) executeCanaryStepWithTransaction(ctx context.Context, tx *gorm.DB, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	// 检查执行条件
	if !s.engine.CheckExecutionCondition(ctx, execution, step) {
		log.Info(fmt.Sprintf("canary step execution condition not met: stepId=%d, stepName=%s", step.ID, step.Name))
		return nil
	}

	// 检查依赖
	if !s.engine.CheckDependencies(ctx, execution, step) {
		log.Info(fmt.Sprintf("canary step dependencies not satisfied: stepId=%d, stepName=%s", step.ID, step.Name))
		return nil
	}

	// 获取步骤处理器
	handler, err := s.registry.GetStepHandler(step.Type)
	if err != nil {
		return err
	}

	// 创建步骤执行记录（在事务中）
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
		RetryCount:   0,
	}

	// 使用事务repo创建记录
	txStepRepo := s.stepExecutionRepo.WithTx(tx)
	if createErr := txStepRepo.Create(stepExecution); createErr != nil {
		log.Error("failed to create step execution record", log.Any("stepId", step.ID), log.Any("error", createErr))
		return createErr
	}

	// 开始执行步骤
	stepExecution.Status = models.StepStatusRunning
	if updateErr := txStepRepo.Save(stepExecution); updateErr != nil {
		log.Error("failed to update step status to running", log.Any("stepId", step.ID), log.Any("error", updateErr))
		return updateErr
	}

	// 执行步骤（注意：这里可能需要传递事务上下文）
	err = handler.Execute(ctx, stepExecution, step)

	// 更新步骤执行状态（在事务中）
	if err != nil {
		stepExecution.Status = models.StepStatusFailed
		stepExecution.ErrorMessage = err.Error()
		now := time.Now()
		stepExecution.EndTime = &now
	} else {
		stepExecution.Status = models.StepStatusCompleted
		now := time.Now()
		stepExecution.EndTime = &now
	}

	// 保存最终状态（在事务中）
	if saveErr := txStepRepo.Save(stepExecution); saveErr != nil {
		log.Error("failed to save step execution status", log.Any("stepId", step.ID), log.Any("error", saveErr))
		return saveErr
	}

	return err
}

// executeParallelStepsWithTransaction 在事务中执行并行步骤
func (s *CanaryStrategy) executeParallelStepsWithTransaction(ctx context.Context, tx *gorm.DB, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	// 对于并行执行，我们需要将事务逻辑推迟到每个步骤内部
	// 这里使用常规的并行执行，但每个步骤内部使用事务
	var wg sync.WaitGroup
	errChan := make(chan error, len(steps))

	for _, step := range steps {
		// 检查依赖和条件
		if !s.engine.CheckDependencies(ctx, execution, &step) {
			errChan <- fmt.Errorf("step %s dependencies not satisfied", step.Name)
			continue
		}

		if !s.engine.CheckExecutionCondition(ctx, execution, &step) {
			// 条件不满足，跳过步骤
			if err := s.skipStepInTransaction(ctx, tx, execution, &step); err != nil {
				log.ErrorE(fmt.Sprintf("failed to skip step %s", step.Name), err)
			}
			continue
		}

		wg.Add(1)
		go func(stepItem models.WorkflowStep) {
			defer wg.Done()

			// 为每个并行步骤创建单独的事务
			stepTx := s.db.WithContext(ctx).Begin()
			defer func() {
				if r := recover(); r != nil {
					stepTx.Rollback()
					errChan <- fmt.Errorf("step %s panicked: %v", stepItem.Name, r)
				}
			}()

			if err := s.executeCanaryStepWithTransaction(ctx, stepTx, execution, &stepItem); err != nil {
				stepTx.Rollback()
				errChan <- fmt.Errorf("step %s execution failed: %w", stepItem.Name, err)
				return
			}

			if err := stepTx.Commit().Error; err != nil {
				errChan <- fmt.Errorf("failed to commit step %s transaction: %w", stepItem.Name, err)
			}
		}(step)
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// 收集所有错误
	var errors []error
	for err := range errChan {
		if err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("parallel execution failed with %d errors: %v", len(errors), errors)
	}

	return nil
}

// skipStepInTransaction 在事务中跳过步骤
func (s *CanaryStrategy) skipStepInTransaction(_ context.Context, tx *gorm.DB, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	// 创建步骤执行记录，标记为跳过
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusSkipped,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
		RetryCount:   0,
	}

	now := time.Now()
	stepExecution.EndTime = &now

	// 使用事务repo保存跳过记录
	txStepRepo := s.stepExecutionRepo.WithTx(tx)
	if err := txStepRepo.Create(stepExecution); err != nil {
		return fmt.Errorf("failed to create skipped step execution record: %w", err)
	}

	return nil
}

// shouldPauseAfterCanaryStep 判断是否需要在灰度步骤后暂停
func (s *CanaryStrategy) shouldPauseAfterCanaryStep(step *models.WorkflowStep) bool {
	// 灰度发布特定的暂停逻辑
	switch step.Type {
	case models.StepTypeApproval:
		// 审批步骤且不是自动执行，则暂停
		return !step.AutoExecute
	case models.StepTypeMonitoring:
		// 监控步骤完成后暂停，等待人工确认
		return true
	case models.StepTypeTrafficSplit:
		// 流量分发后暂停，观察效果
		return true
	default:
		return false
	}
}

// GetDefaultSteps 获取灰度发布默认步骤模板
func (s *CanaryStrategy) GetDefaultSteps(workflow *models.Workflow) []models.WorkflowStep {
	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if workflow.Services != "" {
		if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
			return []models.WorkflowStep{}
		}
	}

	// 复用 genDefaultCanarySteps 方法
	return s.genDefaultCanarySteps(workflow, services)
}

// CanRollback 判断是否可以回滚
func (s *CanaryStrategy) CanRollback(execution *models.WorkflowExecution) bool {
	// 灰度发布支持回滚
	return execution.IsFailed() || execution.IsRunning()
}

// ExecuteRollback 执行回滚
func (s *CanaryStrategy) ExecuteRollback(ctx context.Context, execution *models.WorkflowExecution) error {
	log.Info(fmt.Sprintf("executing rollback for execution: %d", execution.ID))

	// 获取工作流信息
	workflow, err := s.workflowRepo.GetByID(execution.WorkflowID)
	if err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}
	if workflow == nil {
		return fmt.Errorf("workflow not found")
	}

	// 解析服务配置
	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("failed to parse services config: %w", err)
	}

	// 为每个服务执行回滚
	for _, service := range services {
		if err := s.performServiceRollback(ctx, execution, service); err != nil {
			return fmt.Errorf("failed to rollback service %s: %w", service.Name, err)
		}
	}

	return nil
}

// performServiceRollback 执行服务回滚
func (s *CanaryStrategy) performServiceRollback(ctx context.Context, execution *models.WorkflowExecution, service models.WorkflowServiceConfig) error {
	log.Info(fmt.Sprintf("performing rollback for service: %s", service.Name))

	// 创建回滚步骤配置
	config := map[string]any{
		"serviceName": service.Name,
		"rollbackTo":  "previous",
		"strategy":    "rolling",
	}

	configJSON, _ := json.Marshal(config)

	// 创建回滚步骤
	rollbackStep := &models.WorkflowStep{
		WorkflowID:  execution.WorkflowID,
		Name:        fmt.Sprintf("Rollback %s", service.Name),
		Type:        models.StepTypeRollback,
		ServiceName: service.Name,
		Config:      string(configJSON),
	}

	// 获取步骤处理器并执行
	handler, err := s.registry.GetStepHandler(models.StepTypeRollback)
	if err != nil {
		return fmt.Errorf("failed to get rollback handler: %w", err)
	}

	// 创建步骤执行记录
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       rollbackStep.ID,
		Status:       models.StepStatusRunning,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
	}

	return handler.Execute(ctx, stepExecution, rollbackStep)
}

// ConfigureWorkflow 配置工作流
func (s *CanaryStrategy) ConfigureWorkflow(workflow *models.Workflow, request any) error {
	// 只支持 CreateWorkflowRequest 类型
	simpleReq, ok := request.(interface{ GetConfig() map[string]any })
	if !ok {
		return fmt.Errorf("invalid request type: expected SimplifiedCreateWorkflowRequest")
	}

	config := simpleReq.GetConfig()

	configJSON, err := json.Marshal(config)
	if err != nil {
		log.Error("failed to marshal config", log.Any("error", err))
		return err
	}
	workflow.Config = string(configJSON)

	return s.configureFromRequest(workflow, config)
}

// configureFromRequest 从请求配置工作流
func (s *CanaryStrategy) configureFromRequest(workflow *models.Workflow, config map[string]any) error {
	// 从config中解析服务配置
	servicesData, ok := config["services"]
	if !ok {
		return fmt.Errorf("services configuration is required")
	}

	servicesJSON, err := json.Marshal(servicesData)
	if err != nil {
		log.Error("failed to marshal services", log.Any("error", err))
		return err
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal(servicesJSON, &services); err != nil {
		log.Error("failed to parse services config", log.Any("error", err))
		return err
	}

	if len(services) == 0 {
		return fmt.Errorf("at least one service is required")
	}

	// 解析全局配置（可选）
	var globalConfig *models.WorkflowGlobalConfig
	if globalData, exists := config["globalConfig"]; exists {
		globalJSON, err := json.Marshal(globalData)
		if err != nil {
			log.Error("failed to marshal global config", log.Any("error", err))
			return err
		}

		if err := json.Unmarshal(globalJSON, &globalConfig); err != nil {
			log.Error("failed to parse global config", log.Any("error", err))
			return err
		}
	}

	// 依赖关系（可选）
	var dependencies []string
	if depsData, exists := config["dependencies"]; exists {
		if depsList, ok := depsData.([]string); ok {
			dependencies = depsList
		}
	}

	return s.setWorkflowConfig(workflow, services, globalConfig, dependencies)
}

// setWorkflowConfig 设置工作流配置的通用方法
func (s *CanaryStrategy) setWorkflowConfig(
	workflow *models.Workflow,
	services []models.WorkflowServiceConfig,
	globalConfig *models.WorkflowGlobalConfig,
	dependencies []string,
) error {
	// 序列化服务配置
	if len(services) > 0 {
		servicesJSON, err := json.Marshal(services)
		if err != nil {
			return fmt.Errorf("failed to marshal services: %w", err)
		}
		workflow.Services = string(servicesJSON)
	}

	// 序列化全局配置
	if globalConfig != nil {
		globalConfigJSON, err := json.Marshal(globalConfig)
		if err != nil {
			return fmt.Errorf("failed to marshal global config: %w", err)
		}
		workflow.GlobalConfig = string(globalConfigJSON)
	}

	// 序列化依赖关系
	if len(dependencies) > 0 {
		dependenciesJSON, err := json.Marshal(dependencies)
		if err != nil {
			return fmt.Errorf("failed to marshal dependencies: %w", err)
		}
		workflow.Dependencies = string(dependenciesJSON)
	}

	return nil
}

// GenStepsFromRequest 从请求生成工作流步骤
func (s *CanaryStrategy) GenStepsFromRequest(workflow *models.Workflow) ([]models.WorkflowStep, error) {
	return s.genStepsFromWorkflow(workflow)
}

// genStepsFromWorkflow 从workflow中已解析的配置生成步骤
func (s *CanaryStrategy) genStepsFromWorkflow(workflow *models.Workflow) ([]models.WorkflowStep, error) {
	if workflow.Services == "" {
		return nil, fmt.Errorf("no services configuration found in workflow")
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return nil, fmt.Errorf("failed to parse services from workflow: %w", err)
	}

	if len(services) == 0 {
		return nil, fmt.Errorf("at least one service is required")
	}

	var config map[string]any
	if workflow.Config != "" {
		if err := json.Unmarshal([]byte(workflow.Config), &config); err != nil {
			return nil, fmt.Errorf("failed to parse config from workflow: %w", err)
		}
	} else {
		config = make(map[string]any)
	}

	if stepsData, exists := config["steps"]; exists {
		return s.parseStepsFromConfig(workflow, stepsData)
	}

	return s.genStepsFromConfig(workflow, services, config)
}

// parseStepsFromConfig 解析配置中的步骤
func (s *CanaryStrategy) parseStepsFromConfig(workflow *models.Workflow, stepsData any) ([]models.WorkflowStep, error) {
	stepsJSON, err := json.Marshal(stepsData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal steps: %w", err)
	}

	var stepConfigs []WorkflowStepConfig
	if err := json.Unmarshal(stepsJSON, &stepConfigs); err != nil {
		return nil, fmt.Errorf("failed to parse steps config: %w", err)
	}

	var steps []models.WorkflowStep
	for _, stepConfig := range stepConfigs {
		step := models.WorkflowStep{
			WorkflowID:  workflow.ID,
			Name:        stepConfig.Name,
			Type:        stepConfig.Type,
			Order:       stepConfig.Order,
			ServiceName: stepConfig.ServiceName,
			AutoExecute: stepConfig.AutoExecute,
			Timeout:     stepConfig.Timeout,
			RetryCount:  stepConfig.RetryCount,
			Status:      models.StepStatusPending,
		}

		if stepConfig.Config != nil {
			configJSON, err := json.Marshal(stepConfig.Config)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal step config: %w", err)
			}
			step.Config = string(configJSON)
		}

		steps = append(steps, step)
	}

	return steps, nil
}

// StepDefinition 步骤定义
type StepDefinition struct {
	Name           string
	Type           models.StepType
	AutoExecute    bool
	TimeoutKey     string // 在timeouts map中的key
	ConfigBuilder  func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string
	EnabledChecker func(templateMap map[string]any) bool // 检查是否在templateMap中启用
	RequireService bool                                  // 是否需要services不为空
	DefaultEnabled bool                                  // 默认是否启用
}

// getCanaryStepDefinitions 获取灰度发布步骤定义
func (s *CanaryStrategy) getCanaryStepDefinitions() []StepDefinition {
	return []StepDefinition{
		{
			Name:        "审批灰度发布",
			Type:        models.StepTypeApproval,
			AutoExecute: false,
			TimeoutKey:  "approval",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				approvalConfig := s.parseApprovalConfig(config)
				if approvalConfig == nil {
					return s.buildDefaultApprovalConfig()
				}
				return s.buildConfigFromRequest(approvalConfig)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["canaryApproval"])
			},
			RequireService: false,
			DefaultEnabled: true,
		},
		{
			Name:        "部署所有服务灰度版本",
			Type:        models.StepTypeDeployment,
			AutoExecute: true,
			TimeoutKey:  "step",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				return s.buildMultiDeploymentConfig(services, CanaryVersionLabel, config)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["canaryDeploy"])
			},
			RequireService: true,
			DefaultEnabled: true,
		},
		{
			Name:        "配置所有服务流量分发",
			Type:        models.StepTypeTrafficSplit,
			AutoExecute: true,
			TimeoutKey:  "traffic",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				return s.buildMultiTrafficConfig(services, config)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["trafficSplit"])
			},
			RequireService: true,
			DefaultEnabled: true,
		},
		{
			Name:        "端到端验证",
			Type:        models.StepTypeMonitoring,
			AutoExecute: true,
			TimeoutKey:  "monitoring",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				monitoringConfig := s.parseMonitoringConfig(config)
				if monitoringConfig == nil {
					return s.buildDefaultMonitoringConfig()
				}
				return s.buildConfigFromRequest(monitoringConfig)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["monitoring"])
			},
			RequireService: false,
			DefaultEnabled: true,
		},
		{
			Name:        "审批生产发布",
			Type:        models.StepTypeApproval,
			AutoExecute: false,
			TimeoutKey:  "approval",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				approvalConfig := s.parseApprovalConfig(config)
				if approvalConfig == nil {
					return s.buildDefaultApprovalConfig()
				}
				return s.buildConfigFromRequest(approvalConfig)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["prodApproval"])
			},
			RequireService: false,
			DefaultEnabled: true,
		},
		{
			Name:        "部署所有服务生产版本",
			Type:        models.StepTypeDeployment,
			AutoExecute: true,
			TimeoutKey:  "step",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				return s.buildMultiDeploymentConfig(services, "production", config)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["prodDeploy"])
			},
			RequireService: true,
			DefaultEnabled: true,
		},
		{
			Name:        "清理灰度资源",
			Type:        models.StepTypeCleanup,
			AutoExecute: true,
			TimeoutKey:  "cleanup",
			ConfigBuilder: func(s *CanaryStrategy, services []models.WorkflowServiceConfig, config map[string]any) string {
				cleanupConfig := s.parseCleanupConfig(config)
				if cleanupConfig == nil {
					return s.buildDefaultCleanupConfig()
				}
				return s.buildConfigFromRequest(cleanupConfig)
			},
			EnabledChecker: func(templateMap map[string]any) bool {
				return s.isBoolTrue(templateMap["cleanup"])
			},
			RequireService: false,
			DefaultEnabled: true,
		},
	}
}

// buildStepsFromDefinitions 根据步骤定义构建步骤
func (s *CanaryStrategy) buildStepsFromDefinitions(
	workflow *models.Workflow,
	services []models.WorkflowServiceConfig,
	config map[string]any,
	templateMap map[string]any,
	useTemplate bool,
) []models.WorkflowStep {
	var steps []models.WorkflowStep
	stepOrder := 1
	timeouts := s.parseTimeoutConfig(config)
	definitions := s.getCanaryStepDefinitions()

	for _, def := range definitions {
		// 检查是否启用此步骤
		enabled := def.DefaultEnabled
		if useTemplate && templateMap != nil {
			enabled = def.EnabledChecker(templateMap)
		}

		// 如果不启用，跳过
		if !enabled {
			continue
		}

		// 如果需要服务但服务列表为空，跳过
		if def.RequireService && len(services) == 0 {
			continue
		}

		// 获取超时时间
		timeout := timeouts[def.TimeoutKey]

		// 构建步骤
		step := models.WorkflowStep{
			WorkflowID:  workflow.ID,
			Name:        def.Name,
			Type:        def.Type,
			Order:       stepOrder,
			AutoExecute: def.AutoExecute,
			Timeout:     timeout,
			Config:      def.ConfigBuilder(s, services, config),
		}

		steps = append(steps, step)
		stepOrder++
	}

	return steps
}

// genStepsFromConfig 从配置生成灰度发布步骤
func (s *CanaryStrategy) genStepsFromConfig(workflow *models.Workflow, services []models.WorkflowServiceConfig, config map[string]any) ([]models.WorkflowStep, error) {
	// 解析步骤模板配置
	stepTemplate, exists := config["stepTemplate"]
	if !exists {
		// 如果没有stepTemplate配置，使用默认步骤
		return s.buildStepsFromDefinitions(workflow, services, config, nil, false), nil
	}

	templateMap, ok := stepTemplate.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("stepTemplate must be an object/map, got: %T", stepTemplate)
	}

	// 使用模板配置生成步骤
	return s.buildStepsFromDefinitions(workflow, services, config, templateMap, true), nil
}

// genDefaultCanarySteps 生成默认的灰度发布步骤
func (s *CanaryStrategy) genDefaultCanarySteps(workflow *models.Workflow, services []models.WorkflowServiceConfig) []models.WorkflowStep {
	// 使用默认配置生成步骤
	defaultConfig := make(map[string]any)
	return s.buildStepsFromDefinitions(workflow, services, defaultConfig, nil, false)
}

// parseTimeoutConfig 解析超时配置
func (s *CanaryStrategy) parseTimeoutConfig(config map[string]any) map[string]int {
	// 设置默认超时时间（单位：秒）
	timeouts := map[string]int{
		"step":       900,  // 15分钟 - 部署步骤默认超时时间
		"approval":   3600, // 1小时 - 审批步骤默认超时时间
		"traffic":    300,  // 5分钟 - 流量配置默认超时时间
		"monitoring": 1800, // 30分钟 - 监控验证默认超时时间
		"cleanup":    300,  // 5分钟 - 清理步骤默认超时时间
	}

	if timeoutsData, exists := config["timeouts"]; exists {
		if timeoutMap, ok := timeoutsData.(map[string]any); ok {
			if step, exists := timeoutMap["step"]; exists {
				if stepInt, ok := step.(float64); ok {
					timeouts["step"] = int(stepInt)
				}
			}
			if approval, exists := timeoutMap["approval"]; exists {
				if approvalInt, ok := approval.(float64); ok {
					timeouts["approval"] = int(approvalInt)
				}
			}
			if traffic, exists := timeoutMap["traffic"]; exists {
				if trafficInt, ok := traffic.(float64); ok {
					timeouts["traffic"] = int(trafficInt)
				}
			}
			if monitoring, exists := timeoutMap["monitoring"]; exists {
				if monitoringInt, ok := monitoring.(float64); ok {
					timeouts["monitoring"] = int(monitoringInt)
				}
			}
			if cleanup, exists := timeoutMap["cleanup"]; exists {
				if cleanupInt, ok := cleanup.(float64); ok {
					timeouts["cleanup"] = int(cleanupInt)
				}
			}
		}
	}

	return timeouts
}

// parseApprovalConfig 解析审批配置
func (s *CanaryStrategy) parseApprovalConfig(config map[string]any) map[string]any {
	if approvalData, exists := config["approval"]; exists {
		if approvalMap, ok := approvalData.(map[string]any); ok {
			return approvalMap
		}
	}
	return nil
}

// parseMonitoringConfig 解析监控配置
func (s *CanaryStrategy) parseMonitoringConfig(config map[string]any) map[string]any {
	if monitoringData, exists := config["monitoring"]; exists {
		if monitoringMap, ok := monitoringData.(map[string]any); ok {
			return monitoringMap
		}
	}
	return nil
}

// parseCleanupConfig 解析清理配置
func (s *CanaryStrategy) parseCleanupConfig(config map[string]any) map[string]any {
	if cleanupData, exists := config["cleanup"]; exists {
		if cleanupMap, ok := cleanupData.(map[string]any); ok {
			return cleanupMap
		}
	}
	return nil
}

// isBoolTrue 检查值是否为true
func (s *CanaryStrategy) isBoolTrue(value any) bool {
	if boolVal, ok := value.(bool); ok {
		return boolVal
	}
	return false
}

// buildConfigFromRequest 从请求配置构建步骤配置
func (s *CanaryStrategy) buildConfigFromRequest(configData map[string]any) string {
	if configData == nil {
		configData = make(map[string]any)
	}

	configBytes, _ := json.Marshal(configData)
	return string(configBytes)
}

// buildMultiDeploymentConfig 从请求构建多服务部署配置
func (s *CanaryStrategy) buildMultiDeploymentConfig(services []models.WorkflowServiceConfig, deployType string, config map[string]any) string {
	var deploymentConfigs []models.DeploymentConfig

	for _, service := range services {
		deploymentConfig := models.DeploymentConfig{
			ServiceName: service.Name,
			Images:      service.Images,
			Version:     deployType,
			Replicas:    service.Replicas,
			Resources:   service.Resources,
			Environment: service.Environment,
			Strategy:    service.Strategy,
		}

		// 从配置中获取部署特定设置
		if deployData, exists := config["deployment"]; exists {
			if deployMap, ok := deployData.(map[string]any); ok {
				if strategy, exists := deployMap["strategy"]; exists {
					if strategyStr, ok := strategy.(string); ok {
						deploymentConfig.Strategy = strategyStr
					}
				}
			}
		}

		// 如果没有设置策略，根据部署类型设置默认策略
		if deploymentConfig.Strategy == "" {
			if deployType == CanaryVersionLabel {
				deploymentConfig.Strategy = CanaryVersionLabel
			} else {
				deploymentConfig.Strategy = "rolling"
			}
		}

		deploymentConfigs = append(deploymentConfigs, deploymentConfig)
	}

	result := map[string]any{
		"deploymentConfigs": deploymentConfigs,
	}

	configBytes, _ := json.Marshal(result)
	return string(configBytes)
}

// buildMultiTrafficConfig 从请求构建多服务流量配置
func (s *CanaryStrategy) buildMultiTrafficConfig(services []models.WorkflowServiceConfig, config map[string]any) string {
	var trafficConfigs []models.TrafficConfig

	for _, service := range services {
		trafficConfig := models.TrafficConfig{
			ServiceName: service.Name,
			Strategy:    "weight",
		}

		// 从配置中获取流量分配
		if trafficData, exists := config["traffic"]; exists {
			if trafficMap, ok := trafficData.(map[string]any); ok {
				if weights, exists := trafficMap["weights"]; exists {
					if weightsSlice, ok := weights.([]any); ok {
						var trafficWeights []models.TrafficWeight
						for _, weight := range weightsSlice {
							if weightMap, ok := weight.(map[string]any); ok {
								tw := models.TrafficWeight{}
								if version, exists := weightMap["version"]; exists {
									if versionStr, ok := version.(string); ok {
										tw.Version = versionStr
									}
								}
								if weightVal, exists := weightMap["weight"]; exists {
									if weightInt, ok := weightVal.(float64); ok {
										tw.Weight = int32(weightInt)
									}
								}
								trafficWeights = append(trafficWeights, tw)
							}
						}
						trafficConfig.Weights = trafficWeights
					}
				}
			}
		}

		// 如果没有配置权重，使用默认的灰度权重
		if len(trafficConfig.Weights) == 0 {
			trafficConfig.Weights = []models.TrafficWeight{
				{Version: CanaryVersionLabel, Weight: 10},
				{Version: "stable", Weight: 90},
			}
		}

		trafficConfigs = append(trafficConfigs, trafficConfig)
	}

	result := map[string]any{
		"trafficConfigs": trafficConfigs,
	}

	configBytes, _ := json.Marshal(result)
	return string(configBytes)
}

// buildDefaultApprovalConfig 构建默认审批配置
func (s *CanaryStrategy) buildDefaultApprovalConfig() string {
	config := map[string]any{
		"approvalConfig": models.ApprovalConfig{
			Approvers:     []string{"admin", "ops-lead"},
			ConditionType: "and",
			Timeout:       3600,
		},
	}

	configBytes, _ := json.Marshal(config)
	return string(configBytes)
}

// buildDefaultMonitoringConfig 构建默认监控配置
func (s *CanaryStrategy) buildDefaultMonitoringConfig() string {
	config := map[string]any{
		"monitorConfig": models.MonitorConfig{
			Duration: "30m",
			Metrics:  []string{"response_time", "error_rate", "throughput"},
			Thresholds: []models.MonitorThreshold{
				{Name: "error_rate", Operator: "<", Value: 0.01, Unit: "%"},
				{Name: "response_time", Operator: "<", Value: 500, Unit: "ms"},
			},
		},
	}

	configBytes, _ := json.Marshal(config)
	return string(configBytes)
}

// buildDefaultCleanupConfig 构建默认清理配置
func (s *CanaryStrategy) buildDefaultCleanupConfig() string {
	config := map[string]any{
		"cleanupConfig": map[string]any{
			"cleanupType": "canary_cleanup",
			"targets":     []string{"canary-deployments", "canary-services", "canary-ingress"},
		},
	}

	configBytes, _ := json.Marshal(config)
	return string(configBytes)
}

// GetConfigTemplate 获取灰度发布配置模板
func (s *CanaryStrategy) GetConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return config.GetCanaryConfigTemplate()
}
