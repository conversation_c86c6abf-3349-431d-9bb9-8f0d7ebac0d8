package strategy

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/app/workflow/config"
	"pilot-api/internal/pkg/types"
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// CommonStrategyExecutor 通用策略执行器
type CommonStrategyExecutor struct {
	*trace.Service
	db       *gorm.DB
	registry *workflowpkg.WorkflowEngineRegistry
	engine   *workflowpkg.WorkflowEngine
}

// NewCommonStrategyExecutor 创建通用策略执行器
func NewCommonStrategyExecutor(ts *trace.Service, db *gorm.DB, registry *workflowpkg.WorkflowEngineRegistry, engine *workflowpkg.WorkflowEngine) *CommonStrategyExecutor {
	return &CommonStrategyExecutor{
		Service:  trace.IfNil(ts),
		db:       db,
		registry: registry,
		engine:   engine,
	}
}

// ExecuteWorkflowWithStrategy 通用工作流执行逻辑
func (e *CommonStrategyExecutor) ExecuteWorkflowWithStrategy(
	ctx context.Context,
	execution *models.WorkflowExecution,
	workflow *models.Workflow,
	steps []models.WorkflowStep,
	strategyName string,
	serviceExecutor ServiceExecutorFunc,
) error {
	log.Info(fmt.Sprintf("executing %s workflow: %s", strategyName, workflow.Name))

	// 解析服务配置
	services, err := e.parseServices(workflow.Services)
	if err != nil {
		return fmt.Errorf("failed to parse services config: %w", err)
	}

	// 解析全局配置
	globalConfig, err := e.parseGlobalConfig(workflow.GlobalConfig)
	if err != nil {
		return fmt.Errorf("failed to parse global config: %w", err)
	}

	// 按服务分组步骤
	serviceSteps := e.engine.GroupStepsByService(steps)

	// 按服务顺序执行
	for _, service := range services {
		log.Info(fmt.Sprintf("executing %s steps for service: %s", strategyName, service.Name))

		if stepsForService, exists := serviceSteps[service.Name]; exists {
			// 执行该服务的步骤
			if err := serviceExecutor(ctx, execution, stepsForService, service, globalConfig); err != nil {
				return fmt.Errorf("%s service %s execution failed: %w", strategyName, service.Name, err)
			}
		}
	}

	return nil
}

// ServiceExecutorFunc 服务执行函数类型
type ServiceExecutorFunc func(
	ctx context.Context,
	execution *models.WorkflowExecution,
	steps []models.WorkflowStep,
	service models.WorkflowServiceConfig,
	globalConfig *models.WorkflowGlobalConfig,
) error

// ExecuteServiceStepsWithTransaction 通用服务步骤执行逻辑（带事务）
func (e *CommonStrategyExecutor) ExecuteServiceStepsWithTransaction(
	ctx context.Context,
	execution *models.WorkflowExecution,
	steps []models.WorkflowStep,
	service models.WorkflowServiceConfig,
	strategyName string,
	stepGroupExecutor StepGroupExecutorFunc,
) error {
	// 开始事务，确保服务级别的原子性操作
	tx := e.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// 记录服务开始执行
	serviceStatus := models.ServiceExecutionStatus{
		ServiceName:      service.Name,
		Namespace:        execution.Context, // 从执行上下文获取namespace
		DeploymentStatus: "pending",
		HealthStatus:     "unknown",
		LastUpdated:      time.Now().Format(time.RFC3339),
	}

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update service execution status: %w", err)
	}

	if err := tx.Save(execution).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save execution status: %w", err)
	}

	// 按策略分组和执行步骤
	if err := stepGroupExecutor(ctx, tx, execution, steps, service, strategyName); err != nil {
		// 更新服务状态为失败
		serviceStatus.DeploymentStatus = DeploymentStatusFailed
		serviceStatus.ErrorMessage = err.Error()
		serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

		if updateErr := execution.UpdateServiceStatus(service.Name, serviceStatus); updateErr == nil {
			tx.Save(execution)
		}

		tx.Rollback()
		return err
	}

	// 更新服务状态为成功
	serviceStatus.DeploymentStatus = "completed"
	serviceStatus.HealthStatus = HealthStatusHealthy
	serviceStatus.LastUpdated = time.Now().Format(time.RFC3339)

	if err := execution.UpdateServiceStatus(service.Name, serviceStatus); err != nil {
		log.ErrorE("failed to update service status to completed", err)
	}

	if err := tx.Save(execution).Error; err != nil {
		log.ErrorE("failed to save execution status", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// StepGroupExecutorFunc 步骤组执行函数类型
type StepGroupExecutorFunc func(
	ctx context.Context,
	tx *gorm.DB,
	execution *models.WorkflowExecution,
	steps []models.WorkflowStep,
	service models.WorkflowServiceConfig,
	strategyName string,
) error

// ExecuteStepWithSavepoint 在保存点中执行步骤（用于事务管理）
func (e *CommonStrategyExecutor) ExecuteStepWithSavepoint(
	ctx context.Context,
	tx *gorm.DB,
	execution *models.WorkflowExecution,
	step *models.WorkflowStep,
	groupName string,
) error {
	// 创建事务保存点
	savepoint := fmt.Sprintf("sp_%s_%s", step.ServiceName, groupName)
	if err := tx.Exec(fmt.Sprintf("SAVEPOINT %s", savepoint)).Error; err != nil {
		return fmt.Errorf("failed to create savepoint: %w", err)
	}

	// 执行步骤
	stepHandler, err := e.registry.GetStepHandler(step.Type)
	if err != nil {
		return fmt.Errorf("failed to get step handler for type %s: %w", step.Type, err)
	}

	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusRunning,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
	}

	// 保存步骤执行记录
	if createErr := tx.Create(stepExecution).Error; createErr != nil {
		// 回滚到保存点
		tx.Exec(fmt.Sprintf("ROLLBACK TO SAVEPOINT %s", savepoint))
		return fmt.Errorf("failed to create step execution: %w", createErr)
	}

	// 执行步骤
	err = stepHandler.Execute(ctx, stepExecution, step)
	if err != nil {
		// 回滚到保存点
		tx.Exec(fmt.Sprintf("ROLLBACK TO SAVEPOINT %s", savepoint))
		return fmt.Errorf("step execution failed: %w", err)
	}

	// 更新步骤执行状态
	stepExecution.Status = models.StepStatusCompleted
	endTime := time.Now()
	stepExecution.EndTime = &endTime

	if err := tx.Save(stepExecution).Error; err != nil {
		log.ErrorE("failed to update step execution status", err)
	}

	// 释放保存点
	if err := tx.Exec(fmt.Sprintf("RELEASE SAVEPOINT %s", savepoint)).Error; err != nil {
		log.ErrorE(fmt.Sprintf("failed to release savepoint %s", savepoint), err)
	}

	return nil
}

// ValidateWorkflowConfig 通用配置验证
func (e *CommonStrategyExecutor) ValidateWorkflowConfig(workflow *models.Workflow, strategyName string) error {
	if workflow.Services == "" {
		return fmt.Errorf("%s workflow requires services configuration", strategyName)
	}

	var services []models.WorkflowServiceConfig
	if err := json.Unmarshal([]byte(workflow.Services), &services); err != nil {
		return fmt.Errorf("invalid services configuration: %w", err)
	}

	if len(services) == 0 {
		return fmt.Errorf("%s workflow requires at least one service", strategyName)
	}

	return nil
}

// ConfigureWorkflowFromRequest 通用工作流配置方法（使用新的type-safe配置系统）
func (e *CommonStrategyExecutor) ConfigureWorkflowFromRequest(
	workflow *models.Workflow,
	request any,
	strategyName string,
) error {
	// 尝试将request转换为通用配置请求接口
	configRequest, ok := request.(ConfigurableRequest)
	if !ok {
		return fmt.Errorf("invalid request type for %s strategy", strategyName)
	}

	// 使用新的配置系统解析请求
	adapter := config.NewTemplateConfigAdapter(
		configRequest.GetName(),
		configRequest.GetDescription(),
		workflow.Type,
		configRequest.GetNamespace(),
		configRequest.GetClusterID(),
		configRequest.GetLabels(),
		configRequest.GetConfig(),
	)

	if !adapter.IsValid() {
		return fmt.Errorf("invalid configuration for %s workflow", strategyName)
	}

	// 使用type-safe配置
	services := adapter.GetServices()
	globalConfig := adapter.GetGlobalConfig()

	// 转换为模型格式
	workflowServices := e.convertCanaryServicesToWorkflowServices(services)
	workflowGlobalConfig := e.convertCanaryGlobalConfigToWorkflowGlobalConfig(globalConfig)

	// 序列化配置
	if len(workflowServices) > 0 {
		servicesJSON, err := json.Marshal(workflowServices)
		if err != nil {
			return fmt.Errorf("failed to marshal services: %w", err)
		}
		workflow.Services = string(servicesJSON)
	}

	if workflowGlobalConfig != nil {
		globalConfigJSON, err := json.Marshal(workflowGlobalConfig)
		if err != nil {
			return fmt.Errorf("failed to marshal global config: %w", err)
		}
		workflow.GlobalConfig = string(globalConfigJSON)
	}

	return nil
}

// ConfigurableRequest 可配置请求接口
type ConfigurableRequest interface {
	GetName() string
	GetDescription() string
	GetNamespace() string
	GetClusterID() string
	GetLabels() map[string]string
	GetConfig() map[string]any
}

// 辅助方法

func (e *CommonStrategyExecutor) parseServices(servicesJSON string) ([]models.WorkflowServiceConfig, error) {
	var services []models.WorkflowServiceConfig
	if servicesJSON != "" {
		if err := json.Unmarshal([]byte(servicesJSON), &services); err != nil {
			return nil, err
		}
	}
	return services, nil
}

func (e *CommonStrategyExecutor) parseGlobalConfig(globalConfigJSON string) (*models.WorkflowGlobalConfig, error) {
	var globalConfig *models.WorkflowGlobalConfig
	if globalConfigJSON != "" {
		if err := json.Unmarshal([]byte(globalConfigJSON), &globalConfig); err != nil {
			return nil, err
		}
	}
	return globalConfig, nil
}

func (e *CommonStrategyExecutor) convertCanaryServicesToWorkflowServices(canaryServices []types.CanaryService) []models.WorkflowServiceConfig {
	workflowServices := make([]models.WorkflowServiceConfig, len(canaryServices))
	for i, canaryService := range canaryServices {
		workflowServices[i] = models.WorkflowServiceConfig{
			Name:         canaryService.ServiceName,
			Order:        canaryService.Order,
			Images:       canaryService.Images,
			Version:      canaryService.Version,
			Replicas:     canaryService.CanaryReplicas,
			Resources:    canaryService.Resources,
			Environment:  canaryService.Envs,
			Strategy:     canaryService.Strategy,
			Dependencies: canaryService.Dependencies,
		}
	}
	return workflowServices
}

func (e *CommonStrategyExecutor) convertCanaryGlobalConfigToWorkflowGlobalConfig(canaryGlobalConfig *types.CanaryGlobalConfig) *models.WorkflowGlobalConfig {
	if canaryGlobalConfig == nil {
		return nil
	}

	workflowGlobalConfig := &models.WorkflowGlobalConfig{}

	if canaryGlobalConfig.GlobalTimeout != nil {
		workflowGlobalConfig.GlobalTimeout = &models.WorkflowGlobalTimeout{
			StepTimeout:       canaryGlobalConfig.GlobalTimeout.StepTimeout,
			ApprovalTimeout:   canaryGlobalConfig.GlobalTimeout.ApprovalTimeout,
			MonitoringTimeout: canaryGlobalConfig.GlobalTimeout.MonitoringTimeout,
			RollbackTimeout:   canaryGlobalConfig.GlobalTimeout.RollbackTimeout,
		}
	}

	if canaryGlobalConfig.GlobalNotification != nil {
		workflowGlobalConfig.GlobalNotification = &models.WorkflowGlobalNotification{
			Enabled:  canaryGlobalConfig.GlobalNotification.Enabled,
			Channels: canaryGlobalConfig.GlobalNotification.Channels,
			Events:   canaryGlobalConfig.GlobalNotification.Events,
			Template: canaryGlobalConfig.GlobalNotification.Template,
			Users:    canaryGlobalConfig.GlobalNotification.Users,
		}
	}

	if canaryGlobalConfig.GlobalRollback != nil {
		workflowGlobalConfig.GlobalRollback = &models.WorkflowGlobalRollback{
			Enabled:      canaryGlobalConfig.GlobalRollback.AutoRollback,
			AutoRollback: canaryGlobalConfig.GlobalRollback.AutoRollback,
		}
	}

	if canaryGlobalConfig.GlobalMonitoring != nil {
		workflowGlobalConfig.GlobalMonitoring = &models.WorkflowGlobalMonitoring{
			Enabled: canaryGlobalConfig.GlobalMonitoring.EnableTracing,
		}
	}

	return workflowGlobalConfig
}
