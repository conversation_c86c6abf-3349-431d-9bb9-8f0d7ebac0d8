package workflow

import (
	workflowpkg "pilot-api/internal/pkg/workflow"
	"pilot-api/pkg/models"
)

// WorkflowResponse 工作流响应
type WorkflowResponse struct {
	models.Workflow
	Steps []models.WorkflowStep `json:"steps"`
}

// WorkflowExecutionResponse 工作流执行响应
type WorkflowExecutionResponse struct {
	models.WorkflowExecution
	StepExecutions []models.StepExecution `json:"stepExecutions"`
}

// WorkflowMetricsResponse 工作流指标响应
type WorkflowMetricsResponse struct {
	WorkflowID      int64   `json:"workflowId"`
	TotalExecutions int64   `json:"totalExecutions"`
	SuccessRate     float64 `json:"successRate"`
	FailureRate     float64 `json:"failureRate"`
	AverageTime     float64 `json:"averageTime"`
	RunningCount    int64   `json:"runningCount"`
}

// WorkflowTemplateResponse 工作流模板响应
type WorkflowTemplateResponse struct {
	StrategyType models.WorkflowType          `json:"strategyType"`
	Metadata     workflowpkg.StrategyMetadata `json:"metadata"`
	Steps        []models.WorkflowStep        `json:"steps"`
}

// WorkflowConfigTemplateResponse 工作流配置模板响应
type WorkflowConfigTemplateResponse struct {
	Type         models.WorkflowType          `json:"type"`         // 工作流类型
	Name         string                       `json:"name"`         // 模板名称
	Description  string                       `json:"description"`  // 模板描述
	ConfigSchema WorkflowConfigSchema         `json:"configSchema"` // 配置Schema
	Metadata     workflowpkg.StrategyMetadata `json:"metadata"`     // 策略元数据
}

// WorkflowConfigSchema 工作流配置Schema
type WorkflowConfigSchema struct {
	Schema      map[string]any `json:"schema"`      // JSON Schema
	DefaultData map[string]any `json:"defaultData"` // 默认数据
	Examples    map[string]any `json:"examples"`    // 示例数据
}

// WorkflowListResponse 工作流列表响应（带分页）
type WorkflowListResponse struct {
	List     []*models.Workflow `json:"list"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"pageSize"`
}

// WorkflowExecutionListResponse 工作流执行列表响应（带分页）
type WorkflowExecutionListResponse struct {
	List     []*WorkflowExecutionWithWorkflow `json:"list"`
	Total    int64                            `json:"total"`
	Page     int                              `json:"page"`
	PageSize int                              `json:"pageSize"`
}

// WorkflowExecutionWithWorkflow 工作流执行和工作流信息的组合
type WorkflowExecutionWithWorkflow struct {
	models.WorkflowExecution
	WorkflowName string `json:"workflowName"`
	WorkflowType string `json:"workflowType"`
}

// WorkflowStatusSummary 工作流状态汇总
type WorkflowStatusSummary struct {
	TotalWorkflows    int64 `json:"totalWorkflows"`
	DraftWorkflows    int64 `json:"draftWorkflows"`
	PendingWorkflows  int64 `json:"pendingWorkflows"`
	ApprovedWorkflows int64 `json:"approvedWorkflows"`
	RejectedWorkflows int64 `json:"rejectedWorkflows"`
}

// WorkflowExecutionSummary 工作流执行汇总
type WorkflowExecutionSummary struct {
	TotalExecutions     int64 `json:"totalExecutions"`
	RunningExecutions   int64 `json:"runningExecutions"`
	CompletedExecutions int64 `json:"completedExecutions"`
	FailedExecutions    int64 `json:"failedExecutions"`
	CanceledExecutions  int64 `json:"canceledExecutions"`
}

// WorkflowTypesResponse 工作流类型列表响应
type WorkflowTypesResponse struct {
	Types []WorkflowTypeInfo `json:"types"`
}

// WorkflowTypeInfo 工作流类型信息
type WorkflowTypeInfo struct {
	Type        models.WorkflowType `json:"type"`        // 工作流类型
	Name        string              `json:"name"`        // 名称
	Description string              `json:"description"` // 描述
	Version     string              `json:"version"`     // 版本
	Author      string              `json:"author"`      // 作者
	Supported   bool                `json:"supported"`   // 是否支持
	Tags        []string            `json:"tags"`        // 标签
	Features    []string            `json:"features"`    // 特性
}

// StepExecutionDetail 步骤执行详情
type StepExecutionDetail struct {
	models.StepExecution
	StepName string `json:"stepName"`
	StepType string `json:"stepType"`
}

// WorkflowExecutionDetailResponse 工作流执行详情响应
type WorkflowExecutionDetailResponse struct {
	WorkflowExecution models.WorkflowExecution        `json:"workflowExecution"`
	WorkflowInfo      WorkflowBasicInfo               `json:"workflowInfo"`
	StepExecutions    []StepExecutionDetail           `json:"stepExecutions"`
	ServiceStatuses   []models.ServiceExecutionStatus `json:"serviceStatuses"`
}

// WorkflowBasicInfo 工作流基本信息
type WorkflowBasicInfo struct {
	ID          int64               `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Type        models.WorkflowType `json:"type"`
	Namespace   string              `json:"namespace"`
	ClusterID   string              `json:"clusterId"`
}

// ApprovalStepResponse 审批步骤响应
type ApprovalStepResponse struct {
	ExecutionID    int64  `json:"executionId"`
	StepID         int64  `json:"stepId"`
	StepName       string `json:"stepName"`
	Status         string `json:"status"`
	ApprovalResult string `json:"approvalResult"`
	Comment        string `json:"comment"`
	UpdatedAt      string `json:"updatedAt"`
}

// CreateWorkflowFromTemplateResponse 从模板创建工作流响应
type CreateWorkflowFromTemplateResponse struct {
	Steps []models.WorkflowStep `json:"steps"`
}
