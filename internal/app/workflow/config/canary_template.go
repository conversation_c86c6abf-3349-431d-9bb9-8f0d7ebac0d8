package config

import (
	workflowpkg "pilot-api/internal/pkg/workflow"
)

// GetCanaryConfigTemplate 获取灰度发布配置模板
func GetCanaryConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return &workflowpkg.WorkflowConfigTemplate{
		Name:        "灰度发布工作流",
		Description: "支持多服务的端到端灰度发布策略，具备流量传播和依赖管理能力",
		ConfigForm: workflowpkg.WorkflowConfigForm{
			Sections: []workflowpkg.ConfigSection{
				{
					Title:       "基础配置",
					Description: "工作流的基本信息配置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "name",
							Label:       "工作流名称",
							Type:        "text",
							Required:    true,
							Placeholder: "请输入工作流名称",
							Description: "用于标识工作流的唯一名称",
							Validation: &workflowpkg.Validation{
								MinLen: intPtr(1),
								MaxLen: intPtr(100),
							},
						},
						{
							Key:         "description",
							Label:       "工作流描述",
							Type:        "textarea",
							Required:    false,
							Placeholder: "请输入工作流描述",
							Description: "工作流的详细说明",
							Validation: &workflowpkg.Validation{
								MaxLen: intPtr(500),
							},
						},
						{
							Key:         "namespace",
							Label:       "命名空间",
							Type:        "text",
							Required:    true,
							Placeholder: "例如：production",
							Description: "Kubernetes命名空间",
						},
						{
							Key:         "clusterId",
							Label:       "集群ID",
							Type:        "select",
							Required:    true,
							Description: "目标Kubernetes集群",
							Options: []workflowpkg.Option{
								{Label: "生产集群", Value: "prod-cluster"},
								{Label: "预发集群", Value: "staging-cluster"},
								{Label: "测试集群", Value: "test-cluster"},
							},
						},
					},
				},
				{
					Title:       "服务配置",
					Description: "配置参与灰度发布的服务列表",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "services",
							Label:       "服务列表",
							Type:        "array",
							Required:    true,
							Description: "参与灰度发布的服务配置",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(1),
							},
							Default: []map[string]any{
								{
									"serviceName":    "service-a",
									"order":          0,
									"canaryReplicas": 1,
									"prodReplicas":   3,
									"trafficRatio":   20,
									"images": map[string]string{
										"app": "nginx:latest",
									},
								},
							},
						},
					},
				},
				{
					Title:       "灰度策略配置",
					Description: "灰度发布的具体策略参数",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "canaryConfig.propagationHeader",
							Label:       "传播请求头",
							Type:        "text",
							Required:    false,
							Default:     "x-canary-version",
							Description: "用于流量传播的HTTP请求头名称",
							Placeholder: "x-canary-version",
						},
						{
							Key:         "canaryConfig.propagationValue",
							Label:       "传播请求头值",
							Type:        "text",
							Required:    false,
							Default:     "canary",
							Description: "用于流量传播的HTTP请求头值",
							Placeholder: "canary",
						},
						{
							Key:         "canaryConfig.entryServices",
							Label:       "入口服务",
							Type:        "array",
							Required:    false,
							Description: "流量入口服务列表，留空则自动识别",
						},
						{
							Key:         "canaryConfig.serviceDependencies",
							Label:       "服务依赖关系",
							Type:        "object",
							Required:    false,
							Description: "定义服务间的依赖关系，用于控制发布顺序",
							Default: map[string]any{
								"service-b": []string{"service-a"},
								"service-c": []string{"service-b"},
							},
						},
						{
							Key:         "canaryConfig.trafficStrategy",
							Label:       "流量策略",
							Type:        "select",
							Required:    false,
							Default:     "weight",
							Description: "流量分发策略",
							Options: []workflowpkg.Option{
								{Label: "权重路由", Value: "weight"},
								{Label: "请求头路由", Value: "header"},
								{Label: "Cookie路由", Value: "cookie"},
							},
						},
						{
							Key:         "canaryConfig.defaultTrafficRatio",
							Label:       "默认流量比例",
							Type:        "number",
							Required:    false,
							Default:     20,
							Description: "默认灰度流量比例（百分比）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(1),
								Max: float64Ptr(100),
							},
						},
					},
				},
				{
					Title:       "全局配置",
					Description: "工作流的全局设置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "globalConfig.globalTimeout.stepTimeout",
							Label:       "步骤超时时间",
							Type:        "number",
							Required:    false,
							Default:     600,
							Description: "单个步骤的最大执行时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(60),
								Max: float64Ptr(3600),
							},
						},
						{
							Key:         "globalConfig.globalTimeout.approvalTimeout",
							Label:       "审批超时时间",
							Type:        "number",
							Required:    false,
							Default:     3600,
							Description: "审批步骤的最大等待时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(300),
								Max: float64Ptr(86400),
							},
						},
						{
							Key:         "globalConfig.globalNotification.users",
							Label:       "审批人列表",
							Type:        "array",
							Required:    false,
							Description: "工作流审批人员列表",
							Default:     []string{"admin", "ops-lead"},
						},
						{
							Key:         "globalConfig.globalRollback.autoRollback",
							Label:       "自动回滚",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "当发生错误时是否自动回滚",
						},
						{
							Key:         "globalConfig.globalMonitoring.enableTracing",
							Label:       "启用监控验证",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "是否启用端到端监控验证",
						},
					},
				},
			},
		},
		Metadata: workflowpkg.StrategyMetadata{
			Name:        "Canary Deployment Strategy",
			Description: "Advanced canary deployment strategy that supports both single service and multi-service scenarios with propagation capabilities",
			Version:     "1.0.0",
			Author:      "Makeblock DevOps Team",
			Tags:        []string{"deployment", "canary", "traffic-splitting", "rollback"},
			Features: []string{
				"traffic-propagation",
				"end-to-end-canary",
				"traffic-splitting",
				"health-monitoring",
				"automatic-rollback",
				"approval-gates",
				"multi-service-support",
				"parallel-execution",
				"dependency-management",
			},
		},
	}
}

// intPtr 创建int指针
func intPtr(i int) *int {
	return &i
}

// float64Ptr 创建float64指针
func float64Ptr(f float64) *float64 {
	return &f
}
