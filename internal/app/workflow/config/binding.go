package config

import (
	"pilot-api/internal/pkg/types"
	"pilot-api/pkg/models"
)

// WorkflowConfig 工作流配置接口
type WorkflowConfig interface {
	GetName() string
	GetDescription() string
	GetType() models.WorkflowType
	GetNamespace() string
	GetClusterID() string
	GetLabels() map[string]string
	Validate() error
}

// BaseWorkflowConfig 基础工作流配置
type BaseWorkflowConfig struct {
	Name         string               `config:"key:name,required" validation:"minLen:1,maxLen:100" json:"name"`
	Description  string               `config:"key:description" validation:"maxLen:500" json:"description"`
	Namespace    string               `config:"key:namespace,required" json:"namespace"`
	ClusterID    string               `config:"key:clusterId,required" json:"clusterId"`
	Labels       map[string]string    `config:"key:labels" json:"labels"`
	Type         models.WorkflowType  `config:"key:type,required" json:"type"`
	GlobalConfig *GlobalConfigBinding `config:"key:globalConfig" json:"globalConfig"`
}

// GlobalConfigBinding 全局配置绑定
type GlobalConfigBinding struct {
	GlobalTimeout      *GlobalTimeoutBinding      `config:"key:globalTimeout" json:"globalTimeout"`
	GlobalNotification *GlobalNotificationBinding `config:"key:globalNotification" json:"globalNotification"`
	GlobalRollback     *GlobalRollbackBinding     `config:"key:globalRollback" json:"globalRollback"`
	GlobalMonitoring   *GlobalMonitoringBinding   `config:"key:globalMonitoring" json:"globalMonitoring"`
}

// GlobalTimeoutBinding 全局超时配置绑定
type GlobalTimeoutBinding struct {
	StepTimeout       int `config:"key:stepTimeout,default:600" validation:"min:60,max:3600" json:"stepTimeout"`
	ApprovalTimeout   int `config:"key:approvalTimeout,default:3600" validation:"min:300,max:86400" json:"approvalTimeout"`
	MonitoringTimeout int `config:"key:monitoringTimeout,default:300" validation:"min:60,max:1800" json:"monitoringTimeout"`
	RollbackTimeout   int `config:"key:rollbackTimeout,default:180" validation:"min:30,max:600" json:"rollbackTimeout"`
}

// GlobalNotificationBinding 全局通知配置绑定
type GlobalNotificationBinding struct {
	Enabled  bool     `config:"key:enabled,default:true" json:"enabled"`
	Channels []string `config:"key:channels" json:"channels"`
	Events   []string `config:"key:events" json:"events"`
	Template string   `config:"key:template" json:"template"`
	Users    []string `config:"key:users" json:"users"`
}

// GlobalRollbackBinding 全局回滚配置绑定
type GlobalRollbackBinding struct {
	AutoRollback     bool    `config:"key:autoRollback,default:true" json:"autoRollback"`
	ErrorThreshold   float64 `config:"key:errorThreshold,default:0.05" validation:"min:0,max:1" json:"errorThreshold"`
	LatencyThreshold float64 `config:"key:latencyThreshold,default:2000" validation:"min:100" json:"latencyThreshold"`
}

// GlobalMonitoringBinding 全局监控配置绑定
type GlobalMonitoringBinding struct {
	EnableTracing bool     `config:"key:enableTracing,default:true" json:"enableTracing"`
	TraceHeaders  []string `config:"key:traceHeaders" json:"traceHeaders"`
}

// CanaryWorkflowConfig 灰度工作流配置
type CanaryWorkflowConfig struct {
	BaseWorkflowConfig
	Services     []CanaryServiceBinding `config:"key:services,required" validation:"min:1" json:"services"`
	CanaryConfig *CanaryConfigBinding   `config:"key:canaryConfig" json:"canaryConfig"`
}

// CanaryServiceBinding 灰度服务配置绑定
type CanaryServiceBinding struct {
	ServiceName      string                      `config:"key:serviceName,required" json:"serviceName"`
	Order            int                         `config:"key:order,default:0" validation:"min:0" json:"order"`
	Images           map[string]string           `config:"key:images,required" json:"images"`
	Version          string                      `config:"key:version" json:"version"`
	Envs             map[string]string           `config:"key:envs" json:"envs"`
	Resources        map[string]string           `config:"key:resources" json:"resources"`
	Dependencies     []string                    `config:"key:dependencies" json:"dependencies"`
	CanaryReplicas   int32                       `config:"key:canaryReplicas,default:1" validation:"min:1" json:"canaryReplicas"`
	ProdReplicas     int32                       `config:"key:prodReplicas,default:3" validation:"min:1" json:"prodReplicas"`
	TrafficRatio     int32                       `config:"key:trafficRatio,default:20" validation:"min:0,max:100" json:"trafficRatio"`
	Strategy         string                      `config:"key:strategy,default:canary" json:"strategy"`
	RoutingConfig    *ServiceRoutingBinding      `config:"key:routingConfig" json:"routingConfig"`
	MonitoringConfig *ServiceMonitoringBinding   `config:"key:monitoringConfig" json:"monitoringConfig"`
	Policy           *CanaryServicePolicyBinding `config:"key:policy" json:"policy"`
}

// CanaryConfigBinding 灰度配置绑定
type CanaryConfigBinding struct {
	PropagationHeader   string              `config:"key:propagationHeader,default:x-canary-version" json:"propagationHeader"`
	PropagationValue    string              `config:"key:propagationValue,default:canary" json:"propagationValue"`
	EntryServices       []string            `config:"key:entryServices" json:"entryServices"`
	ServiceDependencies map[string][]string `config:"key:serviceDependencies" json:"serviceDependencies"`
	TrafficStrategy     string              `config:"key:trafficStrategy,default:weight" json:"trafficStrategy"`
	DefaultTrafficRatio int32               `config:"key:defaultTrafficRatio,default:20" validation:"min:1,max:100" json:"defaultTrafficRatio"`
}

// ServiceRoutingBinding 服务路由配置绑定
type ServiceRoutingBinding struct {
	Strategy      string                `config:"key:strategy,default:weight" json:"strategy"`
	HeaderRouting *HeaderRoutingBinding `config:"key:headerRouting" json:"headerRouting"`
	WeightRouting *WeightRoutingBinding `config:"key:weightRouting" json:"weightRouting"`
	CookieRouting *CookieRoutingBinding `config:"key:cookieRouting" json:"cookieRouting"`
}

// HeaderRoutingBinding 基于请求头的路由配置绑定
type HeaderRoutingBinding struct {
	HeaderName  string `config:"key:headerName,required" json:"headerName"`
	HeaderValue string `config:"key:headerValue,required" json:"headerValue"`
}

// WeightRoutingBinding 基于权重的路由配置绑定
type WeightRoutingBinding struct {
	CanaryWeight int32 `config:"key:canaryWeight,default:20" validation:"min:0,max:100" json:"canaryWeight"`
	StableWeight int32 `config:"key:stableWeight,default:80" validation:"min:0,max:100" json:"stableWeight"`
}

// CookieRoutingBinding 基于Cookie的路由配置绑定
type CookieRoutingBinding struct {
	CookieName  string `config:"key:cookieName,required" json:"cookieName"`
	CookieValue string `config:"key:cookieValue,required" json:"cookieValue"`
}

// ServiceMonitoringBinding 服务监控配置绑定
type ServiceMonitoringBinding struct {
	Metrics    []string                  `config:"key:metrics" json:"metrics"`
	Thresholds []ServiceThresholdBinding `config:"key:thresholds" json:"thresholds"`
	Duration   string                    `config:"key:duration,default:5m" json:"duration"`
	Interval   string                    `config:"key:interval,default:30s" json:"interval"`
}

// ServiceThresholdBinding 服务阈值绑定
type ServiceThresholdBinding struct {
	Name     string  `config:"key:name,required" json:"name"`
	Operator string  `config:"key:operator,default:>=" json:"operator"`
	Value    float64 `config:"key:value,required" json:"value"`
	Unit     string  `config:"key:unit" json:"unit"`
}

// CanaryServicePolicyBinding 服务级别策略绑定
type CanaryServicePolicyBinding struct {
	AutoPromotion        bool                   `config:"key:autoPromotion,default:false" json:"autoPromotion"`
	AutoPromotionMetrics []string               `config:"key:autoPromotionMetrics" json:"autoPromotionMetrics"`
	AutoRollback         bool                   `config:"key:autoRollback,default:true" json:"autoRollback"`
	AutoRollbackMetrics  []string               `config:"key:autoRollbackMetrics" json:"autoRollbackMetrics"`
	HealthCheck          *HealthCheckBinding    `config:"key:healthCheck" json:"healthCheck"`
	ResourceLimits       *ResourceLimitsBinding `config:"key:resourceLimits" json:"resourceLimits"`
}

// HealthCheckBinding 健康检查配置绑定
type HealthCheckBinding struct {
	Path                string `config:"key:path,default:/health" json:"path"`
	IntervalSeconds     int    `config:"key:intervalSeconds,default:10" validation:"min:1" json:"intervalSeconds"`
	TimeoutSeconds      int    `config:"key:timeoutSeconds,default:5" validation:"min:1" json:"timeoutSeconds"`
	SuccessThreshold    int    `config:"key:successThreshold,default:1" validation:"min:1" json:"successThreshold"`
	FailureThreshold    int    `config:"key:failureThreshold,default:3" validation:"min:1" json:"failureThreshold"`
	InitialDelaySeconds int    `config:"key:initialDelaySeconds,default:10" validation:"min:0" json:"initialDelaySeconds"`
}

// ResourceLimitsBinding 资源限制配置绑定
type ResourceLimitsBinding struct {
	CPU    string `config:"key:cpu,default:500m" json:"cpu"`
	Memory string `config:"key:memory,default:512Mi" json:"memory"`
}

// 实现 WorkflowConfig 接口
func (c *BaseWorkflowConfig) GetName() string {
	return c.Name
}

func (c *BaseWorkflowConfig) GetDescription() string {
	return c.Description
}

func (c *BaseWorkflowConfig) GetType() models.WorkflowType {
	return c.Type
}

func (c *BaseWorkflowConfig) GetNamespace() string {
	return c.Namespace
}

func (c *BaseWorkflowConfig) GetClusterID() string {
	return c.ClusterID
}

func (c *BaseWorkflowConfig) GetLabels() map[string]string {
	if c.Labels == nil {
		return make(map[string]string)
	}
	return c.Labels
}

func (c *BaseWorkflowConfig) Validate() error {
	// 基础验证逻辑
	return nil
}

// ToCanaryServices 将 CanaryServiceBinding 转换为 types.CanaryService
func ToCanaryServices(bindings []CanaryServiceBinding) []types.CanaryService {
	services := make([]types.CanaryService, len(bindings))

	for i, binding := range bindings {
		service := types.CanaryService{
			ServiceName:    binding.ServiceName,
			Order:          binding.Order,
			Images:         binding.Images,
			Version:        binding.Version,
			Envs:           binding.Envs,
			Resources:      binding.Resources,
			Dependencies:   binding.Dependencies,
			CanaryReplicas: binding.CanaryReplicas,
			ProdReplicas:   binding.ProdReplicas,
			TrafficRatio:   binding.TrafficRatio,
			Strategy:       binding.Strategy,
		}

		// 转换路由配置
		if binding.RoutingConfig != nil {
			service.RoutingConfig = &types.ServiceRoutingConfig{
				Strategy: binding.RoutingConfig.Strategy,
			}

			if binding.RoutingConfig.HeaderRouting != nil {
				service.RoutingConfig.HeaderRouting = &types.HeaderRoutingConfig{
					HeaderName:  binding.RoutingConfig.HeaderRouting.HeaderName,
					HeaderValue: binding.RoutingConfig.HeaderRouting.HeaderValue,
				}
			}

			if binding.RoutingConfig.WeightRouting != nil {
				service.RoutingConfig.WeightRouting = &types.WeightRoutingConfig{
					CanaryWeight: binding.RoutingConfig.WeightRouting.CanaryWeight,
					StableWeight: binding.RoutingConfig.WeightRouting.StableWeight,
				}
			}

			if binding.RoutingConfig.CookieRouting != nil {
				service.RoutingConfig.CookieRouting = &types.CookieRoutingConfig{
					CookieName:  binding.RoutingConfig.CookieRouting.CookieName,
					CookieValue: binding.RoutingConfig.CookieRouting.CookieValue,
				}
			}
		}

		// 转换监控配置
		if binding.MonitoringConfig != nil {
			service.MonitoringConfig = &types.ServiceMonitoringConfig{
				Metrics:  binding.MonitoringConfig.Metrics,
				Duration: binding.MonitoringConfig.Duration,
				Interval: binding.MonitoringConfig.Interval,
			}

			// 转换阈值配置
			if len(binding.MonitoringConfig.Thresholds) > 0 {
				service.MonitoringConfig.Thresholds = make([]types.ServiceThreshold, len(binding.MonitoringConfig.Thresholds))
				for j, threshold := range binding.MonitoringConfig.Thresholds {
					service.MonitoringConfig.Thresholds[j] = types.ServiceThreshold{
						Name:     threshold.Name,
						Operator: threshold.Operator,
						Value:    threshold.Value,
						Unit:     threshold.Unit,
					}
				}
			}
		}

		// 转换策略配置
		if binding.Policy != nil {
			service.Policy = &types.CanaryServicePolicy{
				AutoPromotion:        binding.Policy.AutoPromotion,
				AutoPromotionMetrics: binding.Policy.AutoPromotionMetrics,
				AutoRollback:         binding.Policy.AutoRollback,
				AutoRollbackMetrics:  binding.Policy.AutoRollbackMetrics,
			}

			if binding.Policy.HealthCheck != nil {
				service.Policy.HealthCheck = &types.HealthCheckConfig{
					Path:                binding.Policy.HealthCheck.Path,
					IntervalSeconds:     binding.Policy.HealthCheck.IntervalSeconds,
					TimeoutSeconds:      binding.Policy.HealthCheck.TimeoutSeconds,
					SuccessThreshold:    binding.Policy.HealthCheck.SuccessThreshold,
					FailureThreshold:    binding.Policy.HealthCheck.FailureThreshold,
					InitialDelaySeconds: binding.Policy.HealthCheck.InitialDelaySeconds,
				}
			}

			if binding.Policy.ResourceLimits != nil {
				service.Policy.ResourceLimits = &types.ResourceLimitsConfig{
					CPU:    binding.Policy.ResourceLimits.CPU,
					Memory: binding.Policy.ResourceLimits.Memory,
				}
			}
		}

		services[i] = service
	}

	return services
}

// ToCanaryGlobalConfig 将 GlobalConfigBinding 转换为 types.CanaryGlobalConfig
func (binding *GlobalConfigBinding) ToCanaryGlobalConfig() *types.CanaryGlobalConfig {
	if binding == nil {
		return nil
	}

	config := &types.CanaryGlobalConfig{}

	if binding.GlobalTimeout != nil {
		config.GlobalTimeout = &types.GlobalTimeoutConfig{
			StepTimeout:       binding.GlobalTimeout.StepTimeout,
			ApprovalTimeout:   binding.GlobalTimeout.ApprovalTimeout,
			MonitoringTimeout: binding.GlobalTimeout.MonitoringTimeout,
			RollbackTimeout:   binding.GlobalTimeout.RollbackTimeout,
		}
	}

	if binding.GlobalNotification != nil {
		config.GlobalNotification = &types.GlobalNotificationConfig{
			Enabled:  binding.GlobalNotification.Enabled,
			Channels: binding.GlobalNotification.Channels,
			Events:   binding.GlobalNotification.Events,
			Template: binding.GlobalNotification.Template,
			Users:    binding.GlobalNotification.Users,
		}
	}

	if binding.GlobalRollback != nil {
		config.GlobalRollback = &types.GlobalRollbackConfig{
			AutoRollback:     binding.GlobalRollback.AutoRollback,
			ErrorThreshold:   binding.GlobalRollback.ErrorThreshold,
			LatencyThreshold: binding.GlobalRollback.LatencyThreshold,
		}
	}

	if binding.GlobalMonitoring != nil {
		config.GlobalMonitoring = &types.GlobalMonitoringConfig{
			EnableTracing: binding.GlobalMonitoring.EnableTracing,
			TraceHeaders:  binding.GlobalMonitoring.TraceHeaders,
		}
	}

	return config
}

// ConfigBinder 配置绑定器
type ConfigBinder struct {
	parser *ConfigParser
}

// NewConfigBinder 创建新的配置绑定器
func NewConfigBinder() *ConfigBinder {
	return &ConfigBinder{
		parser: NewConfigParser(),
	}
}

// BindCanaryConfig 绑定灰度工作流配置
func (b *ConfigBinder) BindCanaryConfig(source map[string]any) (*CanaryWorkflowConfig, *ParseResult) {
	config := &CanaryWorkflowConfig{}
	result := b.parser.ParseConfig(source, config)

	if len(result.Errors) > 0 {
		return nil, result
	}

	return config, result
}

// RegisterCustomValidator 注册自定义验证器
func (b *ConfigBinder) RegisterCustomValidator(name string, validator ValidatorFunc) {
	b.parser.RegisterValidator(name, validator)
}

// RegisterCustomConverter 注册自定义转换器
func (b *ConfigBinder) RegisterCustomConverter(typeName string, converter ConverterFunc) {
	b.parser.RegisterConverter(typeName, converter)
}
