package config

import (
	workflowpkg "pilot-api/internal/pkg/workflow"
)

// GetABTestConfigTemplate 获取A/B测试配置模板
func GetABTestConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return &workflowpkg.WorkflowConfigTemplate{
		Name:        "A/B测试工作流",
		Description: "基于数据驱动的A/B测试策略，支持多版本并行测试和自动决策",
		ConfigForm: workflowpkg.WorkflowConfigForm{
			Sections: []workflowpkg.ConfigSection{
				{
					Title:       "基础配置",
					Description: "工作流的基本信息配置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "name",
							Label:       "工作流名称",
							Type:        "text",
							Required:    true,
							Placeholder: "请输入工作流名称",
							Description: "用于标识工作流的唯一名称",
							Validation: &workflowpkg.Validation{
								MinLen: intPtr(1),
								MaxLen: intPtr(100),
							},
						},
						{
							Key:         "description",
							Label:       "工作流描述",
							Type:        "textarea",
							Required:    false,
							Placeholder: "请输入工作流描述",
							Description: "工作流的详细说明",
							Validation: &workflowpkg.Validation{
								MaxLen: intPtr(500),
							},
						},
						{
							Key:         "namespace",
							Label:       "命名空间",
							Type:        "text",
							Required:    true,
							Placeholder: "例如：production",
							Description: "Kubernetes命名空间",
						},
						{
							Key:         "clusterId",
							Label:       "集群ID",
							Type:        "select",
							Required:    true,
							Description: "目标Kubernetes集群",
							Options: []workflowpkg.Option{
								{Label: "生产集群", Value: "prod-cluster"},
								{Label: "预发集群", Value: "staging-cluster"},
								{Label: "测试集群", Value: "test-cluster"},
							},
						},
					},
				},
				{
					Title:       "服务配置",
					Description: "配置参与A/B测试的服务列表",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "services",
							Label:       "服务列表",
							Type:        "array",
							Required:    true,
							Description: "参与A/B测试的服务配置",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(1),
							},
							Default: []map[string]any{
								{
									"serviceName": "service-a",
									"order":       0,
									"replicas":    3,
									"images": map[string]string{
										"app": "nginx:latest",
									},
									"variants": []map[string]any{
										{
											"name":   "version-a",
											"weight": 50,
											"image":  "nginx:1.20",
										},
										{
											"name":   "version-b",
											"weight": 50,
											"image":  "nginx:1.21",
										},
									},
								},
							},
						},
					},
				},
				{
					Title:       "A/B测试策略配置",
					Description: "A/B测试的具体策略参数",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "abTestConfig.testDuration",
							Label:       "测试持续时间",
							Type:        "text",
							Required:    false,
							Default:     "7d",
							Description: "A/B测试持续时间（如: 1h, 1d, 7d）",
							Placeholder: "7d",
						},
						{
							Key:         "abTestConfig.trafficSplitStrategy",
							Label:       "流量分流策略",
							Type:        "select",
							Required:    false,
							Default:     "random",
							Description: "流量分流方式",
							Options: []workflowpkg.Option{
								{Label: "随机分流", Value: "random"},
								{Label: "用户ID分流", Value: "user_id"},
								{Label: "IP地址分流", Value: "ip_hash"},
								{Label: "Cookie分流", Value: "cookie"},
							},
						},
						{
							Key:         "abTestConfig.successMetrics",
							Label:       "成功指标",
							Type:        "array",
							Required:    false,
							Description: "定义A/B测试的成功指标",
							Default:     []string{"conversion_rate", "response_time", "error_rate"},
						},
						{
							Key:         "abTestConfig.autoDecision",
							Label:       "自动决策",
							Type:        "boolean",
							Required:    false,
							Default:     false,
							Description: "是否基于测试结果自动决策推广版本",
						},
						{
							Key:         "abTestConfig.confidenceLevel",
							Label:       "置信度水平",
							Type:        "number",
							Required:    false,
							Default:     95,
							Description: "统计置信度水平（百分比）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(80),
								Max: float64Ptr(99),
							},
						},
						{
							Key:         "abTestConfig.minimumSampleSize",
							Label:       "最小样本量",
							Type:        "number",
							Required:    false,
							Default:     1000,
							Description: "进行决策所需的最小样本量",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(100),
							},
						},
					},
				},
				{
					Title:       "全局配置",
					Description: "工作流的全局设置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "globalConfig.globalTimeout.stepTimeout",
							Label:       "步骤超时时间",
							Type:        "number",
							Required:    false,
							Default:     600,
							Description: "单个步骤的最大执行时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(60),
								Max: float64Ptr(3600),
							},
						},
						{
							Key:         "globalConfig.globalTimeout.approvalTimeout",
							Label:       "审批超时时间",
							Type:        "number",
							Required:    false,
							Default:     3600,
							Description: "审批步骤的最大等待时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(300),
								Max: float64Ptr(86400),
							},
						},
						{
							Key:         "globalConfig.globalNotification.users",
							Label:       "审批人列表",
							Type:        "array",
							Required:    false,
							Description: "工作流审批人员列表",
							Default:     []string{"admin", "ops-lead"},
						},
						{
							Key:         "globalConfig.globalRollback.autoRollback",
							Label:       "自动回滚",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "当发生错误时是否自动回滚",
						},
						{
							Key:         "globalConfig.globalMonitoring.enableTracing",
							Label:       "启用监控验证",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "是否启用端到端监控验证",
						},
					},
				},
			},
		},
		Metadata: workflowpkg.StrategyMetadata{
			Name:        "A/B Test Strategy",
			Description: "Data-driven A/B testing strategy with multi-variant support and automatic decision making",
			Version:     "1.0.0",
			Author:      "Makeblock DevOps Team",
			Tags:        []string{"testing", "ab-test", "data-driven", "experimentation"},
			Features: []string{
				"multi-variant-testing",
				"traffic-splitting",
				"statistical-analysis",
				"automatic-decision",
				"confidence-intervals",
				"sample-size-calculation",
				"rollback-capability",
			},
		},
	}
}
