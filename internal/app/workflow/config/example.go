package config

import (
	"encoding/json"
	"fmt"
	"log"
	"pilot-api/pkg/models"
)

// ExampleUsage 演示如何使用新的优雅配置系统
func ExampleUsage() {
	fmt.Println("=== 🎉 新的优雅配置系统示例 ===")

	// 示例配置数据
	configData := map[string]any{
		"name":        "user-service-canary",
		"description": "用户服务灰度发布",
		"namespace":   "production",
		"clusterId":   "prod-cluster",
		"type":        models.WorkflowTypeCanary,
		"services": []map[string]any{
			{
				"serviceName":    "user-service",
				"order":          0,
				"canaryReplicas": 2,
				"prodReplicas":   5,
				"trafficRatio":   20,
				"images": map[string]string{
					"app": "user-service:v2.0.0",
				},
				"routingConfig": map[string]any{
					"strategy": "header",
					"headerRouting": map[string]any{
						"headerName":  "x-user-group",
						"headerValue": "beta-users",
					},
				},
			},
		},
		"canaryConfig": map[string]any{
			"propagationHeader":   "x-canary-version",
			"propagationValue":    "canary",
			"trafficStrategy":     "header",
			"defaultTrafficRatio": 20,
			"entryServices":       []string{"user-service"},
		},
		"globalConfig": map[string]any{
			"globalTimeout": map[string]any{
				"stepTimeout":     600,
				"approvalTimeout": 3600,
			},
			"globalRollback": map[string]any{
				"autoRollback": true,
			},
		},
	}

	// 🚀 一行代码创建类型安全的配置！
	adapter := NewTemplateConfigAdapter(
		"user-service-canary",
		"用户服务灰度发布",
		models.WorkflowTypeCanary,
		"production",
		"prod-cluster",
		map[string]string{
			"app":  "user-service",
			"tier": "production",
		},
		configData,
	)

	// ✅ 类型安全的访问
	if !adapter.IsValid() {
		log.Fatal("配置无效")
	}

	fmt.Printf("✅ 工作流名称: %s\n", adapter.GetName())
	fmt.Printf("✅ 工作流类型: %s\n", adapter.GetType())
	fmt.Printf("✅ 命名空间: %s\n", adapter.GetNamespace())

	// 🎯 获取服务配置 - 完全类型安全
	services := adapter.GetServices()
	fmt.Printf("✅ 服务数量: %d\n", len(services))
	for _, service := range services {
		fmt.Printf("  📦 服务: %s, 灰度副本: %d, 流量比例: %d%%\n",
			service.ServiceName, service.CanaryReplicas, service.TrafficRatio)

		// 路由配置
		if service.RoutingConfig != nil && service.RoutingConfig.HeaderRouting != nil {
			fmt.Printf("    🔀 路由策略: Header (%s = %s)\n",
				service.RoutingConfig.HeaderRouting.HeaderName,
				service.RoutingConfig.HeaderRouting.HeaderValue)
		}
	}

	// 🌐 获取传播配置
	header, value := adapter.GetPropagationConfig()
	fmt.Printf("✅ 传播配置: %s = %s\n", header, value)

	// ⚙️ 获取全局配置
	globalConfig := adapter.GetGlobalConfig()
	if globalConfig != nil && globalConfig.GlobalTimeout != nil {
		fmt.Printf("✅ 步骤超时: %d 秒\n", globalConfig.GlobalTimeout.StepTimeout)
		fmt.Printf("✅ 审批超时: %d 秒\n", globalConfig.GlobalTimeout.ApprovalTimeout)
	}

	// 📋 获取原始配置（自动转换）
	rawConfig := adapter.GetConfig()
	jsonData, _ := json.MarshalIndent(rawConfig, "", "  ")
	fmt.Printf("✅ 配置 JSON（前200字符）:\n%s...\n",
		string(jsonData)[:min(200, len(jsonData))])

	// 🆚 对比展示
	fmt.Println("\n=== 🆚 新旧系统对比 ===")
	fmt.Print(`
【❌ 旧系统 - 繁琐的手动类型断言】:
if serviceName, ok := serviceMap["serviceName"].(string); ok {
    canaryService.ServiceName = serviceName
}
if order, ok := serviceMap["order"].(float64); ok {
    canaryService.Order = int(order)
}
// ... 50+ 行重复代码

【✅ 新系统 - 优雅的结构体标签】:
type CanaryServiceBinding struct {
    ServiceName    string ` + "`config:\"key:serviceName,required\" json:\"serviceName\"`" + `
    Order          int    ` + "`config:\"key:order,default:0\" validation:\"min:0\" json:\"order\"`" + `
    CanaryReplicas int32  ` + "`config:\"key:canaryReplicas,default:1\" validation:\"min:1\" json:\"canaryReplicas\"`" + `
}

// 一行代码完成解析、验证、默认值！
config, result := binder.BindCanaryConfig(data)
`)

	fmt.Println("🎯 优势总结:")
	fmt.Println("  ✅ 类型安全: 编译时检查，零运行时错误")
	fmt.Println("  ✅ 自动验证: 支持必填、范围、长度等验证规则")
	fmt.Println("  ✅ 默认值: 自动应用，简化配置")
	fmt.Println("  ✅ 清晰API: 统一的配置管理接口")
	fmt.Println("  ✅ 零冗余: 消除了所有手动类型断言")
	fmt.Println("  ✅ 易扩展: 新增字段只需添加标签")
}

// 🔧 演示配置绑定器的直接使用
func ExampleConfigBinder() {
	fmt.Println("\n=== 🔧 ConfigBinder 直接使用示例 ===")

	binder := NewConfigBinder()

	configData := map[string]any{
		"name":      "demo-workflow",
		"namespace": "test",
		"clusterId": "test-cluster",
		"type":      models.WorkflowTypeCanary,
		"services": []map[string]any{
			{
				"serviceName":    "demo-service",
				"canaryReplicas": 1,
				"prodReplicas":   3,
				"images": map[string]string{
					"app": "demo:latest",
				},
			},
		},
	}

	// 🎯 类型安全的配置绑定
	config, result := binder.BindCanaryConfig(configData)

	if result != nil && len(result.Errors) > 0 {
		fmt.Printf("❌ 解析错误: %d 个\n", len(result.Errors))
		for _, err := range result.Errors {
			fmt.Printf("  - %s: %s\n", err.Field, err.Message)
		}
		return
	}

	if config != nil {
		fmt.Printf("✅ 配置解析成功: %s\n", config.GetName())
		fmt.Printf("✅ 服务数量: %d\n", len(config.Services))

		// 🔄 转换为业务类型
		services := ToCanaryServices(config.Services)
		for _, service := range services {
			fmt.Printf("  📦 %s: %d 灰度副本\n", service.ServiceName, service.CanaryReplicas)
		}
	}
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 在实际使用中，你可以这样调用：
// func main() {
//     config.ExampleUsage()
//     config.ExampleConfigBinder()
// }
