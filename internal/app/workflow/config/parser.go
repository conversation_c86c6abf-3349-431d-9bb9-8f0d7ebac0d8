package config

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

const (
	TrueString = "true"
)

// ConfigParser 统一配置解析器
type ConfigParser struct {
	validators map[string]ValidatorFunc
	converters map[string]ConverterFunc
}

// ValidatorFunc 验证函数类型
type ValidatorFunc func(value any, rules map[string]any) error

// ConverterFunc 转换函数类型
type ConverterFunc func(value any) (any, error)

// ParseResult 解析结果
type ParseResult struct {
	Data   map[string]any `json:"data"`
	Errors []ParseError   `json:"errors,omitempty"`
}

// ParseError 解析错误
type ParseError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// NewConfigParser 创建新的配置解析器
func NewConfigParser() *ConfigParser {
	parser := &ConfigParser{
		validators: make(map[string]ValidatorFunc),
		converters: make(map[string]ConverterFunc),
	}

	// 注册默认验证器
	parser.registerDefaultValidators()
	// 注册默认转换器
	parser.registerDefaultConverters()

	return parser
}

// ParseConfig 解析配置，使用结构体标签进行自动映射
func (p *ConfigParser) ParseConfig(source map[string]any, target any) *ParseResult {
	result := &ParseResult{
		Data:   make(map[string]any),
		Errors: []ParseError{},
	}

	targetValue := reflect.ValueOf(target)
	if targetValue.Kind() != reflect.Ptr || targetValue.Elem().Kind() != reflect.Struct {
		result.Errors = append(result.Errors, ParseError{
			Field:   "target",
			Message: "target must be a pointer to struct",
			Code:    "INVALID_TARGET",
		})
		return result
	}

	targetType := targetValue.Elem().Type()
	targetVal := targetValue.Elem()

	for i := 0; i < targetType.NumField(); i++ {
		field := targetType.Field(i)
		fieldVal := targetVal.Field(i)

		if !fieldVal.CanSet() {
			continue
		}

		// 获取配置标签
		configTag := field.Tag.Get("config")
		if configTag == "" || configTag == "-" {
			continue
		}

		// 解析标签
		tags := parseStructTag(configTag)
		sourceKey := tags["key"]
		if sourceKey == "" {
			sourceKey = strings.ToLower(field.Name)
		}

		// 从源数据中获取值
		sourceValue, exists := getNestedValue(source, sourceKey)

		// 处理必填验证
		if !exists && tags["required"] == TrueString {
			result.Errors = append(result.Errors, ParseError{
				Field:   sourceKey,
				Message: fmt.Sprintf("field '%s' is required", sourceKey),
				Code:    "FIELD_REQUIRED",
			})
			continue
		}

		// 设置默认值
		if !exists && tags["default"] != "" {
			sourceValue = parseDefaultValue(tags["default"], field.Type)
		}

		if !exists && sourceValue == nil {
			continue
		}

		// 类型转换和验证
		convertedValue, err := p.convertValue(sourceValue, field.Type)
		if err != nil {
			result.Errors = append(result.Errors, ParseError{
				Field:   sourceKey,
				Message: fmt.Sprintf("conversion error: %v", err),
				Code:    "CONVERSION_ERROR",
			})
			continue
		}

		// 验证值
		if validatorName := tags["validator"]; validatorName != "" {
			if validator, exists := p.validators[validatorName]; exists {
				validationRules := parseValidationRules(field.Tag.Get("validation"))
				if err := validator(convertedValue, validationRules); err != nil {
					result.Errors = append(result.Errors, ParseError{
						Field:   sourceKey,
						Message: fmt.Sprintf("validation error: %v", err),
						Code:    "VALIDATION_ERROR",
					})
					continue
				}
			}
		}

		// 设置值
		if err := setFieldValue(fieldVal, convertedValue); err != nil {
			result.Errors = append(result.Errors, ParseError{
				Field:   sourceKey,
				Message: fmt.Sprintf("failed to set field value: %v", err),
				Code:    "SET_VALUE_ERROR",
			})
			continue
		}

		// 添加到结果数据中
		result.Data[sourceKey] = convertedValue
	}

	return result
}

// 解析结构体标签
func parseStructTag(tag string) map[string]string {
	result := make(map[string]string)
	pairs := strings.Split(tag, ",")

	for _, pair := range pairs {
		if strings.Contains(pair, ":") {
			parts := strings.SplitN(pair, ":", 2)
			result[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
		} else {
			result[strings.TrimSpace(pair)] = "true"
		}
	}

	return result
}

// 获取嵌套值（支持 dot notation）
func getNestedValue(data map[string]any, key string) (any, bool) {
	if !strings.Contains(key, ".") {
		value, exists := data[key]
		return value, exists
	}

	keys := strings.Split(key, ".")
	current := data

	for i, k := range keys {
		if i == len(keys)-1 {
			value, exists := current[k]
			return value, exists
		}

		next, exists := current[k]
		if !exists {
			return nil, false
		}

		nextMap, ok := next.(map[string]any)
		if !ok {
			return nil, false
		}

		current = nextMap
	}

	return nil, false
}

// 解析默认值
func parseDefaultValue(defaultStr string, fieldType reflect.Type) any {
	switch fieldType.Kind() {
	case reflect.String:
		return defaultStr
	case reflect.Int, reflect.Int32, reflect.Int64:
		if val, err := strconv.ParseInt(defaultStr, 10, 64); err == nil {
			return val
		}
	case reflect.Float32, reflect.Float64:
		if val, err := strconv.ParseFloat(defaultStr, 64); err == nil {
			return val
		}
	case reflect.Bool:
		if val, err := strconv.ParseBool(defaultStr); err == nil {
			return val
		}
	case reflect.Slice:
		var arr []any
		if err := json.Unmarshal([]byte(defaultStr), &arr); err == nil {
			return arr
		}
	case reflect.Map:
		var obj map[string]any
		if err := json.Unmarshal([]byte(defaultStr), &obj); err == nil {
			return obj
		}
	}
	return defaultStr
}

// 转换值到目标类型
func (p *ConfigParser) convertValue(value any, targetType reflect.Type) (any, error) {
	if value == nil {
		return nil, nil
	}

	valueType := reflect.TypeOf(value)
	if valueType == targetType {
		return value, nil
	}

	// 使用注册的转换器
	if converter, exists := p.converters[targetType.String()]; exists {
		return converter(value)
	}

	// 默认转换逻辑
	return p.defaultConvert(value, targetType)
}

// 默认转换逻辑
func (p *ConfigParser) defaultConvert(value any, targetType reflect.Type) (any, error) {
	switch targetType.Kind() {
	case reflect.String:
		return fmt.Sprintf("%v", value), nil
	case reflect.Int, reflect.Int32, reflect.Int64:
		return convertToInt(value)
	case reflect.Float32, reflect.Float64:
		return convertToFloat(value)
	case reflect.Bool:
		return convertToBool(value)
	case reflect.Slice:
		return convertToSlice(value, targetType)
	case reflect.Map:
		return convertToMap(value, targetType)
	default:
		return value, nil
	}
}

// 设置字段值
func setFieldValue(fieldVal reflect.Value, value any) error {
	if value == nil {
		return nil
	}

	valueReflect := reflect.ValueOf(value)

	if !valueReflect.Type().AssignableTo(fieldVal.Type()) {
		// 尝试类型转换
		if valueReflect.Type().ConvertibleTo(fieldVal.Type()) {
			convertedValue := valueReflect.Convert(fieldVal.Type())
			fieldVal.Set(convertedValue)
			return nil
		}
		return fmt.Errorf("cannot assign value of type %v to field of type %v", valueReflect.Type(), fieldVal.Type())
	}

	fieldVal.Set(valueReflect)
	return nil
}

// 类型转换辅助函数
func convertToInt(value any) (int64, error) {
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int32:
		return int64(v), nil
	case int64:
		return v, nil
	case float32:
		return int64(v), nil
	case float64:
		return int64(v), nil
	case string:
		return strconv.ParseInt(v, 10, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to int", value)
	}
}

func convertToFloat(value any) (float64, error) {
	switch v := value.(type) {
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case float32:
		return float64(v), nil
	case float64:
		return v, nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float", value)
	}
}

func convertToBool(value any) (bool, error) {
	switch v := value.(type) {
	case bool:
		return v, nil
	case string:
		return strconv.ParseBool(v)
	case int, int32, int64:
		return v != 0, nil
	case float32, float64:
		return v != 0, nil
	default:
		return false, fmt.Errorf("cannot convert %T to bool", value)
	}
}

func convertToSlice(value any, targetType reflect.Type) (any, error) {
	valueReflect := reflect.ValueOf(value)

	if valueReflect.Kind() != reflect.Slice {
		return nil, fmt.Errorf("cannot convert %T to slice", value)
	}

	elemType := targetType.Elem()
	resultSlice := reflect.MakeSlice(targetType, 0, valueReflect.Len())

	for i := 0; i < valueReflect.Len(); i++ {
		elemValue := valueReflect.Index(i).Interface()

		convertedElem, err := convertToTargetType(elemValue, elemType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert slice element %d: %v", i, err)
		}

		resultSlice = reflect.Append(resultSlice, reflect.ValueOf(convertedElem))
	}

	return resultSlice.Interface(), nil
}

func convertToMap(value any, targetType reflect.Type) (any, error) {
	valueReflect := reflect.ValueOf(value)

	if valueReflect.Kind() != reflect.Map {
		return nil, fmt.Errorf("cannot convert %T to map", value)
	}

	keyType := targetType.Key()
	elemType := targetType.Elem()
	resultMap := reflect.MakeMap(targetType)

	for _, key := range valueReflect.MapKeys() {
		keyValue := key.Interface()
		elemValue := valueReflect.MapIndex(key).Interface()

		// 转换键
		convertedKey, err := convertToTargetType(keyValue, keyType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert map key: %v", err)
		}

		// 转换值
		convertedElem, err := convertToTargetType(elemValue, elemType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert map value: %v", err)
		}

		resultMap.SetMapIndex(reflect.ValueOf(convertedKey), reflect.ValueOf(convertedElem))
	}

	return resultMap.Interface(), nil
}

func convertToTargetType(value any, targetType reflect.Type) (any, error) {
	if value == nil {
		return reflect.Zero(targetType).Interface(), nil
	}

	valueType := reflect.TypeOf(value)
	if valueType == targetType {
		return value, nil
	}

	if valueType.AssignableTo(targetType) {
		return value, nil
	}

	if valueType.ConvertibleTo(targetType) {
		return reflect.ValueOf(value).Convert(targetType).Interface(), nil
	}

	return nil, fmt.Errorf("cannot convert %v to %v", valueType, targetType)
}

// 解析验证规则
func parseValidationRules(validation string) map[string]any {
	rules := make(map[string]any)
	if validation == "" {
		return rules
	}

	pairs := strings.Split(validation, ",")
	for _, pair := range pairs {
		if strings.Contains(pair, ":") {
			parts := strings.SplitN(pair, ":", 2)
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			if num, err := strconv.ParseFloat(value, 64); err == nil {
				rules[key] = num
			} else {
				rules[key] = value
			}
		} else {
			rules[strings.TrimSpace(pair)] = true
		}
	}

	return rules
}

// 注册默认验证器
func (p *ConfigParser) registerDefaultValidators() {
	// 范围验证器
	p.validators["range"] = func(value any, rules map[string]any) error {
		num, err := convertToFloat(value)
		if err != nil {
			return err
		}

		if min, ok := rules["min"]; ok {
			if minFloat, err := convertToFloat(min); err == nil && num < minFloat {
				return fmt.Errorf("value %v is less than minimum %v", num, minFloat)
			}
		}

		if max, ok := rules["max"]; ok {
			if maxFloat, err := convertToFloat(max); err == nil && num > maxFloat {
				return fmt.Errorf("value %v is greater than maximum %v", num, maxFloat)
			}
		}

		return nil
	}

	// 长度验证器
	p.validators["length"] = func(value any, rules map[string]any) error {
		var length int

		switch v := value.(type) {
		case string:
			length = len(v)
		case []any:
			length = len(v)
		default:
			return fmt.Errorf("length validation not supported for type %T", value)
		}

		if minLen, ok := rules["minLen"]; ok {
			if minLenInt, err := convertToInt(minLen); err == nil && int64(length) < minLenInt {
				return fmt.Errorf("length %d is less than minimum %d", length, minLenInt)
			}
		}

		if maxLen, ok := rules["maxLen"]; ok {
			if maxLenInt, err := convertToInt(maxLen); err == nil && int64(length) > maxLenInt {
				return fmt.Errorf("length %d is greater than maximum %d", length, maxLenInt)
			}
		}

		return nil
	}
}

// 注册默认转换器
func (p *ConfigParser) registerDefaultConverters() {
	// 可以在这里注册特殊的类型转换器
}

// RegisterValidator 注册自定义验证器
func (p *ConfigParser) RegisterValidator(name string, validator ValidatorFunc) {
	p.validators[name] = validator
}

// RegisterConverter 注册自定义转换器
func (p *ConfigParser) RegisterConverter(typeName string, converter ConverterFunc) {
	p.converters[typeName] = converter
}
