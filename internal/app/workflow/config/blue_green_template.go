package config

import (
	workflowpkg "pilot-api/internal/pkg/workflow"
)

// GetBlueGreenConfigTemplate 获取蓝绿部署配置模板
func GetBlueGreenConfigTemplate() *workflowpkg.WorkflowConfigTemplate {
	return &workflowpkg.WorkflowConfigTemplate{
		Name:        "蓝绿部署工作流",
		Description: "零停机时间的蓝绿部署策略，支持快速切换和回滚",
		ConfigForm: workflowpkg.WorkflowConfigForm{
			Sections: []workflowpkg.ConfigSection{
				{
					Title:       "基础配置",
					Description: "工作流的基本信息配置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "name",
							Label:       "工作流名称",
							Type:        "text",
							Required:    true,
							Placeholder: "请输入工作流名称",
							Description: "用于标识工作流的唯一名称",
							Validation: &workflowpkg.Validation{
								MinLen: intPtr(1),
								MaxLen: intPtr(100),
							},
						},
						{
							Key:         "description",
							Label:       "工作流描述",
							Type:        "textarea",
							Required:    false,
							Placeholder: "请输入工作流描述",
							Description: "工作流的详细说明",
							Validation: &workflowpkg.Validation{
								MaxLen: intPtr(500),
							},
						},
						{
							Key:         "namespace",
							Label:       "命名空间",
							Type:        "text",
							Required:    true,
							Placeholder: "例如：production",
							Description: "Kubernetes命名空间",
						},
						{
							Key:         "clusterId",
							Label:       "集群ID",
							Type:        "select",
							Required:    true,
							Description: "目标Kubernetes集群",
							Options: []workflowpkg.Option{
								{Label: "生产集群", Value: "prod-cluster"},
								{Label: "预发集群", Value: "staging-cluster"},
								{Label: "测试集群", Value: "test-cluster"},
							},
						},
					},
				},
				{
					Title:       "服务配置",
					Description: "配置参与蓝绿部署的服务列表",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "services",
							Label:       "服务列表",
							Type:        "array",
							Required:    true,
							Description: "参与蓝绿部署的服务配置",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(1),
							},
							Default: []map[string]any{
								{
									"serviceName": "service-a",
									"order":       0,
									"replicas":    3,
									"images": map[string]string{
										"app": "nginx:latest",
									},
								},
							},
						},
					},
				},
				{
					Title:       "蓝绿策略配置",
					Description: "蓝绿部署的具体策略参数",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "blueGreenConfig.switchMode",
							Label:       "切换模式",
							Type:        "select",
							Required:    false,
							Default:     "instant",
							Description: "流量切换模式",
							Options: []workflowpkg.Option{
								{Label: "瞬时切换", Value: "instant"},
								{Label: "渐进切换", Value: "progressive"},
							},
						},
						{
							Key:         "blueGreenConfig.healthCheckTimeout",
							Label:       "健康检查超时",
							Type:        "number",
							Required:    false,
							Default:     300,
							Description: "绿环境健康检查超时时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(60),
								Max: float64Ptr(1800),
							},
						},
						{
							Key:         "blueGreenConfig.preserveBlue",
							Label:       "保留蓝环境",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "切换后是否保留蓝环境用于回滚",
						},
						{
							Key:         "blueGreenConfig.preservePeriod",
							Label:       "保留时间",
							Type:        "text",
							Required:    false,
							Default:     "24h",
							Description: "蓝环境保留时间（如: 1h, 24h, 7d）",
							Placeholder: "24h",
						},
					},
				},
				{
					Title:       "全局配置",
					Description: "工作流的全局设置",
					Fields: []workflowpkg.ConfigField{
						{
							Key:         "globalConfig.globalTimeout.stepTimeout",
							Label:       "步骤超时时间",
							Type:        "number",
							Required:    false,
							Default:     600,
							Description: "单个步骤的最大执行时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(60),
								Max: float64Ptr(3600),
							},
						},
						{
							Key:         "globalConfig.globalTimeout.approvalTimeout",
							Label:       "审批超时时间",
							Type:        "number",
							Required:    false,
							Default:     3600,
							Description: "审批步骤的最大等待时间（秒）",
							Validation: &workflowpkg.Validation{
								Min: float64Ptr(300),
								Max: float64Ptr(86400),
							},
						},
						{
							Key:         "globalConfig.globalNotification.users",
							Label:       "审批人列表",
							Type:        "array",
							Required:    false,
							Description: "工作流审批人员列表",
							Default:     []string{"admin", "ops-lead"},
						},
						{
							Key:         "globalConfig.globalRollback.autoRollback",
							Label:       "自动回滚",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "当发生错误时是否自动回滚",
						},
						{
							Key:         "globalConfig.globalMonitoring.enableTracing",
							Label:       "启用监控验证",
							Type:        "boolean",
							Required:    false,
							Default:     true,
							Description: "是否启用端到端监控验证",
						},
					},
				},
			},
		},
		Metadata: workflowpkg.StrategyMetadata{
			Name:        "Blue-Green Deployment Strategy",
			Description: "Zero-downtime blue-green deployment strategy with instant switch and rollback capabilities",
			Version:     "1.0.0",
			Author:      "Makeblock DevOps Team",
			Tags:        []string{"deployment", "blue-green", "zero-downtime", "rollback"},
			Features: []string{
				"zero-downtime",
				"instant-switch",
				"quick-rollback",
				"health-monitoring",
				"environment-preservation",
				"approval-gates",
			},
		},
	}
}
