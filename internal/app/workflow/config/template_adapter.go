package config

import (
	"fmt"
	"pilot-api/internal/pkg/types"
	"pilot-api/pkg/models"
)

// TemplateConfigAdapter 模板配置适配器 - 纯类型安全版本
type TemplateConfigAdapter struct {
	binder *ConfigBinder
	config *CanaryWorkflowConfig
}

// NewTemplateConfigAdapter 创建模板配置适配器
func NewTemplateConfigAdapter(
	name, description string,
	workflowType models.WorkflowType,
	namespace, clusterID string,
	labels map[string]string,
	rawConfig map[string]any,
) *TemplateConfigAdapter {
	adapter := &TemplateConfigAdapter{
		binder: NewConfigBinder(),
	}

	// 设置基础配置
	configData := map[string]any{
		"name":        name,
		"description": description,
		"type":        workflowType,
		"namespace":   namespace,
		"clusterId":   clusterID,
		"labels":      labels,
	}

	// 合并原始配置
	for k, v := range rawConfig {
		configData[k] = v
	}

	// 解析配置
	config, result := adapter.binder.BindCanaryConfig(configData)
	if result != nil && len(result.Errors) == 0 && config != nil {
		adapter.config = config
	}

	return adapter
}

// GetName 获取名称
func (a *TemplateConfigAdapter) GetName() string {
	if a.config == nil {
		return ""
	}
	return a.config.GetName()
}

// GetDescription 获取描述
func (a *TemplateConfigAdapter) GetDescription() string {
	if a.config == nil {
		return ""
	}
	return a.config.GetDescription()
}

// GetType 获取类型
func (a *TemplateConfigAdapter) GetType() models.WorkflowType {
	if a.config == nil {
		return ""
	}
	return a.config.GetType()
}

// GetNamespace 获取命名空间
func (a *TemplateConfigAdapter) GetNamespace() string {
	if a.config == nil {
		return ""
	}
	return a.config.GetNamespace()
}

// GetClusterID 获取集群ID
func (a *TemplateConfigAdapter) GetClusterID() string {
	if a.config == nil {
		return ""
	}
	return a.config.GetClusterID()
}

// GetLabels 获取标签
func (a *TemplateConfigAdapter) GetLabels() map[string]string {
	if a.config == nil {
		return make(map[string]string)
	}
	return a.config.GetLabels()
}

// GetConfig 获取原始配置（转换为map格式）
func (a *TemplateConfigAdapter) GetConfig() map[string]any {
	if a.config == nil {
		return make(map[string]any)
	}

	// 将类型安全的配置转换为map格式
	config := map[string]any{
		"name":        a.config.Name,
		"description": a.config.Description,
		"type":        a.config.Type,
		"namespace":   a.config.Namespace,
		"clusterId":   a.config.ClusterID,
		"labels":      a.config.Labels,
	}

	// 添加服务配置
	if len(a.config.Services) > 0 {
		services := make([]map[string]any, len(a.config.Services))
		for i, service := range a.config.Services {
			services[i] = map[string]any{
				"serviceName":    service.ServiceName,
				"order":          service.Order,
				"images":         service.Images,
				"version":        service.Version,
				"envs":           service.Envs,
				"resources":      service.Resources,
				"dependencies":   service.Dependencies,
				"canaryReplicas": service.CanaryReplicas,
				"prodReplicas":   service.ProdReplicas,
				"trafficRatio":   service.TrafficRatio,
				"strategy":       service.Strategy,
			}
		}
		config["services"] = services
	}

	// 添加灰度配置
	if a.config.CanaryConfig != nil {
		config["canaryConfig"] = map[string]any{
			"propagationHeader":   a.config.CanaryConfig.PropagationHeader,
			"propagationValue":    a.config.CanaryConfig.PropagationValue,
			"entryServices":       a.config.CanaryConfig.EntryServices,
			"serviceDependencies": a.config.CanaryConfig.ServiceDependencies,
			"trafficStrategy":     a.config.CanaryConfig.TrafficStrategy,
			"defaultTrafficRatio": a.config.CanaryConfig.DefaultTrafficRatio,
		}
	}

	// 添加全局配置
	if a.config.GlobalConfig != nil {
		globalConfig := make(map[string]any)

		if a.config.GlobalConfig.GlobalTimeout != nil {
			globalConfig["globalTimeout"] = map[string]any{
				"stepTimeout":       a.config.GlobalConfig.GlobalTimeout.StepTimeout,
				"approvalTimeout":   a.config.GlobalConfig.GlobalTimeout.ApprovalTimeout,
				"monitoringTimeout": a.config.GlobalConfig.GlobalTimeout.MonitoringTimeout,
				"rollbackTimeout":   a.config.GlobalConfig.GlobalTimeout.RollbackTimeout,
			}
		}

		if a.config.GlobalConfig.GlobalNotification != nil {
			globalConfig["globalNotification"] = map[string]any{
				"enabled":  a.config.GlobalConfig.GlobalNotification.Enabled,
				"channels": a.config.GlobalConfig.GlobalNotification.Channels,
				"events":   a.config.GlobalConfig.GlobalNotification.Events,
				"template": a.config.GlobalConfig.GlobalNotification.Template,
				"users":    a.config.GlobalConfig.GlobalNotification.Users,
			}
		}

		if a.config.GlobalConfig.GlobalRollback != nil {
			globalConfig["globalRollback"] = map[string]any{
				"autoRollback":     a.config.GlobalConfig.GlobalRollback.AutoRollback,
				"errorThreshold":   a.config.GlobalConfig.GlobalRollback.ErrorThreshold,
				"latencyThreshold": a.config.GlobalConfig.GlobalRollback.LatencyThreshold,
			}
		}

		if a.config.GlobalConfig.GlobalMonitoring != nil {
			globalConfig["globalMonitoring"] = map[string]any{
				"enableTracing": a.config.GlobalConfig.GlobalMonitoring.EnableTracing,
				"traceHeaders":  a.config.GlobalConfig.GlobalMonitoring.TraceHeaders,
			}
		}

		config["globalConfig"] = globalConfig
	}

	return config
}

// GetServices 获取服务配置 - 类型安全版本
func (a *TemplateConfigAdapter) GetServices() []types.CanaryService {
	if a.config == nil {
		return []types.CanaryService{}
	}

	return ToCanaryServices(a.config.Services)
}

// GetGlobalConfig 获取全局配置 - 类型安全版本
func (a *TemplateConfigAdapter) GetGlobalConfig() *types.CanaryGlobalConfig {
	if a.config == nil || a.config.GlobalConfig == nil {
		return nil
	}

	return a.config.GlobalConfig.ToCanaryGlobalConfig()
}

// GetCanaryConfig 获取灰度配置 - 类型安全版本
func (a *TemplateConfigAdapter) GetCanaryConfig() any {
	if a.config == nil || a.config.CanaryConfig == nil {
		return map[string]any{
			"enableCanary": false,
		}
	}

	return map[string]any{
		"enableCanary":        true,
		"propagationHeader":   a.config.CanaryConfig.PropagationHeader,
		"propagationValue":    a.config.CanaryConfig.PropagationValue,
		"trafficStrategy":     a.config.CanaryConfig.TrafficStrategy,
		"defaultTrafficRatio": a.config.CanaryConfig.DefaultTrafficRatio,
		"entryServices":       a.config.CanaryConfig.EntryServices,
		"serviceDependencies": a.config.CanaryConfig.ServiceDependencies,
	}
}

// IsCanaryEnabled 检查是否启用灰度
func (a *TemplateConfigAdapter) IsCanaryEnabled() bool {
	return a.config != nil && a.config.Type == models.WorkflowTypeCanary
}

// GetPropagationConfig 获取传播配置 - 类型安全版本
func (a *TemplateConfigAdapter) GetPropagationConfig() (string, string) {
	if a.config == nil || a.config.CanaryConfig == nil {
		return types.DefaultPropagationHeader, types.DefaultCanaryValue
	}

	header := a.config.CanaryConfig.PropagationHeader
	if header == "" {
		header = types.DefaultPropagationHeader
	}

	value := a.config.CanaryConfig.PropagationValue
	if value == "" {
		value = types.DefaultCanaryValue
	}

	return header, value
}

// GetTypedConfig 获取类型安全的配置对象
func (a *TemplateConfigAdapter) GetTypedConfig() *CanaryWorkflowConfig {
	return a.config
}

// IsValid 检查配置是否有效
func (a *TemplateConfigAdapter) IsValid() bool {
	return a.config != nil
}

// Validate 验证配置
func (a *TemplateConfigAdapter) Validate() error {
	if a.config == nil {
		return fmt.Errorf("configuration is nil")
	}

	return a.config.Validate()
}
