package workflow

import (
	"pilot-api/internal/pkg/request"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"strconv"

	"pilot-api/pkg/models"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// getWorkflowService 获取工作流服务实例
func getWorkflowService(c *gin.Context) *WorkflowService {
	ts := trace.NewTraceServiceFromGin(c)
	return GetWorkflowManager().GetService(ts)
}

// GetWorkflow 获取工作流详情
func GetWorkflow(c *gin.Context) {
	uuid := c.Param("uuid")
	if uuid == "" {
		response.Err(c, response.RequestParamError("uuid is required"))
		return
	}

	svc := getWorkflowService(c)
	workflow, err := svc.GetWorkflow(uuid)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, workflow)
}

// DeleteWorkflow 删除工作流
func DeleteWorkflow(c *gin.Context) {
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	for _, uuid := range req.UUIDs {
		if err := svc.DeleteWorkflow(uuid, c.GetString(util.UserName)); err != nil {
			response.ErrorResponse(c, err)
			return
		}
	}

	response.OK(c, nil)
}

// ListWorkflows 获取工作流列表
func ListWorkflows(c *gin.Context) {
	var req ListWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	list, err := svc.ListWorkflows(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}

// ExecuteWorkflow 执行工作流
func ExecuteWorkflow(c *gin.Context) {
	var req ExecuteWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	execution, err := svc.ExecuteWorkflow(req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, execution)
}

// PauseWorkflow 暂停工作流
func PauseWorkflow(c *gin.Context) {
	executionIDStr := c.Param("executionId")
	executionID, err := strconv.ParseInt(executionIDStr, 10, 64)
	if err != nil {
		response.Err(c, response.RequestParamError("invalid execution ID"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.PauseWorkflow(executionID, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// ResumeWorkflow 恢复工作流
func ResumeWorkflow(c *gin.Context) {
	executionIDStr := c.Param("executionId")
	executionID, err := strconv.ParseInt(executionIDStr, 10, 64)
	if err != nil {
		response.Err(c, response.RequestParamError("invalid execution ID"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.ResumeWorkflow(executionID, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// CancelWorkflow 取消工作流
func CancelWorkflow(c *gin.Context) {
	executionIDStr := c.Param("executionId")
	executionID, err := strconv.ParseInt(executionIDStr, 10, 64)
	if err != nil {
		response.Err(c, response.RequestParamError("invalid execution ID"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.CancelWorkflow(executionID, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// GetWorkflowExecution 获取工作流执行状态
func GetWorkflowExecution(c *gin.Context) {
	executionIDStr := c.Param("executionId")
	executionID, err := strconv.ParseInt(executionIDStr, 10, 64)
	if err != nil {
		response.Err(c, response.RequestParamError("invalid execution ID"))
		return
	}

	svc := getWorkflowService(c)
	execution, err := svc.GetWorkflowExecution(executionID)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, execution)
}

// ListWorkflowExecutions 获取工作流执行列表
func ListWorkflowExecutions(c *gin.Context) {
	var req ListWorkflowExecutionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	list, err := svc.ListWorkflowExecutions(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}

// DeleteWorkflowExecution 删除工作流执行记录
func DeleteWorkflowExecution(c *gin.Context) {
	executionIDStr := c.Param("executionId")
	executionID, err := strconv.ParseInt(executionIDStr, 10, 64)
	if err != nil {
		response.Err(c, response.RequestParamError("invalid execution ID"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.DeleteWorkflowExecution(executionID, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// GetWorkflowMetrics 获取工作流指标
func GetWorkflowMetrics(c *gin.Context) {
	uuid := c.Param("uuid")
	if uuid == "" {
		response.Err(c, response.RequestParamError("uuid is required"))
		return
	}

	// 先获取工作流ID
	svc := getWorkflowService(c)
	workflow, err := svc.GetWorkflow(uuid)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	metrics, err := svc.GetWorkflowMetrics(workflow.ID)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, metrics)
}

// GetWorkflowTemplate 用于预览工作流配置
func GetWorkflowTemplate(c *gin.Context) {
	var req GetWorkflowTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	template, err := svc.GetWorkflowTemplate(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, template)
}

// ApproveStep 审批步骤
func ApproveStep(c *gin.Context) {
	var req ApproveStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.ApproveStep(req, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// SubmitForApproval 提交审批
func SubmitForApproval(c *gin.Context) {
	uuid := c.Param("uuid")
	if uuid == "" {
		response.Err(c, response.RequestParamError("uuid is required"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.SubmitForApproval(uuid, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// ApproveWorkflow 审批工作流
func ApproveWorkflow(c *gin.Context) {
	uuid := c.Param("uuid")
	if uuid == "" {
		response.Err(c, response.RequestParamError("uuid is required"))
		return
	}

	action := c.Query("action")
	if action != "approve" && action != "reject" { // nolint:goconst
		response.Err(c, response.RequestParamError("action must be approve or reject"))
		return
	}

	svc := getWorkflowService(c)
	if err := svc.ApproveWorkflow(uuid, action, c.GetString(util.UserName)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, nil)
}

// GetWorkflowConfigTemplate 获取工作流配置模板
func GetWorkflowConfigTemplate(c *gin.Context) {
	workflowType := c.Query("type")
	if workflowType == "" {
		response.Err(c, response.RequestParamError("workflow type is required"))
		return
	}

	svc := getWorkflowService(c)
	templateResp, err := svc.GetWorkflowConfigTemplate(models.WorkflowType(workflowType))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, templateResp)
}

// CreateWorkflowFromTemplate 根据模板创建工作流
func CreateWorkflowFromTemplate(c *gin.Context) {
	var req CreateWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	svc := getWorkflowService(c)
	workflowResp, err := svc.CreateWorkflowFromTemplate(&req, c.GetString(util.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, workflowResp)
}

// ListWorkflowTypes 获取支持的工作流类型列表
func ListWorkflowTypes(c *gin.Context) {
	svc := getWorkflowService(c)
	factories := svc.GetEngineRegistry().GetAllStrategyFactories()

	var workflowTypes []WorkflowTypeInfo
	for workflowType, factory := range factories {
		metadata := factory.GetMetadata()
		workflowTypes = append(workflowTypes, WorkflowTypeInfo{
			Type:        workflowType,
			Name:        metadata.Name,
			Description: metadata.Description,
			Version:     metadata.Version,
			Author:      metadata.Author,
			Tags:        metadata.Tags,
			Features:    metadata.Features,
		})
	}

	response.OK(c, workflowTypes)
}
