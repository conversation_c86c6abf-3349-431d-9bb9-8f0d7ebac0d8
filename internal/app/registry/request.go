package registry

import (
	"pilot-api/internal/pkg/registry"
	"pilot-api/internal/pkg/request"
)

// GetRepositoryTagsRequest 获取镜像仓库标签列表请求（支持搜索）
type GetRepositoryTagsRequest struct {
	request.PageRequest
	RegistryType   registry.RegistryType `json:"registryType" binding:"required"`   // registry类型
	NamespaceName  string                `json:"namespaceName" binding:"required"`  // 命名空间名称
	RepositoryName string                `json:"repositoryName" binding:"required"` // 仓库名称
	Keyword        string                `json:"keyword"`                           // 标签搜索关键字（可选）
}

// GetRepositoriesRequest 获取镜像仓库列表请求
type GetRepositoriesRequest struct {
	request.PageRequest
	RegistryType  registry.RegistryType `json:"registryType" binding:"required"` // registry类型
	NamespaceName string                `json:"namespaceName"`                   // 命名空间名称（可选）
	Keyword       string                `json:"keyword"`                         // 仓库搜索关键字（可选）
}
