package registry

import (
	"pilot-api/internal/pkg/registry"
	"pilot-api/internal/pkg/response"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// TODO: 考虑将整个镜像作为传参，返回镜像列表
// GetRepositoryTags 获取镜像仓库标签列表（支持搜索）
func GetRepositoryTags(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req GetRepositoryTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	// TODO: 先试用默认的类型
	req.RegistryType = registry.RegistryTypeAliyun
	svc := NewRegistryService(ts, mysql.GetDB())
	list, err := svc.GetRepositoryTags(req)
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, list)
}
