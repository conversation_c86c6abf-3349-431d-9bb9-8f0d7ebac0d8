package registry

import (
	"context"
	"pilot-api/internal/pkg/registry"
	"pilot-api/internal/pkg/response"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

type RegistryService struct {
	*trace.Service
	registryFactory *registry.RegistryFactory
}

func NewRegistryService(ts *trace.Service, dbEngine *gorm.DB) *RegistryService {
	svc := &RegistryService{
		Service:         trace.IfNil(ts),
		registryFactory: registry.NewRegistryFactory(),
	}

	return svc
}

// GetRepositoryTags 获取镜像仓库标签列表
func (s *RegistryService) GetRepositoryTags(req GetRepositoryTagsRequest) (*response.PageModel[string], error) {
	// 验证registry类型
	if !s.registryFactory.ValidateType(req.RegistryType) {
		return nil, response.RequestParamError("不支持的registry类型: " + string(req.RegistryType))
	}

	// 创建对应的registry客户端
	registryClient, err := s.registryFactory.CreateClient(req.RegistryType)
	if err != nil {
		return nil, response.UnknownError("创建registry客户端失败: " + err.Error())
	}

	ctx := s.AttachTrace(context.Background())

	// 调用底层SDK
	result, err := registryClient.GetRepositoryTags(ctx, req.NamespaceName, req.RepositoryName, req.Keyword, req.Page, req.PageSize)
	if err != nil {
		return nil, response.UnknownError("获取镜像标签失败: " + err.Error())
	}

	return &response.PageModel[string]{
		List:     result.Tags,
		Total:    int64(result.Total),
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}
