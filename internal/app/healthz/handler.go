package healthz

import (
	"net/http"
	"pilot-api/config"

	"github.com/gin-gonic/gin"
)

// Healthz model
type Healthz struct {
	Name    string `json:"name"`
	Env     string `json:"env"`
	Version string `json:"version"`
}

// @Summary Health Check
// @Description Returns the health status of the application
// @Tags Healthz
// @Accept json
// @Produce json
// @Success 200 {object} Healthz
// @Router /healthz [get]
func handleHealthz(c *gin.Context) {
	c.JSON(http.StatusOK, &Healthz{
		Name:    config.Items().AppName,
		Env:     config.Items().ProjectEnv,
		Version: config.Items().APIVersion,
	})
}
