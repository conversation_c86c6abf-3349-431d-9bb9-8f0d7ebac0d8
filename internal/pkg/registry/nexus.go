package registry

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"pilot-api/config"
	"strings"

	"git.makeblock.com/makeblock-go/log"
)

// TODO: 实现nexus的registry client

// NexusRegistry Nexus镜像仓库客户端
type NexusRegistry struct {
	endpoint string
	username string
	password string
	client   *http.Client
}

// NexusRepositoryInfo Nexus仓库信息
type NexusRepositoryInfo struct {
	Name   string `json:"name"`
	Format string `json:"format"`
	Type   string `json:"type"`
	URL    string `json:"url"`
}

// NexusTag Nexus标签信息
type NexusTag struct {
	Name string `json:"name"`
}

// NexusSearchResult Nexus搜索结果
type NexusSearchResult struct {
	Items []struct {
		ID         string                 `json:"id"`
		Repository string                 `json:"repository"`
		Format     string                 `json:"format"`
		Group      string                 `json:"group"`
		Name       string                 `json:"name"`
		Version    string                 `json:"version"`
		Assets     []NexusAsset           `json:"assets"`
		Tags       map[string]interface{} `json:"tags"`
	} `json:"items"`
	ContinuationToken string `json:"continuationToken"`
}

// NexusAsset Nexus资产信息
type NexusAsset struct {
	DownloadURL string `json:"downloadUrl"`
	Path        string `json:"path"`
	ID          string `json:"id"`
	Repository  string `json:"repository"`
	Format      string `json:"format"`
}

// NewNexusRegistry 创建Nexus镜像仓库客户端
func NewNexusRegistry() (RegistryClient, error) {
	cfg := config.Items().NexusRegistry
	if cfg.Endpoint == "" {
		return nil, fmt.Errorf("nexus registry endpoint is required")
	}

	return &NexusRegistry{
		endpoint: strings.TrimSuffix(cfg.Endpoint, "/"),
		username: cfg.Username,
		password: cfg.Password,
		client:   &http.Client{},
	}, nil
}

// GetRepositoryTags 获取镜像仓库标签列表
func (n *NexusRegistry) GetRepositoryTags(ctx context.Context, namespaceName, repositoryName, keyword string, pageNumber, pageSize int) (*TagListResult, error) {
	log.Info("NexusRegistry GetRepositoryTags",
		log.Any("namespaceName", namespaceName),
		log.Any("repositoryName", repositoryName),
		log.Any("keyword", keyword))

	// 构建搜索URL
	searchURL := fmt.Sprintf("%s/service/rest/v1/search", n.endpoint)

	// 构建查询参数
	params := url.Values{}
	params.Set("repository", namespaceName)
	params.Set("format", "docker")

	// 构建搜索关键字，组合仓库名和标签关键字
	searchQuery := repositoryName
	if keyword != "" {
		searchQuery = fmt.Sprintf("%s AND version=*%s*", repositoryName, keyword)
	}
	params.Set("name", searchQuery)

	// 分页参数（Nexus使用不同的分页方式）
	if pageNumber > 1 {
		// Nexus使用continuationToken进行分页，这里简化处理
		// 在实际项目中可能需要缓存continuationToken
		log.Debug("Nexus pagination not fully implemented", log.Any("page", pageNumber))
	}

	fullURL := fmt.Sprintf("%s?%s", searchURL, params.Encode())

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加认证
	if n.username != "" && n.password != "" {
		req.SetBasicAuth(n.username, n.password)
	}

	resp, err := n.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("nexus API returned status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var searchResult NexusSearchResult
	if err := json.Unmarshal(body, &searchResult); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 提取标签（版本）信息
	tagSet := make(map[string]bool)
	for _, item := range searchResult.Items {
		if item.Name == repositoryName && item.Version != "" {
			tagSet[item.Version] = true
		}
	}

	// 转换为slice并分页
	var allTags []string
	for tag := range tagSet {
		allTags = append(allTags, tag)
	}

	// 简单的内存分页处理
	startIndex := (pageNumber - 1) * pageSize
	endIndex := startIndex + pageSize

	var tags []string
	if startIndex < len(allTags) {
		if endIndex > len(allTags) {
			endIndex = len(allTags)
		}
		tags = allTags[startIndex:endIndex]
	}

	return &TagListResult{
		Tags:       tags,
		Total:      len(allTags),
		PageNumber: pageNumber,
		PageSize:   pageSize,
	}, nil
}

// GetRepositories 获取镜像仓库列表
func (n *NexusRegistry) GetRepositories(ctx context.Context, namespaceName, keyword string, pageNumber, pageSize int) (*RepositoryListResult, error) {
	log.Info("NexusRegistry GetRepositories",
		log.Any("namespaceName", namespaceName),
		log.Any("keyword", keyword))

	// 获取所有仓库信息
	repoURL := fmt.Sprintf("%s/service/rest/v1/repositories", n.endpoint)

	req, err := http.NewRequestWithContext(ctx, "GET", repoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加认证
	if n.username != "" && n.password != "" {
		req.SetBasicAuth(n.username, n.password)
	}

	resp, err := n.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("nexus API returned status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var nexusRepos []NexusRepositoryInfo
	if err := json.Unmarshal(body, &nexusRepos); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 过滤Docker格式的仓库
	var filteredRepos []RepositoryInfo
	for _, repo := range nexusRepos {
		// 只包含Docker格式的仓库
		if repo.Format != "docker" {
			continue
		}

		// 检查是否匹配命名空间
		if namespaceName != "" && repo.Name != namespaceName {
			continue
		}

		// 检查关键字过滤
		if keyword != "" && !strings.Contains(repo.Name, keyword) {
			continue
		}

		filteredRepos = append(filteredRepos, RepositoryInfo{
			Name:         repo.Name,
			Summary:      fmt.Sprintf("Nexus %s repository", repo.Format),
			TagCount:     0, // Nexus API不直接提供标签数量，设为0
			CreateTime:   "",
			ModifiedTime: "",
		})
	}

	// 分页处理
	startIndex := (pageNumber - 1) * pageSize
	endIndex := startIndex + pageSize

	var pagedRepos []RepositoryInfo
	if startIndex < len(filteredRepos) {
		if endIndex > len(filteredRepos) {
			endIndex = len(filteredRepos)
		}
		pagedRepos = filteredRepos[startIndex:endIndex]
	}

	return &RepositoryListResult{
		Repositories: pagedRepos,
		Total:        len(filteredRepos),
		PageNumber:   pageNumber,
		PageSize:     pageSize,
	}, nil
}
