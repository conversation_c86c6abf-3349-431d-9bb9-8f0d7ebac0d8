package registry

import (
	"context"
	"fmt"
	"pilot-api/config"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAliRegistryListTags(t *testing.T) {
	config.Load()
	// 使用工厂模式创建阿里云registry客户端
	client, err := NewRegistryFactory().CreateClient(RegistryTypeAliyun)
	if err != nil {
		t.Logf("Failed to create aliyun registry client (this might be expected if config is missing): %v", err)
		return
	}

	namespaceName := "makeblock"
	repositoryName := "pilot-api"
	keyword := ""
	pageNumber := 1
	pageSize := 10

	// 调用GetRepositoryTags方法
	result, err := client.GetRepositoryTags(context.Background(), namespaceName, repositoryName, keyword, pageNumber, pageSize)
	if err != nil {
		t.Logf("GetRepositoryTags failed (this might be expected in test environment): %v", err)
	} else {
		assert.NotNil(t, result)
		t.Logf("GetRepositoryTags success, total: %d, tags count: %d", result.Total, len(result.Tags))
	}
	fmt.Println(result.Tags)
}
