package registry

import (
	"context"
	"fmt"
	"pilot-api/config"
	"strconv"

	"git.makeblock.com/makeblock-go/log"
	cr20181201 "github.com/alibabacloud-go/cr-20181201/v3/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// TODO: 优化配置
// RegistryClient 阿里云镜像仓库客户端
type AliRegistry struct {
	client *cr20181201.Client
	config *RegistryConfig
}

// RegistryConfig 阿里云镜像仓库配置
type RegistryConfig struct {
	AccessKeyId     string
	AccessKeySecret string
	RegionId        string
	InstanceId      string
	Endpoint        string
	Username        string
	Password        string
}

// NewAliRegistry 创建阿里云镜像仓库客户端
func NewAliRegistry() (RegistryClient, error) {
	cfg := config.Items().AliyunRegistry
	registryConfig := &RegistryConfig{
		AccessKeyId:     cfg.AccessKeyId,
		AccessKeySecret: cfg.AccessKeySecret,
		RegionId:        cfg.RegionId,
		InstanceId:      cfg.InstanceId,
		Endpoint:        cfg.Endpoint,
	}
	config := &openapi.Config{
		AccessKeyId:     tea.String(cfg.AccessKeyId),
		AccessKeySecret: tea.String(cfg.AccessKeySecret),
		RegionId:        tea.String(cfg.RegionId),
		Endpoint:        tea.String(cfg.Endpoint),
	}

	// 创建客户端
	client, err := cr20181201.NewClient(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create openapi client: %w", err)
	}

	return &AliRegistry{
		client: client,
		config: registryConfig,
	}, nil
}

// GetRepositoryTags 获取镜像仓库标签列表（支持搜索）
func (r *AliRegistry) GetRepositoryTags(ctx context.Context, namespaceName,
	repositoryName, keyword string, pageNumber, pageSize int) (*TagListResult, error) {
	log.Info("GetRepositoryTags", log.Any("namespaceName", namespaceName),
		log.Any("repositoryName", repositoryName), log.Any("keyword", keyword))
	repoId, err := r.GetRepoID(ctx, namespaceName, repositoryName)
	if err != nil {
		return nil, err
	}

	// Validate page parameters to prevent overflow
	if pageNumber > 2147483647 || pageSize > 2147483647 {
		return nil, fmt.Errorf("page parameters too large")
	}

	req := &cr20181201.ListRepoTagRequest{
		InstanceId: tea.String(r.config.InstanceId),
		RepoId:     tea.String(repoId),
		PageNo:     tea.Int32(int32(pageNumber)), //nolint:gosec // validated above
		PageSize:   tea.Int32(int32(pageSize)),   //nolint:gosec // validated above
	}
	runtime := &util.RuntimeOptions{}
	resp, err := r.client.ListRepoTagWithOptions(req, runtime)
	if err != nil {
		log.Error("ListRepoTag error", log.Any("error", err))
		return nil, err
	}

	tags := make([]string, 0)
	for _, image := range resp.Body.Images {
		tags = append(tags, *image.Tag)
	}

	// 将字符串指针转换为int
	totalStr := *resp.Body.TotalCount
	total, err := strconv.Atoi(totalStr)
	if err != nil {
		log.Error("Failed to convert TotalCount to int", log.Any("error", err))
		return nil, fmt.Errorf("failed to convert TotalCount to int: %w", err)
	}

	return &TagListResult{
		Tags:       tags,
		Total:      total,
		PageNumber: pageNumber,
		PageSize:   pageSize,
	}, nil
}

func (r *AliRegistry) GetRepoID(ctx context.Context, namespaceName, repositoryName string) (string, error) {
	req := &cr20181201.GetRepositoryRequest{
		InstanceId:        tea.String(r.config.InstanceId),
		RepoNamespaceName: tea.String(namespaceName),
		RepoName:          tea.String(repositoryName),
	}

	runtime := &util.RuntimeOptions{}
	resp, err := r.client.GetRepositoryWithOptions(req, runtime)
	if err != nil {
		log.Error("GetRepoID error", log.Any("error", err))
		return "", err
	}

	return *resp.Body.RepoId, nil
}
