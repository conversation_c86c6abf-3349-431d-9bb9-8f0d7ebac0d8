package registry

import (
	"fmt"
)

// RegistryType 定义支持的registry类型
type RegistryType string

const (
	// RegistryTypeAliyun 阿里云容器镜像仓库
	RegistryTypeAliyun RegistryType = "aliyun"
	// RegistryTypeNexus Nexus镜像仓库
	RegistryTypeNexus RegistryType = "nexus"
	// RegistryTypeDockerHub Docker Hub
	RegistryTypeDockerHub RegistryType = "dockerhub"
)

// RegistryFactory registry工厂
type RegistryFactory struct{}

// NewRegistryFactory 创建registry工厂
func NewRegistryFactory() *RegistryFactory {
	return &RegistryFactory{}
}

// CreateClient 根据类型创建对应的registry客户端
func (f *RegistryFactory) CreateClient(registryType RegistryType) (RegistryClient, error) {
	switch registryType {
	case RegistryTypeAliyun:
		return NewAliRegistry()
	case RegistryTypeNexus:
		return NewNexusRegistry()
	case RegistryTypeDockerHub:
		return nil, fmt.Errorf("dockerhub registry not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported registry type: %s", registryType)
	}
}

// GetSupportedTypes 获取支持的registry类型列表
func (f *RegistryFactory) GetSupportedTypes() []RegistryType {
	return []RegistryType{
		RegistryTypeAliyun,
		RegistryTypeNexus,
	}
}

// ValidateType 验证registry类型是否支持
func (f *RegistryFactory) ValidateType(registryType RegistryType) bool {
	supportedTypes := f.GetSupportedTypes()
	for _, t := range supportedTypes {
		if t == registryType {
			return true
		}
	}
	return false
}
