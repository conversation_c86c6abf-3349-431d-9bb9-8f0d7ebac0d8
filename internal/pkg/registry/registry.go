package registry

import "context"

// TagListResult 标签列表结果
type TagListResult struct {
	Tags       []string `json:"tags"`       // 标签名称列表
	Total      int      `json:"total"`      // 总数
	PageNumber int      `json:"pageNumber"` // 页码
	PageSize   int      `json:"pageSize"`   // 每页大小
}

// RepositoryListResult 仓库列表结果
type RepositoryListResult struct {
	Repositories []RepositoryInfo `json:"repositories"` // 仓库信息列表
	Total        int              `json:"total"`        // 总数
	PageNumber   int              `json:"pageNumber"`   // 页码
	PageSize     int              `json:"pageSize"`     // 每页大小
}

// RepositoryInfo 仓库信息
type RepositoryInfo struct {
	Name         string `json:"name"`         // 仓库名称
	Summary      string `json:"summary"`      // 仓库摘要
	TagCount     int    `json:"tagCount"`     // 标签数量
	CreateTime   string `json:"createTime"`   // 创建时间
	ModifiedTime string `json:"modifiedTime"` // 修改时间
}

type RegistryClient interface {
	GetRepositoryTags(ctx context.Context, namespaceName, repositoryName, keyword string, pageNumber, pageSize int) (*TagListResult, error)
	// GetRepositories(ctx context.Context, namespaceName, keyword string, pageNumber, pageSize int) (*RepositoryListResult, error)
}
