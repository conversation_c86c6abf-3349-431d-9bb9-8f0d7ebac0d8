package cluster

import (
	"context"
	"fmt"
	"pilot-api/internal/pkg/util"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"

	"gorm.io/gorm"
	istio "istio.io/client-go/pkg/clientset/versioned"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// ClusterClient 集群客户端信息
type ClusterClient struct {
	KubeClient  kubernetes.Interface
	IstioClient istio.Interface
	LastUsed    time.Time
}

// ClusterManager 集群管理器
type ClusterManager struct {
	db      *gorm.DB
	clients map[string]*ClusterClient
	mutex   sync.RWMutex
}

// ClusterInfo 集群信息结构体
type ClusterInfo struct {
	UUID       string `gorm:"column:uuid"`
	Name       string `gorm:"column:name"`
	KubeConfig string `gorm:"column:kube_config"`
	Enable     bool   `gorm:"column:enable"`
}

// NewClusterManager 创建集群管理器
func NewClusterManager(db *gorm.DB) *ClusterManager {
	return &ClusterManager{
		db:      db,
		clients: make(map[string]*ClusterClient),
	}
}

// GetClusterClient 获取集群客户端
func (cm *ClusterManager) GetClusterClient(clusterID string) (kubernetes.Interface, istio.Interface, error) {
	cm.mutex.RLock()
	client, exists := cm.clients[clusterID]
	cm.mutex.RUnlock()

	if exists {
		cm.mutex.Lock()
		client.LastUsed = time.Now()
		cm.mutex.Unlock()
		return client.KubeClient, client.IstioClient, nil
	}

	return cm.createClusterClient(clusterID)
}

// createClusterClient 创建集群客户端
func (cm *ClusterManager) createClusterClient(clusterID string) (kubernetes.Interface, istio.Interface, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if client, exists := cm.clients[clusterID]; exists {
		client.LastUsed = time.Now()
		return client.KubeClient, client.IstioClient, nil
	}

	clusterInfo, err := cm.getClusterInfo(clusterID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get cluster info: %w", err)
	}

	if clusterInfo.KubeConfig == "" {
		return nil, nil, fmt.Errorf("cluster %s has no kubeconfig", clusterID)
	}

	decryptedKubeConfig, err := util.DefaultDecrypt(clusterInfo.KubeConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decrypt kubeconfig for cluster %s: %w", clusterID, err)
	}

	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(decryptedKubeConfig))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create k8s config for cluster %s: %w", clusterID, err)
	}

	kubeClient, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create kubernetes client for cluster %s: %w", clusterID, err)
	}

	istioClient, err := istio.NewForConfig(config)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create istio client for cluster %s: %w", clusterID, err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = kubeClient.CoreV1().Namespaces().List(ctx, v1.ListOptions{Limit: 1})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to test connection to cluster %s: %w", clusterID, err)
	}

	clusterClient := &ClusterClient{
		KubeClient:  kubeClient,
		IstioClient: istioClient,
		LastUsed:    time.Now(),
	}
	cm.clients[clusterID] = clusterClient

	log.Printf("Created new cluster client for cluster: %s", clusterID)
	return kubeClient, istioClient, nil
}

// getClusterInfo 从数据库获取集群信息
func (cm *ClusterManager) getClusterInfo(clusterID string) (*ClusterInfo, error) {
	var clusterInfo ClusterInfo
	err := cm.db.Table("clusters").
		Select("uuid, name, kube_config, enable").
		Where("uuid = ? AND deleted_at is null", clusterID).
		First(&clusterInfo).Error
	if err != nil {
		return nil, err
	}
	return &clusterInfo, nil
}

// GetClusterInfo 获取集群基本信息（供外部使用）
func (cm *ClusterManager) GetClusterInfo(clusterID string) (*ClusterInfo, error) {
	return cm.getClusterInfo(clusterID)
}

// ListEnabledClusters 获取所有启用的集群列表
func (cm *ClusterManager) ListEnabledClusters() ([]*ClusterInfo, error) {
	var clusters []*ClusterInfo
	err := cm.db.Table("clusters").
		Select("uuid, name, kube_config, enable").
		Where("enable = ? AND deleted_at is null", true).
		Find(&clusters).Error
	if err != nil {
		return nil, err
	}
	return clusters, nil
}

// InvalidateClusterCache 清除指定集群的缓存（支持单个或多个集群）
func (cm *ClusterManager) InvalidateClusterCache(clusterIDs ...string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	for _, clusterID := range clusterIDs {
		if _, exists := cm.clients[clusterID]; exists {
			delete(cm.clients, clusterID)
			log.Printf("Invalidated cache for cluster: %s", clusterID)
		}
	}
}

// RefreshClusterCache 刷新指定集群的缓存
func (cm *ClusterManager) RefreshClusterCache(clusterID string) error {
	cm.InvalidateClusterCache(clusterID)

	_, _, err := cm.GetClusterClient(clusterID)
	if err != nil {
		log.ErrorE("Failed to refresh cluster cache", err)
		return err
	}

	return nil
}

// GetClusterConfig 获取集群配置
func (cm *ClusterManager) GetClusterConfig(clusterID string) (*rest.Config, error) {
	clusterInfo, err := cm.getClusterInfo(clusterID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster info: %w", err)
	}

	if clusterInfo.KubeConfig == "" {
		return nil, fmt.Errorf("cluster %s has no kubeconfig", clusterID)
	}

	decryptedKubeConfig, err := util.DefaultDecrypt(clusterInfo.KubeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt kubeconfig for cluster %s: %w", clusterID, err)
	}

	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(decryptedKubeConfig))
	if err != nil {
		return nil, fmt.Errorf("failed to create k8s config for cluster %s: %w", clusterID, err)
	}

	return config, nil
}
