package migrate

import (
	"pilot-api/pkg/models"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
)

// AutoMigrate auto migrate
func AutoMigrate() {
	modelInstances := []any{
		&models.Cluster{},
		// virtual service
		&models.VirtualService{},
		&models.VirtualServiceHistory{},
		// domain
		//&models.DomainInfo{},
		&models.DomainProvider{},
		&models.DomainRecord{},
		// canary
		&models.Workflow{},
		&models.WorkflowExecution{},
		&models.WorkflowStep{},
		&models.StepExecution{},
	}
	for _, m := range modelInstances {
		if err := mysql.GetDB().AutoMigrate(m); err != nil {
			log.ErrorE("AutoMigrate", err)
		}
	}
}
