package middleware

import (
	"errors"
	"pilot-api/config"
	pkgResponse "pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"

	errorsUtil "git.makeblock.com/makeblock-go/utils/v2/errors"

	"github.com/gin-gonic/gin"
)

const (
	PilotAdmin = "admin"
	RouteAdmin = "admin-route"
	DnsAdmin   = "admin-dns"
)

const (
	HeaderUToken = "utoken"
	UserName     = "username"
	UUID         = "uuid"
	UserEmail    = "email"
)

// 效能平台接口路径
const (
	TokenKey = "x-token"
	UserPath = "/efficacy/v1/open/user"
	AuthPath = "/efficacy/v1/open/auth"
)

func GetUserName(c *gin.Context) string {
	if user, exists := c.Get("username"); exists {
		return user.(string)
	}
	return ""
}

func GetUserEmail(c *gin.Context) string {
	if email, exists := c.Get("email"); exists {
		return email.(string)
	}
	return ""
}

func IsAdmin(c *gin.Context) bool {
	return HasRole(c, PilotAdmin)
}

func HasRole(c *gin.Context, roles ...string) bool {
	value, exists := c.Get("roles")
	if !exists {
		return false
	}
	roleMap := value.(map[string]struct{})
	for _, role := range roles {
		if _, ok := roleMap[role]; ok {
			return true
		}
	}
	return false
}

// AuthRequest 请求认证
func AuthRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("authRequest", true)
		c.Next()
	}
}

// AuthIsAdmin 检查当前用户是否是管理员, must be used before AuthUToken
func AuthIsAdmin() gin.HandlerFunc {
	return AuthRoles(PilotAdmin)
}

// AuthRoles 角色认证
func AuthRoles(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("domain", "pilot")
		c.Set("requireRoles", true) // 是否需要获取角色来做权限判断
		c.Set("roles", roles)
		c.Next()
	}
}

type AuthUTokenResponse struct {
	User  *models.User        `json:"user"`
	Roles map[string]struct{} `json:"roles"`
}

func AuthUToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		req := map[string]any{}
		// 获取token
		token := util.Coalesce(
			c.Request.Header.Get(HeaderUToken),
			c.Query(HeaderUToken))

		// 尝试从缓存中获取认证信息
		if cacheResponse, hit := GetAuthFromCache(c, token); hit {
			// 设置用户信息
			usingCache := true
			// 如果需要获取角色信息
			if _, ok := c.Keys["requireRoles"]; ok {
				// 如果角色为空则需要重新请求
				if len(cacheResponse.Roles) == 0 {
					usingCache = false
				}
				c.Set("roles", cacheResponse.Roles)
			}
			// 缓存命中且角色非空，直接使用缓存中的数据
			if usingCache {
				c.Set("user", cacheResponse.User)
				c.Set("email", cacheResponse.User.Email)
				c.Set("username", cacheResponse.User.UserName)
				c.Next()
				return
			}
		}

		// 缓存未命中，请求认证服务
		// 请求路径
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		if raw != "" {
			path = path + "?" + raw
		}
		method := c.Request.Method
		// 请求参数
		req["raw"] = raw
		req["path"] = path
		req["method"] = method
		// 是否检查当前用户是否是管理员
		req["domain"] = c.Keys["domain"]
		req[HeaderUToken] = token
		// 是否认证请求
		req["authRequest"] = c.Keys["authRequest"]
		// 是否需要获取角色
		req["requireRoles"] = c.Keys["requireRoles"]
		// 请求认证服务
		response, err := util.HttpPost[AuthUTokenResponse](
			config.Items().Efficacy.Endpoint+AuthPath,
			req,
			map[string]string{
				HeaderUToken: token,
				TokenKey:     config.Items().Efficacy.Token,
			})
		// token失效
		if err != nil && errors.Is(err, pkgResponse.TokenInvalidError("")) {
			pkgResponse.Err(c, pkgResponse.TokenInvalidError(""))
			c.Abort()
			return
		}
		// 其他错误
		if err != nil {
			errorsUtil.ResponseError(c, err)
			c.Abort()
			return
		}

		// 将认证结果存入缓存
		_ = SetAuthToCache(c, token, response)

		// 设置用户信息
		c.Set("user", response.User)
		c.Set("email", response.User.Email)
		c.Set("username", response.User.UserName)
		// 设置用户角色
		if _, ok := c.Keys["requireRoles"]; ok {
			c.Set("roles", response.Roles)
		}
		c.Next()
	}
}

// UTokenFromQuery 如果header中没有utoken，则尝试从query中获取utoken
func UTokenFromQuery() gin.HandlerFunc {
	return func(c *gin.Context) {
		utoken := c.Query("utoken")
		if utoken != "" {
			c.Request.Header.Set("utoken", utoken)
		}
		c.Next()
	}
}
