package middleware

import (
	"encoding/json"
	"fmt"
	"pilot-api/config"
	"time"

	"git.makeblock.com/makeblock-go/redis"
	"github.com/gin-gonic/gin"
)

const (
	// UserAuthCachePrefix 用户认证缓存前缀
	UserAuthCachePrefix = "user_auth:"
	// UserAuthCacheExpiration 用户认证缓存过期时间（10分钟）
	UserAuthCacheExpiration = 10 * time.Minute
)

// GetAuthCacheKey 获取认证缓存的key
func GetAuthCacheKey(token string) string {
	return fmt.Sprintf("%s%s%s", config.Items().Redis.Prefix, UserAuthCachePrefix, token)
}

// GetAuthFromCache 从缓存中获取认证信息
func GetAuthFromCache(c *gin.Context, token string) (*AuthUTokenResponse, bool) {
	if token == "" {
		return nil, false
	}

	cacheKey := GetAuthCacheKey(token)
	cacheData, err := redis.GetClient().Get(c, cacheKey).Result()
	if err != nil {
		return nil, false
	}

	var response AuthUTokenResponse
	if err := json.Unmarshal([]byte(cacheData), &response); err != nil {
		return nil, false
	}

	return &response, true
}

// SetAuthToCache 将认证信息存入缓存
func SetAuthToCache(c *gin.Context, token string, response *AuthUTokenResponse) error {
	if token == "" || response == nil {
		return nil
	}

	cacheKey := GetAuthCacheKey(token)
	cacheData, err := json.Marshal(response)
	if err != nil {
		return err
	}

	return redis.GetClient().Set(c, cacheKey, cacheData, UserAuthCacheExpiration).Err()
}
