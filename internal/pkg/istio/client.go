package istio

import (
	"context"
	"fmt"
	"pilot-api/internal/pkg/cluster"
	"pilot-api/internal/pkg/types"

	"git.makeblock.com/makeblock-go/log"
	istio "istio.io/client-go/pkg/apis/networking/v1beta1"
	istioclient "istio.io/client-go/pkg/clientset/versioned"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ClientManager Istio客户端管理器
type ClientManager struct {
	clusterManager *cluster.ClusterManager
}

// NewClientManager 创建Istio客户端管理器
func NewClientManager() *ClientManager {
	return &ClientManager{
		clusterManager: cluster.GetClusterManager(),
	}
}

// ApplyChainIstioConfigs 应用灰度发布的Istio配置
func (m *ClientManager) ApplyChainIstioConfigs(ctx context.Context, services []types.CanaryService, namespace, clusterID string) error {
	log.Info(fmt.Sprintf("applying canary Istio configs for %d services in namespace %s", len(services), namespace))

	// 验证配置
	if err := m.ValidateChainConfiguration(services); err != nil {
		return fmt.Errorf("invalid canary configuration: %w", err)
	}

	// 为每个服务创建Istio配置
	for _, service := range services {
		if err := m.ApplyServiceIstioConfigs(ctx, &service, namespace, clusterID); err != nil {
			return fmt.Errorf("failed to apply canary config for service %s: %w", service.ServiceName, err)
		}
	}

	log.Info("successfully applied canary Istio configs")
	return nil
}

// ApplyServiceIstioConfigs 应用单个服务的Istio配置
func (m *ClientManager) ApplyServiceIstioConfigs(ctx context.Context, service *types.CanaryService, namespace, clusterID string) error {
	// 获取Istio客户端
	istioClient, err := m.getIstioClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get istio client: %w", err)
	}

	// 生成VirtualService
	virtualService := service.ToIstioVirtualService(namespace)
	if err := m.applyVirtualService(ctx, istioClient, virtualService); err != nil {
		return fmt.Errorf("failed to apply virtual service %s: %w", virtualService.Name, err)
	}
	log.Info(fmt.Sprintf("Applied VirtualService: %s in namespace: %s", virtualService.Name, virtualService.Namespace))

	// 生成DestinationRule
	destinationRule := service.ToIstioDestinationRule(namespace)
	if err := m.applyDestinationRule(ctx, istioClient, destinationRule); err != nil {
		return fmt.Errorf("failed to apply destination rule %s: %w", destinationRule.Name, err)
	}
	log.Info(fmt.Sprintf("Applied DestinationRule: %s in namespace: %s", destinationRule.Name, destinationRule.Namespace))

	return nil
}

// CleanupChainIstioConfigs 批量清理全链路灰度发布的Istio配置
func (m *ClientManager) CleanupChainIstioConfigs(ctx context.Context, services []types.CanaryService, namespace, clusterID string) error {
	// 获取Istio客户端
	istioClient, err := m.getIstioClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get istio client: %w", err)
	}

	// 按照依赖顺序的反序清理（避免依赖问题）
	orderedServices := m.sortServicesByDependency(services)
	for i := len(orderedServices) - 1; i >= 0; i-- {
		service := orderedServices[i]
		log.Info(fmt.Sprintf("Cleaning up chain Istio configs for service: %s", service.ServiceName))

		// 删除VirtualService
		vsName := service.ServiceName + "-canary"
		if err := m.deleteVirtualService(ctx, istioClient, vsName, namespace); err != nil {
			log.ErrorE(fmt.Sprintf("Failed to delete VirtualService: %s", vsName), err)
		} else {
			log.Info(fmt.Sprintf("Deleted chain VirtualService: %s in namespace: %s", vsName, namespace))
		}

		// 删除DestinationRule
		drName := service.ServiceName + "-canary"
		if err := m.deleteDestinationRule(ctx, istioClient, drName, namespace); err != nil {
			log.ErrorE(fmt.Sprintf("Failed to delete DestinationRule: %s", drName), err)
		} else {
			log.Info(fmt.Sprintf("Deleted chain DestinationRule: %s in namespace: %s", drName, namespace))
		}
	}

	return nil
}

// CleanupServiceIstioConfigs 清理单个服务的Istio配置
func (m *ClientManager) CleanupServiceIstioConfigs(ctx context.Context, serviceName, namespace, clusterID string) error {
	// 获取Istio客户端
	istioClient, err := m.getIstioClient(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get istio client: %w", err)
	}

	// 删除VirtualService
	vsName := serviceName + "-canary"
	if err := m.deleteVirtualService(ctx, istioClient, vsName, namespace); err != nil {
		log.ErrorE(fmt.Sprintf("Failed to delete VirtualService: %s", vsName), err)
	} else {
		log.Info(fmt.Sprintf("Deleted VirtualService: %s in namespace: %s", vsName, namespace))
	}

	// 删除DestinationRule
	drName := serviceName + "-canary"
	if err := m.deleteDestinationRule(ctx, istioClient, drName, namespace); err != nil {
		log.ErrorE(fmt.Sprintf("Failed to delete DestinationRule: %s", drName), err)
	} else {
		log.Info(fmt.Sprintf("Deleted DestinationRule: %s in namespace: %s", drName, namespace))
	}

	return nil
}

// sortServicesByDependency 根据依赖关系对服务进行排序
func (m *ClientManager) sortServicesByDependency(services []types.CanaryService) []types.CanaryService {
	// 如果服务有显式的Order，按Order排序
	orderedServices := make([]types.CanaryService, len(services))
	copy(orderedServices, services)

	// 简单的冒泡排序，按Order排序
	for i := 0; i < len(orderedServices)-1; i++ {
		for j := 0; j < len(orderedServices)-i-1; j++ {
			if orderedServices[j].Order > orderedServices[j+1].Order {
				orderedServices[j], orderedServices[j+1] = orderedServices[j+1], orderedServices[j]
			}
		}
	}

	return orderedServices
}

// ValidateChainConfiguration 验证灰度配置
func (m *ClientManager) ValidateChainConfiguration(services []types.CanaryService) error {
	if len(services) == 0 {
		return fmt.Errorf("no services provided for canary configuration")
	}

	// 验证每个服务的配置
	for i, service := range services {
		if err := m.validateServiceChainConfig(&service); err != nil {
			return fmt.Errorf("service %d (%s): %w", i, service.ServiceName, err)
		}
	}

	// 验证服务依赖关系
	if err := m.validateServiceDependencies(services); err != nil {
		return fmt.Errorf("service dependencies validation failed: %w", err)
	}

	return nil
}

// validateServiceChainConfig 验证单个服务的链路配置
func (m *ClientManager) validateServiceChainConfig(service *types.CanaryService) error {
	if service.ServiceName == "" {
		return fmt.Errorf("service name is required")
	}

	if service.Namespace == "" {
		return fmt.Errorf("namespace is required")
	}

	if len(service.Images) == 0 {
		return fmt.Errorf("at least one image is required")
	}

	if service.CanaryReplicas <= 0 {
		return fmt.Errorf("canary replicas must be positive")
	}

	if service.ProdReplicas <= 0 {
		return fmt.Errorf("production replicas must be positive")
	}

	if service.TrafficRatio < 0 || service.TrafficRatio > 100 {
		return fmt.Errorf("traffic ratio must be between 0 and 100")
	}

	// 验证路由配置
	if service.RoutingConfig != nil {
		if err := m.validateRoutingConfig(service.RoutingConfig); err != nil {
			return fmt.Errorf("invalid routing config: %w", err)
		}
	}

	return nil
}

// validateServiceDependencies 验证服务依赖关系
func (m *ClientManager) validateServiceDependencies(services []types.CanaryService) error {
	// 检查是否有循环依赖
	for _, service := range services {
		if service.Dependencies != nil {
			for _, dep := range service.Dependencies {
				// 检查依赖服务是否存在
				found := false
				for _, s := range services {
					if s.ServiceName == dep {
						found = true
						break
					}
				}
				if !found {
					return fmt.Errorf("service %s depends on non-existent service %s", service.ServiceName, dep)
				}
			}
		}
	}

	// 检查循环依赖
	if err := m.checkCircularDependencies(services); err != nil {
		return err
	}

	return nil
}

// checkCircularDependencies 检查循环依赖
func (m *ClientManager) checkCircularDependencies(services []types.CanaryService) error {
	// 构建依赖图
	graph := make(map[string][]string)
	for _, service := range services {
		graph[service.ServiceName] = service.Dependencies
	}

	// 使用DFS检查循环依赖
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	var dfs func(string) bool
	dfs = func(node string) bool {
		visited[node] = true
		recStack[node] = true

		for _, dep := range graph[node] {
			if !visited[dep] {
				if dfs(dep) {
					return true
				}
			} else if recStack[dep] {
				return true
			}
		}

		recStack[node] = false
		return false
	}

	for serviceName := range graph {
		if !visited[serviceName] {
			if dfs(serviceName) {
				return fmt.Errorf("circular dependency detected involving service: %s", serviceName)
			}
		}
	}

	return nil
}

// getIstioClient 获取Istio客户端
func (m *ClientManager) getIstioClient(clusterID string) (*istioclient.Clientset, error) {
	// 直接从cluster manager获取现有的istio客户端
	_, istioClient, err := m.clusterManager.GetClusterClient(clusterID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster client: %w", err)
	}

	// 将istio.Interface转换为*istioclient.Clientset
	if istioClientset, ok := istioClient.(*istioclient.Clientset); ok {
		return istioClientset, nil
	}

	return nil, fmt.Errorf("failed to convert istio client to clientset")
}

// applyDestinationRule 应用DestinationRule
func (m *ClientManager) applyDestinationRule(ctx context.Context, client *istioclient.Clientset, dr *istio.DestinationRule) error {
	// 检查是否已存在
	existing, err := client.NetworkingV1beta1().DestinationRules(dr.Namespace).Get(ctx, dr.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的DestinationRule
			_, err = client.NetworkingV1beta1().DestinationRules(dr.Namespace).Create(ctx, dr, metav1.CreateOptions{})
			return err
		}
		return err
	}

	// 更新现有的DestinationRule
	// 使用深拷贝避免锁复制
	existing.Spec.Host = dr.Spec.Host
	existing.Spec.TrafficPolicy = dr.Spec.TrafficPolicy
	existing.Spec.Subsets = dr.Spec.Subsets
	existing.Spec.ExportTo = dr.Spec.ExportTo
	existing.Spec.WorkloadSelector = dr.Spec.WorkloadSelector
	existing.Labels = dr.Labels
	_, err = client.NetworkingV1beta1().DestinationRules(dr.Namespace).Update(ctx, existing, metav1.UpdateOptions{})
	return err
}

// applyVirtualService 应用VirtualService
func (m *ClientManager) applyVirtualService(ctx context.Context, client *istioclient.Clientset, vs *istio.VirtualService) error {
	// 检查是否已存在
	existing, err := client.NetworkingV1beta1().VirtualServices(vs.Namespace).Get(ctx, vs.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的VirtualService
			_, err = client.NetworkingV1beta1().VirtualServices(vs.Namespace).Create(ctx, vs, metav1.CreateOptions{})
			return err
		}
		return err
	}

	// 更新现有的VirtualService
	// 使用深拷贝避免锁复制
	existing.Spec.Hosts = vs.Spec.Hosts
	existing.Spec.Gateways = vs.Spec.Gateways
	existing.Spec.Http = vs.Spec.Http
	existing.Spec.Tls = vs.Spec.Tls
	existing.Spec.Tcp = vs.Spec.Tcp
	existing.Spec.ExportTo = vs.Spec.ExportTo
	existing.Labels = vs.Labels
	_, err = client.NetworkingV1beta1().VirtualServices(vs.Namespace).Update(ctx, existing, metav1.UpdateOptions{})
	return err
}

// deleteVirtualService 删除VirtualService
func (m *ClientManager) deleteVirtualService(ctx context.Context, client *istioclient.Clientset, name, namespace string) error {
	return client.NetworkingV1beta1().VirtualServices(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// deleteDestinationRule 删除DestinationRule
func (m *ClientManager) deleteDestinationRule(ctx context.Context, client *istioclient.Clientset, name, namespace string) error {
	return client.NetworkingV1beta1().DestinationRules(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// ValidateIstioConfig 验证Istio配置
func (m *ClientManager) ValidateIstioConfig(services []types.CanaryService) error {
	// 验证服务配置
	for _, service := range services {
		if service.ServiceName == "" {
			return fmt.Errorf("service name is required")
		}
		if service.RoutingConfig != nil {
			if err := m.validateRoutingConfig(service.RoutingConfig); err != nil {
				return fmt.Errorf("invalid routing config for service %s: %w", service.ServiceName, err)
			}
		}
	}
	return nil
}

// validateRoutingConfig 验证路由配置
func (m *ClientManager) validateRoutingConfig(config *types.ServiceRoutingConfig) error {
	if config.Strategy == "" {
		return fmt.Errorf("routing strategy is required")
	}

	switch config.Strategy {
	case "header":
		if config.HeaderRouting == nil {
			return fmt.Errorf("header routing config is required for header strategy")
		}
		if config.HeaderRouting.HeaderName == "" || config.HeaderRouting.HeaderValue == "" {
			return fmt.Errorf("header name and value are required for header routing")
		}
	case "weight":
		if config.WeightRouting == nil {
			return fmt.Errorf("weight routing config is required for weight strategy")
		}
		if config.WeightRouting.CanaryWeight+config.WeightRouting.StableWeight != 100 {
			return fmt.Errorf("canary weight and stable weight must sum to 100")
		}
	case "cookie":
		if config.CookieRouting == nil {
			return fmt.Errorf("cookie routing config is required for cookie strategy")
		}
		if config.CookieRouting.CookieName == "" || config.CookieRouting.CookieValue == "" {
			return fmt.Errorf("cookie name and value are required for cookie routing")
		}
	default:
		return fmt.Errorf("unsupported routing strategy: %s", config.Strategy)
	}

	return nil
}
