package dns

import (
	"context"
	"pilot-api/internal/pkg/response"
	"pilot-api/pkg/models"
)

// DNSProvider DNS云平台提供者接口
type DNSProvider interface {
	// ListDomains 获取账号下的域名列表
	ListDomains(ctx context.Context) ([]DomainInfo, error)

	// ListRecords 获取域名记录列表
	ListRecords(ctx context.Context, domain string, filters *RecordFilters) (*response.PageModel[DNSRecordResponse], error)

	// GetRecord 获取单个域名记录
	GetRecord(ctx context.Context, domain, subdomain string, recordType models.DomainRecordType) (*models.DomainRecord, error)

	// CreateRecord 创建域名记录
	CreateRecord(ctx context.Context, domain string, record *models.DomainRecord) error

	// UpdateRecord 更新域名记录
	UpdateRecord(ctx context.Context, domain string, record *models.DomainRecord) error

	// DeleteRecord 删除域名记录
	DeleteRecord(ctx context.Context, domain string, record *models.DomainRecord) error

	// TestConnection 测试连接
	TestConnection(ctx context.Context) error
}

// DNSRecordResponse DNS记录响应结构
type DNSRecordResponse struct {
	UUID         string                    `json:"uuid"`
	FullDomain   string                    `json:"fullDomain"` // domain + subdomain
	Domain       string                    `json:"domain"`
	Subdomain    string                    `json:"subdomain"`
	RecordType   models.DomainRecordType   `json:"recordType"`
	Value        string                    `json:"value"`
	TTL          int                       `json:"ttl"`
	Priority     int                       `json:"priority"`
	Proxied      *bool                     `json:"proxied"` // Cloudflare DNS Proxy状态
	ProviderName string                    `json:"providerName"`
	Status       models.DomainRecordStatus `json:"status"`
	Creator      string                    `json:"creator"`
	CreatedAt    models.Time               `json:"createdAt"`
}

// RecordFilters 记录过滤条件
type RecordFilters struct {
	Subdomain  string                    `json:"subdomain"`
	RecordType models.DomainRecordType   `json:"recordType"`
	Status     models.DomainRecordStatus `json:"status"`
	Value      string                    `json:"value"`    // 记录值过滤
	Page       int                       `json:"page"`     // 页码，从1开始
	PageSize   int                       `json:"pageSize"` // 每页大小
	Domain     string                    `json:"domain"`   // 过滤条件，查询指定域名的记录
}

// ProviderConfig 提供者配置
type ProviderConfig struct {
	Type   models.DomainProviderType `json:"type"`
	Config string                    `json:"config"` // JSON格式的配置信息
}

// DomainInfo 域名信息结构
type DomainInfo struct {
	Domain string `json:"domain"` // 域名
	Status string `json:"status"` // 域名状态
}
