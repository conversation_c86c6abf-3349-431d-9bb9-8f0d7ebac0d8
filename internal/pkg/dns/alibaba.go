package dns

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/response"
	"pilot-api/pkg/models"
	"time"

	pkgUtil "pilot-api/internal/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	alidns "github.com/alibabacloud-go/alidns-20150109/v4/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// AlibabaConfig 阿里云DNS配置
type AlibabaConfig struct {
	AccessKeyId     string `json:"accessKeyId" validate:"required"`
	AccessKeySecret string `json:"accessKeySecret" validate:"required"`
	RegionId        string `json:"regionId" validate:"required"`
	Endpoint        string `json:"endpoint" validate:"required"`
}

// AlibabaDNSProvider 阿里云DNS提供者
type AlibabaDNSProvider struct {
	client *alidns.Client
	config *AlibabaConfig
}

// NewAlibabaDNSProvider 创建阿里云DNS提供者
func NewAlibabaDNSProvider(configStr string) (*AlibabaDNSProvider, error) {
	var config AlibabaConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		return nil, fmt.Errorf("failed to parse alibaba config: %w", err)
	}

	// 验证配置
	if err := ValidateAlibabaConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid alibaba config: %w", err)
	}

	openapiConfig := &openapi.Config{
		AccessKeyId:     tea.String(config.AccessKeyId),
		AccessKeySecret: tea.String(config.AccessKeySecret),
		RegionId:        tea.String(config.RegionId),
		Endpoint:        tea.String(config.Endpoint),
	}

	client, err := alidns.NewClient(openapiConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create alibaba DNS client: %w", err)
	}

	return &AlibabaDNSProvider{
		client: client,
		config: &config,
	}, nil
}

// ListRecords 获取域名记录列表
func (p *AlibabaDNSProvider) ListRecords(ctx context.Context, domain string, filters *RecordFilters) (*response.PageModel[DNSRecordResponse], error) {
	// 设置默认分页参数
	pageSize := int64(100)
	pageNumber := int64(1)

	// 应用传入的分页参数
	if filters != nil && filters.PageSize > 0 {
		pageSize = int64(filters.PageSize)
	}
	if filters != nil && filters.Page > 0 {
		pageNumber = int64(filters.Page)
	}

	request := &alidns.DescribeDomainRecordsRequest{
		DomainName: tea.String(domain),
		PageSize:   tea.Int64(pageSize),
		PageNumber: tea.Int64(pageNumber),
	}

	// 应用过滤条件
	if filters != nil {
		if filters.Subdomain != "" {
			request.RRKeyWord = tea.String(filters.Subdomain)
		}
		if filters.RecordType != "" {
			request.TypeKeyWord = tea.String(string(filters.RecordType))
		}
		if filters.Value != "" {
			request.ValueKeyWord = tea.String(filters.Value)
		}
	}

	runtime := &util.RuntimeOptions{}
	resp, err := p.client.DescribeDomainRecordsWithOptions(request, runtime)
	if err != nil {
		return nil, fmt.Errorf("failed to describe dns records: %w", err)
	}

	// 转换记录为DNSRecordResponse
	var records []DNSRecordResponse
	for _, record := range resp.Body.DomainRecords.Record {
		subdomain := tea.StringValue(record.RR)
		// 直接使用阿里云的RecordId作为UUID
		recordUUID := tea.StringValue(record.RecordId)

		dnsRecord := DNSRecordResponse{
			UUID:         recordUUID,
			FullDomain:   getFullDomain(subdomain, domain),
			Domain:       domain,
			Subdomain:    subdomain,
			RecordType:   models.DomainRecordType(tea.StringValue(record.Type)),
			Value:        tea.StringValue(record.Value),
			TTL:          int(tea.Int64Value(record.TTL)),
			Priority:     int(tea.Int64Value(record.Priority)),
			ProviderName: "阿里云DNS",
			Status:       models.RecordStatusActive,
			Creator:      "",
			CreatedAt:    models.Time{},
		}
		records = append(records, dnsRecord)
	}

	// 计算总数 - 阿里云API响应中包含总数信息
	total := tea.Int64Value(resp.Body.TotalCount)

	return &response.PageModel[DNSRecordResponse]{
		List:     records,
		Total:    total,
		Page:     int(pageNumber),
		PageSize: int(pageSize),
	}, nil
}

// getFullDomain 构建完整域名
func getFullDomain(subdomain, domain string) string {
	if subdomain == "" || subdomain == "@" {
		return domain
	}
	return subdomain + "." + domain
}

// GetRecord 获取单个域名记录
func (p *AlibabaDNSProvider) GetRecord(ctx context.Context, domain, subdomain string, recordType models.DomainRecordType) (*models.DomainRecord, error) {
	request := &alidns.DescribeDomainRecordsRequest{
		DomainName:  tea.String(domain),
		RRKeyWord:   tea.String(subdomain),
		TypeKeyWord: tea.String(string(recordType)),
		PageSize:    tea.Int64(1),
		PageNumber:  tea.Int64(1),
	}

	runtime := &util.RuntimeOptions{}
	resp, err := p.client.DescribeDomainRecordsWithOptions(request, runtime)
	if err != nil {
		return nil, fmt.Errorf("failed to get dns record: %w", err)
	}

	if len(resp.Body.DomainRecords.Record) == 0 {
		return nil, fmt.Errorf("DNS record not found: %s.%s %s", subdomain, domain, recordType)
	}

	record := resp.Body.DomainRecords.Record[0]
	now := time.Now()
	return &models.DomainRecord{
		BaseModel: models.BaseModel{
			UUID:        pkgUtil.PtrString(record.RecordId),
			GmtCreate:   &now,
			GmtModified: &now,
		},
		Domain:       domain, // 设置域名
		ProviderUUID: "",     // DNS Provider层不需要设置ProviderUUID
		Subdomain:    tea.StringValue(record.RR),
		RecordType:   models.DomainRecordType(tea.StringValue(record.Type)),
		Value:        tea.StringValue(record.Value),
		TTL:          int(tea.Int64Value(record.TTL)),
		Priority:     int(tea.Int64Value(record.Priority)),
		Status:       models.RecordStatusActive,
	}, nil
}

// CreateRecord 创建域名记录
func (p *AlibabaDNSProvider) CreateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	request := &alidns.AddDomainRecordRequest{
		DomainName: tea.String(domain),
		RR:         tea.String(record.Subdomain),
		Type:       tea.String(string(record.RecordType)),
		Value:      tea.String(record.Value),
		TTL:        tea.Int64(int64(record.TTL)),
	}

	// 设置优先级（仅对MX和SRV记录有意义）
	if record.Priority >= 0 && (record.RecordType == models.RecordTypeMX || record.RecordType == models.RecordTypeSRV) {
		request.Priority = tea.Int64(int64(record.Priority))
	}

	runtime := &util.RuntimeOptions{}
	_, err := p.client.AddDomainRecordWithOptions(request, runtime)
	if err != nil {
		return fmt.Errorf("failed to create dns record: %w", err)
	}

	log.Printf("Created dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// UpdateRecord 更新域名记录
func (p *AlibabaDNSProvider) UpdateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 先获取记录ID
	prevRecord := pkgUtil.StringToStruct[models.DomainRecord](record.PrevRecord)
	existingRecord, err := p.GetRecord(ctx, domain, prevRecord.Subdomain, prevRecord.RecordType)
	if err != nil {
		return fmt.Errorf("failed to get existing record: %w", err)
	}
	request := &alidns.UpdateDomainRecordRequest{
		RecordId: tea.String(existingRecord.UUID), // 这里需要实际的RecordId，暂时用UUID
		RR:       tea.String(record.Subdomain),
		Type:     tea.String(string(record.RecordType)),
		Value:    tea.String(record.Value),
		TTL:      tea.Int64(int64(record.TTL)),
	}

	// 设置优先级（仅对MX和SRV记录有意义）
	if record.Priority >= 0 && (record.RecordType == models.RecordTypeMX || record.RecordType == models.RecordTypeSRV) {
		request.Priority = tea.Int64(int64(record.Priority))
	}

	runtime := &util.RuntimeOptions{}
	_, err = p.client.UpdateDomainRecordWithOptions(request, runtime)
	if err != nil {
		return fmt.Errorf("failed to update dns record: %w", err)
	}

	log.Printf("Updated dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// DeleteRecord 删除域名记录
func (p *AlibabaDNSProvider) DeleteRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 先获取记录ID
	existingRecord, err := p.GetRecord(ctx, domain, record.Subdomain, record.RecordType)
	if err != nil {
		return fmt.Errorf("failed to get existing record: %w", err)
	}

	request := &alidns.DeleteDomainRecordRequest{
		RecordId: tea.String(existingRecord.UUID), // 这里需要实际的RecordId，暂时用UUID
	}

	runtime := &util.RuntimeOptions{}
	_, err = p.client.DeleteDomainRecordWithOptions(request, runtime)
	if err != nil {
		return fmt.Errorf("failed to delete dns record: %w", err)
	}

	log.Printf("Deleted dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// ListDomains 获取账号下的域名列表
func (p *AlibabaDNSProvider) ListDomains(ctx context.Context) ([]DomainInfo, error) {
	request := &alidns.DescribeDomainsRequest{
		PageSize:   tea.Int64(100),
		PageNumber: tea.Int64(1),
	}

	runtime := &util.RuntimeOptions{}
	resp, err := p.client.DescribeDomainsWithOptions(request, runtime)
	if err != nil {
		return nil, fmt.Errorf("failed to list alibaba domains: %w", err)
	}

	domains := make([]DomainInfo, 0, len(resp.Body.Domains.Domain))
	for _, domain := range resp.Body.Domains.Domain {
		domains = append(domains, DomainInfo{
			Domain: tea.StringValue(domain.DomainName),
			Status: "active", // 阿里云的域名状态字段可能不同，暂时设为active
		})
	}

	log.Printf("Found %d domains from Alibaba Cloud", len(domains))
	return domains, nil
}

// TestConnection 测试连接
func (p *AlibabaDNSProvider) TestConnection(ctx context.Context) error {
	request := &alidns.DescribeDomainsRequest{
		PageSize:   tea.Int64(1),
		PageNumber: tea.Int64(1),
	}

	runtime := &util.RuntimeOptions{}
	_, err := p.client.DescribeDomainsWithOptions(request, runtime)
	if err != nil {
		return fmt.Errorf("failed to test alibaba DNS connection: %w", err)
	}

	return nil
}
