package dns

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
)

var validate = validator.New()

// ValidateConfig 验证DNS配置
func ValidateConfig(config interface{}) error {
	if err := validate.Struct(config); err != nil {
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			var errorMsgs []string
			for _, e := range validationErrors {
				errorMsgs = append(errorMsgs, formatValidationError(e))
			}
			return fmt.Errorf("配置验证失败: %s", strings.Join(errorMsgs, "; "))
		}
		return fmt.Errorf("配置验证失败: %v", err)
	}
	return nil
}

// formatValidationError 格式化验证错误
func formatValidationError(e validator.FieldError) string {
	field := e.Field()
	tag := e.Tag()
	param := e.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("字段 '%s' 是必需的", field)
	case "email":
		return fmt.Sprintf("字段 '%s' 必须是有效的邮箱地址", field)
	case "min":
		return fmt.Sprintf("字段 '%s' 最小长度为 %s", field, param)
	case "max":
		return fmt.Sprintf("字段 '%s' 最大长度为 %s", field, param)
	default:
		return fmt.Sprintf("字段 '%s' 验证失败: %s", field, tag)
	}
}

// ValidateAlibabaConfig 验证阿里云DNS配置
func ValidateAlibabaConfig(config *AlibabaConfig) error {
	return ValidateConfig(config)
}

// ValidateAzureConfig 验证Azure DNS配置
func ValidateAzureConfig(config *AzureConfig) error {
	return ValidateConfig(config)
}

// ValidateCloudflareConfig 验证Cloudflare DNS配置
func ValidateCloudflareConfig(config *CloudflareConfig) error {
	// APIToken是必需的，或者APIKey+Email组合是必需的
	if config.APIToken == "" {
		if config.APIKey == "" || config.Email == "" {
			return fmt.Errorf("either APIToken or (APIKey + Email) must be provided")
		}
		// 验证邮箱格式
		if config.Email != "" {
			// 使用validator包进行邮箱验证
			if err := validate.Var(config.Email, "email"); err != nil {
				return fmt.Errorf("invalid email format: %s", config.Email)
			}
		}
	}

	return nil
}

// ValidateProviderConfig 根据Provider类型验证配置
func ValidateProviderConfig(providerType string, config interface{}) error {
	switch providerType {
	case "alibaba":
		if alibabaConfig, ok := config.(*AlibabaConfig); ok {
			return ValidateAlibabaConfig(alibabaConfig)
		}
		return fmt.Errorf("无效的阿里云配置类型")
	case "azure":
		if azureConfig, ok := config.(*AzureConfig); ok {
			return ValidateAzureConfig(azureConfig)
		}
		return fmt.Errorf("无效的Azure配置类型")
	case "cloudflare":
		if cloudflareConfig, ok := config.(*CloudflareConfig); ok {
			return ValidateCloudflareConfig(cloudflareConfig)
		}
		return fmt.Errorf("无效的Cloudflare配置类型")
	default:
		return fmt.Errorf("不支持的Provider类型: %s", providerType)
	}
}

// MaskSensitiveFields 掩码敏感字段
func MaskSensitiveFields(config interface{}) interface{} {
	val := reflect.ValueOf(config)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return config
	}

	// 创建配置的副本
	configCopy := reflect.New(val.Type()).Elem()
	configCopy.Set(val)

	// 掩码敏感字段
	maskSensitiveFieldsRecursive(configCopy)

	return configCopy.Interface()
}

// maskSensitiveFieldsRecursive 递归掩码敏感字段
func maskSensitiveFieldsRecursive(val reflect.Value) {
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return
		}
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 检查字段是否可访问
		if !field.IsValid() || !field.CanInterface() {
			continue
		}

		// 检查字段名是否包含敏感信息
		fieldName := strings.ToLower(fieldType.Name)
		if strings.Contains(fieldName, "key") ||
			strings.Contains(fieldName, "secret") ||
			strings.Contains(fieldName, "password") ||
			strings.Contains(fieldName, "token") {
			if field.Kind() == reflect.String && field.CanSet() {
				field.SetString("***")
			}
		} else if field.Kind() == reflect.Struct || field.Kind() == reflect.Ptr {
			maskSensitiveFieldsRecursive(field)
		}
	}
}
