package dns

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/response"
	pkgUtil "pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
	"strings"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"github.com/cloudflare/cloudflare-go"
)

// CloudflareConfig Cloudflare DNS配置
type CloudflareConfig struct {
	APIKey   string `json:"apiKey"`
	Email    string `json:"email"`
	APIToken string `json:"apiToken" validate:"required"`
}

// CloudflareDNSProvider Cloudflare DNS提供者
type CloudflareDNSProvider struct {
	client    *cloudflare.API
	config    *CloudflareConfig
	zoneCache map[string]string // 域名到ZoneID的缓存
	mutex     sync.RWMutex      // 缓存锁
}

// NewCloudflareDNSProvider 创建Cloudflare DNS提供者
func NewCloudflareDNSProvider(configStr string) (*CloudflareDNSProvider, error) {
	var config CloudflareConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		return nil, fmt.Errorf("failed to parse cloudflare config: %w", err)
	}

	// 验证配置
	if err := ValidateCloudflareConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid cloudflare config: %w", err)
	}

	var api *cloudflare.API
	var err error

	// 优先使用API Token，如果没有则使用API Key + Email
	if config.APIToken != "" {
		api, err = cloudflare.NewWithAPIToken(config.APIToken)
	} else if config.APIKey != "" && config.Email != "" {
		api, err = cloudflare.New(config.APIKey, config.Email)
	} else {
		return nil, fmt.Errorf("either APIToken or (APIKey + Email) must be provided")
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create cloudflare client: %w", err)
	}

	return &CloudflareDNSProvider{
		client:    api,
		config:    &config,
		zoneCache: make(map[string]string),
	}, nil
}

// getZoneID 根据域名获取ZoneID，优先使用缓存
func (p *CloudflareDNSProvider) getZoneID(ctx context.Context, domain string) (string, error) {
	// 首先检查缓存
	p.mutex.RLock()
	if zoneID, exists := p.zoneCache[domain]; exists {
		p.mutex.RUnlock()
		return zoneID, nil
	}
	p.mutex.RUnlock()

	// 缓存中没有，通过API查询
	log.Printf("Querying zone ID for domain: %s", domain)

	// 使用域名查询对应的Zone
	zones, err := p.client.ListZones(ctx, domain)
	if err != nil {
		return "", fmt.Errorf("failed to list zones for domain %s: %w", domain, err)
	}

	if len(zones) == 0 {
		return "", fmt.Errorf("no zone found for domain: %s", domain)
	}

	// 查找精确匹配的域名
	var zoneID string
	for _, zone := range zones {
		if zone.Name == domain {
			zoneID = zone.ID
			break
		}
	}

	if zoneID == "" {
		return "", fmt.Errorf("no exact match zone found for domain: %s", domain)
	}

	// 更新缓存
	p.mutex.Lock()
	p.zoneCache[domain] = zoneID
	p.mutex.Unlock()

	log.Printf("Found zone ID %s for domain %s", zoneID, domain)
	return zoneID, nil
}

// ListRecords 获取域名记录列表
func (p *CloudflareDNSProvider) ListRecords(ctx context.Context, domain string, filters *RecordFilters) (*response.PageModel[DNSRecordResponse], error) {
	// 获取ZoneID
	zoneID, err := p.getZoneID(ctx, domain)
	if err != nil {
		return nil, err
	}

	// 设置默认分页参数
	pageSize := 100
	page := 1

	// 应用传入的分页参数
	if filters != nil && filters.PageSize > 0 {
		pageSize = filters.PageSize
	}
	if filters != nil && filters.Page > 0 {
		page = filters.Page
	}

	// 构建资源容器
	rc := cloudflare.ResourceContainer{
		Type:       cloudflare.ZoneType,
		Identifier: zoneID,
	}

	// 构建查询参数
	listParams := cloudflare.ListDNSRecordsParams{
		ResultInfo: cloudflare.ResultInfo{
			Page:    page,
			PerPage: pageSize,
		},
	}

	// 应用过滤条件
	if filters != nil {
		if filters.Subdomain != "" {
			// 构建完整的记录名
			if filters.Subdomain == "@" {
				listParams.Name = domain
			} else {
				listParams.Name = filters.Subdomain + "." + domain
			}
		}
		if filters.RecordType != "" {
			listParams.Type = string(filters.RecordType)
		}
	}

	// 查询DNS记录
	records, resultInfo, err := p.client.ListDNSRecords(ctx, &rc, listParams)
	if err != nil {
		return nil, fmt.Errorf("failed to list cloudflare dns records: %w", err)
	}

	// 转换记录为响应格式
	var dnsRecords []DNSRecordResponse
	for _, record := range records {
		dnsRecord := p.convertCloudflareRecordToResponse(record, domain)

		// 如果有值过滤，进行客户端过滤
		if filters != nil && filters.Value != "" {
			if !strings.Contains(strings.ToLower(dnsRecord.Value), strings.ToLower(filters.Value)) {
				continue
			}
		}

		dnsRecords = append(dnsRecords, dnsRecord)
	}

	// 如果有值过滤，使用过滤后的总数，否则使用API返回的总数
	total := int64(resultInfo.Total)
	if filters != nil && filters.Value != "" {
		total = int64(len(dnsRecords))
	}

	return &response.PageModel[DNSRecordResponse]{
		List:     dnsRecords,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// GetRecord 获取单个域名记录
func (p *CloudflareDNSProvider) GetRecord(ctx context.Context, domain, subdomain string, recordType models.DomainRecordType) (*models.DomainRecord, error) {
	// 获取ZoneID
	zoneID, err := p.getZoneID(ctx, domain)
	if err != nil {
		return nil, err
	}

	// 构建记录名
	recordName := domain
	if subdomain != "" && subdomain != "@" {
		recordName = subdomain + "." + domain
	}

	// 构建资源容器
	rc := cloudflare.ResourceContainer{
		Type:       cloudflare.ZoneType,
		Identifier: zoneID,
	}

	// 构建查询参数
	listParams := cloudflare.ListDNSRecordsParams{
		Name: recordName,
		Type: string(recordType),
	}

	// 查询DNS记录
	records, _, err := p.client.ListDNSRecords(ctx, &rc, listParams)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloudflare dns record: %w", err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("record not found")
	}

	// 转换为模型
	return p.convertCloudflareRecordToModel(records[0], domain), nil
}

// CreateRecord 创建域名记录
func (p *CloudflareDNSProvider) CreateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 获取ZoneID
	zoneID, err := p.getZoneID(ctx, domain)
	if err != nil {
		return err
	}

	// 构建记录名
	recordName := domain
	if record.Subdomain != "" && record.Subdomain != "@" {
		recordName = record.Subdomain + "." + domain
	}

	// 构建资源容器
	rc := cloudflare.ResourceContainer{
		Type:       cloudflare.ZoneType,
		Identifier: zoneID,
	}

	// 构建DNS记录创建参数
	createParams := cloudflare.CreateDNSRecordParams{
		Type:    string(record.RecordType),
		Name:    recordName,
		Content: record.Value,
		TTL:     record.TTL,
	}

	// 设置优先级（仅对MX和SRV记录有效）
	if record.RecordType == models.RecordTypeMX || record.RecordType == models.RecordTypeSRV {
		// 确保priority在uint16范围内
		if record.Priority >= 0 && record.Priority <= 65535 {
			priority := uint16(record.Priority)
			createParams.Priority = &priority
		}
	}

	// 设置Proxy状态（仅对A、AAAA、CNAME记录有效）
	if record.Proxied != nil && (record.RecordType == models.RecordTypeA ||
		record.RecordType == models.RecordTypeAAAA || record.RecordType == models.RecordTypeCNAME) {
		createParams.Proxied = record.Proxied
	}

	// 创建记录
	_, err = p.client.CreateDNSRecord(ctx, &rc, createParams)
	if err != nil {
		return fmt.Errorf("failed to create cloudflare dns record: %w", err)
	}

	log.Printf("Created cloudflare dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// UpdateRecord 更新域名记录
func (p *CloudflareDNSProvider) UpdateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 首先获取要更新的记录
	prev := pkgUtil.StringToStruct[models.DomainRecord](record.PrevRecord)
	existingRecord, err := p.GetRecord(ctx, domain, prev.Subdomain, prev.RecordType)
	if err != nil {
		return fmt.Errorf("failed to find existing record for update: %w", err)
	}

	// 获取ZoneID
	zoneID, err := p.getZoneID(ctx, domain)
	if err != nil {
		return err
	}

	// 构建记录名
	recordName := domain
	if record.Subdomain != "" && record.Subdomain != "@" {
		recordName = record.Subdomain + "." + domain
	}

	// 构建资源容器
	rc := cloudflare.ResourceContainer{
		Type:       cloudflare.ZoneType,
		Identifier: zoneID,
	}

	// 构建更新参数
	updateParams := cloudflare.UpdateDNSRecordParams{
		ID:      existingRecord.UUID,
		Type:    string(record.RecordType),
		Name:    recordName,
		Content: record.Value,
		TTL:     record.TTL,
	}

	// 设置优先级（仅对MX和SRV记录有效）
	if record.RecordType == models.RecordTypeMX || record.RecordType == models.RecordTypeSRV {
		// 确保priority在uint16范围内
		if record.Priority >= 0 && record.Priority <= 65535 {
			priority := uint16(record.Priority)
			updateParams.Priority = &priority
		}
	}

	// 设置Proxy状态（仅对A、AAAA、CNAME记录有效）
	if record.Proxied != nil && (record.RecordType == models.RecordTypeA ||
		record.RecordType == models.RecordTypeAAAA || record.RecordType == models.RecordTypeCNAME) {
		updateParams.Proxied = record.Proxied
	}

	// 更新记录
	_, err = p.client.UpdateDNSRecord(ctx, &rc, updateParams)
	if err != nil {
		return fmt.Errorf("failed to update cloudflare dns record: %w", err)
	}

	log.Printf("Updated cloudflare dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// DeleteRecord 删除域名记录
func (p *CloudflareDNSProvider) DeleteRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 首先获取要删除的记录
	existingRecord, err := p.GetRecord(ctx, domain, record.Subdomain, record.RecordType)
	if err != nil {
		return fmt.Errorf("failed to find record for deletion: %w", err)
	}

	// 获取ZoneID
	zoneID, err := p.getZoneID(ctx, domain)
	if err != nil {
		return err
	}

	// 构建资源容器
	rc := cloudflare.ResourceContainer{
		Type:       cloudflare.ZoneType,
		Identifier: zoneID,
	}

	// 删除记录
	err = p.client.DeleteDNSRecord(ctx, &rc, existingRecord.UUID)
	if err != nil {
		return fmt.Errorf("failed to delete cloudflare dns record: %w", err)
	}

	log.Printf("Deleted cloudflare dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// ListDomains 获取账号下的域名列表
func (p *CloudflareDNSProvider) ListDomains(ctx context.Context) ([]DomainInfo, error) {
	zones, err := p.client.ListZones(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list cloudflare zones: %w", err)
	}

	domains := make([]DomainInfo, len(zones))
	for i, zone := range zones {
		domains[i] = DomainInfo{
			Domain: zone.Name,
			Status: zone.Status,
		}
	}

	log.Printf("Found %d domains from Cloudflare", len(domains))
	return domains, nil
}

// TestConnection 测试连接
func (p *CloudflareDNSProvider) TestConnection(ctx context.Context) error {
	// 尝试列出Zone来测试连接
	_, err := p.client.ListZones(ctx)
	if err != nil {
		return fmt.Errorf("failed to test cloudflare dns connection: %w", err)
	}

	log.Printf("Cloudflare DNS connection test successful")
	return nil
}

// ClearZoneCache 清除Zone缓存（用于测试或强制刷新）
func (p *CloudflareDNSProvider) ClearZoneCache() {
	p.mutex.Lock()
	p.zoneCache = make(map[string]string)
	p.mutex.Unlock()
	log.Printf("Cloudflare zone cache cleared")
}

// convertCloudflareRecordToModel 转换Cloudflare记录为模型
func (p *CloudflareDNSProvider) convertCloudflareRecordToModel(record cloudflare.DNSRecord, domain string) *models.DomainRecord {
	// 提取子域名
	subdomain := p.extractSubdomain(record.Name, domain)

	// 提取优先级
	priority := 0
	if record.Priority != nil {
		priority = int(*record.Priority)
	}

	// 提取Proxy状态
	var proxied *bool
	if record.Proxied != nil {
		proxied = record.Proxied
	}

	now := time.Now()
	return &models.DomainRecord{
		BaseModel: models.BaseModel{
			UUID:        record.ID,
			GmtCreate:   &now,
			GmtModified: &now,
		},
		Subdomain:  subdomain,
		RecordType: models.DomainRecordType(record.Type),
		Value:      record.Content,
		TTL:        record.TTL,
		Priority:   priority,
		Proxied:    proxied,
		Status:     models.RecordStatusActive,
	}
}

// convertCloudflareRecordToResponse 转换Cloudflare记录为响应格式
func (p *CloudflareDNSProvider) convertCloudflareRecordToResponse(record cloudflare.DNSRecord, domain string) DNSRecordResponse {
	// 提取子域名
	subdomain := p.extractSubdomain(record.Name, domain)

	// 提取优先级
	priority := 0
	if record.Priority != nil {
		priority = int(*record.Priority)
	}

	// 提取Proxy状态
	var proxied *bool
	if record.Proxied != nil {
		proxied = record.Proxied
	}

	// 转换创建时间
	createdAt := models.Time{}
	if !record.CreatedOn.IsZero() {
		createdAt = models.Time(record.CreatedOn)
	}

	return DNSRecordResponse{
		UUID:         record.ID,
		FullDomain:   record.Name,
		Domain:       domain,
		Subdomain:    subdomain,
		RecordType:   models.DomainRecordType(record.Type),
		Value:        record.Content,
		TTL:          record.TTL,
		Priority:     priority,
		Proxied:      proxied,
		ProviderName: "Cloudflare DNS",
		Status:       models.RecordStatusActive,
		Creator:      "",
		CreatedAt:    createdAt,
	}
}

// extractSubdomain 从完整域名中提取子域名
func (p *CloudflareDNSProvider) extractSubdomain(fullName, domain string) string {
	if fullName == domain {
		return "@"
	}

	// 安全检查避免越界
	domainSuffix := "." + domain
	if len(fullName) > len(domain) && strings.HasSuffix(fullName, domainSuffix) {
		return fullName[:len(fullName)-len(domainSuffix)]
	}

	return fullName
}
