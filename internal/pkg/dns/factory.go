package dns

import (
	"fmt"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
)

// ProviderFactory DNS提供者工厂
type ProviderFactory struct{}

// NewProviderFactory 创建DNS提供者工厂
func NewProviderFactory() *ProviderFactory {
	return &ProviderFactory{}
}

// CreateProvider 根据配置创建对应的DNS提供者
func (f *ProviderFactory) CreateProvider(config *ProviderConfig) (DNSProvider, error) {
	// 解密配置
	decryptedConfig, err := util.DefaultDecrypt(config.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt provider config: %w", err)
	}

	switch config.Type {
	case models.ProviderTypeAlibaba:
		return NewAlibabaDNSProvider(decryptedConfig)
	case models.ProviderTypeAzure:
		return NewAzureDNSProvider(decryptedConfig)
	case models.ProviderTypeCloudflare:
		return NewCloudflareDNSProvider(decryptedConfig)
	default:
		return nil, fmt.Errorf("unsupported DNS provider type: %s", config.Type)
	}
}

// CreateProviderFromModel 从DomainProvider模型创建DNS提供者
func (f *ProviderFactory) CreateProviderFromModel(provider *models.DomainProvider) (DNSProvider, error) {
	config := &ProviderConfig{
		Type:   provider.Type,
		Config: provider.Config,
	}
	return f.CreateProvider(config)
}
