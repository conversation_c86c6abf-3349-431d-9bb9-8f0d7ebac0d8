package dns

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/response"
	pkgUtil "pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
	"strconv"
	"strings"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/dns/armdns"
)

// AzureConfig Azure DNS配置
type AzureConfig struct {
	SubscriptionID string `json:"subscriptionId" validate:"required"`
	ResourceGroup  string `json:"resourceGroup" validate:"required"`
	ClientID       string `json:"clientId" validate:"required"`
	ClientSecret   string `json:"clientSecret" validate:"required"`
	TenantID       string `json:"tenantId" validate:"required"`
}

// AzureDNSProvider Azure DNS提供者
type AzureDNSProvider struct {
	recordSetsClient *armdns.RecordSetsClient
	zonesClient      *armdns.ZonesClient
	config           *AzureConfig
}

// NewAzureDNSProvider 创建Azure DNS提供者
func NewAzureDNSProvider(configStr string) (*AzureDNSProvider, error) {
	var config AzureConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		return nil, fmt.Errorf("failed to parse azure config: %w", err)
	}

	// 验证配置
	if err := ValidateAzureConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid azure config: %w", err)
	}

	// 创建认证凭据
	credential, err := azidentity.NewClientSecretCredential(
		config.TenantID,
		config.ClientID,
		config.ClientSecret,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create azure credential: %w", err)
	}

	// 创建客户端工厂
	clientFactory, err := armdns.NewClientFactory(config.SubscriptionID, credential, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create azure client factory: %w", err)
	}

	// 创建DNS记录集和区域客户端
	recordSetsClient := clientFactory.NewRecordSetsClient()
	zonesClient := clientFactory.NewZonesClient()

	return &AzureDNSProvider{
		recordSetsClient: recordSetsClient,
		zonesClient:      zonesClient,
		config:           &config,
	}, nil
}

// ListRecords 获取域名记录列表
func (p *AzureDNSProvider) ListRecords(ctx context.Context, domain string, filters *RecordFilters) (*response.PageModel[DNSRecordResponse], error) {
	// 获取所有记录集
	var recordSets []*armdns.RecordSet

	pager := p.recordSetsClient.NewListByDNSZonePager(
		p.config.ResourceGroup,
		strings.TrimSuffix(domain, "."),
		&armdns.RecordSetsClientListByDNSZoneOptions{
			Top:                 nil,
			Recordsetnamesuffix: nil,
		},
	)

	for pager.More() {
		page, err := pager.NextPage(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list azure dns records: %w", err)
		}
		recordSets = append(recordSets, page.Value...)
	}

	var records []DNSRecordResponse
	for _, recordSet := range recordSets {
		// 将一个RecordSet展开为多个DNSRecordResponse
		expandedRecords := p.expandAzureRecordSetToResponses(recordSet, domain)
		// 应用过滤条件
		for _, dnsRecord := range expandedRecords {
			if filters != nil {
				if filters.Subdomain != "" && recordSet.Name != nil && !strings.Contains(strings.ToLower(*recordSet.Name), strings.ToLower(filters.Subdomain)) {
					continue
				}
				if filters.RecordType != "" {
					recordType := p.extractRecordTypeFromAzureType(recordSet.Type)
					if recordType != filters.RecordType {
						continue
					}
				}
				if filters.Value != "" && !strings.Contains(strings.ToLower(dnsRecord.Value), strings.ToLower(filters.Value)) {
					continue
				}
			}
			records = append(records, dnsRecord)
		}
	}

	// 计算分页信息
	total := int64(len(records))
	pageSize := 100
	page := 1

	// 应用传入的分页参数
	if filters != nil {
		if filters.PageSize > 0 {
			pageSize = filters.PageSize
		}
		if filters.Page > 0 {
			page = filters.Page
		}
	}

	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= len(records) {
		records = []DNSRecordResponse{}
	} else if endIndex > len(records) {
		records = records[startIndex:]
	} else {
		records = records[startIndex:endIndex]
	}

	return &response.PageModel[DNSRecordResponse]{
		List:     records,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// GetRecord 获取单个域名记录
func (p *AzureDNSProvider) GetRecord(ctx context.Context, domain, subdomain string, recordType models.DomainRecordType) (*models.DomainRecord, error) {
	azureRecordType := p.convertToAzureRecordType(recordType)

	recordSet, err := p.recordSetsClient.Get(ctx, p.config.ResourceGroup, domain, subdomain, azureRecordType, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get azure dns record: %w", err)
	}

	return p.convertAzureRecordSetToModel(&recordSet.RecordSet, domain), nil
}

// CreateRecord 创建域名记录
func (p *AzureDNSProvider) CreateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	azureRecordType := p.convertToAzureRecordType(record.RecordType)

	// 先尝试获取现有的RecordSet
	existingRecordSet, err := p.recordSetsClient.Get(ctx, p.config.ResourceGroup, domain, record.Subdomain, azureRecordType, nil)
	if err != nil {
		// RecordSet不存在，创建新的
		recordSet, buildErr := p.buildRecordSet(record)
		if buildErr != nil {
			return buildErr
		}
		// 创建新的RecordSet
		_, createErr := p.recordSetsClient.CreateOrUpdate(ctx, p.config.ResourceGroup,
			domain, record.Subdomain, azureRecordType, recordSet, nil)
		if createErr != nil {
			return fmt.Errorf("failed to create azure dns record: %w", createErr)
		}
		log.Printf("Created new azure dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
		return nil
	}

	// RecordSet已存在，添加新记录到现有RecordSet
	updatedRecordSet, err := p.addRecordToRecordSet(&existingRecordSet.RecordSet, record)
	if err != nil {
		return err
	}

	// 使用现有记录的ETag进行更新
	options := &armdns.RecordSetsClientCreateOrUpdateOptions{}
	if existingRecordSet.Etag != nil {
		options.IfMatch = existingRecordSet.Etag
	}

	_, updateErr := p.recordSetsClient.CreateOrUpdate(ctx, p.config.ResourceGroup,
		domain, record.Subdomain, azureRecordType, updatedRecordSet, options)
	if updateErr != nil {
		return fmt.Errorf("failed to add record to existing azure dns recordset: %w", updateErr)
	}

	log.Printf("Added record to existing azure dns recordset: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// buildRecordSet 构建Azure DNS记录集
func (p *AzureDNSProvider) buildRecordSet(record *models.DomainRecord) (armdns.RecordSet, error) {
	recordSet := armdns.RecordSet{
		Properties: &armdns.RecordSetProperties{
			TTL: to.Ptr(int64(record.TTL)),
			// 明确清空所有记录类型字段，确保只有当前类型被设置
			ARecords:    nil,
			AaaaRecords: nil,
			CnameRecord: nil,
			MxRecords:   nil,
			TxtRecords:  nil,
			SrvRecords:  nil,
			NsRecords:   nil,
		},
	}

	// 根据记录类型设置具体的记录值
	switch record.RecordType {
	case models.RecordTypeA:
		recordSet.Properties.ARecords = []*armdns.ARecord{
			{IPv4Address: to.Ptr(record.Value)},
		}
	case models.RecordTypeAAAA:
		recordSet.Properties.AaaaRecords = []*armdns.AaaaRecord{
			{IPv6Address: to.Ptr(record.Value)},
		}
	case models.RecordTypeCNAME:
		recordSet.Properties.CnameRecord = &armdns.CnameRecord{
			Cname: to.Ptr(record.Value),
		}
	case models.RecordTypeMX:
		// 确保priority在int32范围内
		var priority int32
		if record.Priority >= -********** && record.Priority <= ********** {
			priority = int32(record.Priority) //nolint:gosec // 已经验证了范围
		}
		recordSet.Properties.MxRecords = []*armdns.MxRecord{
			{
				Preference: to.Ptr(priority),
				Exchange:   to.Ptr(record.Value),
			},
		}
	case models.RecordTypeTXT:
		recordSet.Properties.TxtRecords = []*armdns.TxtRecord{
			{Value: []*string{to.Ptr(record.Value)}},
		}
	case models.RecordTypeSRV:
		// SRV记录格式: weight port target (priority单独设置)
		parts := strings.Fields(record.Value)
		if len(parts) != 3 {
			return recordSet, fmt.Errorf("invalid SRV record format, expected 'weight port target': %s", record.Value)
		}
		weight, weightErr := strconv.Atoi(parts[0])
		if weightErr != nil {
			return recordSet, fmt.Errorf("invalid SRV weight: %s", parts[0])
		}
		port, portErr := strconv.Atoi(parts[1])
		if portErr != nil {
			return recordSet, fmt.Errorf("invalid SRV port: %s", parts[1])
		}
		target := parts[2]

		// 确保所有值在int32范围内
		var srvPriority, srvWeight, srvPort int32
		if record.Priority >= -********** && record.Priority <= ********** {
			srvPriority = int32(record.Priority) //nolint:gosec // 已经验证了范围
		}
		if weight >= -********** && weight <= ********** {
			srvWeight = int32(weight) //nolint:gosec // 已经验证了范围
		}
		if port >= -********** && port <= ********** {
			srvPort = int32(port) //nolint:gosec // 已经验证了范围
		}

		recordSet.Properties.SrvRecords = []*armdns.SrvRecord{
			{
				Priority: to.Ptr(srvPriority),
				Weight:   to.Ptr(srvWeight),
				Port:     to.Ptr(srvPort),
				Target:   to.Ptr(target),
			},
		}
	default:
		return recordSet, fmt.Errorf("unsupported record type: %s", record.RecordType)
	}

	return recordSet, nil
}

// addRecordToRecordSet 将新记录添加到现有RecordSet中
func (p *AzureDNSProvider) addRecordToRecordSet(existingRecordSet *armdns.RecordSet, record *models.DomainRecord) (armdns.RecordSet, error) {
	// 创建新的RecordSet副本
	newRecordSet := *existingRecordSet

	// 确保Properties不为nil
	if newRecordSet.Properties == nil {
		newRecordSet.Properties = &armdns.RecordSetProperties{}
	}

	// 更新TTL
	newRecordSet.Properties.TTL = to.Ptr(int64(record.TTL))

	// 根据记录类型添加记录
	switch record.RecordType {
	case models.RecordTypeA:
		// 添加A记录
		aRecord := &armdns.ARecord{IPv4Address: to.Ptr(record.Value)}
		newRecordSet.Properties.ARecords = append(newRecordSet.Properties.ARecords, aRecord)

	case models.RecordTypeAAAA:
		// 添加AAAA记录
		aaaaRecord := &armdns.AaaaRecord{IPv6Address: to.Ptr(record.Value)}
		newRecordSet.Properties.AaaaRecords = append(newRecordSet.Properties.AaaaRecords, aaaaRecord)

	case models.RecordTypeCNAME:
		// CNAME记录只能有一个，不能添加
		return newRecordSet, fmt.Errorf("CNAME record already exists, cannot add another CNAME record")

	case models.RecordTypeMX:
		// 添加MX记录
		var priority int32
		if record.Priority >= -********** && record.Priority <= ********** {
			priority = int32(record.Priority) //nolint:gosec // 已经验证了范围
		}
		mxRecord := &armdns.MxRecord{
			Preference: to.Ptr(priority),
			Exchange:   to.Ptr(record.Value),
		}
		newRecordSet.Properties.MxRecords = append(newRecordSet.Properties.MxRecords, mxRecord)

	case models.RecordTypeTXT:
		// 添加TXT记录
		txtRecord := &armdns.TxtRecord{Value: []*string{to.Ptr(record.Value)}}
		newRecordSet.Properties.TxtRecords = append(newRecordSet.Properties.TxtRecords, txtRecord)

	case models.RecordTypeSRV:
		// SRV记录格式: weight port target (priority单独设置)
		parts := strings.Fields(record.Value)
		if len(parts) != 3 {
			return newRecordSet, fmt.Errorf("invalid SRV record format, expected 'weight port target': %s", record.Value)
		}
		weight, weightErr := strconv.Atoi(parts[0])
		if weightErr != nil {
			return newRecordSet, fmt.Errorf("invalid SRV weight: %s", parts[0])
		}
		port, portErr := strconv.Atoi(parts[1])
		if portErr != nil {
			return newRecordSet, fmt.Errorf("invalid SRV port: %s", parts[1])
		}
		target := parts[2]

		// 确保所有值在int32范围内
		var srvPriority, srvWeight, srvPort int32
		if record.Priority >= -********** && record.Priority <= ********** {
			srvPriority = int32(record.Priority) //nolint:gosec // 已经验证了范围
		}
		if weight >= -********** && weight <= ********** {
			srvWeight = int32(weight) //nolint:gosec // 已经验证了范围
		}
		if port >= -********** && port <= ********** {
			srvPort = int32(port) //nolint:gosec // 已经验证了范围
		}

		srvRecord := &armdns.SrvRecord{
			Priority: to.Ptr(srvPriority),
			Weight:   to.Ptr(srvWeight),
			Port:     to.Ptr(srvPort),
			Target:   to.Ptr(target),
		}
		newRecordSet.Properties.SrvRecords = append(newRecordSet.Properties.SrvRecords, srvRecord)

	default:
		return newRecordSet, fmt.Errorf("unsupported record type: %s", record.RecordType)
	}

	return newRecordSet, nil
}

// updateRecordInRecordSet 在RecordSet中更新特定记录
func (p *AzureDNSProvider) updateRecordInRecordSet(existingRecordSet *armdns.RecordSet, prevRecord, newRecord *models.DomainRecord) (armdns.RecordSet, error) {
	// 先删除旧记录，再添加新记录
	recordSetAfterDelete, err := p.removeRecordFromRecordSet(existingRecordSet, prevRecord)
	if err != nil {
		return *existingRecordSet, err
	}
	// 添加新记录
	return p.addRecordToRecordSet(&recordSetAfterDelete, newRecord)
}

// DeleteRecordFromRecordSet 从RecordSet中删除特定记录
func (p *AzureDNSProvider) DeleteRecordFromRecordSet(ctx context.Context,
	domain, subdomain string, recordType models.DomainRecordType, value string, priority int) error {

	azureRecordType := p.convertToAzureRecordType(recordType)

	// 获取现有RecordSet
	existingRecordSet, err := p.recordSetsClient.Get(ctx, p.config.ResourceGroup, domain, subdomain, azureRecordType, nil)
	if err != nil {
		return fmt.Errorf("failed to get azure dns recordset: %w", err)
	}

	// 创建临时记录对象用于匹配
	tempRecord := &models.DomainRecord{
		RecordType: recordType,
		Value:      value,
		Priority:   priority,
	}

	// 从RecordSet中删除记录
	updatedRecordSet, err := p.removeRecordFromRecordSet(&existingRecordSet.RecordSet, tempRecord)
	if err != nil {
		return err
	}

	// 检查RecordSet是否为空
	if p.isRecordSetEmpty(&updatedRecordSet) {
		// 如果RecordSet为空，删除整个RecordSet
		_, deleteErr := p.recordSetsClient.Delete(ctx, p.config.ResourceGroup, domain, subdomain, azureRecordType, nil)
		if deleteErr != nil {
			return fmt.Errorf("failed to delete empty azure dns recordset: %w", deleteErr)
		}
		log.Printf("Deleted empty azure dns recordset: %s.%s %s", subdomain, domain, recordType)
	} else {
		// 否则更新RecordSet
		options := &armdns.RecordSetsClientCreateOrUpdateOptions{}
		if existingRecordSet.Etag != nil {
			options.IfMatch = existingRecordSet.Etag
		}

		_, updateErr := p.recordSetsClient.CreateOrUpdate(ctx, p.config.ResourceGroup, domain, subdomain, azureRecordType, updatedRecordSet, options)
		if updateErr != nil {
			return fmt.Errorf("failed to update azure dns recordset after removing record: %w", updateErr)
		}
		log.Printf("Removed record from azure dns recordset: %s.%s %s", subdomain, domain, recordType)
	}

	return nil
}

// removeRecordFromRecordSet 从RecordSet中移除指定记录
func (p *AzureDNSProvider) removeRecordFromRecordSet(existingRecordSet *armdns.RecordSet,
	record *models.DomainRecord) (armdns.RecordSet, error) {
	// 创建新的RecordSet副本
	newRecordSet := *existingRecordSet

	if newRecordSet.Properties == nil {
		return newRecordSet, fmt.Errorf("recordset properties is nil")
	}

	// 根据记录类型删除对应记录
	switch record.RecordType {
	case models.RecordTypeA:
		p.removeARecord(&newRecordSet, record)
	case models.RecordTypeAAAA:
		p.removeAAAARecord(&newRecordSet, record)
	case models.RecordTypeCNAME:
		p.removeCNAMERecord(&newRecordSet)
	case models.RecordTypeMX:
		p.removeMXRecord(&newRecordSet, record)
	case models.RecordTypeTXT:
		p.removeTXTRecord(&newRecordSet, record)
	case models.RecordTypeSRV:
		p.removeSRVRecord(&newRecordSet, record)
	case models.RecordTypeNS:
		p.removeNSRecord(&newRecordSet, record)
	default:
		return newRecordSet, fmt.Errorf("unsupported record type: %s", record.RecordType)
	}

	return newRecordSet, nil
}

// removeARecord 移除A记录
func (p *AzureDNSProvider) removeARecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newARecords []*armdns.ARecord
	for _, aRecord := range newRecordSet.Properties.ARecords {
		if aRecord.IPv4Address != nil && *aRecord.IPv4Address != record.Value {
			newARecords = append(newARecords, aRecord)
		}
	}
	newRecordSet.Properties.ARecords = newARecords
}

// removeAAAARecord 移除AAAA记录
func (p *AzureDNSProvider) removeAAAARecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newAaaaRecords []*armdns.AaaaRecord
	for _, aaaaRecord := range newRecordSet.Properties.AaaaRecords {
		if aaaaRecord.IPv6Address != nil && *aaaaRecord.IPv6Address != record.Value {
			newAaaaRecords = append(newAaaaRecords, aaaaRecord)
		}
	}
	newRecordSet.Properties.AaaaRecords = newAaaaRecords
}

// removeCNAMERecord 移除CNAME记录
func (p *AzureDNSProvider) removeCNAMERecord(newRecordSet *armdns.RecordSet) {
	// CNAME记录只有一个，直接清空
	newRecordSet.Properties.CnameRecord = nil
}

// removeMXRecord 移除MX记录
func (p *AzureDNSProvider) removeMXRecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newMxRecords []*armdns.MxRecord
	for _, mxRecord := range newRecordSet.Properties.MxRecords {
		if mxRecord.Exchange != nil && mxRecord.Preference != nil {
			if *mxRecord.Exchange != record.Value || int(*mxRecord.Preference) != record.Priority {
				newMxRecords = append(newMxRecords, mxRecord)
			}
		}
	}
	newRecordSet.Properties.MxRecords = newMxRecords
}

// removeTXTRecord 移除TXT记录
func (p *AzureDNSProvider) removeTXTRecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newTxtRecords []*armdns.TxtRecord
	for _, txtRecord := range newRecordSet.Properties.TxtRecords {
		if len(txtRecord.Value) > 0 && txtRecord.Value[0] != nil {
			if *txtRecord.Value[0] != record.Value {
				newTxtRecords = append(newTxtRecords, txtRecord)
			}
		}
	}
	newRecordSet.Properties.TxtRecords = newTxtRecords
}

// removeSRVRecord 移除SRV记录
func (p *AzureDNSProvider) removeSRVRecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newSrvRecords []*armdns.SrvRecord
	for _, srvRecord := range newRecordSet.Properties.SrvRecords {
		if srvRecord.Weight != nil && srvRecord.Port != nil && srvRecord.Target != nil && srvRecord.Priority != nil {
			srvValue := fmt.Sprintf("%d %d %s", *srvRecord.Weight, *srvRecord.Port, *srvRecord.Target)
			if srvValue != record.Value || int(*srvRecord.Priority) != record.Priority {
				newSrvRecords = append(newSrvRecords, srvRecord)
			}
		}
	}
	newRecordSet.Properties.SrvRecords = newSrvRecords
}

// removeNSRecord 移除NS记录
func (p *AzureDNSProvider) removeNSRecord(newRecordSet *armdns.RecordSet, record *models.DomainRecord) {
	var newNsRecords []*armdns.NsRecord
	for _, nsRecord := range newRecordSet.Properties.NsRecords {
		if nsRecord.Nsdname != nil && *nsRecord.Nsdname != record.Value {
			newNsRecords = append(newNsRecords, nsRecord)
		}
	}
	newRecordSet.Properties.NsRecords = newNsRecords
}

// isRecordSetEmpty 检查RecordSet是否为空
func (p *AzureDNSProvider) isRecordSetEmpty(recordSet *armdns.RecordSet) bool {
	if recordSet.Properties == nil {
		return true
	}

	props := recordSet.Properties
	return len(props.ARecords) == 0 &&
		len(props.AaaaRecords) == 0 &&
		props.CnameRecord == nil &&
		len(props.MxRecords) == 0 &&
		len(props.TxtRecords) == 0 &&
		len(props.SrvRecords) == 0 &&
		len(props.NsRecords) == 0
}

// UpdateRecord 更新域名记录
func (p *AzureDNSProvider) UpdateRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// 获取原记录信息
	prevRecord := pkgUtil.StringToStruct[models.DomainRecord](record.PrevRecord)
	prevAzureRecordType := p.convertToAzureRecordType(prevRecord.RecordType)
	newAzureRecordType := p.convertToAzureRecordType(record.RecordType)

	// 如果记录类型或子域名发生了变化，需要先删除原记录，再创建新记录
	if prevRecord.RecordType != record.RecordType || prevRecord.Subdomain != record.Subdomain {
		// 删除原记录（基于值匹配）
		err := p.DeleteRecordFromRecordSet(ctx, domain, prevRecord.Subdomain, prevRecord.RecordType, prevRecord.Value, prevRecord.Priority)
		if err != nil {
			log.Printf("Warning: failed to delete previous record: %v", err)
		}
		// 创建新记录
		return p.CreateRecord(ctx, domain, record)
	}

	// 同一类型和子域名的记录更新：先获取现有RecordSet
	existingRecordSet, err := p.recordSetsClient.Get(ctx, p.config.ResourceGroup, domain, prevRecord.Subdomain, prevAzureRecordType, nil)
	if err != nil {
		// 如果RecordSet不存在，则创建新记录
		return p.CreateRecord(ctx, domain, record)
	}

	// 从RecordSet中更新特定记录
	updatedRecordSet, err := p.updateRecordInRecordSet(&existingRecordSet.RecordSet, &prevRecord, record)
	if err != nil {
		return err
	}

	// 使用现有记录的ETag进行更新
	options := &armdns.RecordSetsClientCreateOrUpdateOptions{}
	if existingRecordSet.Etag != nil {
		options.IfMatch = existingRecordSet.Etag
	}

	_, updateErr := p.recordSetsClient.CreateOrUpdate(ctx, p.config.ResourceGroup, domain, record.Subdomain, newAzureRecordType, updatedRecordSet, options)
	if updateErr != nil {
		return fmt.Errorf("failed to update azure dns record: %w", updateErr)
	}

	log.Printf("Updated azure dns record: %s.%s %s", record.Subdomain, domain, record.RecordType)
	return nil
}

// DeleteRecord 删除域名记录
func (p *AzureDNSProvider) DeleteRecord(ctx context.Context, domain string, record *models.DomainRecord) error {
	// Azure DNS的智能删除逻辑：
	// 1. 如果提供了Value，进行精确删除（从RecordSet中删除特定记录）
	// 2. 如果没有提供Value，删除整个RecordSet

	if record.Value != "" {
		// 精确删除：从RecordSet中删除特定记录
		return p.DeleteRecordFromRecordSet(ctx, domain, record.Subdomain, record.RecordType, record.Value, record.Priority)
	} else {
		// 删除整个RecordSet（兼容旧逻辑）
		azureRecordType := p.convertToAzureRecordType(record.RecordType)

		_, err := p.recordSetsClient.Delete(ctx, p.config.ResourceGroup, domain, record.Subdomain, azureRecordType, nil)
		if err != nil {
			return fmt.Errorf("failed to delete azure dns recordset: %w", err)
		}

		log.Printf("Deleted azure dns recordset: %s.%s %s", record.Subdomain, domain, record.RecordType)
		return nil
	}
}

// ListDomains 获取账号下的域名列表
func (p *AzureDNSProvider) ListDomains(ctx context.Context) ([]DomainInfo, error) {
	pager := p.zonesClient.NewListPager(&armdns.ZonesClientListOptions{
		Top: to.Ptr(int32(100)),
	})

	var domains []DomainInfo
	for pager.More() {
		resp, err := pager.NextPage(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list azure zones: %w", err)
		}

		for _, zone := range resp.Value {
			if zone.Name != nil {
				domains = append(domains, DomainInfo{
					Domain: *zone.Name,
					Status: "active", // Azure的zone状态可能需要从Properties中获取，暂时设为active
				})
			}
		}
	}

	log.Printf("Found %d domains from Azure", len(domains))
	return domains, nil
}

// TestConnection 测试连接
func (p *AzureDNSProvider) TestConnection(ctx context.Context) error {
	// 测试获取DNS区域列表
	pager := p.zonesClient.NewListPager(&armdns.ZonesClientListOptions{
		Top: to.Ptr(int32(1)),
	})

	if !pager.More() {
		log.Printf("Azure DNS connection test successful, but no zones found")
		return nil
	}

	_, err := pager.NextPage(ctx)
	if err != nil {
		return fmt.Errorf("failed to test azure dns connection: %w", err)
	}

	log.Printf("Azure DNS connection test successful")
	return nil
}

// convertToAzureRecordType 转换记录类型为Azure格式
func (p *AzureDNSProvider) convertToAzureRecordType(recordType models.DomainRecordType) armdns.RecordType {
	switch recordType {
	case models.RecordTypeA:
		return armdns.RecordTypeA
	case models.RecordTypeAAAA:
		return armdns.RecordTypeAAAA
	case models.RecordTypeCNAME:
		return armdns.RecordTypeCNAME
	case models.RecordTypeMX:
		return armdns.RecordTypeMX
	case models.RecordTypeTXT:
		return armdns.RecordTypeTXT
	case models.RecordTypeSRV:
		return armdns.RecordTypeSRV
	case models.RecordTypeNS:
		return armdns.RecordTypeNS
	default:
		return armdns.RecordTypeA // 默认为A记录
	}
}

// extractRecordTypeFromAzureType 从Azure记录类型中提取记录类型
func (p *AzureDNSProvider) extractRecordTypeFromAzureType(azureType *string) models.DomainRecordType {
	if azureType == nil {
		return models.RecordTypeA
	}

	// Azure类型格式: Microsoft.Network/dnszones/A
	parts := strings.Split(*azureType, "/")
	if len(parts) >= 3 {
		recordType := parts[2]
		switch recordType {
		case "A":
			return models.RecordTypeA
		case "AAAA":
			return models.RecordTypeAAAA
		case "CNAME":
			return models.RecordTypeCNAME
		case "MX":
			return models.RecordTypeMX
		case "TXT":
			return models.RecordTypeTXT
		case "SRV":
			return models.RecordTypeSRV
		case "NS":
			return models.RecordTypeNS
		}
	}
	return models.RecordTypeA // 默认
}

// convertAzureRecordSetToModel 转换Azure记录集为模型
func (p *AzureDNSProvider) convertAzureRecordSetToModel(recordSet *armdns.RecordSet, _ string) *models.DomainRecord {
	recordType := p.extractRecordTypeFromAzureType(recordSet.Type)
	value := p.extractValueFromAzureRecordSet(recordSet)
	priority := p.extractPriorityFromAzureRecordSet(recordSet)

	now := time.Now()
	uuid := ""
	if recordSet.ID != nil {
		uuid = *recordSet.ID
	}

	ttl := 3600
	if recordSet.Properties != nil && recordSet.Properties.TTL != nil {
		ttl = int(*recordSet.Properties.TTL)
	}

	subdomain := ""
	if recordSet.Name != nil {
		subdomain = *recordSet.Name
	}

	return &models.DomainRecord{
		BaseModel: models.BaseModel{
			UUID:        uuid,
			GmtCreate:   &now,
			GmtModified: &now,
		},
		Subdomain:  subdomain,
		RecordType: recordType,
		Value:      value,
		TTL:        ttl,
		Priority:   priority,
		Status:     models.RecordStatusActive,
	}
}

// expandAzureRecordSetToResponses 将一个Azure RecordSet展开为多个DNSRecordResponse
func (p *AzureDNSProvider) expandAzureRecordSetToResponses(recordSet *armdns.RecordSet,
	domain string) []DNSRecordResponse {
	var records []DNSRecordResponse

	if recordSet.Properties == nil {
		return records
	}

	// 获取记录类型
	recordType := p.extractRecordTypeFromAzureType(recordSet.Type)

	// 根据记录类型展开多个记录
	switch recordType {
	case models.RecordTypeA:
		records = p.expandARecords(recordSet, domain)
	case models.RecordTypeAAAA:
		records = p.expandAAAARecords(recordSet, domain)
	case models.RecordTypeCNAME:
		records = p.expandCNAMERecord(recordSet, domain)
	case models.RecordTypeMX:
		records = p.expandMXRecords(recordSet, domain)
	case models.RecordTypeTXT:
		records = p.expandTXTRecords(recordSet, domain)
	case models.RecordTypeSRV:
		records = p.expandSRVRecords(recordSet, domain)
	case models.RecordTypeNS:
		records = p.expandNSRecords(recordSet, domain)
	}

	return records
}

// expandARecords 展开A记录
func (p *AzureDNSProvider) expandARecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, aRecord := range recordSet.Properties.ARecords {
		if aRecord.IPv4Address != nil {
			records = append(records, p.createSingleRecordResponse(recordSet, domain, *aRecord.IPv4Address, 0, i))
		}
	}
	return records
}

// expandAAAARecords 展开AAAA记录
func (p *AzureDNSProvider) expandAAAARecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, aaaaRecord := range recordSet.Properties.AaaaRecords {
		if aaaaRecord.IPv6Address != nil {
			records = append(records, p.createSingleRecordResponse(recordSet, domain, *aaaaRecord.IPv6Address, 0, i))
		}
	}
	return records
}

// expandCNAMERecord 展开CNAME记录
func (p *AzureDNSProvider) expandCNAMERecord(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	if recordSet.Properties.CnameRecord != nil && recordSet.Properties.CnameRecord.Cname != nil {
		records = append(records, p.createSingleRecordResponse(recordSet, domain, *recordSet.Properties.CnameRecord.Cname, 0, 0))
	}
	return records
}

// expandMXRecords 展开MX记录
func (p *AzureDNSProvider) expandMXRecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, mxRecord := range recordSet.Properties.MxRecords {
		if mxRecord.Exchange != nil {
			priority := 0
			if mxRecord.Preference != nil {
				priority = int(*mxRecord.Preference)
			}
			records = append(records, p.createSingleRecordResponse(recordSet, domain, *mxRecord.Exchange, priority, i))
		}
	}
	return records
}

// expandTXTRecords 展开TXT记录
func (p *AzureDNSProvider) expandTXTRecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, txtRecord := range recordSet.Properties.TxtRecords {
		if len(txtRecord.Value) > 0 && txtRecord.Value[0] != nil {
			records = append(records, p.createSingleRecordResponse(recordSet, domain, *txtRecord.Value[0], 0, i))
		}
	}
	return records
}

// expandSRVRecords 展开SRV记录
func (p *AzureDNSProvider) expandSRVRecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, srvRecord := range recordSet.Properties.SrvRecords {
		if srvRecord.Weight != nil && srvRecord.Port != nil && srvRecord.Target != nil {
			priority := 0
			if srvRecord.Priority != nil {
				priority = int(*srvRecord.Priority)
			}
			value := fmt.Sprintf("%d %d %s", *srvRecord.Weight, *srvRecord.Port, *srvRecord.Target)
			records = append(records, p.createSingleRecordResponse(recordSet, domain, value, priority, i))
		}
	}
	return records
}

// expandNSRecords 展开NS记录
func (p *AzureDNSProvider) expandNSRecords(recordSet *armdns.RecordSet, domain string) []DNSRecordResponse {
	var records []DNSRecordResponse
	for i, nsRecord := range recordSet.Properties.NsRecords {
		if nsRecord.Nsdname != nil {
			records = append(records, p.createSingleRecordResponse(recordSet, domain, *nsRecord.Nsdname, 0, i))
		}
	}
	return records
}

// createSingleRecordResponse 创建单个记录响应
func (p *AzureDNSProvider) createSingleRecordResponse(recordSet *armdns.RecordSet, domain, value string, priority, index int) DNSRecordResponse {
	recordType := p.extractRecordTypeFromAzureType(recordSet.Type)

	subdomain := ""
	if recordSet.Name != nil {
		subdomain = *recordSet.Name
	}

	// 构建完整域名
	fullDomain := domain
	if subdomain != "@" && subdomain != "" {
		fullDomain = subdomain + "." + domain
	}

	ttl := 3600
	if recordSet.Properties != nil && recordSet.Properties.TTL != nil {
		ttl = int(*recordSet.Properties.TTL)
	}

	// 为每个记录生成唯一的UUID（包含索引）
	uuid := ""
	if recordSet.ID != nil {
		if index > 0 {
			uuid = fmt.Sprintf("%s-%d", *recordSet.ID, index)
		} else {
			uuid = *recordSet.ID
		}
	}

	return DNSRecordResponse{
		UUID:         uuid,
		FullDomain:   fullDomain,
		Domain:       domain,
		Subdomain:    subdomain,
		RecordType:   recordType,
		Value:        value,
		TTL:          ttl,
		Priority:     priority,
		ProviderName: "Azure DNS",
		Status:       models.RecordStatusActive,
		Creator:      "",
		CreatedAt:    models.Time{},
	}
}

// extractValueFromAzureRecordSet 从Azure记录集中提取值
func (p *AzureDNSProvider) extractValueFromAzureRecordSet(recordSet *armdns.RecordSet) string {
	if recordSet.Properties == nil {
		return ""
	}

	props := recordSet.Properties

	if len(props.ARecords) > 0 && props.ARecords[0].IPv4Address != nil {
		return *props.ARecords[0].IPv4Address
	}
	if len(props.AaaaRecords) > 0 && props.AaaaRecords[0].IPv6Address != nil {
		return *props.AaaaRecords[0].IPv6Address
	}
	if props.CnameRecord != nil && props.CnameRecord.Cname != nil {
		return *props.CnameRecord.Cname
	}
	if len(props.MxRecords) > 0 && props.MxRecords[0].Exchange != nil {
		return *props.MxRecords[0].Exchange
	}
	if len(props.TxtRecords) > 0 {
		txtRecord := props.TxtRecords[0]
		if len(txtRecord.Value) > 0 && txtRecord.Value[0] != nil {
			return *txtRecord.Value[0]
		}
	}
	if len(props.SrvRecords) > 0 {
		srv := props.SrvRecords[0]
		if srv.Weight != nil && srv.Port != nil && srv.Target != nil {
			return fmt.Sprintf("%d %d %s", *srv.Weight, *srv.Port, *srv.Target)
		}
	}
	return ""
}

// extractPriorityFromAzureRecordSet 从Azure记录集中提取优先级
func (p *AzureDNSProvider) extractPriorityFromAzureRecordSet(recordSet *armdns.RecordSet) int {
	if recordSet.Properties == nil {
		return 0
	}

	props := recordSet.Properties

	if len(props.MxRecords) > 0 && props.MxRecords[0].Preference != nil {
		return int(*props.MxRecords[0].Preference)
	}
	if len(props.SrvRecords) > 0 && props.SrvRecords[0].Priority != nil {
		return int(*props.SrvRecords[0].Priority)
	}
	return 0
}
