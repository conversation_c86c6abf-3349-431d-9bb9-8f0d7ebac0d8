package request

import "time"

type PageRequest struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"pageSize,default=10"`
	Sort     string `form:"sort"`
	Order    string `form:"order"`
}

type InfoRequest struct {
	//统一替换id成uuid方式,防止id暴露
	UUID string `form:"uuid"`
}

type UpdateRequest struct {
	UUID        string     `json:"uuid" form:"uuid" binding:"required"`
	GmtModified *time.Time `json:"gmtModified" form:"gmtModified"`
}

type DeleteRequest struct {
	UUIDs []string `form:"uuids" binding:"required" json:"uuids"`
}

type ContentRequest struct {
	Identity string `form:"identity" binding:"required"`
}

type ParametersRequest struct {
	Name string `form:"name" binding:"required"`
}

type BranchesRequest struct {
	AppUUID string `form:"appUuid" binding:"required"`
	Name    string `form:"name"`
}

type AddRunnerRegisterTokenRequest struct {
	Token string `form:"token"`
}

type MetricRequest struct {
	StartTime  int64  `form:"startTime" binding:"required"`
	EndTime    int64  `form:"stopTime" binding:"required"`
	RunnerUUID string `form:"runnerUUID" binding:"required"`
}

type TasksRequest struct {
	RunnerUUID string `form:"runnerUUID"`
}
