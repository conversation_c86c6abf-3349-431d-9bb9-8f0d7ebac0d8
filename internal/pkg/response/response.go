package response

import (
	"errors"
	"fmt"
	"net/http"

	"gorm.io/gorm"

	"git.makeblock.com/makeblock-go/utils/v2/response"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

type PageModel[T any] struct {
	List     []T   `json:"list"`
	Page     int   `json:"page"`
	PageSize int   `json:"pageSize"`
	Total    int64 `json:"total"`
}

// APIModel api response model
type APIModel struct {
	Code    Code   `json:"code"`
	Data    any    `json:"data"`
	Message string `json:"message"`
}

// NewAPIModel new api response
func NewAPIModel(code Code, message string, data any) *APIModel {
	if data == nil {
		data = EmptyMapData()
	}
	res := &APIModel{
		Code:    code,
		Message: message,
		Data:    data,
	}
	return res
}

// Empty Data
var (
	EmptyMapData = func() map[string]any {
		return make(map[string]any)
	}

	EmptyArrayData = func() []any {
		return make([]any, 0)
	}
)

// JSON response APIModel
func JSON(c *gin.Context, code Code, message string, data any) {
	res := NewAPIModel(code, message, data)
	c.JSON(http.StatusOK, res)
}

// OK response OK
func OK(c *gin.Context, data any) {
	c.JSON(http.StatusOK, NewAPIModel(CodeOK, "success", data))
}

func OKWithMessage(c *gin.Context, message string, data any) {
	c.JSON(http.StatusOK, NewAPIModel(CodeOK, message, data))
}

// Err response error
func Err(c *gin.Context, err Error) {
	res := NewAPIModel(err.Code, err.Error(), EmptyMapData())
	c.JSON(http.StatusOK, res)
}

// ErrorResponse ResponseError
func ErrorResponse(c *gin.Context, err error) {
	if err != nil {
		ts := trace.NewTraceServiceFromGin(c)
		ts.Logger.Error(err.Error())
		// 先判断err是否是下面的Error类型，如果是则直接返回，
		var e Error
		if errors.As(err, &e) {
			Err(c, e)
			return
		}
		// 检查是否是 GORM 重复键错误
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			Err(c, RecordAlreadyExistError(""))
			return
		}
		// 检查是否是记录未找到错误
		if errors.Is(err, gorm.ErrRecordNotFound) {
			Err(c, RecordNotFoundError(""))
			return
		}
		// 否则设置为未知错误
		respErr := response.UnknownError(err.Error())
		errors.As(err, &respErr)
		response.Err(c, respErr)
	}
}

// Abort response abort
func Abort(c *gin.Context, err Error) {
	Err(c, err)
	c.Abort()
}

type Code int

// Error error
type Error struct {
	Code   Code
	Msg    string
	Detail string
}

func (e Error) Error() string {
	if len(e.Msg) < 1 {
		if len(e.Detail) < 1 {
			return "未知系统错误"
		}
		return e.Detail
	}
	if len(e.Detail) < 1 {
		return e.Msg
	}
	return fmt.Sprintf("%s %s", e.Msg, e.Detail)
}

type SelectOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}
