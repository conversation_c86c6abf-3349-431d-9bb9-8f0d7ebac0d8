package response

// Code
const (
	CodeOK Code = 0

	CodeUnknown      Code = 10000
	CodeDBConnect    Code = 10001
	CodeDBOperation  Code = 10002
	CodeRequestParam Code = 10003

	CodeTokenInvalid     Code = 20101
	CodePermissionDenied Code = 20102

	CodeVersionConflict Code = 90541 // 版本冲突错误码
	RecordAlreadyExist  Code = 90542 // 记录已经存在
	RecordNotFound      Code = 90543 // 记录未找到
)

// System error
var (
	UnknownError = func(detail string) Error {
		return Error{Code: CodeUnknown, Msg: "未知系统错误", Detail: detail}
	}

	DBConnectError = func(detail string) Error {
		return Error{Code: CodeDBConnect, Msg: "数据库连接错误", Detail: detail}
	}

	DBOperationError = func(detail string) Error {
		return Error{Code: CodeDBOperation, Msg: "数据库运行错误", Detail: detail}
	}

	RequestParamError = func(detail string) Error {
		return Error{Code: CodeRequestParam, Msg: "请求参数不正确", Detail: detail}
	}

	TokenInvalidError = func(detail string) Error {
		return Error{Code: CodeTokenInvalid, Msg: "utoken失效", Detail: detail}
	}

	PermissionDeniedError = func(detail string) Error {
		return Error{Code: CodePermissionDenied, Msg: "没有权限", Detail: detail}
	}

	VersionConflictError = func(detail string) Error {
		return Error{Code: CodeVersionConflict, Msg: "数据已被其他用户修改，请刷新后重试", Detail: detail}
	}

	RecordAlreadyExistError = func(detail string) Error {
		return Error{Code: RecordAlreadyExist, Msg: "记录已存在, 请检查后重试", Detail: detail}
	}

	RecordNotFoundError = func(detail string) Error {
		return Error{Code: RecordNotFound, Msg: "记录未找到", Detail: detail}
	}
)
