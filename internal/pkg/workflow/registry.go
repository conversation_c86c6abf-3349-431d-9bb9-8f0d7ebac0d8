package workflow

import (
	"context"
	"fmt"
	"maps"
	"pilot-api/pkg/models"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"
)

// StepHandlerRegistry 步骤处理器注册中心
type StepHandlerRegistry struct {
	mu       sync.RWMutex
	handlers map[models.StepType]StepHandler
}

// NewStepHandlerRegistry 创建步骤处理器注册中心
func NewStepHandlerRegistry() *StepHandlerRegistry {
	return &StepHandlerRegistry{
		handlers: make(map[models.StepType]StepHandler),
	}
}

// RegisterStepHandler 注册步骤处理器
func (r *StepHandlerRegistry) RegisterStepHandler(stepType models.StepType, handler StepHandler) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.handlers[stepType] = handler
}

// GetStepHandler 获取步骤处理器
func (r *StepHandlerRegistry) GetStepHandler(stepType models.StepType) (StepHandler, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	handler, exists := r.handlers[stepType]
	if !exists {
		log.Error("step handler not found", log.Any("stepType", stepType), log.Any("error", ErrStepHandlerNotFound))
		return nil, ErrStepHandlerNotFound
	}
	return handler, nil
}

// GetAllStepHandlers 获取所有步骤处理器
func (r *StepHandlerRegistry) GetAllStepHandlers() map[models.StepType]StepHandler {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[models.StepType]StepHandler)
	maps.Copy(result, r.handlers)
	return result
}

// StrategyFactory 工作流策略工厂接口
type StrategyFactory interface {
	// CreateStrategy 创建策略实例
	CreateStrategy() WorkflowStrategy
	// GetSupportedType 获取支持的工作流类型
	GetSupportedType() models.WorkflowType
	// GetMetadata 获取策略元数据
	GetMetadata() StrategyMetadata
}

// StrategyMetadata 策略元数据
type StrategyMetadata struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Version     string   `json:"version"`
	Author      string   `json:"author"`
	Tags        []string `json:"tags"`
	Features    []string `json:"features"`
}

// StrategyRegistry 策略注册中心
type StrategyRegistry struct {
	mu        sync.RWMutex
	factories map[models.WorkflowType]StrategyFactory
}

// NewStrategyRegistry 创建策略注册中心
func NewStrategyRegistry() *StrategyRegistry {
	return &StrategyRegistry{
		factories: make(map[models.WorkflowType]StrategyFactory),
	}
}

// RegisterStrategyFactory 注册策略工厂
func (r *StrategyRegistry) RegisterStrategyFactory(factory StrategyFactory) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	workflowType := factory.GetSupportedType()
	if _, exists := r.factories[workflowType]; exists {
		err := fmt.Errorf("strategy factory for type %s already registered", workflowType)
		log.Error("strategy factory already registered", log.Any("workflowType", workflowType), log.Any("error", err))
		return err
	}

	r.factories[workflowType] = factory
	return nil
}

// GetWorkflowStrategy 获取工作流策略
func (r *StrategyRegistry) GetWorkflowStrategy(workflowType models.WorkflowType) (WorkflowStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	factory, exists := r.factories[workflowType]
	if !exists {
		log.Error("workflow strategy not found", log.Any("workflowType", workflowType), log.Any("error", ErrStrategyNotFound))
		return nil, ErrStrategyNotFound
	}

	// 每次动态创建策略实例
	return factory.CreateStrategy(), nil
}

// GetAllWorkflowStrategies 获取所有工作流策略
func (r *StrategyRegistry) GetAllWorkflowStrategies() map[models.WorkflowType]WorkflowStrategy {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[models.WorkflowType]WorkflowStrategy)
	for workflowType, factory := range r.factories {
		result[workflowType] = factory.CreateStrategy()
	}
	return result
}

// GetStrategyFactory 获取策略工厂
func (r *StrategyRegistry) GetStrategyFactory(workflowType models.WorkflowType) (StrategyFactory, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	factory, exists := r.factories[workflowType]
	if !exists {
		log.Error("strategy factory not found", log.Any("workflowType", workflowType), log.Any("error", ErrStrategyNotFound))
		return nil, ErrStrategyNotFound
	}
	return factory, nil
}

// GetAllStrategyFactories 获取所有策略工厂
func (r *StrategyRegistry) GetAllStrategyFactories() map[models.WorkflowType]StrategyFactory {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[models.WorkflowType]StrategyFactory)
	maps.Copy(result, r.factories)
	return result
}

// ListAvailableStrategies 列出所有可用的策略信息
func (r *StrategyRegistry) ListAvailableStrategies() []StrategyInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var strategies []StrategyInfo
	for workflowType, factory := range r.factories {
		metadata := factory.GetMetadata()
		strategies = append(strategies, StrategyInfo{
			Type:     workflowType,
			Metadata: metadata,
		})
	}

	return strategies
}

// StrategyInfo 策略信息
type StrategyInfo struct {
	Type     models.WorkflowType `json:"type"`
	Metadata StrategyMetadata    `json:"metadata"`
}

// CreateStrategyInstance 动态创建策略实例
func (r *StrategyRegistry) CreateStrategyInstance(workflowType models.WorkflowType) (WorkflowStrategy, error) {
	factory, err := r.GetStrategyFactory(workflowType)
	if err != nil {
		return nil, err
	}

	return factory.CreateStrategy(), nil
}

// ValidateStrategySupport 验证策略是否支持指定功能
func (r *StrategyRegistry) ValidateStrategySupport(workflowType models.WorkflowType, requiredFeatures []string) error {
	factory, err := r.GetStrategyFactory(workflowType)
	if err != nil {
		return err
	}

	metadata := factory.GetMetadata()
	supportedFeatures := make(map[string]bool)
	for _, feature := range metadata.Features {
		supportedFeatures[feature] = true
	}

	for _, required := range requiredFeatures {
		if !supportedFeatures[required] {
			err := fmt.Errorf("strategy %s does not support feature: %s", workflowType, required)
			log.Error("strategy does not support required feature",
				log.Any("workflowType", workflowType),
				log.Any("requiredFeature", required),
				log.Any("error", err))
			return err
		}
	}

	return nil
}

// WorkflowEngineRegistry 工作流引擎注册中心
type WorkflowEngineRegistry struct {
	*StepHandlerRegistry
	*StrategyRegistry
}

// NewWorkflowEngineRegistry 创建工作流引擎注册中心
func NewWorkflowEngineRegistry() *WorkflowEngineRegistry {
	return &WorkflowEngineRegistry{
		StepHandlerRegistry: NewStepHandlerRegistry(),
		StrategyRegistry:    NewStrategyRegistry(),
	}
}

// GetStepHandlerRegistry 获取步骤处理器注册中心
// 注意：由于使用匿名嵌入，可以直接调用 r.GetStepHandler() 等方法
func (r *WorkflowEngineRegistry) GetStepHandlerRegistry() *StepHandlerRegistry {
	return r.StepHandlerRegistry
}

// GetStrategyRegistry 获取工作流策略注册中心
// 注意：由于使用匿名嵌入，可以直接调用 r.GetWorkflowStrategy() 等方法
func (r *WorkflowEngineRegistry) GetStrategyRegistry() *StrategyRegistry {
	return r.StrategyRegistry
}

// ExecuteStepWithRegistry 使用注册中心执行步骤
func (r *WorkflowEngineRegistry) ExecuteStepWithRegistry(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	handler, err := r.GetStepHandler(step.Type)
	if err != nil {
		log.Error("failed to get step handler",
			log.Any("stepType", step.Type),
			log.Any("stepName", step.Name),
			log.Any("error", err))
		return err
	}

	// 创建步骤执行记录
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusRunning,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
	}

	// 执行步骤
	if err := handler.Execute(ctx, stepExecution, step); err != nil {
		log.Error("failed to execute step",
			log.Any("stepName", step.Name),
			log.Any("stepType", step.Type),
			log.Any("executionId", execution.ID),
			log.Any("error", err))
		return err
	}

	return nil
}

// ExecuteWorkflowWithRegistry 使用注册中心执行工作流
func (r *WorkflowEngineRegistry) ExecuteWorkflowWithRegistry(
	ctx context.Context,
	execution *models.WorkflowExecution,
	workflow *models.Workflow,
	steps []models.WorkflowStep,
) error {
	strategy, err := r.GetWorkflowStrategy(workflow.Type)
	if err != nil {
		log.Error("failed to get workflow strategy",
			log.Any("workflowType", workflow.Type),
			log.Any("workflowName", workflow.Name),
			log.Any("workflowId", workflow.ID),
			log.Any("error", err))
		return err
	}

	if err := strategy.ExecuteWorkflow(ctx, execution, workflow, steps); err != nil {
		log.Error("failed to execute workflow",
			log.Any("workflowName", workflow.Name),
			log.Any("workflowType", workflow.Type),
			log.Any("workflowId", workflow.ID),
			log.Any("executionId", execution.ID),
			log.Any("error", err))
		return err
	}

	return nil
}

// RegisterAll 批量注册处理器和策略
func (r *WorkflowEngineRegistry) RegisterAll(
	stepHandlers map[models.StepType]StepHandler,
	strategyFactories []StrategyFactory,
) error {
	// 注册步骤处理器
	for stepType, handler := range stepHandlers {
		r.RegisterStepHandler(stepType, handler) // 直接调用嵌入类型的方法
	}

	// 注册策略工厂
	for _, factory := range strategyFactories {
		if err := r.RegisterStrategyFactory(factory); err != nil { // 直接调用嵌入类型的方法
			return err
		}
	}

	return nil
}

// GetCapabilities 获取注册中心的能力信息
func (r *WorkflowEngineRegistry) GetCapabilities() map[string]interface{} {
	return map[string]interface{}{
		"supportedStepTypes":     r.GetAllStepHandlers(),       // 直接调用嵌入类型的方法
		"supportedWorkflowTypes": r.GetAllWorkflowStrategies(), // 直接调用嵌入类型的方法
		"availableStrategies":    r.ListAvailableStrategies(),  // 直接调用嵌入类型的方法
	}
}

// ValidateWorkflow 验证工作流配置是否有效
func (r *WorkflowEngineRegistry) ValidateWorkflow(workflow *models.Workflow, steps []models.WorkflowStep) error {
	// 验证工作流类型是否支持
	if _, err := r.GetWorkflowStrategy(workflow.Type); err != nil {
		log.Error("unsupported workflow type",
			log.Any("workflowType", workflow.Type),
			log.Any("workflowName", workflow.Name),
			log.Any("error", err))
		return err
	}

	// 验证所有步骤类型是否支持
	for _, step := range steps {
		if _, err := r.GetStepHandler(step.Type); err != nil {
			log.Error("unsupported step type",
				log.Any("stepType", step.Type),
				log.Any("stepName", step.Name),
				log.Any("workflowName", workflow.Name),
				log.Any("error", err))
			return err
		}
	}

	return nil
}

// Execute 统一的执行入口 - 简化调用方式
func (r *WorkflowEngineRegistry) Execute(
	ctx context.Context,
	workflow *models.Workflow,
	steps []models.WorkflowStep,
	executorID, executorName string,
) (*models.WorkflowExecution, error) {
	// 首先验证工作流配置
	if err := r.ValidateWorkflow(workflow, steps); err != nil {
		log.Error("workflow validation failed",
			log.Any("workflowName", workflow.Name),
			log.Any("workflowType", workflow.Type),
			log.Any("error", err))
		return nil, err
	}

	// 创建执行记录
	execution := &models.WorkflowExecution{
		WorkflowID:   workflow.ID,
		Status:       models.WorkflowExecutionStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   executorID,
		ExecutorName: executorName,
		TriggerType:  models.TriggerTypeManual,
	}

	// 开始执行
	execution.Start()

	// 执行工作流
	if err := r.ExecuteWorkflowWithRegistry(ctx, execution, workflow, steps); err != nil {
		execution.Fail(err.Error())
		return execution, err
	}

	execution.Complete()
	return execution, nil
}

// IsStepTypeSupported 检查步骤类型是否支持
func (r *WorkflowEngineRegistry) IsStepTypeSupported(stepType models.StepType) bool {
	_, err := r.GetStepHandler(stepType)
	return err == nil
}

// IsWorkflowTypeSupported 检查工作流类型是否支持
func (r *WorkflowEngineRegistry) IsWorkflowTypeSupported(workflowType models.WorkflowType) bool {
	_, err := r.GetWorkflowStrategy(workflowType)
	return err == nil
}

// GetSupportedTypes 获取所有支持的类型
func (r *WorkflowEngineRegistry) GetSupportedTypes() ([]models.StepType, []models.WorkflowType) {
	stepHandlers := r.GetAllStepHandlers()
	stepTypes := make([]models.StepType, 0, len(stepHandlers))
	for stepType := range stepHandlers {
		stepTypes = append(stepTypes, stepType)
	}

	strategies := r.GetAllWorkflowStrategies()
	workflowTypes := make([]models.WorkflowType, 0, len(strategies))
	for workflowType := range strategies {
		workflowTypes = append(workflowTypes, workflowType)
	}

	return stepTypes, workflowTypes
}
