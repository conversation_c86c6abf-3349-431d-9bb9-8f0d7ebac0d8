package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/types"
	"pilot-api/pkg/models"
	"sort"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"gorm.io/gorm"
)

// WorkflowEngine 工作流引擎 - 纯抽象引擎
type WorkflowEngine struct {
	*trace.Service
	db       *gorm.DB
	registry *WorkflowEngineRegistry
}

// NewWorkflowEngine 创建工作流引擎
func NewWorkflowEngine(ts *trace.Service, db *gorm.DB) *WorkflowEngine {
	return &WorkflowEngine{
		Service:  trace.IfNil(ts),
		db:       db,
		registry: NewWorkflowEngineRegistry(),
	}
}

// NewWorkflowEngineWithRegistry 使用指定的注册中心创建工作流引擎
func NewWorkflowEngineWithRegistry(ts *trace.Service, db *gorm.DB, registry *WorkflowEngineRegistry) *WorkflowEngine {
	return &WorkflowEngine{
		Service:  trace.IfNil(ts),
		db:       db,
		registry: registry,
	}
}

// GetRegistry 获取注册中心
func (e *WorkflowEngine) GetRegistry() *WorkflowEngineRegistry {
	return e.registry
}

// GetDB 获取数据库连接
func (e *WorkflowEngine) GetDB() *gorm.DB {
	return e.db
}

// ExecuteWorkflow 执行工作流
func (e *WorkflowEngine) ExecuteWorkflow(ctx context.Context, workflowID int64, executorID, executorName string) (*models.WorkflowExecution, error) {
	var workflow models.Workflow
	if err := e.db.WithContext(ctx).Preload("Steps").First(&workflow, workflowID).Error; err != nil {
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}

	if !workflow.CanExecute() {
		return nil, fmt.Errorf("workflow cannot be executed, current status: %s", workflow.Status)
	}

	execution := &models.WorkflowExecution{
		WorkflowID:   workflowID,
		Status:       models.WorkflowExecutionStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   executorID,
		ExecutorName: executorName,
		TriggerType:  models.TriggerTypeManual,
		Context:      e.buildExecutionContext(workflow, executorID, executorName),
	}

	if err := e.db.WithContext(ctx).Create(execution).Error; err != nil {
		return nil, fmt.Errorf("failed to create execution: %w", err)
	}

	// 工作流模板状态不需要修改，只有执行实例有状态

	strategy, err := e.registry.GetWorkflowStrategy(workflow.Type)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow strategy: %w", err)
	}

	go e.executeWorkflowAsync(ctx, execution, &workflow, strategy)

	return execution, nil
}

// executeWorkflowAsync 异步执行工作流
func (e *WorkflowEngine) executeWorkflowAsync(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, strategy WorkflowStrategy) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorE("workflow execution panic", fmt.Errorf("%v", r))
			e.updateExecutionStatus(ctx, execution, models.WorkflowExecutionStatusFailed, fmt.Sprintf("panic: %v", r))
		}
	}()

	// 开始执行
	execution.Start()
	if err := e.db.WithContext(ctx).Save(execution).Error; err != nil {
		log.ErrorE("failed to update execution status to running", err)
	}

	// 通过WorkflowID查询Steps
	var steps []models.WorkflowStep
	if err := e.db.WithContext(ctx).Where("workflow_id = ? AND deleted = ?", workflow.ID, false).Find(&steps).Error; err != nil {
		log.ErrorE(fmt.Sprintf("failed to get workflow steps: workflowId=%d", workflow.ID), err)
		e.updateExecutionStatus(ctx, execution, models.WorkflowExecutionStatusFailed, fmt.Sprintf("failed to get workflow steps: %v", err))
		return
	}

	// 使用策略执行工作流
	if err := strategy.ExecuteWorkflow(ctx, execution, workflow, steps); err != nil {
		log.ErrorE(fmt.Sprintf("workflow execution failed: workflowId=%d", workflow.ID), err)
		e.updateExecutionStatus(ctx, execution, models.WorkflowExecutionStatusFailed, err.Error())
		return
	}

	// 所有步骤执行完成
	e.updateExecutionStatus(ctx, execution, models.WorkflowExecutionStatusCompleted, "workflow completed successfully")
}

// ExecuteStep 执行步骤
func (e *WorkflowEngine) ExecuteStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	handler, err := e.registry.GetStepHandler(step.Type)
	if err != nil {
		return err
	}

	// 创建步骤执行记录
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
		RetryCount:   0,
	}

	// 持久化步骤执行记录到数据库
	if createErr := e.db.WithContext(ctx).Create(stepExecution).Error; createErr != nil {
		return fmt.Errorf("failed to create step execution record: %w", createErr)
	}

	// 开始执行步骤
	stepExecution.Status = models.StepStatusRunning
	stepExecution.StartTime = time.Now()
	if saveErr := e.db.WithContext(ctx).Save(stepExecution).Error; saveErr != nil {
		log.ErrorE("failed to update step execution status to running", saveErr)
	}

	// 执行步骤
	err = handler.Execute(ctx, stepExecution, step)

	// 更新步骤执行状态
	if err != nil {
		stepExecution.Status = models.StepStatusFailed
		stepExecution.ErrorMessage = err.Error()
		now := time.Now()
		stepExecution.EndTime = &now
	} else {
		stepExecution.Status = models.StepStatusCompleted
		now := time.Now()
		stepExecution.EndTime = &now
	}

	// 保存最终状态
	if saveErr := e.db.WithContext(ctx).Save(stepExecution).Error; saveErr != nil {
		log.ErrorE("failed to save step execution final status", saveErr)
		// 如果原来没有错误，但保存状态失败，返回保存错误
		if err == nil {
			err = fmt.Errorf("failed to save step execution status: %w", saveErr)
		}
	}

	return err
}

// ExecuteStepWithRetry 执行步骤并支持重试机制
func (e *WorkflowEngine) ExecuteStepWithRetry(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	maxRetries := step.RetryCount
	if maxRetries <= 0 {
		maxRetries = 0 // 不重试
	}

	var lastErr error
	for attempt := 0; attempt <= maxRetries; attempt++ {
		// 如果不是第一次尝试，需要查找现有的步骤执行记录
		var stepExecution *models.StepExecution
		if attempt > 0 {
			var existingExecution models.StepExecution
			err := e.db.WithContext(ctx).Where("execution_id = ? AND step_id = ?",
				execution.ID, step.ID).First(&existingExecution).Error
			if err != nil {
				return fmt.Errorf("failed to find existing step execution for retry: %w", err)
			}
			stepExecution = &existingExecution
			stepExecution.RetryCount = attempt
		}

		// 执行步骤
		if attempt == 0 {
			// 第一次执行，使用正常的ExecuteStep
			err := e.ExecuteStep(ctx, execution, step)
			if err == nil {
				return nil // 成功，无需重试
			}
			lastErr = err
		} else {
			// 重试执行
			handler, err := e.registry.GetStepHandler(step.Type)
			if err != nil {
				return err
			}

			// 更新重试状态
			stepExecution.Status = models.StepStatusRunning
			stepExecution.StartTime = time.Now()
			stepExecution.EndTime = nil
			stepExecution.ErrorMessage = ""

			if updateErr := e.db.WithContext(ctx).Save(stepExecution).Error; updateErr != nil {
				log.ErrorE("failed to update step execution for retry", updateErr)
			}

			// 执行步骤
			err = handler.Execute(ctx, stepExecution, step)

			// 更新执行结果
			if err != nil {
				stepExecution.Status = models.StepStatusFailed
				stepExecution.ErrorMessage = err.Error()
				now := time.Now()
				stepExecution.EndTime = &now
				lastErr = err
			} else {
				stepExecution.Status = models.StepStatusCompleted
				now := time.Now()
				stepExecution.EndTime = &now
				lastErr = nil
			}

			// 保存状态
			if saveErr := e.db.WithContext(ctx).Save(stepExecution).Error; saveErr != nil {
				log.ErrorE("failed to save step execution retry status", saveErr)
			}

			if err == nil {
				return nil // 重试成功
			}
		}

		// 如果还有重试机会，等待一段时间后继续
		if attempt < maxRetries {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(time.Duration(attempt+1) * time.Second):
				// 指数退避：1s, 2s, 3s...
			}
		}
	}

	return fmt.Errorf("step execution failed after %d retries: %w", maxRetries, lastErr)
}

// skipStep 跳过步骤执行
func (e *WorkflowEngine) skipStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) error {
	// 创建步骤执行记录，标记为跳过
	stepExecution := &models.StepExecution{
		ExecutionID:  execution.ID,
		StepID:       step.ID,
		Status:       models.StepStatusSkipped,
		StartTime:    time.Now(),
		ExecutorID:   execution.ExecutorID,
		ExecutorName: execution.ExecutorName,
		ServiceName:  step.ServiceName,
		RetryCount:   0,
	}

	now := time.Now()
	stepExecution.EndTime = &now

	// 保存跳过记录
	if err := e.db.WithContext(ctx).Create(stepExecution).Error; err != nil {
		return fmt.Errorf("failed to create skipped step execution record: %w", err)
	}

	return nil
}

// ExecuteSteps 批量执行步骤 - 通用方法
func (e *WorkflowEngine) ExecuteSteps(ctx context.Context, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	for _, step := range steps {
		// 检查依赖
		if !e.CheckDependencies(ctx, execution, &step) {
			return fmt.Errorf("step %s dependencies not satisfied", step.Name)
		}

		// 检查执行条件
		if !e.CheckExecutionCondition(ctx, execution, &step) {
			// 条件不满足，跳过步骤
			if err := e.skipStep(ctx, execution, &step); err != nil {
				log.ErrorE(fmt.Sprintf("failed to skip step %s", step.Name), err)
			}
			continue
		}

		// 使用支持重试的执行方法
		if err := e.ExecuteStepWithRetry(ctx, execution, &step); err != nil {
			return fmt.Errorf("step %s execution failed: %w", step.Name, err)
		}
	}
	return nil
}

// ExecuteStepsInParallel 并行执行步骤 - 通用方法
func (e *WorkflowEngine) ExecuteStepsInParallel(ctx context.Context, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	// 使用WaitGroup来等待所有goroutine完成
	var wg sync.WaitGroup
	errChan := make(chan error, len(steps))

	for _, step := range steps {
		// 检查依赖
		if !e.CheckDependencies(ctx, execution, &step) {
			errChan <- fmt.Errorf("step %s dependencies not satisfied", step.Name)
			continue
		}

		// 检查执行条件
		if !e.CheckExecutionCondition(ctx, execution, &step) {
			// 条件不满足，跳过步骤
			if err := e.skipStep(ctx, execution, &step); err != nil {
				log.ErrorE(fmt.Sprintf("failed to skip step %s", step.Name), err)
			}
			continue
		}

		wg.Add(1)
		go func(s models.WorkflowStep) {
			defer wg.Done()
			if err := e.ExecuteStepWithRetry(ctx, execution, &s); err != nil {
				errChan <- fmt.Errorf("step %s execution failed: %w", s.Name, err)
			}
		}(step)
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// 收集所有错误
	var errors []error
	for err := range errChan {
		if err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("parallel execution failed with %d errors: %v", len(errors), errors)
	}

	return nil
}

// CheckDependencies 检查依赖 - 通用方法
func (e *WorkflowEngine) CheckDependencies(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) bool {
	if step.DependsOn == "" {
		return true
	}

	var dependencies []int64
	if err := json.Unmarshal([]byte(step.DependsOn), &dependencies); err != nil {
		log.ErrorE("failed to parse step dependencies", err)
		return false
	}

	for _, depStepID := range dependencies {
		var stepExecution models.StepExecution
		err := e.db.WithContext(ctx).Where("execution_id = ? AND step_id = ?", execution.ID, depStepID).First(&stepExecution).Error
		if err != nil {
			log.ErrorE(fmt.Sprintf("failed to get dependency step execution: stepId=%d", depStepID), err)
			return false
		}

		if stepExecution.Status != models.StepStatusCompleted {
			return false
		}
	}

	return true
}

// CheckExecutionCondition 检查执行条件 - 通用方法
func (e *WorkflowEngine) CheckExecutionCondition(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStep) bool {
	if step.Condition == "" {
		return true
	}

	var condition map[string]any
	if err := json.Unmarshal([]byte(step.Condition), &condition); err != nil {
		log.ErrorE("failed to parse step condition", err)
		return false
	}

	return e.evaluateCondition(ctx, execution)
}

// evaluateCondition 评估条件 - 通用方法
func (e *WorkflowEngine) evaluateCondition(_ context.Context, _ *models.WorkflowExecution) bool {
	// 简单的条件评估实现
	// 实际实现中可以使用更复杂的条件评估引擎
	return true
}

// SortStepsByOrder 按顺序排序步骤 - 通用方法
func (e *WorkflowEngine) SortStepsByOrder(steps []models.WorkflowStep) []models.WorkflowStep {
	sort.Slice(steps, func(i, j int) bool {
		if steps[i].ServiceOrder != steps[j].ServiceOrder {
			return steps[i].ServiceOrder < steps[j].ServiceOrder
		}
		return steps[i].Order < steps[j].Order
	})
	return steps
}

// GroupStepsByService 按服务分组步骤 - 通用方法
func (e *WorkflowEngine) GroupStepsByService(steps []models.WorkflowStep) map[string][]models.WorkflowStep {
	serviceSteps := make(map[string][]models.WorkflowStep)
	for _, step := range steps {
		serviceName := step.ServiceName
		if serviceName == "" {
			serviceName = "default"
		}
		serviceSteps[serviceName] = append(serviceSteps[serviceName], step)
	}
	return serviceSteps
}

// GroupStepsByParallelGroup 按并行组分组步骤 - 通用方法
func (e *WorkflowEngine) GroupStepsByParallelGroup(steps []models.WorkflowStep) map[string][]models.WorkflowStep {
	parallelGroups := make(map[string][]models.WorkflowStep)
	for _, step := range steps {
		groupName := step.ParallelGroup
		parallelGroups[groupName] = append(parallelGroups[groupName], step)
	}
	return parallelGroups
}

// PauseWorkflow 暂停工作流
func (e *WorkflowEngine) PauseWorkflow(ctx context.Context, executionID int64) error {
	var execution models.WorkflowExecution
	if err := e.db.WithContext(ctx).First(&execution, executionID).Error; err != nil {
		return fmt.Errorf("failed to get execution: %w", err)
	}

	if !execution.IsRunning() {
		return fmt.Errorf("workflow is not running, current status: %s", execution.Status)
	}

	execution.Pause()
	if err := e.db.WithContext(ctx).Save(&execution).Error; err != nil {
		return fmt.Errorf("failed to update execution status: %w", err)
	}

	return nil
}

// ResumeWorkflow 恢复工作流
func (e *WorkflowEngine) ResumeWorkflow(ctx context.Context, executionID int64) error {
	var execution models.WorkflowExecution
	if err := e.db.WithContext(ctx).First(&execution, executionID).Error; err != nil {
		return fmt.Errorf("failed to get execution: %w", err)
	}

	if execution.Status != models.WorkflowExecutionStatusPaused {
		return fmt.Errorf("workflow is not paused, current status: %s", execution.Status)
	}

	execution.Resume()
	if err := e.db.WithContext(ctx).Save(&execution).Error; err != nil {
		return fmt.Errorf("failed to update execution status: %w", err)
	}

	// 重新开始执行
	var workflow models.Workflow
	if err := e.db.WithContext(ctx).First(&workflow, execution.WorkflowID).Error; err != nil {
		return fmt.Errorf("failed to get workflow: %w", err)
	}

	// 获取工作流策略并执行
	strategy, err := e.registry.GetWorkflowStrategy(workflow.Type)
	if err != nil {
		return fmt.Errorf("failed to get workflow strategy: %w", err)
	}

	go e.executeWorkflowAsync(ctx, &execution, &workflow, strategy)

	return nil
}

// CancelWorkflow 取消工作流
func (e *WorkflowEngine) CancelWorkflow(ctx context.Context, executionID int64) error {
	var execution models.WorkflowExecution
	if err := e.db.WithContext(ctx).First(&execution, executionID).Error; err != nil {
		return fmt.Errorf("failed to get execution: %w", err)
	}

	if execution.IsFinished() {
		return fmt.Errorf("workflow cannot be cancelled, current status: %s", execution.Status)
	}

	// 取消正在运行的步骤
	var stepExecutions []models.StepExecution
	if err := e.db.WithContext(ctx).Where("execution_id = ? AND status = ?", executionID, models.StepStatusRunning).Find(&stepExecutions).Error; err != nil {
		return fmt.Errorf("failed to get running step executions: %w", err)
	}

	for _, stepExecution := range stepExecutions {
		var step models.WorkflowStep
		if err := e.db.WithContext(ctx).First(&step, stepExecution.StepID).Error; err != nil {
			continue
		}

		handler, err := e.registry.GetStepHandler(step.Type)
		if err != nil {
			continue
		}

		if err := handler.Cancel(ctx, &stepExecution); err != nil {
			log.ErrorE(fmt.Sprintf("failed to cancel step %d", stepExecution.StepID), err)
		}
	}

	// 更新执行状态
	execution.Cancel()
	if err := e.db.WithContext(ctx).Save(&execution).Error; err != nil {
		return fmt.Errorf("failed to update execution status: %w", err)
	}

	return nil
}

// GetExecutionStatus 获取工作流执行状态
func (e *WorkflowEngine) GetExecutionStatus(ctx context.Context, executionID int64) (*models.WorkflowExecution, error) {
	var execution models.WorkflowExecution
	if err := e.db.WithContext(ctx).First(&execution, executionID).Error; err != nil {
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}

	return &execution, nil
}

// buildExecutionContext 构建执行上下文
func (e *WorkflowEngine) buildExecutionContext(workflow models.Workflow, executorID, executorName string) string {
	context := types.ExecutionContext{
		WorkflowID:   workflow.ID,
		Namespace:    workflow.Namespace,
		ClusterID:    workflow.ClusterID,
		ExecutorID:   executorID,
		ExecutorName: executorName,
		Variables:    make(map[string]any),
	}

	// 解析服务配置
	if workflow.Services != "" {
		var services []models.WorkflowServiceConfig
		if err := json.Unmarshal([]byte(workflow.Services), &services); err == nil {
			context.Services = services
		}
	}

	// 解析全局配置
	if workflow.GlobalConfig != "" {
		var globalConfig models.WorkflowGlobalConfig
		if err := json.Unmarshal([]byte(workflow.GlobalConfig), &globalConfig); err == nil {
			context.GlobalConfig = &globalConfig
		}
	}

	// 初始化状态和进度
	context.ServiceStatus = make(map[string]string)
	context.ChainProgress = make(map[string]any)
	context.RollbackInfo = make(map[string]any)

	contextBytes, _ := json.Marshal(context)
	return string(contextBytes)
}

// updateExecutionStatus 更新执行状态
func (e *WorkflowEngine) updateExecutionStatus(ctx context.Context, execution *models.WorkflowExecution, status models.WorkflowExecutionStatus, errorMessage string) {
	execution.Status = status
	if status == models.WorkflowExecutionStatusCompleted {
		execution.Complete()
	} else if status == models.WorkflowExecutionStatusFailed {
		execution.Fail(errorMessage)
	}

	if err := e.db.WithContext(ctx).Save(execution).Error; err != nil {
		log.ErrorE("failed to update execution status", err)
	}
}

// ExecuteStepsWithDependencyGraph 基于依赖图的智能并行执行
func (e *WorkflowEngine) ExecuteStepsWithDependencyGraph(ctx context.Context, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	// 构建依赖图
	dependencyGraph := e.buildDependencyGraph(steps)
	completed := make(map[int64]bool)
	inProgress := make(map[int64]bool)

	// 持续执行直到所有步骤完成
	for len(completed) < len(steps) {
		// 找到可以执行的步骤（依赖已满足且未开始执行）
		readySteps := e.findReadySteps(steps, dependencyGraph, completed, inProgress, ctx, execution)

		if len(readySteps) == 0 {
			// 检查是否有正在执行的步骤
			if len(inProgress) == 0 {
				return fmt.Errorf("no steps can be executed - possible circular dependency")
			}
			// 等待正在执行的步骤完成
			time.Sleep(time.Second)
			e.updateCompletedSteps(execution, inProgress, completed, ctx)
			continue
		}

		// 并行执行所有就绪的步骤
		var wg sync.WaitGroup
		errChan := make(chan error, len(readySteps))

		for _, step := range readySteps {
			inProgress[step.ID] = true
			wg.Add(1)

			go func(s models.WorkflowStep) {
				defer wg.Done()
				defer func() {
					delete(inProgress, s.ID)
					completed[s.ID] = true
				}()

				if err := e.ExecuteStepWithRetry(ctx, execution, &s); err != nil {
					errChan <- fmt.Errorf("step %s execution failed: %w", s.Name, err)
				}
			}(step)
		}

		// 等待当前批次完成
		go func() {
			wg.Wait()
			close(errChan)
		}()

		// 检查错误
		for err := range errChan {
			if err != nil {
				return err
			}
		}

		// 更新完成状态
		e.updateCompletedSteps(execution, inProgress, completed, ctx)
	}

	return nil
}

// buildDependencyGraph 构建依赖图
func (e *WorkflowEngine) buildDependencyGraph(steps []models.WorkflowStep) map[int64][]int64 {
	graph := make(map[int64][]int64)

	for _, step := range steps {
		if step.DependsOn != "" {
			var dependencies []int64
			if err := json.Unmarshal([]byte(step.DependsOn), &dependencies); err == nil {
				graph[step.ID] = dependencies
			}
		}
	}

	return graph
}

// findReadySteps 找到可以执行的步骤
func (e *WorkflowEngine) findReadySteps(
	steps []models.WorkflowStep,
	graph map[int64][]int64,
	completed, inProgress map[int64]bool,
	ctx context.Context,
	execution *models.WorkflowExecution,
) []models.WorkflowStep {
	var ready []models.WorkflowStep

	for _, step := range steps {
		// 已完成或正在执行的跳过
		if completed[step.ID] || inProgress[step.ID] {
			continue
		}

		// 检查依赖是否满足
		dependencies := graph[step.ID]
		allDependenciesMet := true

		for _, depID := range dependencies {
			if !completed[depID] {
				allDependenciesMet = false
				break
			}
		}

		if allDependenciesMet {
			// 检查执行条件
			if e.CheckExecutionCondition(ctx, execution, &step) {
				ready = append(ready, step)
			} else {
				// 条件不满足，标记为跳过
				if err := e.skipStep(ctx, execution, &step); err != nil {
					log.ErrorE(fmt.Sprintf("failed to skip step %s", step.Name), err)
				}
				completed[step.ID] = true
			}
		}
	}

	return ready
}

// updateCompletedSteps 更新完成状态
func (e *WorkflowEngine) updateCompletedSteps(execution *models.WorkflowExecution, inProgress, completed map[int64]bool, ctx context.Context) {
	// 检查正在执行的步骤是否已完成
	for stepID := range inProgress {
		var stepExecution models.StepExecution
		err := e.db.WithContext(ctx).Where("execution_id = ? AND step_id = ?",
			execution.ID, stepID).Order("created_at DESC").First(&stepExecution).Error

		if err == nil && (stepExecution.Status == models.StepStatusCompleted ||
			stepExecution.Status == models.StepStatusFailed ||
			stepExecution.Status == models.StepStatusSkipped) {
			delete(inProgress, stepID)
			completed[stepID] = true
		}
	}
}
