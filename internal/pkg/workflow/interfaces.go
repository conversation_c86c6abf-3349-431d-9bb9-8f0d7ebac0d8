package workflow

import (
	"context"
	"pilot-api/pkg/models"
)

// StepHandler 步骤处理器接口
type StepHandler interface {
	// Execute 执行步骤
	Execute(ctx context.Context, execution *models.StepExecution, step *models.WorkflowStep) error
	// Validate 验证步骤配置
	Validate(step *models.WorkflowStep) error
	// GetStatus 获取步骤状态
	GetStatus(ctx context.Context, execution *models.StepExecution) (models.StepStatus, error)
	// Cancel 取消步骤
	Cancel(ctx context.Context, execution *models.StepExecution) error
}

// WorkflowStrategy 工作流策略接口
type WorkflowStrategy interface {
	// GetName 获取策略名称
	GetName() string

	// ValidateWorkflow 验证工作流配置
	ValidateWorkflow(workflow *models.Workflow) error

	// ExecuteWorkflow 执行工作流
	ExecuteWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error

	// GetDefaultSteps 获取默认步骤模板
	GetDefaultSteps(workflow *models.Workflow) []models.WorkflowStep

	// CanRollback 判断是否可以回滚
	CanRollback(execution *models.WorkflowExecution) bool

	// ExecuteRollback 执行回滚
	ExecuteRollback(ctx context.Context, execution *models.WorkflowExecution) error

	// ConfigureWorkflow 配置工作流（设置服务配置、全局配置、步骤等）
	// request 参数为具体的请求类型，由各策略自行解析
	ConfigureWorkflow(workflow *models.Workflow, request any) error

	// GenStepsFromRequest 从请求生成工作流步骤
	// request 参数为具体的请求类型，由各策略自行解析
	GenStepsFromRequest(workflow *models.Workflow) ([]models.WorkflowStep, error)

	// GetConfigTemplate 获取策略的配置模板（新增方法）
	GetConfigTemplate() *WorkflowConfigTemplate
}

// WorkflowConfigTemplate 工作流配置模板
type WorkflowConfigTemplate struct {
	Name        string             `json:"name"`        // 模板名称
	Description string             `json:"description"` // 模板描述
	ConfigForm  WorkflowConfigForm `json:"configForm"`  // 配置表单
	Metadata    StrategyMetadata   `json:"metadata"`    // 策略元数据
}

// WorkflowConfigForm 工作流配置表单
type WorkflowConfigForm struct {
	Sections []ConfigSection `json:"sections"` // 配置区块
}

// ConfigSection 配置区块
type ConfigSection struct {
	Title       string        `json:"title"`       // 区块标题
	Description string        `json:"description"` // 区块描述
	Fields      []ConfigField `json:"fields"`      // 配置字段
}

// ConfigField 配置字段
type ConfigField struct {
	Key          string      `json:"key"`                    // 字段键名
	Label        string      `json:"label"`                  // 字段标签
	Type         string      `json:"type"`                   // 字段类型：text, number, select, boolean, array, object
	Required     bool        `json:"required"`               // 是否必填
	Default      any         `json:"default,omitempty"`      // 默认值
	Options      []Option    `json:"options,omitempty"`      // 选项（select类型）
	Validation   *Validation `json:"validation,omitempty"`   // 验证规则
	Description  string      `json:"description,omitempty"`  // 字段描述
	Placeholder  string      `json:"placeholder,omitempty"`  // 占位符
	Dependencies []string    `json:"dependencies,omitempty"` // 依赖字段
}

// Option 选项
type Option struct {
	Label string `json:"label"` // 选项标签
	Value any    `json:"value"` // 选项值
}

// Validation 验证规则
type Validation struct {
	Min    *float64 `json:"min,omitempty"`    // 最小值
	Max    *float64 `json:"max,omitempty"`    // 最大值
	MinLen *int     `json:"minLen,omitempty"` // 最小长度
	MaxLen *int     `json:"maxLen,omitempty"` // 最大长度
	Regex  string   `json:"regex,omitempty"`  // 正则表达式
}
