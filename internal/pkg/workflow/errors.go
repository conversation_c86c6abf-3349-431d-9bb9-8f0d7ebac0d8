package workflow

import "errors"

// 工作流验证错误
var (
	ErrWorkflowNameRequired      = errors.New("workflow name is required")
	ErrWorkflowNamespaceRequired = errors.New("workflow namespace is required")
	ErrWorkflowClusterIDRequired = errors.New("workflow cluster ID is required")
	ErrWorkflowServicesRequired  = errors.New("workflow services configuration is required")
	ErrWorkflowInvalidConfig     = errors.New("workflow configuration is invalid")
)

// 工作流执行错误
var (
	ErrWorkflowNotFound        = errors.New("workflow not found")
	ErrWorkflowNotApproved     = errors.New("workflow is not approved")
	ErrWorkflowAlreadyRunning  = errors.New("workflow is already running")
	ErrWorkflowExecutionFailed = errors.New("workflow execution failed")
	ErrWorkflowTimeout         = errors.New("workflow execution timeout")
)

// 步骤执行错误
var (
	ErrStepHandlerNotFound  = errors.New("step handler not found")
	ErrStepValidationFailed = errors.New("step validation failed")
	ErrStepExecutionFailed  = errors.New("step execution failed")
	ErrStepTimeout          = errors.New("step execution timeout")
	ErrStepCancelled        = errors.New("step execution cancelled")
)

// 策略错误
var (
	ErrStrategyNotFound         = errors.New("workflow strategy not found")
	ErrStrategyValidationFailed = errors.New("strategy validation failed")
	ErrStrategyExecutionFailed  = errors.New("strategy execution failed")
)

// 审批错误
var (
	ErrApprovalTimeout     = errors.New("approval timeout")
	ErrApprovalRejected    = errors.New("approval rejected")
	ErrApprovalNotFound    = errors.New("approval not found")
	ErrInvalidApprover     = errors.New("invalid approver")
	ErrApprovalAlreadyDone = errors.New("approval already done")
)

// 回滚错误
var (
	ErrRollbackNotSupported = errors.New("rollback not supported")
	ErrRollbackFailed       = errors.New("rollback failed")
	ErrRollbackTimeout      = errors.New("rollback timeout")
)

// 配置错误
var (
	ErrConfigSourceNotFound   = errors.New("configuration source not found")
	ErrConfigConversionFailed = errors.New("configuration conversion failed")
	ErrInvalidConfigFormat    = errors.New("invalid configuration format")
	ErrConfigValidationFailed = errors.New("configuration validation failed")
)

// 引擎错误
var (
	ErrEngineNotInitialized = errors.New("workflow engine not initialized")
	ErrInvalidEngineState   = errors.New("invalid engine state")
	ErrEngineShutdown       = errors.New("workflow engine is shutting down")
)
