package workflow

import (
	"encoding/json"
	"fmt"
	"pilot-api/pkg/models"
)

// UnifiedWorkflowConfig 统一的工作流配置结构
type UnifiedWorkflowConfig struct {
	// 基础信息
	Name        string              `json:"name"`
	Type        models.WorkflowType `json:"type"`
	Description string              `json:"description,omitempty"`
	Version     string              `json:"version,omitempty"`

	// 服务配置
	Services []ServiceConfig `json:"services"`

	// 全局设置
	Global GlobalConfig `json:"global,omitempty"`

	// 策略特定配置
	Strategy StrategyConfig `json:"strategy,omitempty"`
}

// ServiceConfig 简化的服务配置
type ServiceConfig struct {
	Name         string            `json:"name"`
	Order        int               `json:"order,omitempty"`
	Images       map[string]string `json:"images,omitempty"`
	Replicas     int32             `json:"replicas"`
	Resources    ResourceConfig    `json:"resources,omitempty"`
	Environment  map[string]string `json:"environment,omitempty"`
	HealthCheck  HealthCheckConfig `json:"healthCheck,omitempty"`
	Traffic      TrafficConfig     `json:"traffic,omitempty"`
	Monitoring   MonitoringConfig  `json:"monitoring,omitempty"`
	Dependencies []string          `json:"dependencies,omitempty"`
}

// ResourceConfig 资源配置
type ResourceConfig struct {
	CPU     string `json:"cpu,omitempty"`
	Memory  string `json:"memory,omitempty"`
	Storage string `json:"storage,omitempty"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled             bool   `json:"enabled"`
	Path                string `json:"path,omitempty"`
	Port                int    `json:"port,omitempty"`
	InitialDelaySeconds int    `json:"initialDelaySeconds,omitempty"`
	PeriodSeconds       int    `json:"periodSeconds,omitempty"`
	TimeoutSeconds      int    `json:"timeoutSeconds,omitempty"`
	SuccessThreshold    int    `json:"successThreshold,omitempty"`
	FailureThreshold    int    `json:"failureThreshold,omitempty"`
}

// TrafficConfig 流量配置
type TrafficConfig struct {
	Strategy string        `json:"strategy,omitempty"` // canary, blue-green, ab-test
	Weights  []WeightRule  `json:"weights,omitempty"`
	Rules    []RoutingRule `json:"rules,omitempty"`
}

// WeightRule 权重规则
type WeightRule struct {
	Version string `json:"version"`
	Weight  int32  `json:"weight"`
}

// RoutingRule 路由规则
type RoutingRule struct {
	Type     string `json:"type"` // header, parameter, user
	Key      string `json:"key"`
	Value    string `json:"value"`
	Operator string `json:"operator"` // equals, contains, regex
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled    bool            `json:"enabled"`
	Metrics    []string        `json:"metrics,omitempty"`
	Thresholds []ThresholdRule `json:"thresholds,omitempty"`
	Duration   string          `json:"duration,omitempty"`
	Interval   string          `json:"interval,omitempty"`
}

// ThresholdRule 阈值规则
type ThresholdRule struct {
	Name     string  `json:"name"`
	Operator string  `json:"operator"` // >=, <=, >, <, ==
	Value    float64 `json:"value"`
	Unit     string  `json:"unit,omitempty"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	Timeout      TimeoutConfig      `json:"timeout,omitempty"`
	Approval     ApprovalConfig     `json:"approval,omitempty"`
	Notification NotificationConfig `json:"notification,omitempty"`
	Rollback     RollbackConfig     `json:"rollback,omitempty"`
	Monitoring   MonitoringConfig   `json:"monitoring,omitempty"`
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	Step     int `json:"step,omitempty"`     // 步骤超时（秒）
	Approval int `json:"approval,omitempty"` // 审批超时（秒）
	Workflow int `json:"workflow,omitempty"` // 工作流超时（秒）
	Rollback int `json:"rollback,omitempty"` // 回滚超时（秒）
}

// ApprovalConfig 审批配置
type ApprovalConfig struct {
	Enabled     bool     `json:"enabled"`
	Approvers   []string `json:"approvers,omitempty"`
	RequireAll  bool     `json:"requireAll,omitempty"`
	AutoTimeout int      `json:"autoTimeout,omitempty"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Enabled  bool     `json:"enabled"`
	Channels []string `json:"channels,omitempty"` // email, slack, webhook
	Events   []string `json:"events,omitempty"`   // start, success, failure, approval
	Users    []string `json:"users,omitempty"`
}

// RollbackConfig 回滚配置
type RollbackConfig struct {
	Enabled      bool   `json:"enabled"`
	AutoRollback bool   `json:"autoRollback,omitempty"`
	Strategy     string `json:"strategy,omitempty"` // immediate, gradual, manual
	Trigger      string `json:"trigger,omitempty"`  // manual, auto, threshold
}

// StrategyConfig 策略特定配置
type StrategyConfig struct {
	// Canary策略配置
	Canary *CanaryConfig `json:"canary,omitempty"`
	// BlueGreen策略配置
	BlueGreen *BlueGreenConfig `json:"blueGreen,omitempty"`
	// ABTest策略配置
	ABTest *ABTestConfig `json:"abTest,omitempty"`
}

// CanaryConfig 灰度发布配置
type CanaryConfig struct {
	InitialWeight    int32 `json:"initialWeight,omitempty"`    // 初始流量权重
	StepWeight       int32 `json:"stepWeight,omitempty"`       // 每步增加权重
	MaxWeight        int32 `json:"maxWeight,omitempty"`        // 最大权重
	PromotionTimeout int   `json:"promotionTimeout,omitempty"` // 晋升超时
	AnalysisInterval int   `json:"analysisInterval,omitempty"` // 分析间隔
	SuccessThreshold int   `json:"successThreshold,omitempty"` // 成功阈值
}

// BlueGreenConfig 蓝绿部署配置
type BlueGreenConfig struct {
	PreviewTime     int  `json:"previewTime,omitempty"`     // 预览时间（秒）
	AutoPromotion   bool `json:"autoPromotion,omitempty"`   // 自动晋升
	PreserveTime    int  `json:"preserveTime,omitempty"`    // 环境保留时间（秒）
	ValidationSteps int  `json:"validationSteps,omitempty"` // 验证步骤数
	RollbackOnFail  bool `json:"rollbackOnFail,omitempty"`  // 失败时回滚
}

// ABTestConfig A/B测试配置
type ABTestConfig struct {
	Duration      int              `json:"duration,omitempty"`      // 测试持续时间（秒）
	TrafficSplit  map[string]int32 `json:"trafficSplit,omitempty"`  // 流量分配
	SuccessMetric string           `json:"successMetric,omitempty"` // 成功指标
	Confidence    float64          `json:"confidence,omitempty"`    // 置信度
	Variants      []VariantConfig  `json:"variants,omitempty"`      // 变体配置
}

// VariantConfig 变体配置
type VariantConfig struct {
	Name        string            `json:"name"`
	Weight      int32             `json:"weight"`
	Image       string            `json:"image,omitempty"`
	Environment map[string]string `json:"environment,omitempty"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
}

// NewConfigManager 创建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{}
}

// ParseWorkflowConfig 解析工作流配置
func (cm *ConfigManager) ParseWorkflowConfig(workflow *models.Workflow) (*UnifiedWorkflowConfig, error) {
	config := &UnifiedWorkflowConfig{
		Name:        workflow.Name,
		Type:        workflow.Type,
		Description: workflow.Description,
	}

	// 解析服务配置
	if workflow.Services != "" {
		var legacyServices []models.WorkflowServiceConfig
		if err := json.Unmarshal([]byte(workflow.Services), &legacyServices); err != nil {
			return nil, fmt.Errorf("failed to parse services config: %w", err)
		}

		config.Services = cm.convertLegacyServices(legacyServices)
	}

	// 解析全局配置
	if workflow.GlobalConfig != "" {
		var legacyGlobal models.WorkflowGlobalConfig
		if err := json.Unmarshal([]byte(workflow.GlobalConfig), &legacyGlobal); err != nil {
			return nil, fmt.Errorf("failed to parse global config: %w", err)
		}

		config.Global = cm.convertLegacyGlobal(&legacyGlobal)
	}

	// 根据工作流类型设置策略配置
	config.Strategy = cm.buildStrategyConfig(workflow.Type, workflow.Config)

	return config, nil
}

// BuildWorkflowConfig 构建工作流配置
func (cm *ConfigManager) BuildWorkflowConfig(config *UnifiedWorkflowConfig) (*models.Workflow, error) {
	workflow := &models.Workflow{
		Name:        config.Name,
		Type:        config.Type,
		Description: config.Description,
	}

	// 转换服务配置
	legacyServices := cm.convertToLegacyServices(config.Services)
	servicesJSON, err := json.Marshal(legacyServices)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal services config: %w", err)
	}
	workflow.Services = string(servicesJSON)

	// 转换全局配置
	legacyGlobal := cm.convertToLegacyGlobal(&config.Global)
	globalJSON, err := json.Marshal(legacyGlobal)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal global config: %w", err)
	}
	workflow.GlobalConfig = string(globalJSON)

	// 转换策略配置
	strategyJSON, err := json.Marshal(config.Strategy)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal strategy config: %w", err)
	}
	workflow.Config = string(strategyJSON)

	return workflow, nil
}

// ValidateConfig 验证配置
func (cm *ConfigManager) ValidateConfig(config *UnifiedWorkflowConfig) error {
	// 基础验证
	if config.Name == "" {
		return fmt.Errorf("workflow name is required")
	}

	if len(config.Services) == 0 {
		return fmt.Errorf("at least one service is required")
	}

	// 验证服务配置
	serviceNames := make(map[string]bool)
	for _, service := range config.Services {
		if service.Name == "" {
			return fmt.Errorf("service name is required")
		}

		if serviceNames[service.Name] {
			return fmt.Errorf("duplicate service name: %s", service.Name)
		}
		serviceNames[service.Name] = true

		if len(service.Images) == 0 {
			return fmt.Errorf("service %s requires image configuration", service.Name)
		}

		if service.Replicas <= 0 {
			return fmt.Errorf("service %s requires positive replicas", service.Name)
		}
	}

	// 验证策略特定配置
	switch config.Type {
	case models.WorkflowTypeCanary:
		if config.Strategy.Canary != nil {
			if err := cm.validateCanaryConfig(config.Strategy.Canary); err != nil {
				return fmt.Errorf("invalid canary config: %w", err)
			}
		}
	case models.WorkflowTypeBlueGreen:
		if config.Strategy.BlueGreen != nil {
			if err := cm.validateBlueGreenConfig(config.Strategy.BlueGreen); err != nil {
				return fmt.Errorf("invalid blue-green config: %w", err)
			}
		}
	case models.WorkflowTypeABTest:
		if config.Strategy.ABTest != nil {
			if err := cm.validateABTestConfig(config.Strategy.ABTest); err != nil {
				return fmt.Errorf("invalid ab-test config: %w", err)
			}
		}
	}

	return nil
}

// 转换方法
func (cm *ConfigManager) convertLegacyServices(legacy []models.WorkflowServiceConfig) []ServiceConfig {
	var services []ServiceConfig

	for _, ls := range legacy {
		service := ServiceConfig{
			Name:         ls.Name,
			Order:        ls.Order,
			Images:       ls.Images,
			Replicas:     ls.Replicas,
			Environment:  ls.Environment,
			Dependencies: ls.Dependencies,
		}

		// 转换资源配置
		if ls.Resources != nil {
			service.Resources = ResourceConfig{
				CPU:     ls.Resources["cpu"],
				Memory:  ls.Resources["memory"],
				Storage: ls.Resources["storage"],
			}
		}

		// 转换健康检查配置
		if ls.HealthCheck != nil {
			service.HealthCheck = HealthCheckConfig{
				Enabled:             true,
				Path:                ls.HealthCheck.Path,
				Port:                ls.HealthCheck.Port,
				InitialDelaySeconds: ls.HealthCheck.InitialDelaySeconds,
				PeriodSeconds:       ls.HealthCheck.PeriodSeconds,
				TimeoutSeconds:      ls.HealthCheck.TimeoutSeconds,
				SuccessThreshold:    ls.HealthCheck.SuccessThreshold,
				FailureThreshold:    ls.HealthCheck.FailureThreshold,
			}
		}

		// 转换流量配置
		if ls.Routing != nil {
			weights := make([]WeightRule, len(ls.Routing.Weights))
			for i, w := range ls.Routing.Weights {
				weights[i] = WeightRule{
					Version: w.Version,
					Weight:  w.Weight,
				}
			}

			rules := make([]RoutingRule, len(ls.Routing.Rules))
			for i, r := range ls.Routing.Rules {
				rules[i] = RoutingRule{
					Type:     r.Type,
					Key:      r.Key,
					Value:    r.Value,
					Operator: r.Operator,
				}
			}

			service.Traffic = TrafficConfig{
				Strategy: ls.Routing.Strategy,
				Weights:  weights,
				Rules:    rules,
			}
		}

		// 转换监控配置
		if ls.Monitoring != nil {
			thresholds := make([]ThresholdRule, len(ls.Monitoring.Thresholds))
			for i, t := range ls.Monitoring.Thresholds {
				thresholds[i] = ThresholdRule{
					Name:     t.Name,
					Operator: t.Operator,
					Value:    t.Value,
					Unit:     t.Unit,
				}
			}

			service.Monitoring = MonitoringConfig{
				Enabled:    true,
				Metrics:    ls.Monitoring.Metrics,
				Thresholds: thresholds,
				Duration:   ls.Monitoring.Duration,
				Interval:   ls.Monitoring.Interval,
			}
		}

		services = append(services, service)
	}

	return services
}

func (cm *ConfigManager) convertLegacyGlobal(legacy *models.WorkflowGlobalConfig) GlobalConfig {
	global := GlobalConfig{}

	// 转换监控配置
	if legacy.GlobalMonitoring != nil {
		global.Monitoring = MonitoringConfig{
			Enabled:  legacy.GlobalMonitoring.Enabled,
			Metrics:  legacy.GlobalMonitoring.Metrics,
			Duration: legacy.GlobalMonitoring.Duration,
			Interval: legacy.GlobalMonitoring.Interval,
		}
	}

	// 转换回滚配置
	if legacy.GlobalRollback != nil {
		global.Rollback = RollbackConfig{
			Enabled:      legacy.GlobalRollback.Enabled,
			AutoRollback: legacy.GlobalRollback.AutoRollback,
			Strategy:     "immediate",
			Trigger:      legacy.GlobalRollback.RollbackTrigger,
		}
	}

	// 转换超时配置
	if legacy.GlobalTimeout != nil {
		global.Timeout = TimeoutConfig{
			Step:     legacy.GlobalTimeout.StepTimeout,
			Approval: legacy.GlobalTimeout.ApprovalTimeout,
			Workflow: legacy.GlobalTimeout.MonitoringTimeout + legacy.GlobalTimeout.RollbackTimeout,
			Rollback: legacy.GlobalTimeout.RollbackTimeout,
		}
	}

	// 转换通知配置
	if legacy.GlobalNotification != nil {
		global.Notification = NotificationConfig{
			Enabled:  legacy.GlobalNotification.Enabled,
			Channels: legacy.GlobalNotification.Channels,
			Events:   legacy.GlobalNotification.Events,
			Users:    legacy.GlobalNotification.Users,
		}
	}

	return global
}

func (cm *ConfigManager) convertToLegacyServices(services []ServiceConfig) []models.WorkflowServiceConfig {
	var legacy []models.WorkflowServiceConfig

	for _, s := range services {
		ls := models.WorkflowServiceConfig{
			Name:         s.Name,
			Order:        s.Order,
			Images:       s.Images,
			Replicas:     s.Replicas,
			Environment:  s.Environment,
			Dependencies: s.Dependencies,
		}

		// 转换资源配置
		if s.Resources.CPU != "" || s.Resources.Memory != "" || s.Resources.Storage != "" {
			ls.Resources = map[string]string{
				"cpu":     s.Resources.CPU,
				"memory":  s.Resources.Memory,
				"storage": s.Resources.Storage,
			}
		}

		// 转换健康检查配置
		if s.HealthCheck.Enabled {
			ls.HealthCheck = &models.WorkflowServiceHealthCheck{
				Path:                s.HealthCheck.Path,
				Port:                s.HealthCheck.Port,
				InitialDelaySeconds: s.HealthCheck.InitialDelaySeconds,
				PeriodSeconds:       s.HealthCheck.PeriodSeconds,
				TimeoutSeconds:      s.HealthCheck.TimeoutSeconds,
				SuccessThreshold:    s.HealthCheck.SuccessThreshold,
				FailureThreshold:    s.HealthCheck.FailureThreshold,
			}
		}

		// 转换流量配置
		if len(s.Traffic.Weights) > 0 || len(s.Traffic.Rules) > 0 {
			routing := &models.WorkflowServiceRouting{
				Strategy: s.Traffic.Strategy,
			}

			for _, w := range s.Traffic.Weights {
				routing.Weights = append(routing.Weights, models.WorkflowServiceWeight{
					Version: w.Version,
					Weight:  w.Weight,
				})
			}

			for _, r := range s.Traffic.Rules {
				routing.Rules = append(routing.Rules, models.WorkflowServiceRule{
					Type:     r.Type,
					Key:      r.Key,
					Value:    r.Value,
					Operator: r.Operator,
				})
			}

			ls.Routing = routing
		}

		// 转换监控配置
		if s.Monitoring.Enabled {
			monitoring := &models.WorkflowServiceMonitoring{
				Metrics:  s.Monitoring.Metrics,
				Duration: s.Monitoring.Duration,
				Interval: s.Monitoring.Interval,
			}

			for _, t := range s.Monitoring.Thresholds {
				monitoring.Thresholds = append(monitoring.Thresholds, models.WorkflowServiceThreshold{
					Name:     t.Name,
					Operator: t.Operator,
					Value:    t.Value,
					Unit:     t.Unit,
				})
			}

			ls.Monitoring = monitoring
		}

		legacy = append(legacy, ls)
	}

	return legacy
}

func (cm *ConfigManager) convertToLegacyGlobal(global *GlobalConfig) *models.WorkflowGlobalConfig {
	legacy := &models.WorkflowGlobalConfig{}

	// 转换监控配置
	if global.Monitoring.Enabled {
		legacy.GlobalMonitoring = &models.WorkflowGlobalMonitoring{
			Enabled:  global.Monitoring.Enabled,
			Duration: global.Monitoring.Duration,
			Interval: global.Monitoring.Interval,
			Metrics:  global.Monitoring.Metrics,
		}
	}

	// 转换回滚配置
	if global.Rollback.Enabled {
		legacy.GlobalRollback = &models.WorkflowGlobalRollback{
			Enabled:         global.Rollback.Enabled,
			AutoRollback:    global.Rollback.AutoRollback,
			RollbackTrigger: global.Rollback.Trigger,
		}
	}

	// 转换超时配置
	if global.Timeout.Step > 0 || global.Timeout.Approval > 0 {
		legacy.GlobalTimeout = &models.WorkflowGlobalTimeout{
			StepTimeout:       global.Timeout.Step,
			ApprovalTimeout:   global.Timeout.Approval,
			MonitoringTimeout: global.Timeout.Workflow / 2,
			RollbackTimeout:   global.Timeout.Rollback,
		}
	}

	// 转换通知配置
	if global.Notification.Enabled {
		legacy.GlobalNotification = &models.WorkflowGlobalNotification{
			Enabled:  global.Notification.Enabled,
			Channels: global.Notification.Channels,
			Events:   global.Notification.Events,
			Users:    global.Notification.Users,
		}
	}

	return legacy
}

func (cm *ConfigManager) buildStrategyConfig(workflowType models.WorkflowType, _ string) StrategyConfig {
	strategy := StrategyConfig{}

	switch workflowType {
	case models.WorkflowTypeCanary:
		strategy.Canary = &CanaryConfig{
			InitialWeight:    10,
			StepWeight:       10,
			MaxWeight:        100,
			PromotionTimeout: 3600,
			AnalysisInterval: 300,
			SuccessThreshold: 95,
		}
	case models.WorkflowTypeBlueGreen:
		strategy.BlueGreen = &BlueGreenConfig{
			PreviewTime:     1800,
			AutoPromotion:   false,
			PreserveTime:    86400,
			ValidationSteps: 3,
			RollbackOnFail:  true,
		}
	case models.WorkflowTypeABTest:
		strategy.ABTest = &ABTestConfig{
			Duration:      86400,
			SuccessMetric: "conversion_rate",
			Confidence:    0.95,
			TrafficSplit:  map[string]int32{"A": 50, "B": 50},
		}
	}

	return strategy
}

// 验证方法
func (cm *ConfigManager) validateCanaryConfig(config *CanaryConfig) error {
	if config.InitialWeight < 0 || config.InitialWeight > 100 {
		return fmt.Errorf("initial weight must be between 0 and 100")
	}
	if config.MaxWeight < config.InitialWeight {
		return fmt.Errorf("max weight must be greater than initial weight")
	}
	return nil
}

func (cm *ConfigManager) validateBlueGreenConfig(config *BlueGreenConfig) error {
	if config.PreviewTime < 0 {
		return fmt.Errorf("preview time must be positive")
	}
	if config.ValidationSteps < 1 {
		return fmt.Errorf("validation steps must be at least 1")
	}
	return nil
}

func (cm *ConfigManager) validateABTestConfig(config *ABTestConfig) error {
	if config.Duration <= 0 {
		return fmt.Errorf("duration must be positive")
	}
	if config.Confidence < 0 || config.Confidence > 1 {
		return fmt.Errorf("confidence must be between 0 and 1")
	}

	totalWeight := int32(0)
	for _, weight := range config.TrafficSplit {
		totalWeight += weight
	}
	if totalWeight != 100 {
		return fmt.Errorf("traffic split weights must sum to 100")
	}

	return nil
}
