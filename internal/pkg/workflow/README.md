# Workflow Engine - 纯抽象工作流引擎

## 架构设计

本包 (`internal/pkg/workflow`) 提供了一个纯抽象的工作流引擎，不包含任何具体的业务逻辑。所有具体的业务逻辑都在应用层 (`internal/app/workflow`) 实现。

## 核心组件

### 1. 接口定义 (`interfaces.go`)
- `StepHandler`: 步骤处理器接口
- `WorkflowStrategy`: 工作流策略接口

### 2. 类型定义 (`types.go`)
- `ExecutionContext`: 执行上下文
- `ExecutionResult`: 执行结果
- `StepExecutionResult`: 步骤执行结果

### 3. 基础策略 (`strategy.go`)
- `BaseWorkflowStrategy`: 基础工作流策略实现

### 4. 注册中心 (`registry.go`)
- `StepHandlerRegistry`: 步骤处理器注册中心
- `WorkflowStrategyRegistry`: 工作流策略注册中心
- `WorkflowEngineRegistry`: 工作流引擎注册中心

### 5. 工作流引擎 (`engine.go`)
- `WorkflowEngine`: 纯抽象工作流引擎
- 提供通用的工作流执行框架
- 不包含具体的业务逻辑

### 6. 错误定义 (`errors.go`)
- 定义了所有工作流相关的错误类型
- 统一的错误处理策略

## 设计原则

### 1. 抽象性
- 本包只提供抽象接口和通用功能
- 不包含任何具体的业务逻辑（如灰度发布逻辑）
- 所有具体实现都在应用层完成

### 2. 可扩展性
- 通过策略模式支持不同类型的工作流
- 通过注册中心支持插件式的步骤处理器
- 支持自定义工作流策略

### 3. 通用性
- 提供通用的工作流执行框架
- 支持步骤依赖、条件执行、并行执行等通用功能
- 支持工作流的暂停、恢复、取消等操作

### 4. 统一错误处理
- 所有错误类型都在errors.go中定义
- 使用Go 1.13+的错误包装机制
- 支持错误类型检查和链式错误处理

## 优化后的架构

### 职责分离
- **pkg/workflow**: 提供抽象接口和通用功能，不包含业务逻辑
- **app/workflow**: 实现具体的业务逻辑
  - 具体的工作流策略实现（如 CanaryStrategy）
  - 具体的步骤处理器实现
  - 业务相关的工作流执行逻辑
  - 配置管理和转换

### 避免重复代码
- 移除了两个WorkflowEngine之间的重复方法
- 简化了WorkflowEngineInterface，只保留必要的方法
- 统一了配置构建和转换逻辑

### 统一错误处理
- 所有错误类型都在pkg/workflow/errors.go中定义
- 配置管理器使用统一的错误类型
- 支持错误包装和类型检查

## 使用方式

### 1. 创建工作流引擎
```go
engine := workflow.NewWorkflowEngine(traceService, db)
```

### 2. 注册步骤处理器
```go
stepRegistry := engine.GetRegistry().GetStepHandlerRegistry()
stepRegistry.RegisterStepHandler(models.StepTypeDeployment, deploymentHandler)
```

### 3. 注册工作流策略
```go
strategyRegistry := engine.GetRegistry().GetWorkflowStrategyRegistry()
strategyRegistry.RegisterWorkflowStrategy(models.WorkflowTypeCanary, canaryStrategy)
```

### 4. 执行工作流
```go
execution, err := engine.ExecuteWorkflow(ctx, workflowID, executorID, executorName)
```

### 5. 错误处理
```go
if err != nil {
    if errors.Is(err, workflow.ErrWorkflowNotFound) {
        // 处理工作流未找到错误
    } else if errors.Is(err, workflow.ErrConfigSourceNotFound) {
        // 处理配置源未找到错误
    }
}
```

## 扩展指南

### 1. 添加新的工作流类型
1. 在应用层实现 `WorkflowStrategy` 接口
2. 在应用层注册新的工作流策略
3. 实现具体的业务逻辑

### 2. 添加新的步骤类型
1. 在应用层实现 `StepHandler` 接口
2. 在应用层注册新的步骤处理器
3. 实现具体的步骤执行逻辑

### 3. 自定义执行逻辑
1. 继承 `BaseWorkflowStrategy` 或直接实现 `WorkflowStrategy`
2. 使用工作流引擎提供的通用方法
3. 实现特定的业务逻辑

### 4. 添加新的配置源
1. 在app/workflow/config目录实现 `ConfigSourceProvider` 接口
2. 在ConfigSourceManager中注册新的配置源提供者
3. 实现配置加载、验证和转换逻辑

## 优势

1. **职责分离**: 抽象层专注于框架，应用层专注于业务
2. **代码复用**: 通用功能可以被多个应用层使用
3. **测试友好**: 抽象层和应用层可以独立测试
4. **维护性**: 框架变更不影响业务逻辑，业务逻辑变更不影响框架
5. **可扩展**: 支持插件式的扩展机制
6. **统一错误处理**: 错误类型统一，便于调试和监控
7. **避免重复**: 消除了重复代码，提高了代码质量
