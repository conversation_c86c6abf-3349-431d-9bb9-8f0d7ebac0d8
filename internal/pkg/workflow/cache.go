package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/config"
	"pilot-api/pkg/models"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/redis"
	"gorm.io/gorm"
)

// CacheManager 缓存管理器
type CacheManager struct {
	enabled bool
	ttl     time.Duration
	prefix  string
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled    bool          `json:"enabled"`
	DefaultTTL time.Duration `json:"defaultTTL"`
	Prefix     string        `json:"prefix,omitempty"`
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(cacheConfig CacheConfig) *CacheManager {
	prefix := cacheConfig.Prefix
	if prefix == "" {
		// 使用项目配置的Redis前缀
		prefix = config.Items().Redis.Prefix + "workflow:"
	}

	return &CacheManager{
		enabled: cacheConfig.Enabled,
		ttl:     cacheConfig.DefaultTTL,
		prefix:  prefix,
	}
}

// getCacheKey 获取缓存key
func (cm *CacheManager) getCacheKey(key string) string {
	return cm.prefix + key
}

// GetWorkflow 从缓存获取工作流
func (cm *CacheManager) GetWorkflow(ctx context.Context, workflowID int64) (*models.Workflow, bool) {
	if !cm.enabled {
		return nil, false
	}

	key := cm.getCacheKey(fmt.Sprintf("workflow:%d", workflowID))

	data, err := redis.GetClient().Get(ctx, key).Result()
	if err != nil {
		return nil, false
	}

	var workflow models.Workflow
	if err := json.Unmarshal([]byte(data), &workflow); err != nil {
		return nil, false
	}

	return &workflow, true
}

// SetWorkflow 设置工作流到缓存
func (cm *CacheManager) SetWorkflow(ctx context.Context, workflow *models.Workflow) {
	if !cm.enabled || workflow == nil {
		return
	}

	key := cm.getCacheKey(fmt.Sprintf("workflow:%d", workflow.ID))

	data, err := json.Marshal(workflow)
	if err != nil {
		return
	}

	redis.GetClient().Set(ctx, key, data, cm.ttl)
}

// GetWorkflowExecution 从缓存获取工作流执行
func (cm *CacheManager) GetWorkflowExecution(ctx context.Context, executionID int64) (*models.WorkflowExecution, bool) {
	if !cm.enabled {
		return nil, false
	}

	key := cm.getCacheKey(fmt.Sprintf("execution:%d", executionID))

	data, err := redis.GetClient().Get(ctx, key).Result()
	if err != nil {
		return nil, false
	}

	var execution models.WorkflowExecution
	if err := json.Unmarshal([]byte(data), &execution); err != nil {
		return nil, false
	}

	return &execution, true
}

// SetWorkflowExecution 设置工作流执行到缓存
func (cm *CacheManager) SetWorkflowExecution(ctx context.Context, execution *models.WorkflowExecution) {
	if !cm.enabled || execution == nil {
		return
	}

	key := cm.getCacheKey(fmt.Sprintf("execution:%d", execution.ID))

	data, err := json.Marshal(execution)
	if err != nil {
		return
	}

	// 执行状态变化频繁，使用较短的TTL
	shortTTL := cm.ttl / 4
	redis.GetClient().Set(ctx, key, data, shortTTL)
}

// GetSteps 从缓存获取工作流步骤
func (cm *CacheManager) GetSteps(ctx context.Context, workflowID int64) ([]models.WorkflowStep, bool) {
	if !cm.enabled {
		return nil, false
	}

	key := cm.getCacheKey(fmt.Sprintf("steps:%d", workflowID))

	data, err := redis.GetClient().Get(ctx, key).Result()
	if err != nil {
		return nil, false
	}

	var steps []models.WorkflowStep
	if err := json.Unmarshal([]byte(data), &steps); err != nil {
		return nil, false
	}

	return steps, true
}

// SetSteps 设置工作流步骤到缓存
func (cm *CacheManager) SetSteps(ctx context.Context, workflowID int64, steps []models.WorkflowStep) {
	if !cm.enabled || len(steps) == 0 {
		return
	}

	key := cm.getCacheKey(fmt.Sprintf("steps:%d", workflowID))

	data, err := json.Marshal(steps)
	if err != nil {
		return
	}

	// 步骤相对稳定，使用默认TTL
	redis.GetClient().Set(ctx, key, data, cm.ttl)
}

// InvalidateWorkflow 使工作流缓存失效
func (cm *CacheManager) InvalidateWorkflow(ctx context.Context, workflowID int64) {
	if !cm.enabled {
		return
	}

	keys := []string{
		cm.getCacheKey(fmt.Sprintf("workflow:%d", workflowID)),
		cm.getCacheKey(fmt.Sprintf("steps:%d", workflowID)),
	}

	for _, key := range keys {
		redis.GetClient().Del(ctx, key)
	}
}

// InvalidateExecution 使工作流执行缓存失效
func (cm *CacheManager) InvalidateExecution(ctx context.Context, executionID int64) {
	if !cm.enabled {
		return
	}

	key := cm.getCacheKey(fmt.Sprintf("execution:%d", executionID))
	redis.GetClient().Del(ctx, key)
}

// DatabasePool 数据库连接池管理器
type DatabasePool struct {
	db   *gorm.DB
	pool *ConnectionPool
}

// ConnectionPool 连接池
type ConnectionPool struct {
	connections chan *gorm.DB
	maxSize     int
	currentSize int
	mu          sync.Mutex
}

// NewDatabasePool 创建数据库连接池
func NewDatabasePool(db *gorm.DB, maxConnections int) *DatabasePool {
	pool := &ConnectionPool{
		connections: make(chan *gorm.DB, maxConnections),
		maxSize:     maxConnections,
		currentSize: 0,
	}

	// 预创建一些连接
	for i := 0; i < maxConnections/2; i++ {
		conn := db.Session(&gorm.Session{})
		pool.connections <- conn
		pool.currentSize++
	}

	return &DatabasePool{
		db:   db,
		pool: pool,
	}
}

// GetConnection 获取数据库连接
func (dp *DatabasePool) GetConnection(ctx context.Context) *gorm.DB {
	select {
	case conn := <-dp.pool.connections:
		return conn.WithContext(ctx)
	default:
		// 池中没有可用连接，创建新连接
		dp.pool.mu.Lock()
		if dp.pool.currentSize < dp.pool.maxSize {
			conn := dp.db.Session(&gorm.Session{})
			dp.pool.currentSize++
			dp.pool.mu.Unlock()
			return conn.WithContext(ctx)
		}
		dp.pool.mu.Unlock()

		// 如果达到最大连接数，等待可用连接
		conn := <-dp.pool.connections
		return conn.WithContext(ctx)
	}
}

// ReturnConnection 归还数据库连接
func (dp *DatabasePool) ReturnConnection(conn *gorm.DB) {
	select {
	case dp.pool.connections <- conn:
		// 成功归还到池中
	default:
		// 池已满，关闭连接
		dp.pool.mu.Lock()
		dp.pool.currentSize--
		dp.pool.mu.Unlock()
	}
}

// OptimizedWorkflowEngine 优化的工作流引擎
type OptimizedWorkflowEngine struct {
	*WorkflowEngine
	cache     *CacheManager
	dbPool    *DatabasePool
	batchSize int
}

// NewOptimizedWorkflowEngine 创建优化的工作流引擎
func NewOptimizedWorkflowEngine(engine *WorkflowEngine, cacheConfig CacheConfig, maxConnections int) *OptimizedWorkflowEngine {
	cache := NewCacheManager(cacheConfig)
	dbPool := NewDatabasePool(engine.GetDB(), maxConnections)

	return &OptimizedWorkflowEngine{
		WorkflowEngine: engine,
		cache:          cache,
		dbPool:         dbPool,
		batchSize:      50, // 批处理大小
	}
}

// GetWorkflowWithCache 带缓存的获取工作流
func (oe *OptimizedWorkflowEngine) GetWorkflowWithCache(ctx context.Context, workflowID int64) (*models.Workflow, error) {
	// 首先尝试从缓存获取
	if workflow, found := oe.cache.GetWorkflow(ctx, workflowID); found {
		return workflow, nil
	}

	// 缓存未命中，从数据库获取
	conn := oe.dbPool.GetConnection(ctx)
	defer oe.dbPool.ReturnConnection(conn)

	var workflow models.Workflow
	if err := conn.First(&workflow, workflowID).Error; err != nil {
		return nil, err
	}

	// 设置到缓存
	oe.cache.SetWorkflow(ctx, &workflow)

	return &workflow, nil
}

// GetWorkflowStepsWithCache 带缓存的获取工作流步骤
func (oe *OptimizedWorkflowEngine) GetWorkflowStepsWithCache(ctx context.Context, workflowID int64) ([]models.WorkflowStep, error) {
	// 首先尝试从缓存获取
	if steps, found := oe.cache.GetSteps(ctx, workflowID); found {
		return steps, nil
	}

	// 缓存未命中，从数据库获取
	conn := oe.dbPool.GetConnection(ctx)
	defer oe.dbPool.ReturnConnection(conn)

	var steps []models.WorkflowStep
	if err := conn.Where("workflow_id = ? AND deleted = ?", workflowID, false).Find(&steps).Error; err != nil {
		return nil, err
	}

	// 设置到缓存
	oe.cache.SetSteps(ctx, workflowID, steps)

	return steps, nil
}

// BatchGetWorkflowExecutions 批量获取工作流执行记录
func (oe *OptimizedWorkflowEngine) BatchGetWorkflowExecutions(ctx context.Context, executionIDs []int64) ([]models.WorkflowExecution, error) {
	var executions []models.WorkflowExecution
	var missingIDs []int64

	// 先从缓存中获取
	for _, id := range executionIDs {
		if execution, found := oe.cache.GetWorkflowExecution(ctx, id); found {
			executions = append(executions, *execution)
		} else {
			missingIDs = append(missingIDs, id)
		}
	}

	// 批量从数据库获取缓存未命中的记录
	if len(missingIDs) > 0 {
		conn := oe.dbPool.GetConnection(ctx)
		defer oe.dbPool.ReturnConnection(conn)

		var dbExecutions []models.WorkflowExecution
		if err := conn.Where("id IN ?", missingIDs).Find(&dbExecutions).Error; err != nil {
			return nil, err
		}

		// 将从数据库获取的记录添加到结果并设置缓存
		for _, execution := range dbExecutions {
			executions = append(executions, execution)
			oe.cache.SetWorkflowExecution(ctx, &execution)
		}
	}

	return executions, nil
}

// ExecuteWorkflowOptimized 优化的工作流执行
func (oe *OptimizedWorkflowEngine) ExecuteWorkflowOptimized(ctx context.Context, workflowID int64, executorID, executorName string) (*models.WorkflowExecution, error) {
	// 使用缓存获取工作流
	workflow, err := oe.GetWorkflowWithCache(ctx, workflowID)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}

	if !workflow.CanExecute() {
		return nil, fmt.Errorf("workflow cannot be executed, current status: %s", workflow.Status)
	}

	// 创建执行记录
	execution := &models.WorkflowExecution{
		WorkflowID:   workflowID,
		Status:       models.WorkflowExecutionStatusPending,
		StartTime:    time.Now(),
		ExecutorID:   executorID,
		ExecutorName: executorName,
		TriggerType:  models.TriggerTypeManual,
		Context:      oe.buildExecutionContext(*workflow, executorID, executorName),
	}

	conn := oe.dbPool.GetConnection(ctx)
	defer oe.dbPool.ReturnConnection(conn)

	if createErr := conn.Create(execution).Error; createErr != nil {
		return nil, fmt.Errorf("failed to create execution: %w", createErr)
	}

	// 设置执行记录到缓存
	oe.cache.SetWorkflowExecution(ctx, execution)

	// 获取策略并异步执行
	strategy, err := oe.registry.GetWorkflowStrategy(workflow.Type)
	if err != nil {
		return nil, fmt.Errorf("failed to get workflow strategy: %w", err)
	}

	go oe.executeWorkflowAsyncOptimized(ctx, execution, workflow, strategy)

	return execution, nil
}

// executeWorkflowAsyncOptimized 优化的异步工作流执行
func (oe *OptimizedWorkflowEngine) executeWorkflowAsyncOptimized(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, _ WorkflowStrategy) {
	defer func() {
		if r := recover(); r != nil {
			oe.updateExecutionStatusOptimized(ctx, execution, models.WorkflowExecutionStatusFailed, fmt.Sprintf("panic: %v", r))
		}
	}()

	// 开始执行
	execution.Start()
	oe.updateExecutionStatusOptimized(ctx, execution, models.WorkflowExecutionStatusRunning, "")

	// 使用缓存获取步骤
	steps, err := oe.GetWorkflowStepsWithCache(ctx, workflow.ID)
	if err != nil {
		oe.updateExecutionStatusOptimized(ctx, execution, models.WorkflowExecutionStatusFailed, fmt.Sprintf("failed to get workflow steps: %v", err))
		return
	}

	// 使用优化的依赖图执行
	if err := oe.ExecuteStepsWithDependencyGraph(ctx, execution, steps); err != nil {
		oe.updateExecutionStatusOptimized(ctx, execution, models.WorkflowExecutionStatusFailed, err.Error())
		return
	}

	// 所有步骤执行完成
	oe.updateExecutionStatusOptimized(ctx, execution, models.WorkflowExecutionStatusCompleted, "workflow completed successfully")
}

// updateExecutionStatusOptimized 优化的执行状态更新
func (oe *OptimizedWorkflowEngine) updateExecutionStatusOptimized(
	ctx context.Context,
	execution *models.WorkflowExecution,
	status models.WorkflowExecutionStatus,
	errorMessage string,
) {
	execution.Status = status
	if status == models.WorkflowExecutionStatusCompleted {
		execution.Complete()
	} else if status == models.WorkflowExecutionStatusFailed {
		execution.Fail(errorMessage)
	}

	conn := oe.dbPool.GetConnection(ctx)
	defer oe.dbPool.ReturnConnection(conn)

	if err := conn.Save(execution).Error; err != nil {
		return
	}

	// 更新缓存
	oe.cache.SetWorkflowExecution(ctx, execution)
}
