package workflow

import (
	"context"
	"pilot-api/pkg/models"
)

// BaseWorkflowStrategy 基础工作流策略
type BaseWorkflowStrategy struct {
	name string
}

// NewBaseWorkflowStrategy 创建基础工作流策略
func NewBaseWorkflowStrategy(name string) *BaseWorkflowStrategy {
	return &BaseWorkflowStrategy{
		name: name,
	}
}

// GetName 获取策略名称
func (s *BaseWorkflowStrategy) GetName() string {
	return s.name
}

// ValidateWorkflow 验证工作流配置（基础实现）
func (s *BaseWorkflowStrategy) ValidateWorkflow(workflow *models.Workflow) error {
	if workflow.Name == "" {
		return ErrWorkflowNameRequired
	}
	if workflow.Namespace == "" {
		return ErrWorkflowNamespaceRequired
	}
	if workflow.ClusterID == "" {
		return ErrWorkflowClusterIDRequired
	}
	return nil
}

// ExecuteWorkflow 执行工作流（基础实现 - 顺序执行）
func (s *BaseWorkflowStrategy) ExecuteWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, steps []models.WorkflowStep) error {
	// 默认按顺序执行步骤
	return s.executeSequentialSteps(ctx, execution, steps)
}

// GetDefaultSteps 获取默认步骤模板（基础实现）
func (s *BaseWorkflowStrategy) GetDefaultSteps(workflow *models.Workflow) []models.WorkflowStep {
	return []models.WorkflowStep{}
}

// CanRollback 判断是否可以回滚（基础实现）
func (s *BaseWorkflowStrategy) CanRollback(execution *models.WorkflowExecution) bool {
	return execution.IsFailed() || execution.IsCompleted()
}

// ExecuteRollback 执行回滚（基础实现）
func (s *BaseWorkflowStrategy) ExecuteRollback(ctx context.Context, execution *models.WorkflowExecution) error {
	// 基础实现不支持回滚
	return ErrRollbackNotSupported
}

// ConfigureWorkflow 配置工作流（基础实现）
func (s *BaseWorkflowStrategy) ConfigureWorkflow(workflow *models.Workflow, request any) error {
	// 基础实现不进行任何配置
	return nil
}

// GenStepsFromRequest 从请求生成工作流步骤（基础实现）
func (s *BaseWorkflowStrategy) GenStepsFromRequest(workflow *models.Workflow, request any) ([]models.WorkflowStep, error) {
	// 基础实现返回空步骤列表
	return []models.WorkflowStep{}, nil
}

// GetConfigTemplate 获取策略的配置模板（基础实现）
func (s *BaseWorkflowStrategy) GetConfigTemplate() *WorkflowConfigTemplate {
	// 基础实现返回空模板
	return &WorkflowConfigTemplate{
		Name:        s.name,
		Description: "基础工作流策略",
		ConfigForm: WorkflowConfigForm{
			Sections: []ConfigSection{},
		},
		Metadata: StrategyMetadata{
			Name:        s.name,
			Description: "基础工作流策略实现",
			Version:     "1.0.0",
			Author:      "系统",
			Tags:        []string{"base"},
			Features:    []string{},
		},
	}
}

// executeSequentialSteps 顺序执行步骤
func (s *BaseWorkflowStrategy) executeSequentialSteps(ctx context.Context, execution *models.WorkflowExecution, steps []models.WorkflowStep) error {
	// 按顺序执行步骤的逻辑
	// 这里可以调用步骤处理器来执行具体步骤
	return nil
}
