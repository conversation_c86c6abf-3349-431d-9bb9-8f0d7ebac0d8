package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"pilot-api/internal/pkg/util"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"github.com/avast/retry-go/v4"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

// NotificationService 通知服务
type NotificationService struct {
	client        *lark.Client
	gatewayChatID string
}

// NewNotificationService 创建通知服务
func NewNotificationService(appID, appSecret, gatewayChatID string) *NotificationService {
	return &NotificationService{
		client:        lark.NewClient(appID, appSecret),
		gatewayChatID: gatewayChatID,
	}
}

// DiffContent diff内容结构
type DiffContent struct {
	Type    string `json:"type"`    // add, delete, modify
	Content string `json:"content"` // 具体内容
	Line    int    `json:"line"`    // 行号
}

// NotificationMessage 通知消息
type NotificationMessage struct {
	Title      string            `json:"title"`
	Content    string            `json:"content"`
	Type       string            `json:"type"` // approval, rollback, error
	Recipients []string          `json:"recipients"`
	Channels   []string          `json:"channels"` // feishu, email, webhook
	Metadata   map[string]string `json:"metadata"`
	Timestamp  time.Time         `json:"timestamp"`
	// 新增字段
	Application string        `json:"application"` // 应用名称
	Plugin      string        `json:"plugin"`      // 配置插件
	Applicant   string        `json:"applicant"`   // 申请人
	ApprovalURL string        `json:"approvalURL"` // 审批链接
	DiffData    []DiffContent `json:"diffData"`    // diff内容
}

// SendNotification 发送通知
func (s *NotificationService) SendNotification(msg *NotificationMessage) error {
	for _, channel := range msg.Channels {
		switch channel {
		case "feishu":
			if err := s.sendFeishuNotification(msg); err != nil {
				log.ErrorE("发送飞书通知失败", err)
			}
		case "email":
			if err := s.sendEmailNotification(msg); err != nil {
				log.ErrorE("发送邮件通知失败", err)
			}
		case "webhook":
			if err := s.sendWebhookNotification(msg); err != nil {
				log.ErrorE("发送Webhook通知失败", err)
			}
		}
	}
	return nil
}

// sendFeishuNotification 发送飞书通知
func (s *NotificationService) sendFeishuNotification(msg *NotificationMessage) error {
	if s.client == nil {
		return fmt.Errorf("飞书客户端未初始化")
	}

	// 构建飞书卡片消息
	cardContent := s.buildFeishuCard(msg)
	contentBytes, err := json.Marshal(cardContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}
	content := string(contentBytes)

	// 发送到默认群
	if s.gatewayChatID != "" {
		return s.sendMessageToChat(s.gatewayChatID, content)
	}

	return fmt.Errorf("未配置群ID")
}

// sendEmailNotification 发送邮件通知
//
//nolint:unparam // placeholder implementation always returns
//nolint:unparam // placeholder implementation always returns nil
func (s *NotificationService) sendEmailNotification(msg *NotificationMessage) error {
	// 这里实现邮件发送逻辑
	// 可以使用gomail或其他邮件库
	log.Info(fmt.Sprintf("发送邮件通知: %s to %v", msg.Title, msg.Recipients))
	return nil
}

// sendWebhookNotification 发送Webhook通知
//
//nolint:unparam // placeholder implementation always returns nil
func (s *NotificationService) sendWebhookNotification(msg *NotificationMessage) error {
	// 这里实现Webhook发送逻辑
	log.Info(fmt.Sprintf("发送Webhook通知: %s", msg.Title))
	return nil
}

// buildFeishuCard 构建飞书卡片消息
func (s *NotificationService) buildFeishuCard(msg *NotificationMessage) map[string]interface{} {
	elements := []map[string]interface{}{}

	// 使用紧凑的字段布局，将信息放在一行
	fields := []map[string]interface{}{}

	if msg.Application != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**应用**\n%s", msg.Application),
			},
		})
	}

	if msg.Plugin != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**插件**\n%s", msg.Plugin),
			},
		})
	}

	fields = append(fields, map[string]interface{}{
		"is_short": true,
		"text": map[string]interface{}{
			"tag":     "lark_md",
			"content": fmt.Sprintf("**时间**\n%s", msg.Timestamp.Format("01-02 15:04")),
		},
	})

	if msg.Applicant != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**申请人**\n%s", msg.Applicant),
			},
		})
	}

	// 将所有字段放在一个div中
	if len(fields) > 0 {
		elements = append(elements, map[string]interface{}{
			"tag":    "div",
			"fields": fields,
		})
	}

	// 备注信息（如果有且不是默认的"-"）
	if msg.Content != "" && msg.Content != "-" {
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**备注**\n%s", msg.Content),
			},
		})
	}

	// 添加操作按钮
	if msg.ApprovalURL != "" {
		elements = append(elements, map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"tag":     "plain_text",
						"content": "详情",
					},
					"type": "primary",
					"url":  msg.ApprovalURL,
				},
			},
		})
	}

	return map[string]interface{}{
		"config": map[string]interface{}{
			"wide_screen_mode": true,
		},
		"header": map[string]interface{}{
			"title": map[string]interface{}{
				"tag":     "plain_text",
				"content": msg.Title,
			},
			"template": s.getFeishuTemplate(msg.Type),
		},
		"elements": elements,
	}
}

// sendMessageToChat 发送消息到群聊
func (s *NotificationService) sendMessageToChat(chatID, content string) error {
	if s.client == nil {
		return fmt.Errorf("飞书客户端未初始化，请检查AppID和AppSecret配置")
	}

	ctx := context.Background()

	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType("chat_id").
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(chatID).
			MsgType("interactive").
			Content(content).
			Build()).
		Build()

	if err := retry.Do(func() error {
		resp, err := s.client.Im.Message.Create(ctx, req)
		if err != nil {
			return err
		}
		if !resp.Success() {
			return fmt.Errorf("code: %d, message: %s", resp.Code, resp.Msg)
		}
		return nil
	}, retry.Attempts(3), retry.Delay(time.Second), retry.DelayType(retry.FixedDelay)); err != nil {
		log.Error("failed to send message", log.Any("error", err))
		return err
	}

	return nil
}

// sendMessageToUser 发送消息给用户
func (s *NotificationService) sendMessageToUser(userEmail, content string) error {
	if s.client == nil {
		return fmt.Errorf("飞书客户端未初始化，请检查AppID和AppSecret配置")
	}

	ctx := context.Background()

	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(util.Email).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(userEmail).
			MsgType("interactive").
			Content(content).
			Build()).
		Build()

	if err := retry.Do(func() error {
		resp, err := s.client.Im.Message.Create(ctx, req)
		if err != nil {
			return err
		}
		if !resp.Success() {
			return fmt.Errorf("code: %d, message: %s", resp.Code, resp.Msg)
		}
		return nil
	}, retry.Attempts(3), retry.Delay(time.Second), retry.DelayType(retry.FixedDelay)); err != nil {
		log.Error("failed to send message", log.Any("error", err))
		return err
	}

	return nil
}

const (
	msgTypeApproval = "approval"
	msgTypeError    = "error"
	msgTypeCreate   = "create"
	msgTypeUpdate   = "update"
	msgTypeDelete   = "delete"
)

const (
	colorOrange = "orange"
	colorRed    = "red"
)

// getFeishuTemplate 获取飞书消息模板颜色
func (s *NotificationService) getFeishuTemplate(msgType string) string {
	switch msgType {
	case msgTypeApproval:
		return "blue"
	case msgTypeCreate:
		return "green" // 新增操作使用绿色
	case msgTypeUpdate:
		return colorOrange // 修改操作使用橙色
	case msgTypeDelete:
		return colorRed // 删除操作使用红色
	case "rollback":
		return colorOrange
	case msgTypeError:
		return colorRed
	default:
		return "grey"
	}
}

// SendVirtualServiceApprovalNotification 发送VirtualService审批通知
func (s *NotificationService) SendVirtualServiceApprovalNotification(
	vsName, namespace, cluster, action, approver, comment string,
) error {
	title := fmt.Sprintf("VirtualService审批通知 - %s", action)
	content := fmt.Sprintf(`
**VirtualService审批通知**

- **资源名称**: %s
- **命名空间**: %s
- **集群**: %s
- **操作**: %s
- **审批人**: %s
- **审批意见**: %s
`, vsName, namespace, cluster, action, approver, comment)

	msg := &NotificationMessage{
		Title:     title,
		Content:   content,
		Type:      "approval",
		Channels:  []string{"feishu"},
		Timestamp: time.Now(),
	}

	return s.SendNotification(msg)
}

// SendVirtualServiceRollbackNotification 发送VirtualService回滚通知
func (s *NotificationService) SendVirtualServiceRollbackNotification(
	vsName, namespace, cluster, operator, comment string, version int,
) error {
	title := "VirtualService回滚通知"
	content := fmt.Sprintf(`
**VirtualService回滚通知**

- **资源名称**: %s
- **命名空间**: %s
- **集群**: %s
- **回滚版本**: v%d
- **操作人**: %s
- **回滚原因**: %s
`, vsName, namespace, cluster, version, operator, comment)

	msg := &NotificationMessage{
		Title:     title,
		Content:   content,
		Type:      "rollback",
		Channels:  []string{"feishu"},
		Timestamp: time.Now(),
	}

	return s.SendNotification(msg)
}

// SendVirtualServiceSubmitNotification 发送VirtualService提交变更通知到群
func (s *NotificationService) SendVirtualServiceSubmitNotification(
	vsName, namespace, cluster, applicant, content, approvalURL string,
) error {
	title := "VirtualService变更申请通知"

	// 构建飞书卡片消息
	cardContent := s.buildFeishuCard(&NotificationMessage{
		Title:       title,
		Content:     "-", // 备注信息，如果没有则为"-"
		Type:        "approval",
		Timestamp:   time.Now(),
		Application: vsName,
		Plugin:      "istio",
		Applicant:   applicant,
		ApprovalURL: approvalURL,
	})

	contentBytes, err := json.Marshal(cardContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}
	contentStr := string(contentBytes)

	// 发送到网关审批群
	return s.sendMessageToChat(s.gatewayChatID, contentStr)
}

// SendVirtualServiceApprovalResultNotification 发送VirtualService审批结果通知给申请人
func (s *NotificationService) SendVirtualServiceApprovalResultNotification(
	vsName, namespace, cluster, action, approver, comment, applicant string,
) error {
	var title string
	switch action {
	case "approved":
		title = "VirtualService审批通过"
	case "rejected":
		title = "VirtualService审批拒绝"
	default:
		title = "VirtualService审批通知"
	}

	// 构建飞书卡片消息
	cardContent := s.buildFeishuCard(&NotificationMessage{
		Title:       title,
		Content:     comment, // 审批意见作为备注信息
		Type:        "approval",
		Timestamp:   time.Now(),
		Application: vsName,
		Plugin:      "istio",
		Applicant:   applicant,
		// 审批结果通知不需要跳转链接
	})

	contentBytes, err := json.Marshal(cardContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}
	contentStr := string(contentBytes)

	// 发送给申请人
	return s.sendMessageToUser(applicant, contentStr)
}

// SendDomainRecordSubmitNotification 发送DNS记录提交变更通知到群
func (s *NotificationService) SendDomainRecordSubmitNotification(
	domain, subdomain, recordType, value, operationType, applicant, approvalURL, reason string,
) error {
	// 操作类型中文映射
	operationTypeText := map[string]string{
		"create": "新增",
		"update": "修改",
		"delete": "删除",
	}

	title := fmt.Sprintf("DNS记录变更申请通知 - %s", operationTypeText[operationType])

	// 构建完整域名
	fullDomain := domain
	if subdomain != "" {
		fullDomain = subdomain + "." + domain
	}

	// 构建飞书卡片消息
	content := fmt.Sprintf("[%s] %s %s", operationTypeText[operationType], recordType, value)
	if reason != "" {
		content += fmt.Sprintf("\n申请理由: %s", reason)
	}

	// 根据操作类型选择合适的消息类型和颜色
	var msgType string
	switch operationType {
	case "create":
		msgType = msgTypeCreate
	case "update":
		msgType = msgTypeUpdate
	case "delete":
		msgType = msgTypeDelete
	default:
		msgType = msgTypeApproval
	}

	cardContent := s.buildFeishuCard(&NotificationMessage{
		Title:       title,
		Content:     content,
		Type:        msgType,
		Timestamp:   time.Now(),
		Application: fullDomain,
		Plugin:      "dns",
		Applicant:   applicant,
		ApprovalURL: approvalURL,
	})

	contentBytes, err := json.Marshal(cardContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}
	contentStr := string(contentBytes)

	// 发送到网关审批群
	return s.sendMessageToChat(s.gatewayChatID, contentStr)
}

// SendDomainRecordApprovalResultNotification 发送DNS记录审批结果通知给申请人
func (s *NotificationService) SendDomainRecordApprovalResultNotification(
	domain, subdomain, recordType, value, operationType, action, approver, comment, applicant string,
) error {
	// 构建完整域名
	fullDomain := domain
	if subdomain != "" {
		fullDomain = subdomain + "." + domain
	}

	actionText := map[string]string{
		"approve": "审批通过",
		"reject":  "审批拒绝",
	}

	operationTypeText := map[string]string{
		"create": "新增",
		"update": "修改",
		"delete": "删除",
	}

	title := fmt.Sprintf("DNS记录审批结果通知 - %s", actionText[action])
	content := fmt.Sprintf(`
**DNS记录审批结果**

- **域名**: %s
- **变更类型**: %s
- **记录类型**: %s
- **记录值**: %s
- **审批结果**: %s
- **审批人**: %s
- **审批意见**: %s
`, fullDomain, operationTypeText[operationType], recordType, value, actionText[action], approver, comment)

	// 构建飞书卡片消息
	msgType := "error"
	if action == "approve" {
		msgType = "approval"
	}
	cardContent := s.buildFeishuCard(&NotificationMessage{
		Title:     title,
		Content:   content,
		Type:      msgType,
		Timestamp: time.Now(),
	})

	contentBytes, err := json.Marshal(cardContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}
	contentStr := string(contentBytes)

	// 发送给申请人
	return s.sendMessageToUser(applicant, contentStr)
}
