package util

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"pilot-api/config"

	"git.makeblock.com/makeblock-go/log"
)

// DefaultEncrypt 默认加密(只用于服务端)
func DefaultEncrypt(text string) (string, error) {
	return Encrypt(config.Items().SecretKey, text)
}

// DefaultDecrypt 默认解密(只用于服务端)
func DefaultDecrypt(text string) (string, error) {
	return Decrypt(config.Items().<PERSON>Key, text)
}

// Encrypt 加密
func Encrypt(key, text string) (string, error) {
	plainText := []byte(text)
	cipherText, err := encrypt(plainText, []byte(key))
	if err != nil {
		log.ErrorE("error encrypting: %v", err)
		return "", err
	}
	return fmt.Sprintf("%x", cipherText), nil
}

// Decrypt 解密
func Decrypt(key, text string) (string, error) {
	cipherText, err := hex.DecodeString(text)
	if err != nil {
		log.ErrorE("Error decoding hex string: %v", err)
		return "", err
	}
	decryptedText, err := decrypt(cipherText, []byte(key))
	if err != nil {
		log.ErrorE("Error decrypting: %v", err)
		return "", err
	}
	return string(decryptedText), nil
}

func encrypt(plainText []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	cipherText := make([]byte, aes.BlockSize+len(plainText))
	iv := cipherText[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	stream := cipher.NewCFBEncrypter(block, iv) // nolint
	stream.XORKeyStream(cipherText[aes.BlockSize:], plainText)

	return cipherText, nil
}

func decrypt(cipherText []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	if len(cipherText) < aes.BlockSize {
		return nil, fmt.Errorf("cipherText too short")
	}

	iv := cipherText[:aes.BlockSize]
	cipherText = cipherText[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv) // nolint
	stream.XORKeyStream(cipherText, cipherText)

	return cipherText, nil
}
