package util

import (
	"errors"

	"git.makeblock.com/makeblock-go/log"
	"github.com/go-resty/resty/v2"

	pkgResponse "pilot-api/internal/pkg/response"
)

// APIResponse api response model
type APIResponse[T any] struct {
	Code    uint   `json:"code"`
	Data    T      `json:"data"`
	Message string `json:"message"`
}

// HttpPost 实现http请求的封装, 通过泛型响应对应的模型
func HttpPost[T any](url string, req any, headers map[string]string) (response *T, error error) {
	var ret APIResponse[T]
	res, err := resty.New().R().
		SetHeaders(headers).
		SetResult(&ret).
		SetBody(req).
		Post(url)
	if err != nil {
		log.ErrorE("http post error", err)
		return nil, err
	}
	// 请求成功状态码是200 和 201
	if res.StatusCode() != 200 && res.StatusCode() != 201 {
		return nil, errors.New(res.Status())
	}
	// token无效
	if ret.Code == uint(pkgResponse.CodeTokenInvalid) {
		return nil, pkgResponse.TokenInvalidError("")
	}
	// 业务状态码是0
	if ret.Code != 0 {
		return nil, errors.New(ret.Message)
	}
	// 返回数据
	return &ret.Data, nil
}
