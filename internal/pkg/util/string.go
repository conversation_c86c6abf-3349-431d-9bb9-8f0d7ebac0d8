package util

import (
	"encoding/json"
	"strings"

	"github.com/google/uuid"
)

const UserName = "username"
const Email = "email"
const Hide = "******"

var DateTimeFormat = "2006-01-02 15:04:05"

func UUID() string {
	return strings.ReplaceAll(uuid.NewString(), "-", "")
}

func Like(s string) string {
	return "%" + s + "%"
}

// Contains checks if the target string contains the substring (case-insensitive)
func Contains(target, substring string) bool {
	return strings.Contains(strings.ToLower(target), strings.ToLower(substring))
}

// Coalesce returns the first non-zero value from the provided values.
// If all values are zero, returns the last value.
// If no values are provided, returns the zero value.
func Coalesce[T comparable](values ...T) T {
	var zero T
	if len(values) == 0 {
		return zero
	}
	for i := 0; i < len(values)-1; i++ {
		if values[i] != zero {
			return values[i]
		}
	}
	return values[len(values)-1]
}

func PtrBool(v bool) *bool {
	return &v
}

func StructToString(record any) string {
	jsonBytes, _ := json.Marshal(record)
	return string(jsonBytes)
}

func StringToStruct[T any](s string) T {
	ret := new(T)
	_ = json.Unmarshal([]byte(s), &ret)
	return *ret
}

func PtrString(id *string) string {
	if id == nil {
		return ""
	}
	return *id
}
