package types

import (
	"pilot-api/pkg/models"
)

// ExecutionContext 执行上下文
type ExecutionContext struct {
	WorkflowID   int64          `json:"workflowId"`
	ExecutionID  int64          `json:"executionId"`
	Variables    map[string]any `json:"variables"`
	ServiceName  string         `json:"serviceName"`
	Namespace    string         `json:"namespace"`
	ClusterID    string         `json:"clusterId"`
	ExecutorID   string         `json:"executorId"`
	ExecutorName string         `json:"executorName"`

	// 全链路灰度发布扩展
	Services      []models.WorkflowServiceConfig `json:"services"`
	GlobalConfig  *models.WorkflowGlobalConfig   `json:"globalConfig"`
	ServiceStatus map[string]string              `json:"serviceStatus"`
	ChainProgress map[string]any                 `json:"chainProgress"`
	RollbackInfo  map[string]any                 `json:"rollbackInfo"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    map[string]any `json:"data,omitempty"`
	Error   error          `json:"error,omitempty"`
}

// StepExecutionResult 步骤执行结果
type StepExecutionResult struct {
	StepID    int64             `json:"stepId"`
	StepName  string            `json:"stepName"`
	Status    models.StepStatus `json:"status"`
	Message   string            `json:"message"`
	StartTime int64             `json:"startTime"`
	EndTime   int64             `json:"endTime"`
	Data      map[string]any    `json:"data,omitempty"`
	Error     error             `json:"error,omitempty"`
}
