package types

import (
	networkingv1beta1 "istio.io/api/networking/v1beta1"
	istio "istio.io/client-go/pkg/apis/networking/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// 常量定义
const (
	DefaultPropagationHeader = "x-canary-version"
	DefaultCanaryValue       = "canary"
)

// CanaryService 灰度发布服务配置
type CanaryService struct {
	ServiceName      string                   `json:"serviceName" binding:"required"`       // 服务名称
	Namespace        string                   `json:"namespace" binding:"required"`         // 命名空间
	Order            int                      `json:"order"`                                // 在链路中的顺序（0开始）
	Images           map[string]string        `json:"images"`                               // 镜像版本
	Version          string                   `json:"version"`                              // 版本号
	Envs             map[string]string        `json:"envs"`                                 // 环境变量
	Resources        map[string]string        `json:"resources,omitempty"`                  // 资源配置
	Dependencies     []string                 `json:"dependencies,omitempty"`               // 依赖服务
	CanaryReplicas   int32                    `json:"canaryReplicas" binding:"min=1"`       // 灰度副本数
	ProdReplicas     int32                    `json:"prodReplicas" binding:"min=1"`         // 生产副本数
	TrafficRatio     int32                    `json:"trafficRatio" binding:"min=0,max=100"` // 灰度流量比例(百分比)
	Strategy         string                   `json:"strategy,omitempty"`                   // 部署策略: canary, blue-green, rolling
	RoutingConfig    *ServiceRoutingConfig    `json:"routingConfig"`                        // 路由配置
	MonitoringConfig *ServiceMonitoringConfig `json:"monitoringConfig"`                     // 监控配置
	Policy           *CanaryServicePolicy     `json:"policy"`
}

// ServiceMonitoringConfig 服务监控配置
type ServiceMonitoringConfig struct {
	Metrics    []string           `json:"metrics"`    // 监控指标
	Thresholds []ServiceThreshold `json:"thresholds"` // 阈值配置
	Duration   string             `json:"duration"`   // 监控持续时间
	Interval   string             `json:"interval"`   // 监控间隔
}

// ServiceThreshold 服务阈值
type ServiceThreshold struct {
	Name     string  `json:"name"`     // 指标名称
	Operator string  `json:"operator"` // 操作符: >=, <=, >, <, ==
	Value    float64 `json:"value"`    // 阈值
	Unit     string  `json:"unit"`     // 单位
}

// ServiceRoutingConfig 服务路由配置
type ServiceRoutingConfig struct {
	// 基于请求头的路由
	HeaderRouting *HeaderRoutingConfig `json:"headerRouting,omitempty"`

	// 基于权重的路由
	WeightRouting *WeightRoutingConfig `json:"weightRouting,omitempty"`

	// 基于Cookie的路由
	CookieRouting *CookieRoutingConfig `json:"cookieRouting,omitempty"`

	// 路由策略类型
	Strategy string `json:"strategy"` // header/weight/cookie
}

// HeaderRoutingConfig 基于请求头的路由配置
type HeaderRoutingConfig struct {
	HeaderName  string `json:"headerName"`  // 请求头名称（如：client-id）
	HeaderValue string `json:"headerValue"` // 请求头值（如：gray-user-123）
}

// WeightRoutingConfig 基于权重的路由配置
type WeightRoutingConfig struct {
	CanaryWeight int32 `json:"canaryWeight"` // 灰度权重
	StableWeight int32 `json:"stableWeight"` // 稳定版本权重
}

// CookieRoutingConfig 基于Cookie的路由配置
type CookieRoutingConfig struct {
	CookieName  string `json:"cookieName"`  // Cookie名称
	CookieValue string `json:"cookieValue"` // Cookie值
}

// CanaryServicePolicy 服务级别策略
type CanaryServicePolicy struct {
	// 自动化策略
	AutoPromotion        bool     `json:"autoPromotion"`        // 是否自动晋升
	AutoPromotionMetrics []string `json:"autoPromotionMetrics"` // 自动晋升指标

	// 回滚策略
	AutoRollback        bool     `json:"autoRollback"`        // 是否自动回滚
	AutoRollbackMetrics []string `json:"autoRollbackMetrics"` // 自动回滚指标

	// 健康检查
	HealthCheck *HealthCheckConfig `json:"healthCheck,omitempty"`

	// 资源限制
	ResourceLimits *ResourceLimitsConfig `json:"resourceLimits,omitempty"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Path                string `json:"path"`                // 健康检查路径
	IntervalSeconds     int    `json:"intervalSeconds"`     // 检查间隔(秒)
	TimeoutSeconds      int    `json:"timeoutSeconds"`      // 超时时间(秒)
	SuccessThreshold    int    `json:"successThreshold"`    // 成功阈值
	FailureThreshold    int    `json:"failureThreshold"`    // 失败阈值
	InitialDelaySeconds int    `json:"initialDelaySeconds"` // 初始延迟(秒)
}

// ResourceLimitsConfig 资源限制配置
type ResourceLimitsConfig struct {
	CPU    string `json:"cpu"`    // CPU限制
	Memory string `json:"memory"` // 内存限制
}

// CanaryGlobalConfig 全局配置
type CanaryGlobalConfig struct {
	// 全局路由策略
	GlobalRoutingStrategy string `json:"globalRoutingStrategy"` // 全局路由策略：header/weight/cookie

	// 全局请求头配置（用于全链路传递）
	GlobalHeaders map[string]string `json:"globalHeaders"` // 全局请求头

	// 全局监控配置
	GlobalMonitoring *GlobalMonitoringConfig `json:"globalMonitoring"`

	// 全局回滚配置
	GlobalRollback *GlobalRollbackConfig `json:"globalRollback"`

	// 全局超时配置
	GlobalTimeout *GlobalTimeoutConfig `json:"globalTimeout"`

	// 全局通知配置
	GlobalNotification *GlobalNotificationConfig `json:"globalNotification"`
}

// GlobalMonitoringConfig 全局监控配置
type GlobalMonitoringConfig struct {
	EnableTracing bool     `json:"enableTracing"` // 是否启用链路追踪
	TraceHeaders  []string `json:"traceHeaders"`  // 追踪请求头
}

// GlobalRollbackConfig 全局回滚配置
type GlobalRollbackConfig struct {
	AutoRollback     bool    `json:"autoRollback"`     // 是否自动回滚
	ErrorThreshold   float64 `json:"errorThreshold"`   // 错误率阈值
	LatencyThreshold float64 `json:"latencyThreshold"` // 延迟阈值
}

// GlobalTimeoutConfig 全局超时配置
type GlobalTimeoutConfig struct {
	StepTimeout       int `json:"stepTimeout"`       // 步骤超时时间(秒)
	ApprovalTimeout   int `json:"approvalTimeout"`   // 审批超时时间(秒)
	MonitoringTimeout int `json:"monitoringTimeout"` // 监控超时时间(秒)
	RollbackTimeout   int `json:"rollbackTimeout"`   // 回滚超时时间(秒)
}

// GlobalNotificationConfig 全局通知配置
type GlobalNotificationConfig struct {
	Enabled  bool     `json:"enabled"`  // 是否启用通知
	Channels []string `json:"channels"` // 通知渠道: email, slack, webhook
	Events   []string `json:"events"`   // 通知事件: start, success, failure, approval
	Template string   `json:"template"` // 通知模板
	Users    []string `json:"users"`    // 通知用户列表
}

// CanaryApproval 灰度发布审批记录
type CanaryApproval struct {
	ApproverEmail string `json:"approverEmail"` // 审批人邮箱
	ApproverName  string `json:"approverName"`  // 审批人姓名
	Status        string `json:"status"`        // 审批状态：pending/approved/rejected
	ApprovalTime  string `json:"approvalTime"`  // 审批时间
	Comment       string `json:"comment"`       // 审批意见
}

// 保留原有的结构以保持兼容性
type ApproveUser struct {
	UserID   string `json:"userId"`
	UserName string `json:"userName"`
	Status   string `json:"status"`
}

type ManualValidateConfig struct {
	Reviewer      []string `json:"reviewer,omitempty"`      //审批人
	ConditionType string   `json:"conditionType,omitempty"` //条件类型：and/or
}

// ToIstioVirtualService 将当前路由配置转换为Istio VirtualService (支持全链路传播)
func (c *CanaryService) ToIstioVirtualService(namespace string) *istio.VirtualService {
	vs := &istio.VirtualService{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.ServiceName + "-canary",
			Namespace: namespace,
			Labels: map[string]string{
				"app.kubernetes.io/name":       c.ServiceName,
				"app.kubernetes.io/component":  "canary",
				"app.kubernetes.io/managed-by": "pilot-api",
				"canary.pilot.io/chain":        "enabled",
			},
		},
		Spec: networkingv1beta1.VirtualService{
			Hosts: []string{c.ServiceName},
			Http:  []*networkingv1beta1.HTTPRoute{},
		},
	}

	// 如果没有路由配置，返回默认配置
	if c.RoutingConfig == nil {
		vs.Spec.Http = []*networkingv1beta1.HTTPRoute{{
			Route: []*networkingv1beta1.HTTPRouteDestination{{
				Destination: &networkingv1beta1.Destination{
					Host:   c.ServiceName,
					Subset: "stable",
				},
				Weight: 100,
			}},
		}}
		return vs
	}

	// 根据路由策略生成不同的配置
	switch c.RoutingConfig.Strategy {
	case "header":
		vs.Spec.Http = c.generateHeaderRoutingWithChainSupport()
	case "weight":
		vs.Spec.Http = c.generateWeightRoutingWithChainSupport()
	case "cookie":
		vs.Spec.Http = c.generateCookieRoutingWithChainSupport()
	default:
		vs.Spec.Http = c.generateWeightRoutingWithChainSupport() // 默认使用权重路由
	}

	return vs
}

// generateHeaderRoutingWithChainSupport 生成支持灰度传播的基于请求头的路由
func (c *CanaryService) generateHeaderRoutingWithChainSupport() []*networkingv1beta1.HTTPRoute {
	if c.RoutingConfig.HeaderRouting == nil {
		return []*networkingv1beta1.HTTPRoute{}
	}

	propagationHeader := DefaultPropagationHeader
	propagationValue := DefaultCanaryValue

	routes := []*networkingv1beta1.HTTPRoute{
		{
			Name: "canary-header-route",
			Match: []*networkingv1beta1.HTTPMatchRequest{{
				Headers: map[string]*networkingv1beta1.StringMatch{
					c.RoutingConfig.HeaderRouting.HeaderName: {
						MatchType: &networkingv1beta1.StringMatch_Exact{
							Exact: c.RoutingConfig.HeaderRouting.HeaderValue,
						},
					},
				},
			}},
			Route: []*networkingv1beta1.HTTPRouteDestination{{
				Destination: &networkingv1beta1.Destination{
					Host:   c.ServiceName,
					Subset: "canary",
				},
				Weight: 100,
				Headers: &networkingv1beta1.Headers{
					Request: &networkingv1beta1.Headers_HeaderOperations{
						Set: map[string]string{
							propagationHeader: propagationValue, // 设置传播头
						},
					},
				},
			}},
		},
	}

	// 添加传播路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "canary-propagation-route",
		Match: []*networkingv1beta1.HTTPMatchRequest{{
			Headers: map[string]*networkingv1beta1.StringMatch{
				propagationHeader: {
					MatchType: &networkingv1beta1.StringMatch_Exact{
						Exact: propagationValue,
					},
				},
			},
		}},
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "canary",
			},
			Weight: 100,
			Headers: &networkingv1beta1.Headers{
				Request: &networkingv1beta1.Headers_HeaderOperations{
					Set: map[string]string{
						propagationHeader: propagationValue, // 继续传播
					},
				},
			},
		}},
	})

	// 默认稳定版本路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "stable-route",
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "stable",
			},
			Weight: 100,
		}},
	})

	return routes
}

// generateWeightRoutingWithChainSupport 生成支持灰度传播的基于权重的路由
func (c *CanaryService) generateWeightRoutingWithChainSupport() []*networkingv1beta1.HTTPRoute {
	if c.RoutingConfig.WeightRouting == nil {
		return []*networkingv1beta1.HTTPRoute{}
	}

	propagationHeader := DefaultPropagationHeader
	propagationValue := DefaultCanaryValue

	routes := []*networkingv1beta1.HTTPRoute{}

	// 优先处理传播路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "canary-propagation-route",
		Match: []*networkingv1beta1.HTTPMatchRequest{{
			Headers: map[string]*networkingv1beta1.StringMatch{
				propagationHeader: {
					MatchType: &networkingv1beta1.StringMatch_Exact{
						Exact: propagationValue,
					},
				},
			},
		}},
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "canary",
			},
			Weight: 100,
			Headers: &networkingv1beta1.Headers{
				Request: &networkingv1beta1.Headers_HeaderOperations{
					Set: map[string]string{
						propagationHeader: propagationValue, // 继续传播
					},
				},
			},
		}},
	})

	// 权重路由
	canaryWeight := c.RoutingConfig.WeightRouting.CanaryWeight
	if canaryWeight > 100 || canaryWeight < 0 {
		canaryWeight = 50 // 默认值
	}
	stableWeight := c.RoutingConfig.WeightRouting.StableWeight
	if stableWeight > 100 || stableWeight < 0 {
		stableWeight = 50 // 默认值
	}

	weightRoute := &networkingv1beta1.HTTPRoute{
		Name: "weight-based-route",
		Route: []*networkingv1beta1.HTTPRouteDestination{
			{
				Destination: &networkingv1beta1.Destination{
					Host:   c.ServiceName,
					Subset: "canary",
				},
				Weight: canaryWeight,
			},
			{
				Destination: &networkingv1beta1.Destination{
					Host:   c.ServiceName,
					Subset: "stable",
				},
				Weight: stableWeight,
			},
		},
	}

	// 为灰度流量添加传播头
	weightRoute.Route[0].Headers = &networkingv1beta1.Headers{
		Request: &networkingv1beta1.Headers_HeaderOperations{
			Set: map[string]string{
				propagationHeader: propagationValue,
			},
		},
	}

	routes = append(routes, weightRoute)

	return routes
}

// generateCookieRoutingWithChainSupport 生成支持灰度传播的基于Cookie的路由
func (c *CanaryService) generateCookieRoutingWithChainSupport() []*networkingv1beta1.HTTPRoute {
	if c.RoutingConfig.CookieRouting == nil {
		return []*networkingv1beta1.HTTPRoute{}
	}

	propagationHeader := DefaultPropagationHeader
	propagationValue := DefaultCanaryValue

	// Cookie路由通过匹配Cookie头实现
	cookiePattern := c.RoutingConfig.CookieRouting.CookieName + "=" + c.RoutingConfig.CookieRouting.CookieValue

	routes := []*networkingv1beta1.HTTPRoute{}

	// 优先处理传播路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "canary-propagation-route",
		Match: []*networkingv1beta1.HTTPMatchRequest{{
			Headers: map[string]*networkingv1beta1.StringMatch{
				propagationHeader: {
					MatchType: &networkingv1beta1.StringMatch_Exact{
						Exact: propagationValue,
					},
				},
			},
		}},
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "canary",
			},
			Weight: 100,
			Headers: &networkingv1beta1.Headers{
				Request: &networkingv1beta1.Headers_HeaderOperations{
					Set: map[string]string{
						propagationHeader: propagationValue, // 继续传播
					},
				},
			},
		}},
	})

	// Cookie路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "cookie-based-route",
		Match: []*networkingv1beta1.HTTPMatchRequest{{
			Headers: map[string]*networkingv1beta1.StringMatch{
				"cookie": {
					MatchType: &networkingv1beta1.StringMatch_Regex{
						Regex: cookiePattern,
					},
				},
			},
		}},
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "canary",
			},
			Weight: 100,
			Headers: &networkingv1beta1.Headers{
				Request: &networkingv1beta1.Headers_HeaderOperations{
					Set: map[string]string{
						propagationHeader: propagationValue, // 设置传播头
					},
				},
			},
		}},
	})

	// 默认稳定版本路由
	routes = append(routes, &networkingv1beta1.HTTPRoute{
		Name: "stable-route",
		Route: []*networkingv1beta1.HTTPRouteDestination{{
			Destination: &networkingv1beta1.Destination{
				Host:   c.ServiceName,
				Subset: "stable",
			},
			Weight: 100,
		}},
	})

	return routes
}

// ToIstioDestinationRule 将服务配置转换为Istio DestinationRule
func (c *CanaryService) ToIstioDestinationRule(namespace string) *istio.DestinationRule {
	return &istio.DestinationRule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.ServiceName + "-canary",
			Namespace: namespace,
			Labels: map[string]string{
				"app.kubernetes.io/name":       c.ServiceName,
				"app.kubernetes.io/component":  "canary",
				"app.kubernetes.io/managed-by": "pilot-api",
				"canary.pilot.io/chain":        "enabled",
			},
		},
		Spec: networkingv1beta1.DestinationRule{
			Host: c.ServiceName,
			Subsets: []*networkingv1beta1.Subset{
				{
					Name: "canary",
					Labels: map[string]string{
						"version": "canary",
					},
				},
				{
					Name: "stable",
					Labels: map[string]string{
						"version": "stable",
					},
				},
			},
		},
	}
}

// GetPropagationHeaders 获取需要传播的请求头配置
func (c *CanaryService) GetPropagationHeaders() map[string]string {
	propagationHeader := DefaultPropagationHeader
	propagationValue := DefaultCanaryValue

	return map[string]string{
		propagationHeader: propagationValue,
	}
}

// ShouldPropagateToService 判断是否应该向指定服务传播灰度流量
func (c *CanaryService) ShouldPropagateToService(serviceName string) bool {
	// 默认传播到所有服务
	return true
}
