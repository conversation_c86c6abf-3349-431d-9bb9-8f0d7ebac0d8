apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: readonly-all-resources-clusterrole
  namespace: default
rules:
  # 核心 API 组的只读权限（排除 secrets）
  - apiGroups: [""]
    resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "persistentvolumes", "nodes", "namespaces", "configmaps", "events", "serviceaccounts"]
    verbs: ["get", "list", "watch"]

  # apps API 组的只读权限
  - apiGroups: ["apps"]
    resources: ["*"]
    verbs: ["get", "list", "watch"]

  # extensions API 组的只读权限
  - apiGroups: ["extensions"]
    resources: ["*"]
    verbs: ["get", "list", "watch"]

  # networking.k8s.io API 组的只读权限
  - apiGroups: ["networking.k8s.io"]
    resources: ["*"]
    verbs: ["get", "list", "watch"]

  # networking.istio.io API 组的只读权限
  - apiGroups: ["networking.istio.io"]
    resources: ["*"]
    verbs: ["get", "list", "watch"]

  # 其他常用 API 组的只读权限
  - apiGroups: ["batch", "autoscaling", "policy", "rbac.authorization.k8s.io"]
    resources: ["*"]
    verbs: ["get", "list", "watch"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: readonly-all-resources-sa
  namespace: default

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: readonly-all-resources-clusterrolebinding
subjects:
  - kind: ServiceAccount
    name: readonly-all-resources-sa
    namespace: default
roleRef:
  kind: ClusterRole
  name: readonly-all-resources-clusterrole
  apiGroup: rbac.authorization.k8s.io

# kubectl create token readonly-all-resources-sa -n default --duration 26280h

# eyJhbGciOiJSUzI1NiIsImtpZCI6InpNSGsyTWwtYXBiYUU3QVJ0TXBDN2Q1SGllTXZqQnduZWdQNlFmWjR4UWcifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DCpB5VMwqfpIeSSUeTfMNsEFcZUDjasEQE20Yln3sqTyjBrtbT8IYASLAyfUWuncvYz8wXCtIj0oLj6f1LjiNa1v9VLTtS47jV0afWga9HZngSH9HGtaSsPcDaX2-MFJkUE576TddN0F00JOE2MYXQGet5eKvWv7jyqfeqxfZRVlV4cT6GB-Xrh2CrtNxvWw0PpFPBHa3-NgcbDbDCfARFgJNCk_gNxZLNWqYXji7ghK6J8ssm7TMSz4ploOpTNnWASMDcBjz-kGH347sfjx576ucoiB9IFgdVFlNgzq5pFwxjCcrRWqTvHwJ0Tx9RsNKpnO-88Qr7wftYDcpyqsJA
