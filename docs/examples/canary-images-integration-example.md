# Canary模型镜像集成示例

## 概述

本文档展示如何使用canary模型中的`Images`字段来动态控制workflow部署中的镜像配置。系统会在执行部署步骤时自动从canary配置中获取最新的镜像信息，无需手动修改workflow配置。

## 完整流程示例

### 1. 创建Canary配置

首先创建一个canary配置，定义服务的镜像信息：

```bash
curl -X POST http://localhost:8080/api/v1/canary \
  -H "Content-Type: application/json" \
  -d '{
    "name": "web-service-canary",
    "description": "Web服务灰度发布配置",
    "namespace": "default",
    "clusterId": "cluster-1",
    "services": [
      {
        "serviceName": "web-service",
        "namespace": "default",
        "order": 1,
        "images": {
          "web-app": "nginx:1.21.1",
          "sidecar": "envoy:v1.18.1",
          "log-collector": "fluentd:v1.12.1"
        },
        "envs": {
          "ENV": "production",
          "LOG_LEVEL": "info",
          "SIDECAR_ENABLED": "true"
        },
        "canaryReplicas": 1,
        "prodReplicas": 3,
        "trafficRatio": 50
      }
    ],
    "canaryApprovers": ["<EMAIL>"],
    "prodApprovers": ["<EMAIL>"],
    "approvalType": "or"
  }'
```

### 2. 创建Workflow

创建一个workflow，注意这里的镜像配置会被canary配置覆盖：

```bash
curl -X POST http://localhost:8080/api/v1/workflows/canary \
  -H "Content-Type: application/json" \
  -d '{
    "name": "web-service-canary",
    "description": "Web服务灰度发布工作流",
    "namespace": "default",
    "clusterId": "cluster-1",
    "services": [
      {
        "name": "web-service",
        "order": 1,
        "images": {
          "web-app": "nginx:1.20.0",
          "sidecar": "envoy:v1.17.0"
        },
        "version": "v1.0.0",
        "replicas": 3,
        "canaryReplicas": 1,
        "environment": {
          "ENV": "staging"
        },
        "strategy": "canary"
      }
    ],
    "steps": [
      {
        "name": "审批web-service灰度发布",
        "type": "approval",
        "order": 1,
        "serviceName": "web-service",
        "config": {
          "approvalConfig": {
            "approvers": ["<EMAIL>"],
            "conditionType": "or",
            "timeout": 3600
          }
        },
        "autoExecute": false,
        "timeout": 3600
      },
      {
        "name": "审批生产发布",
        "type": "approval",
        "order": 10,
        "config": {
          "approvalConfig": {
            "approvers": ["<EMAIL>"],
            "conditionType": "or",
            "timeout": 3600
          }
        },
        "autoExecute": false,
        "timeout": 3600
      }
    ]
  }'
```

### 3. 执行Workflow

执行workflow时，系统会自动从canary配置中获取最新的镜像信息：

```bash
curl -X POST http://localhost:8080/api/v1/workflows/{workflow_id}/execute \
  -H "Content-Type: application/json" \
  -d '{
    "executorId": "admin",
    "executorName": "管理员",
    "triggerType": "manual"
  }'
```

**实际部署时使用的镜像配置：**
- `web-app`: `nginx:1.21.1` (来自canary配置，覆盖workflow中的`nginx:1.20.0`)
- `sidecar`: `envoy:v1.18.1` (来自canary配置，覆盖workflow中的`envoy:v1.17.0`)
- `log-collector`: `fluentd:v1.12.1` (来自canary配置，workflow中没有此容器)

**实际部署时使用的环境变量：**
- `ENV`: `production` (来自canary配置，覆盖workflow中的`staging`)
- `LOG_LEVEL`: `info` (来自canary配置)
- `SIDECAR_ENABLED`: `true` (来自canary配置)

### 4. 更新镜像版本

当需要更新镜像版本时，只需更新canary配置：

```bash
curl -X PUT http://localhost:8080/api/v1/canary/{canary_uuid} \
  -H "Content-Type: application/json" \
  -d '{
    "uuid": "{canary_uuid}",
    "name": "web-service-canary",
    "description": "Web服务灰度发布配置",
    "namespace": "default",
    "clusterId": "cluster-1",
    "services": [
      {
        "serviceName": "web-service",
        "namespace": "default",
        "order": 1,
        "images": {
          "web-app": "nginx:1.22.0",
          "sidecar": "envoy:v1.19.0",
          "log-collector": "fluentd:v1.13.0"
        },
        "envs": {
          "ENV": "production",
          "LOG_LEVEL": "debug",
          "SIDECAR_ENABLED": "true"
        },
        "canaryReplicas": 2,
        "prodReplicas": 5,
        "trafficRatio": 30
      }
    ]
  }'
```

### 5. 再次执行Workflow

再次执行同一个workflow，系统会使用更新后的镜像配置：

```bash
curl -X POST http://localhost:8080/api/v1/workflows/{workflow_id}/execute \
  -H "Content-Type: application/json" \
  -d '{
    "executorId": "admin",
    "executorName": "管理员",
    "triggerType": "manual"
  }'
```

**此次部署使用的镜像配置：**
- `web-app`: `nginx:1.22.0` (更新后的版本)
- `sidecar`: `envoy:v1.19.0` (更新后的版本)
- `log-collector`: `fluentd:v1.13.0` (更新后的版本)

## 配置优先级

系统按以下优先级合并配置：

1. **Canary配置优先级最高**: canary模型中的`Images`和`Envs`会覆盖workflow中的对应配置
2. **Workflow配置作为基础**: 如果canary配置中没有某个字段，则使用workflow中的配置
3. **环境变量合并**: canary配置中的环境变量会与workflow配置合并，canary配置优先

## 日志示例

执行部署时的日志输出：

```
INFO updating images for service web-service from canary config
INFO executing canary deployment step: 部署web-service灰度版本, service: web-service, image: nginx:1.22.0
INFO deploying canary version for service: web-service, image: nginx:1.22.0, replicas: 2
INFO created canary deployment: web-service-canary
```

## 故障处理

如果无法获取canary配置，系统会：

1. 记录错误日志但不中断执行
2. 使用workflow中的原始配置继续部署
3. 在日志中标记使用了fallback配置

```
ERROR failed to update images from canary config: no canary config found for workflow web-service-canary, using workflow config
INFO executing canary deployment step: 部署web-service灰度版本, service: web-service, image: nginx:1.20.0
```

## 最佳实践

1. **保持配置同步**: 确保canary配置与workflow配置中的服务名称一致
2. **镜像版本管理**: 在canary配置中使用具体的镜像版本标签，避免使用`latest`
3. **环境变量管理**: 将环境相关的配置放在canary配置中，便于动态调整
4. **监控部署**: 关注部署日志，确认使用了正确的镜像配置
5. **回滚准备**: 保留旧版本的canary配置，便于快速回滚

## API参考

### 获取Canary配置
```bash
GET /api/v1/canary/{uuid}
```

### 更新Canary配置
```bash
PUT /api/v1/canary/{uuid}
```

### 执行Workflow
```bash
POST /api/v1/workflows/{id}/execute
```

### 查看执行日志
```bash
GET /api/v1/workflows/executions/{execution_id}/logs
```
