# Istio集成使用指南

## 概述

当前的路由配置**完全兼容**Istio的VirtualService和DestinationRule配置。我们使用Istio官方的SDK包，可以将灰度发布配置直接转换为Istio原生配置。

## 🎯 SDK包使用

### 导入的包
```go
import (
    networkingv1beta1 "istio.io/api/networking/v1beta1"
    istio "istio.io/client-go/pkg/apis/networking/v1beta1"
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)
```

### 主要优势
1. **官方SDK** - 使用Istio官方的client-go包
2. **类型安全** - 完全的类型检查和编译时验证
3. **版本兼容** - 自动跟随Istio版本更新
4. **无重复定义** - 直接复用Istio的结构体定义

## 🎯 兼容性映射

### 1. Header路由配置
```json
{
  "routingConfig": {
    "strategy": "header",
    "headerRouting": {
      "headerName": "client-id",
      "headerValue": "gray-user"
    }
  }
}
```

**转换为Istio VirtualService：**
```yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-name-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-name
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-name
  http:
  - name: canary-header-route
    match:
    - headers:
        client-id:
          exact: gray-user
    route:
    - destination:
        host: service-name
        subset: canary
      weight: 100
  - name: stable-route
    route:
    - destination:
        host: service-name
        subset: stable
      weight: 100
```

### 2. 权重路由配置
```json
{
  "routingConfig": {
    "strategy": "weight",
    "weightRouting": {
      "canaryWeight": 30,
      "stableWeight": 70
    }
  }
}
```

**转换为Istio VirtualService：**
```yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-name-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-name
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-name
  http:
  - name: weight-based-route
    route:
    - destination:
        host: service-name
        subset: canary
      weight: 30
    - destination:
        host: service-name
        subset: stable
      weight: 70
```

### 3. Cookie路由配置
```json
{
  "routingConfig": {
    "strategy": "cookie",
    "cookieRouting": {
      "cookieName": "canary-user",
      "cookieValue": "true"
    }
  }
}
```

**转换为Istio VirtualService：**
```yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-name-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-name
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-name
  http:
  - name: canary-cookie-route
    match:
    - headers:
        cookie:
          regex: ".*canary-user=true.*"
    route:
    - destination:
        host: service-name
        subset: canary
      weight: 100
  - name: stable-route
    route:
    - destination:
        host: service-name
        subset: stable
      weight: 100
```

## 📋 代码使用示例

### 单个服务转换
```go
package main

import (
    "fmt"
    "encoding/json"
    "pilot-api/pkg/models"
    networkingv1beta1 "istio.io/api/networking/v1beta1"
    istio "istio.io/client-go/pkg/apis/networking/v1beta1"
)

func main() {
    // 创建服务配置
    service := models.CanaryService{
        ServiceName: "user-service",
        Namespace:   "default",
        RoutingConfig: &models.ServiceRoutingConfig{
            Strategy: "header",
            HeaderRouting: &models.HeaderRoutingConfig{
                HeaderName:  "client-id",
                HeaderValue: "gray-user-123",
            },
        },
    }

    // 转换为Istio VirtualService
    vs := service.ToIstioVirtualService("default")
    fmt.Printf("VirtualService: %+v\n", vs)

    // 转换为Istio DestinationRule
    dr := service.ToIstioDestinationRule("default")
    fmt.Printf("DestinationRule: %+v\n", dr)

    // 可以直接序列化为YAML或JSON
    vsJSON, _ := json.MarshalIndent(vs, "", "  ")
    fmt.Printf("VirtualService JSON:\n%s\n", string(vsJSON))
}
```

### 全链路服务转换
```go
func convertFullChainCanary() {
    // 创建全链路灰度配置
    canary := models.Canary{
        Name:      "full-chain-canary",
        Namespace: "default",
        Services: []models.CanaryService{
            {
                ServiceName: "service-a",
                Order:       0,
                RoutingConfig: &models.ServiceRoutingConfig{
                    Strategy: "header",
                    HeaderRouting: &models.HeaderRoutingConfig{
                        HeaderName:  "client-id",
                        HeaderValue: "gray-user",
                    },
                },
            },
            {
                ServiceName: "service-b",
                Order:       1,
                RoutingConfig: &models.ServiceRoutingConfig{
                    Strategy: "weight",
                    WeightRouting: &models.WeightRoutingConfig{
                        CanaryWeight: 30,
                        StableWeight: 70,
                    },
                },
            },
        },
    }

    // 转换为Istio配置
    virtualServices, destinationRules := canary.ToIstioConfigs()

    fmt.Printf("Generated %d VirtualServices and %d DestinationRules\n",
        len(virtualServices), len(destinationRules))

    // 可以直接应用到Kubernetes集群
    for _, vs := range virtualServices {
        fmt.Printf("VirtualService: %s\n", vs.Name)
        // 使用Istio client应用到集群
        // istioClient.NetworkingV1beta1().VirtualServices(vs.Namespace).Create(ctx, vs, metav1.CreateOptions{})
    }
}
```

### 与Kubernetes集成
```go
import (
    "context"
    istioclient "istio.io/client-go/pkg/clientset/versioned"
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
    "k8s.io/client-go/kubernetes"
    "k8s.io/client-go/tools/clientcmd"
)

func applyIstioConfigs(canary *models.Canary) error {
    // 创建Kubernetes客户端
    config, err := clientcmd.BuildConfigFromFlags("", "~/.kube/config")
    if err != nil {
        return err
    }

    // 创建Istio客户端
    istioClient, err := istioclient.NewForConfig(config)
    if err != nil {
        return err
    }

    // 转换配置
    virtualServices, destinationRules := canary.ToIstioConfigs()

    ctx := context.Background()

    // 应用VirtualService
    for _, vs := range virtualServices {
        _, err := istioClient.NetworkingV1beta1().
            VirtualServices(vs.Namespace).
            Create(ctx, vs, metav1.CreateOptions{})
        if err != nil {
            return fmt.Errorf("failed to create VirtualService %s: %v", vs.Name, err)
        }
    }

    // 应用DestinationRule
    for _, dr := range destinationRules {
        _, err := istioClient.NetworkingV1beta1().
            DestinationRules(dr.Namespace).
            Create(ctx, dr, metav1.CreateOptions{})
        if err != nil {
            return fmt.Errorf("failed to create DestinationRule %s: %v", dr.Name, err)
        }
    }

    return nil
}
```

## 🎯 主要优势

1. **零学习成本** - 用户无需了解Istio语法
2. **自动转换** - 一键生成标准Istio配置
3. **功能完整** - 支持所有主要路由策略
4. **扩展性强** - 可轻松添加新的路由类型
5. **官方SDK** - 使用Istio官方包，保证兼容性
6. **类型安全** - 编译时检查，避免运行时错误
7. **版本跟随** - 自动跟随Istio版本更新

## 📦 依赖管理

项目已经包含了必要的Istio依赖：
```go
require (
    istio.io/api v0.0.0-20211206163441-1a632586cbd4
    istio.io/client-go v1.12.1
    k8s.io/api v0.33.2
    k8s.io/apimachinery v0.33.2
    k8s.io/client-go v0.33.2
)
```

这样，我们就实现了与Istio的完美集成，既保持了代码的简洁性，又确保了与Istio生态系统的完全兼容。
