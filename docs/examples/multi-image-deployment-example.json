{"name": "多镜像服务灰度发布示例", "description": "展示如何配置多容器服务的灰度发布", "namespace": "default", "clusterId": "cluster-1", "type": "canary", "services": [{"name": "web-service", "order": 1, "images": {"web-app": "nginx:1.21.0", "sidecar": "envoy:v1.18.0", "log-collector": "fluentd:v1.12.0"}, "version": "v1.0.0", "replicas": 3, "canaryReplicas": 1, "trafficRatio": 10, "resources": {"cpu_request": "100m", "memory_request": "128Mi", "cpu_limit": "500m", "memory_limit": "512Mi"}, "environment": {"ENV": "production", "LOG_LEVEL": "info", "SIDECAR_ENABLED": "true"}, "routing": {"strategy": "weight", "weights": [{"version": "canary", "weight": 10}, {"version": "stable", "weight": 90}]}, "healthCheck": {"path": "/health", "port": 8080, "initialDelaySeconds": 10, "periodSeconds": 30, "timeoutSeconds": 5, "successThreshold": 1, "failureThreshold": 3}, "strategy": "canary"}, {"name": "api-service", "order": 2, "images": {"api-app": "myapp/api:v2.0.0", "redis": "redis:6.2.0", "monitoring": "prometheus/node-exporter:v1.2.0"}, "version": "v2.0.0", "replicas": 5, "canaryReplicas": 2, "trafficRatio": 20, "resources": {"cpu_request": "200m", "memory_request": "256Mi", "cpu_limit": "1000m", "memory_limit": "1Gi"}, "environment": {"ENV": "production", "DB_HOST": "db.example.com", "REDIS_URL": "redis://localhost:6379", "API_VERSION": "v2"}, "routing": {"strategy": "header", "headers": [{"name": "X-Canary-User", "value": "beta-user", "match": "exact"}]}, "dependencies": ["web-service"], "strategy": "canary"}], "globalConfig": {"globalRoutingStrategy": "weight", "globalHeaders": {"X-Request-ID": "auto-generated", "X-Trace-ID": "auto-generated"}, "globalMonitoring": {"enabled": true, "duration": "10m", "interval": "30s", "metrics": ["http_requests_total", "http_request_duration_seconds", "cpu_usage", "memory_usage"]}, "globalRollback": {"enabled": true, "autoRollback": true, "rollbackTrigger": "threshold", "thresholdConfig": "{\"errorRate\": 5.0, \"latencyP95\": 1000}"}, "globalTimeout": {"stepTimeout": 1800, "approvalTimeout": 3600, "monitoringTimeout": 600, "rollbackTimeout": 300}}, "steps": [{"name": "审批web-service灰度发布", "type": "approval", "order": 1, "serviceName": "web-service", "config": {"approvalConfig": {"approvers": ["<EMAIL>", "<EMAIL>"], "conditionType": "or", "timeout": 3600}}, "autoExecute": false, "timeout": 3600}, {"name": "审批api-service灰度发布", "type": "approval", "order": 2, "serviceName": "api-service", "config": {"approvalConfig": {"approvers": ["<EMAIL>", "<EMAIL>"], "conditionType": "and", "timeout": 3600}}, "autoExecute": false, "timeout": 3600}, {"name": "审批生产发布", "type": "approval", "order": 10, "config": {"approvalConfig": {"approvers": ["<EMAIL>", "<EMAIL>"], "conditionType": "or", "timeout": 7200}}, "autoExecute": false, "timeout": 7200}]}