# Istio集成示例 - 展示如何将当前路由配置转换为Istio VirtualService和DestinationRule

# 1. 原始的灰度发布配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: canary-config-example
  namespace: default
data:
  canary.json: |
    {
      "name": "full-chain-canary",
      "description": "全链路灰度发布示例",
      "namespace": "default",
      "clusterId": "cluster-1",
      "services": [
        {
          "serviceName": "service-a",
          "namespace": "default",
          "order": 0,
          "canaryImage": "service-a:v2.0",
          "productionImage": "service-a:v1.0",
          "canaryReplicas": 2,
          "prodReplicas": 3,
          "trafficRatio": 20,
          "routingConfig": {
            "strategy": "header",
            "headerRouting": {
              "headerName": "client-id",
              "headerValue": "gray-user"
            }
          }
        },
        {
          "serviceName": "service-b",
          "namespace": "default",
          "order": 1,
          "canaryImage": "service-b:v2.0",
          "productionImage": "service-b:v1.0",
          "canaryReplicas": 1,
          "prodReplicas": 2,
          "trafficRatio": 30,
          "routingConfig": {
            "strategy": "weight",
            "weightRouting": {
              "canaryWeight": 30,
              "stableWeight": 70
            }
          }
        },
        {
          "serviceName": "service-c",
          "namespace": "default",
          "order": 2,
          "canaryImage": "service-c:v2.0",
          "productionImage": "service-c:v1.0",
          "canaryReplicas": 1,
          "prodReplicas": 2,
          "trafficRatio": 10,
          "routingConfig": {
            "strategy": "cookie",
            "cookieRouting": {
              "cookieName": "canary-user",
              "cookieValue": "true"
            }
          }
        }
      ]
    }

---
# 2. 转换后的Istio VirtualService配置

# Service A - 基于请求头的路由
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-a-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-a
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-a
  http:
  - name: canary-header-route
    match:
    - headers:
        client-id:
          exact: gray-user
    route:
    - destination:
        host: service-a
        subset: canary
      weight: 100
  - name: stable-route
    route:
    - destination:
        host: service-a
        subset: stable
      weight: 100

---
# Service B - 基于权重的路由
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-b-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-b
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-b
  http:
  - name: weight-based-route
    route:
    - destination:
        host: service-b
        subset: canary
      weight: 30
    - destination:
        host: service-b
        subset: stable
      weight: 70

---
# Service C - 基于Cookie的路由
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: service-c-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-c
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-c
  http:
  - name: canary-cookie-route
    match:
    - headers:
        cookie:
          regex: ".*canary-user=true.*"
    route:
    - destination:
        host: service-c
        subset: canary
      weight: 100
  - name: stable-route
    route:
    - destination:
        host: service-c
        subset: stable
      weight: 100

---
# 3. 对应的Istio DestinationRule配置

# Service A DestinationRule
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: service-a-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-a
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  host: service-a
  subsets:
  - name: canary
    labels:
      version: canary
  - name: stable
    labels:
      version: stable

---
# Service B DestinationRule
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: service-b-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-b
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  host: service-b
  subsets:
  - name: canary
    labels:
      version: canary
  - name: stable
    labels:
      version: stable

---
# Service C DestinationRule
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: service-c-canary
  namespace: default
  labels:
    app.kubernetes.io/name: service-c
    app.kubernetes.io/component: canary
    app.kubernetes.io/managed-by: pilot-api
spec:
  host: service-c
  subsets:
  - name: canary
    labels:
      version: canary
  - name: stable
    labels:
      version: stable

---
# 4. 全链路追踪配置（可选）
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: chain-tracing
  namespace: default
  labels:
    app.kubernetes.io/component: chain-tracing
    app.kubernetes.io/managed-by: pilot-api
spec:
  hosts:
  - service-a
  - service-b
  - service-c
  http:
  - headers:
      request:
        set:
          x-request-id: "%REQ(x-request-id)%"
          x-b3-traceid: "%REQ(x-b3-traceid)%"
          x-b3-spanid: "%REQ(x-b3-spanid)%"
          x-b3-parentspanid: "%REQ(x-b3-parentspanid)%"
          x-b3-sampled: "%REQ(x-b3-sampled)%"
          x-b3-flags: "%REQ(x-b3-flags)%"
          x-ot-span-context: "%REQ(x-ot-span-context)%"
    route:
    - destination:
        host: service-a
    - destination:
        host: service-b
    - destination:
        host: service-c

---
# 5. 网关配置（如果需要外部访问）
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: canary-gateway
  namespace: default
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "canary.example.com"

---
# 6. 入口VirtualService（连接Gateway）
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: canary-ingress
  namespace: default
spec:
  hosts:
  - "canary.example.com"
  gateways:
  - canary-gateway
  http:
  - match:
    - uri:
        prefix: "/api/v1/service-a"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: service-a
  - match:
    - uri:
        prefix: "/api/v1/service-b"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: service-b
  - match:
    - uri:
        prefix: "/api/v1/service-c"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: service-c
