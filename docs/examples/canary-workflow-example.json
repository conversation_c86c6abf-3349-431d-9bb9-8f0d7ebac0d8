{"name": "全链路灰度发布 - 用户服务链路", "description": "用户服务链路 A → B → C 的全链路灰度发布", "namespace": "default", "clusterId": "cluster-001", "services": [{"serviceName": "service-a", "namespace": "default", "order": 0, "canaryImage": "registry.example.com/service-a:v1.1.0", "productionImage": "registry.example.com/service-a:v1.0.0", "canaryReplicas": 2, "prodReplicas": 5, "trafficRatio": 20, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate", "response_time"], "healthCheck": {"path": "/health", "intervalSeconds": 30, "timeoutSeconds": 5, "successThreshold": 1, "failureThreshold": 3, "initialDelaySeconds": 10}, "resourceLimits": {"cpu": "500m", "memory": "512Mi"}}, "deploymentStatus": "pending", "healthStatus": "unknown"}, {"serviceName": "service-b", "namespace": "default", "order": 1, "canaryImage": "registry.example.com/service-b:v1.1.0", "productionImage": "registry.example.com/service-b:v1.0.0", "canaryReplicas": 3, "prodReplicas": 6, "trafficRatio": 20, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate"], "healthCheck": {"path": "/health", "intervalSeconds": 30, "timeoutSeconds": 5, "successThreshold": 1, "failureThreshold": 3, "initialDelaySeconds": 10}, "resourceLimits": {"cpu": "1000m", "memory": "1Gi"}}, "deploymentStatus": "pending", "healthStatus": "unknown"}, {"serviceName": "service-c", "namespace": "default", "order": 2, "canaryImage": "registry.example.com/service-c:v1.1.0", "productionImage": "registry.example.com/service-c:v1.0.0", "canaryReplicas": 2, "prodReplicas": 4, "trafficRatio": 20, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate"], "healthCheck": {"path": "/health", "intervalSeconds": 30, "timeoutSeconds": 5, "successThreshold": 1, "failureThreshold": 3, "initialDelaySeconds": 10}, "resourceLimits": {"cpu": "500m", "memory": "512Mi"}}, "deploymentStatus": "pending", "healthStatus": "unknown"}], "globalConfig": {"globalRoutingStrategy": "header", "globalHeaders": {"client-id": "gray-user", "trace-id": "canary-trace"}, "globalMonitoring": {"enableTracing": true, "traceHeaders": ["trace-id", "span-id"]}, "globalRollback": {"autoRollback": true, "errorThreshold": 5.0, "latencyThreshold": 1000.0}}, "canaryApprovers": ["<EMAIL>", "<EMAIL>"], "prodApprovers": ["<EMAIL>", "<EMAIL>"], "approvalType": "and", "monitorDuration": "10m", "monitorMetrics": ["cpu_usage", "memory_usage", "response_time", "error_rate"], "approvalTimeout": 3600, "stepTimeout": 1800, "labels": {"app": "user-service-chain", "version": "v1.1.0", "env": "production", "type": "canary"}}