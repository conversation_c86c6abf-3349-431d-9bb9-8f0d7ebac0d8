{"canaryUuid": "550e8400-e29b-41d4-a716-************", "services": [{"canaryUuid": "550e8400-e29b-41d4-a716-************", "serviceName": "service-a", "canaryImage": "registry.example.com/service-a:v1.2.0", "productionImage": "registry.example.com/service-a:v1.0.0", "canaryReplicas": 3, "prodReplicas": 5, "trafficRatio": 30, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate"]}}, {"canaryUuid": "550e8400-e29b-41d4-a716-************", "serviceName": "service-b", "canaryImage": "registry.example.com/service-b:v1.2.0", "productionImage": "registry.example.com/service-b:v1.0.0", "canaryReplicas": 4, "prodReplicas": 6, "trafficRatio": 30, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate"]}}, {"canaryUuid": "550e8400-e29b-41d4-a716-************", "serviceName": "service-c", "canaryImage": "registry.example.com/service-c:v1.2.0", "productionImage": "registry.example.com/service-c:v1.0.0", "canaryReplicas": 2, "prodReplicas": 4, "trafficRatio": 30, "routingConfig": {"strategy": "header", "headerRouting": {"headerName": "client-id", "headerValue": "gray-user"}}, "policy": {"autoPromotion": false, "autoRollback": true, "autoRollbackMetrics": ["error_rate"]}}]}