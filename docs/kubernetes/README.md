# 创建用户

```bash
kubectl apply -f account.yaml
```

生成token, 有效期26280小时=3年

```bash
kubectl create token pilot-sa -n default --duration 26280h
```

测试

```bash
kubectl --token="eyJhbGciOiJSUzI1NiIsImtpZCI6Ikg2ak81dGZHVGFmTEl4TE1hKbmxld0RoenFCN21DQ016bzFiRE0ifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QYZ5lTedluInwQPB9Ze0WuC3iO5KKWwW2wotVBCAhgyJw05M79sfnV8VpVSOIpT5MTztOsisi3VlWfAyIhUyrwIcPVSlZt4BitDQ9UPtu0sNKZ36pQ_c4U-8n3tbU_107su2XbRao4n5Rw8HldEud5WCIRcQ6KPdrXpfivSfrvUBT0t5U89uGOqbmnwkEkI995INkjlgZOgw2ZzroOJzd9czLdWCWIW_mIBICjOUsRm8NjVEQR9-XOrNFsh3jn9bbWghTxE0iKy3lbffrd2zICs6WwGElujoe2-MyyBJeDos9HXp1Si0tFbpz24xWL8JXjVBAHVKMG-yrVV3RfHP0oTuB4LtKzEtOiUVpHBOZxxiZrKmo9iB49ConEwY4i2BgxWhiWLIokqc7N9iNk_ChnoncOU6sS3i5J6mRX2UoTEwXudoGMy3H0IVu8fH0Su6faQkuB3xyHxwFDsbnHF8VfBi0Hyd1d8Eo3saPL8GgZz0om_RxP-93jWcXyvcDXi6o0i_nLDgTxkGW6zjTVJatXn3xDppG6j6177AL7jn3oVwXFVALYXlN2Wnk6n5lEekxZLAedoxkI5Amk2HrOVSiO-LvvTZFVxBRFb4bq9ZMUIUz0YbGC65Hpy6hL9sV1-jKAwVBjJbbJXuCnioWxqyCI-5xMRsgi3xiK5GUKcMtic" get pods --server https://mb-eu-west-prod-dns-9stq42z3.hcp.westeurope.azmk8s.io:443 --insecure-skip-tls-verify=true -A
```

生成kubeconfig

```yaml
apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tJSUU2VENDQXRHZ0F3SUJBZ0lSQU5acTZTMzFyWktvSVBHSUk1dVE3aDB3RFFZSktvWklodmNOQVFFTEJRQXcKRFRFTE1Ba0dBMVVFQXhNQ1kyRXdJQmNOTWpNd09EQXhNREUxT0RBMldoZ1BNakExTXpBNE1ERXdNakE0TURaYQpNQTB4Q3pBSkJnTlZCQU1UQW1OaE1JSUNJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBZzhBTUlJQ0NnS0NBZ0VBCnlUZlNsTFJzQ08yYlNldnM4ZHRLeEc2UmRPWWJJNmdIa2JyT0UwQldwSkEySCtFNVlmNHBzOXg1OHJBNUpYeFMKcStta1U2QjFMRHhubjBnVlc0SGxXeDIwU0c4MDFtMjBFdXZqS2VXRVlzMlNDUVRDM2ZyeE45RE5FUEdhUDMvWAptTWJHT01aeGxUa1ZMRjBCNTlFWHlOY2JJYnNmQ0lkTlJVSmV3SUdiWVVCY3Bjb2dRRjhFc09idllXbEFjM0ZBCm9odURSWmZicENINnV6VDZYRWtvQ01vM2ZwS2lPZzhHMHQwM0xRQjUyNCthWm1kbEF0a0ZQRDlKd3BwYW9YRVkKY004bGFndHFzRjUxZnFrU2RGU0t1ODRMQzFPakZuNHRRdmEzMU1obzFPNlZub01ZeTNTR3YwZEpMZWFGeFhMZwp0Y08xNkdUZGppZ3NCdE5hNjBERHpqamwwcjNHTC9iR3JhMUVaL1ZDYmV4bkxseWZVSGdSSnVxdzY0N3N0RHdSCjRtSGM3ejduSGVUcDA1czBNdlFBeTh2UkdqOUNLUWk0OEdlVC8xRysxN0JGY21jNFAvSURzalIwUTFJb1VFbSsKam1rZmMwaG1tMVE5UGY1VVFKOFJXZmN4cW5QL3lWc3pjV2J5WDhFRjlXcXZjdGE4Nm03Zkt0d0x4VDRxRGRWUQpzV2FwSXBwdWpTZVVMdnlLYTdlMkdEcW5xZTh4MTJRdlFmaVJyNm15QlRjOTlCQnlKejZGSzZWMnpQTmRndjNQCnFmalJicVlJMzVNb0pzNkNSTGY1NFlwbVROVFNvMXNPVHFTZEhkVzRyTDhWSFVsUWhRUW9lL21xM2c5WDdEODcKMkZBMnhRaFFER3VoeHoxelVvTjZ2SW92eSt2T2xWV05Ya21rQ2tpQTlPRUNBd0VBQWFOQ01FQXdEZ1lEVlIwUApBUUgvQkFRREFnS2tNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHdIUVlEVlIwT0JCWUVGS0RWb0dmeVl5T2I3RGtQCnhrVFZrNjZoM0ZOZU1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQ0FRQm4vQjFxV1Rsa1lmamJYV01pSUNaTHlDZ0cKcThubERUdHozcTRRVW9rV3FZNUVROUZ1WFZyUnFobTErMDlxd1k1ZHFycnpwUCtFWXd1TzEzamorVEJVY1l1aApqdzBMMk1LTTFuTjVRWEJRWG9ZTUtpSE55UUNUMFFQaTlFdHpYZW9zMXc0cEMzUlhxdVBSZEl1Y2oxV3hRcXN2CkthNEdpUFVaVVovaXExVGdmaWN6dHlSV0VaQlZyKzVKZHNacmZmSDFMVjRxTTVvOWhnVEJKTnhGbE1wYXhQUW8KaGw2cUNlalBXL0pCU0RXU0txS1RFYklSMlpoR1pYT2F6TTJUZFI5WG56R3pyekxkdEVFVk85YnZSc0hFaHk5cQovdi92L3J6T2VGQjdjeFZ4SFpzZjBhcCs4WDJIb3RZKzJ5b3pIMVdYTjY1dmJ1YUJLZXNsa2oyTHlsRzd2WWZSCjVmeEhBeXVzY21OOW4zS1FEY3BJM1Y3d05JUGE5MUIvem5id3FWYjdmMW92UWZQRXNXaFN5VENBZ1pEUmtOT2EKUFQxdTUxNlpxdGEydWhOQ0dMOWRMQ0lhWGs3MnFPSHpaRmdWMENTSzE2QzNwbHlVa2JSclgxemFxdzZBYTA3SApEZWFtS0lNcW1pN0NwcE8wUXl5RFNxQlovY0hiL2RPQTBCQXpLWFhiQ2s0Zzg4clo4cFFCWnpoT3NGUFg4UDl3CmdOZ2ZWbmpWc2p1OEE2cG1lZ1Q1Y3Bqb2Y0LzJhTlg5U1d6enl1T1NHaDZSN1RlMmtHUkNnc2VXN0pZTDVjMXcKR2V5VjMyVlduWk92MnhScy94WW1MWEU1RWlCSEdjUHVYYStZMjVnTy8vNGtRZkhGM0FPRlgxMWErWkhXZzdXSgpvb0JSMEpPQXZDOXhvbkc5ZFE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    server: https://mb-eu-west-prod-dns-9stqz3.hcp.westeurope.azmk8s.io:443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: limited-sa
  name: kubernetes
current-context: kubernetes
kind: Config
preferences: {}
users:
- name: limited-sa
  user:
    token: eyJhbGciOiJSUzI1NiIsImtpZCI6Ikg2ak81dGZHVGFmTEl4TE1hKbmxld0RoenFCN21DQ016bzFiRE0ifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QYZ5lTedluInwQPB9Ze0WuC3iO5KKWwW2wotVBCAhgyJw05M79sfnV8VpVSOIpT5MTztOsisi3VlWfAyIhUyrwIcPVSlZt4BitDQ9UPtu0sNKZ36pQ_c4U-8n3tbU_107su2XbRao4n5Rw8HldEud5WCIRcQ6KPdrXpfivSfrvUBT0t5U89uGOqbmnwkEkI995INkjlgZOgw2ZzroOJzd9czLdWCWIW_mIBICjOUsRm8NjVEQR9-XOrNFsh3jn9bbWghTxE0iKy3lbffrd2zICs6WwGElujoe2-MyyBJeDos9HXp1Si0tFbpz24xWL8JXjVBAHVKMG-yrVV3RfHP0oTuB4LtKzEtOiUVpHBOZxxiZrKmo9iB49ConEwY4i2BgxWhiWLIokqc7N9iNk_ChnoncOU6sS3i5J6mRX2UoTEwXudoGMy3H0IVu8fH0Su6faQkuB3xyHxwFDsbnHF8VfBi0Hyd1d8Eo3saPL8GgZz0om_RxP-93jWcXyvcDXi6o0i_nLDgTxkGW6zjTVJatXn3xDppG6j6177AL7jn3oVwXFVALYXlN2Wnk6n5lEekxZLAedoxkI5Amk2HrOVSiO-LvvTZFVxBRFb4bq9ZMUIUz0YbGC65Hpy6hL9sV1-jKAwVBjJbbJXuCnioWxqyCI-5xMRsgi3xiK5GUKcMtic
```
