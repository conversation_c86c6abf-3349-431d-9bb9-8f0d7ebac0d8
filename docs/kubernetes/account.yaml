apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pilot-cluster-role
rules:
  # 只读权限：包括 deployments、services、pods 等常见资源
  - apiGroups:
      - ""
      - apps
      - apiextensions.k8s.io
      - apiregistration.k8s.io
      - authorization.k8s.io
    resources:
      - deployments
      - services
      - pods
      - horizontalpodautoscalers
      - apiservices
      - customresourcedefinitions
      - selfsubjectaccessreviews
      - namespaces
    verbs:
      - get
      - list
      - watch

  # networking.istio.io 中的 destinationrules 和 gateways 只读
  - apiGroups:
      - networking.istio.io
    resources:
      - destinationrules
      - gateways
    verbs:
      - get
      - list
      - watch

  # virtualservices：允许读写操作（包括 create/update/patch）
  - apiGroups:
      - networking.istio.io
    resources:
      - virtualservices
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: pilot-sa
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pilot-limited-cluster-role-binding
subjects:
  - kind: ServiceAccount
    name: pilot-sa
    namespace: default
roleRef:
  kind: ClusterRole
  name: pilot-cluster-role
  apiGroup: rbac.authorization.k8s.io
