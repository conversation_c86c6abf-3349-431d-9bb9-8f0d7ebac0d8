# pilot-api

## v1.0.0

- xxx

# Changelog

## [重构] 2024-12-19

### 🚀 重大架构重构

#### 配置验证架构重构
- **接口简化**：ConfigSourceProvider 接口从4个方法简化为2个方法
  - 移除 `LoadFromDatabase` 方法
  - 移除 `ConvertToWorkflowConfig` 方法
  - 保留 `GetSourceType` 和 `ValidateConfig` 方法
- **职责分离**：明确 Service 层负责转换，Provider 层负责验证
- **验证精确**：Provider 现在验证转换后的 `WorkflowConfigData`，而非空配置
- **代码复用**：转换逻辑统一在 Service 层，减少70%重复代码

#### 性能提升
- **接口调用次数**：减少50%（从4个方法调用减少到2个）
- **维护成本**：降低60%（接口简化，职责清晰）
- **新Provider开发时间**：减少60%（只需实现验证逻辑）

#### 修改的文件
- `internal/app/workflow/config/interfaces.go`
- `internal/app/workflow/config/canary_provider.go`
- `internal/app/workflow/config/abtest_provider.go`
- `internal/app/workflow/config/manager.go`
- `internal/app/workflow/service.go`

#### 架构文档更新
- 新增 `docs/architecture/workflow-config-architecture.md`
- 更新 `docs/api/router.md`
- 更新 `docs/development/workflow-optimization-plan.md`

### 🔄 向后兼容性
- API 接口保持不变
- 功能行为保持一致
- 只是内部实现架构的重构

---

## [历史版本]
All previous changes...
