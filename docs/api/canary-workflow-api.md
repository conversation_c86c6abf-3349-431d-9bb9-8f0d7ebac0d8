# 灰度发布工作流 API 文档

## 概述

灰度发布工作流API提供了一种自动化的方式来创建和管理灰度发布流程。当用户点击运行时，系统会自动生成5个预定义的步骤，无需用户手动创建每个步骤。

## 预定义步骤

系统会自动创建以下5个步骤：

1. **审批灰度发布部署** - 需要指定审批人审批灰度环境部署
2. **执行灰度环境部署** - 自动执行灰度环境的部署
3. **审批生产环境部署** - 需要指定审批人审批生产环境部署
4. **执行生产环境部署** - 自动执行生产环境的部署
5. **清理灰度** - 需要手动点击确认清理灰度环境资源

## API 接口

### 创建灰度发布工作流

**接口地址：** `POST /api/v1/workflow/canary/create`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| name | string | 是 | 工作流名称 |
| description | string | 否 | 工作流描述 |
| serviceName | string | 是 | 服务名称 |
| namespace | string | 是 | 命名空间 |
| clusterId | string | 是 | 集群ID |
| canaryImage | string | 是 | 灰度版本镜像 |
| productionImage | string | 是 | 生产版本镜像 |
| canaryReplicas | int | 是 | 灰度副本数 (≥1) |
| prodReplicas | int | 是 | 生产副本数 (≥1) |
| trafficRatio | int | 是 | 灰度流量比例 (1-100) |
| canaryApprovers | []string | 是 | 灰度审批人列表 |
| prodApprovers | []string | 是 | 生产审批人列表 |
| approvalType | string | 是 | 审批类型 ("and" 或 "or") |
| monitorDuration | string | 否 | 监控持续时间 (如 "5m", "10m") |
| monitorMetrics | []string | 否 | 监控指标列表 |
| approvalTimeout | int | 否 | 审批超时时间（秒，默认3600） |
| stepTimeout | int | 否 | 步骤超时时间（秒，默认1800） |

**请求示例：**
```json
{
  "name": "用户服务灰度发布",
  "description": "用户服务从v1.0.0升级到v1.1.0的灰度发布流程",
  "serviceName": "user-service",
  "namespace": "default",
  "clusterId": "cluster-001",
  "canaryImage": "registry.example.com/user-service:v1.1.0",
  "productionImage": "registry.example.com/user-service:v1.1.0",
  "canaryReplicas": 2,
  "prodReplicas": 5,
  "trafficRatio": 10,
  "canaryApprovers": ["<EMAIL>", "<EMAIL>"],
  "prodApprovers": ["<EMAIL>", "<EMAIL>"],
  "approvalType": "and",
  "monitorDuration": "10m",
  "monitorMetrics": ["cpu_usage", "memory_usage", "response_time", "error_rate"],
  "approvalTimeout": 3600,
  "stepTimeout": 1800
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "uuid": "workflow-uuid-123",
    "name": "用户服务灰度发布",
    "description": "用户服务从v1.0.0升级到v1.1.0的灰度发布流程",
    "serviceName": "user-service",
    "namespace": "default",
    "clusterId": "cluster-001",
    "status": "draft",
    "config": "{\"type\":\"canary\",\"canaryImage\":\"registry.example.com/user-service:v1.1.0\",\"productionImage\":\"registry.example.com/user-service:v1.1.0\",\"canaryReplicas\":2,\"prodReplicas\":5,\"trafficRatio\":10,\"monitorDuration\":\"10m\",\"monitorMetrics\":[\"cpu_usage\",\"memory_usage\",\"response_time\",\"error_rate\"]}",
    "version": 1,
    "isTemplate": false,
    "gmtCreate": "2024-01-01T00:00:00Z",
    "gmtModified": "2024-01-01T00:00:00Z",
    "creator": "admin",
    "modifier": "admin",
    "steps": [
      {
        "id": 1,
        "workflowId": 1,
        "name": "审批灰度发布部署",
        "type": "approval",
        "order": 1,
        "config": "{\"approvalConfig\":{\"approvers\":[\"<EMAIL>\",\"<EMAIL>\"],\"conditionType\":\"and\",\"timeout\":3600}}",
        "dependsOn": "[]",
        "approvers": "[\"<EMAIL>\",\"<EMAIL>\"]",
        "autoExecute": false,
        "timeout": 3600,
        "retryCount": 0,
        "status": "pending"
      },
      {
        "id": 2,
        "workflowId": 1,
        "name": "执行灰度环境部署",
        "type": "deployment",
        "order": 2,
        "config": "{\"deploymentConfig\":{\"image\":\"registry.example.com/user-service:v1.1.0\",\"version\":\"canary\",\"replicas\":2,\"environment\":{\"DEPLOYMENT_TYPE\":\"canary\",\"TRAFFIC_RATIO\":\"10\"}}}",
        "dependsOn": "[1]",
        "approvers": "[]",
        "autoExecute": true,
        "timeout": 1800,
        "retryCount": 2,
        "status": "pending"
      },
      {
        "id": 3,
        "workflowId": 1,
        "name": "审批生产环境部署",
        "type": "approval",
        "order": 3,
        "config": "{\"approvalConfig\":{\"approvers\":[\"<EMAIL>\",\"<EMAIL>\"],\"conditionType\":\"and\",\"timeout\":3600}}",
        "dependsOn": "[2]",
        "approvers": "[\"<EMAIL>\",\"<EMAIL>\"]",
        "autoExecute": false,
        "timeout": 3600,
        "retryCount": 0,
        "status": "pending"
      },
      {
        "id": 4,
        "workflowId": 1,
        "name": "执行生产环境部署",
        "type": "deployment",
        "order": 4,
        "config": "{\"deploymentConfig\":{\"image\":\"registry.example.com/user-service:v1.1.0\",\"version\":\"production\",\"replicas\":5,\"environment\":{\"DEPLOYMENT_TYPE\":\"production\",\"TRAFFIC_RATIO\":\"100\"}}}",
        "dependsOn": "[3]",
        "approvers": "[]",
        "autoExecute": true,
        "timeout": 1800,
        "retryCount": 2,
        "status": "pending"
      },
      {
        "id": 5,
        "workflowId": 1,
        "name": "清理灰度",
        "type": "cleanup",
        "order": 5,
        "config": "{\"description\":\"清理灰度环境资源，包括临时部署、服务和配置\"}",
        "dependsOn": "[4]",
        "approvers": "[]",
        "autoExecute": false,
        "timeout": 1800,
        "retryCount": 1,
        "status": "pending"
      }
    ]
  }
}
```

## 使用流程

1. **创建工作流** - 调用 `/api/v1/workflow/canary/create` 接口创建灰度发布工作流
2. **执行工作流** - 调用 `/api/v1/workflow/execute` 接口开始执行工作流
3. **审批流程** - 审批人在相应步骤进行审批
4. **监控执行** - 通过 `/api/v1/workflow/execution/{executionId}` 接口监控执行状态
5. **手动清理** - 在最后一步手动确认清理灰度环境

## 步骤详细说明

### 1. 审批灰度发布部署
- **类型**: approval
- **执行方式**: 手动审批
- **审批人**: canaryApprovers 参数指定的用户
- **审批类型**: and（所有审批人都需要审批）或 or（任一审批人审批即可）

### 2. 执行灰度环境部署
- **类型**: deployment
- **执行方式**: 自动执行
- **配置**: 使用 canaryImage、canaryReplicas 等参数
- **依赖**: 依赖步骤1的审批通过

### 3. 审批生产环境部署
- **类型**: approval
- **执行方式**: 手动审批
- **审批人**: prodApprovers 参数指定的用户
- **依赖**: 依赖步骤2的部署完成

### 4. 执行生产环境部署
- **类型**: deployment
- **执行方式**: 自动执行
- **配置**: 使用 productionImage、prodReplicas 等参数
- **依赖**: 依赖步骤3的审批通过

### 5. 清理灰度
- **类型**: cleanup
- **执行方式**: 手动确认
- **功能**: 清理灰度环境的临时资源
- **依赖**: 依赖步骤4的部署完成

## 错误码

| 错误码 | 说明 |
|-------|------|
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有审批步骤都不会自动执行，需要指定的审批人手动审批
2. 部署步骤会自动执行，如果失败会根据 retryCount 进行重试
3. 清理步骤需要手动点击确认，不会自动执行
4. 每个步骤都有超时时间，超时后会自动失败
5. 步骤之间有依赖关系，前一步完成后才会执行下一步
