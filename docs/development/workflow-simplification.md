# Workflow系统简化文档

## 概述

本文档记录了对Workflow系统的重大简化，移除了复杂的请求结构体，确保所有配置都来源于Canary模型。

## 主要变更

### 1. 删除的请求结构体

以下结构体已被完全删除，因为所有配置现在都来源于canary：

- `WorkflowServiceRequest` - 工作流服务请求
- `WorkflowServiceRoutingRequest` - 工作流服务路由请求
- `WorkflowServiceRuleRequest` - 工作流服务规则请求
- `WorkflowServiceWeightRequest` - 工作流服务权重请求
- `WorkflowServiceHeaderRequest` - 工作流服务头部路由请求
- `WorkflowServiceCookieRequest` - 工作流服务Cookie路由请求
- `WorkflowServiceHealthCheckRequest` - 工作流服务健康检查请求
- `WorkflowServiceMonitoringRequest` - 工作流服务监控请求
- `WorkflowServiceThresholdRequest` - 工作流服务阈值请求
- `WorkflowGlobalRequest` - 工作流全局配置请求
- `WorkflowGlobalMonitoringRequest` - 工作流全局监控请求
- `WorkflowGlobalRollbackRequest` - 工作流全局回滚请求
- `WorkflowGlobalTimeoutRequest` - 工作流全局超时请求
- `WorkflowGlobalNotificationRequest` - 工作流全局通知请求
- `WorkflowApprovalRequest` - 工作流审批请求
- `WorkflowMonitoringRequest` - 工作流监控请求
- `WorkflowChainMonitoringRequest` - 工作流链路监控请求
- `WorkflowChainMetricRequest` - 工作流链路指标请求
- `WorkflowChainAlertRequest` - 工作流链路告警请求
- `WorkflowRollbackRequest` - 工作流回滚请求
- `WorkflowServiceRollbackRequest` - 工作流服务回滚请求
- `WorkflowChainRollbackRequest` - 工作流链路回滚请求
- `WorkflowChainRollbackRuleRequest` - 工作流链路回滚规则请求

### 2. 简化的CreateCanaryWorkflowRequest

现在的`CreateCanaryWorkflowRequest`结构体大大简化：

```go
type CreateCanaryWorkflowRequest struct {
	Name        string `json:"name"`        // 工作流名称（如果提供了canaryUuid，则可选）
	Description string `json:"description"` // 工作流描述

	// 从canary配置创建工作流的选项
	CanaryUUID string `json:"canaryUuid,omitempty"` // Canary配置UUID

	// 向后兼容字段（已废弃，建议使用canaryUuid）
	ServiceName     string   `json:"serviceName,omitempty"`     // 主要服务名(向后兼容)
	Namespace       string   `json:"namespace,omitempty"`       // 命名空间（向后兼容）
	ClusterID       string   `json:"clusterId,omitempty"`       // 集群ID（向后兼容）
	CanaryImage     string   `json:"canaryImage,omitempty"`     // 灰度版本镜像（向后兼容）
	ProductionImage string   `json:"productionImage,omitempty"` // 生产版本镜像（向后兼容）
	CanaryReplicas  int32    `json:"canaryReplicas,omitempty"`  // 灰度副本数（向后兼容）
	ProdReplicas    int32    `json:"prodReplicas,omitempty"`    // 生产副本数（向后兼容）
	TrafficRatio    int32    `json:"trafficRatio,omitempty"`    // 灰度流量比例（向后兼容）
	CanaryApprovers []string `json:"canaryApprovers,omitempty"` // 灰度审批人（向后兼容）
	ProdApprovers   []string `json:"prodApprovers,omitempty"`   // 生产审批人（向后兼容）
	ApprovalType    string   `json:"approvalType,omitempty"`    // 审批类型（向后兼容）
	MonitorDuration string   `json:"monitorDuration,omitempty"` // 监控持续时间（向后兼容）
	MonitorMetrics  []string `json:"monitorMetrics,omitempty"`  // 监控指标（向后兼容）
	ApprovalTimeout int      `json:"approvalTimeout,omitempty"` // 审批超时时间（向后兼容）
	StepTimeout     int      `json:"stepTimeout,omitempty"`     // 步骤超时时间（向后兼容）
}
```

### 3. 删除的服务函数

以下函数已被删除，因为它们使用了已删除的结构体：

- `createChainCanarySteps` - 创建全链路灰度发布步骤
- `createServiceSteps` - 为单个服务创建步骤
- `buildServiceApprovalConfig` - 构建服务审批配置
- `buildServiceDeploymentConfig` - 构建服务部署配置
- `buildServiceTrafficConfig` - 构建服务流量配置
- `buildServiceMonitoringConfig` - 构建服务监控配置
- `buildServiceCleanupConfig` - 构建服务清理配置
- `buildServicesConfig` - 构建服务配置
- `buildServiceRouting` - 构建服务路由配置
- `buildServiceHealthCheck` - 构建服务健康检查配置
- `buildServiceMonitoring` - 构建服务监控配置
- `convertToServiceRoutingConfig` - 转换服务路由配置
- `buildGlobalConfig` - 构建全局配置
- `buildDependenciesConfig` - 构建依赖关系配置
- `buildApprovalSteps` - 构建审批步骤配置
- `buildMonitoringConfig` - 构建监控配置
- `buildRollbackConfig` - 构建回滚配置

### 4. 保留的功能

#### 4.1 从Canary配置创建工作流

主要功能通过以下方法实现：

- `CreateWorkflowFromCanary` - 从canary配置创建工作流
- `createWorkflowFromCanaryUUID` - 从canary UUID创建工作流
- `buildServicesFromCanary` - 从canary配置构建服务配置
- `buildGlobalConfigFromCanary` - 从canary配置构建全局配置
- `createStepsFromCanary` - 从canary配置创建步骤

#### 4.2 传统模式支持

为了向后兼容，保留了传统模式的支持：

- `createLegacyCanarySteps` - 创建传统灰度发布步骤
- `buildLegacyServicesConfig` - 构建传统服务配置
- `buildLegacyApprovalSteps` - 构建传统审批步骤配置
- `buildLegacyMonitoringConfig` - 构建传统监控配置

### 5. 工作流创建方式

#### 5.1 推荐方式：从Canary配置创建

```json
{
  "name": "my-workflow",
  "description": "从canary配置创建的工作流",
  "canaryUuid": "550e8400-e29b-41d4-a716-************"
}
```

#### 5.2 传统方式（向后兼容）

```json
{
  "name": "legacy-workflow",
  "description": "传统方式创建的工作流",
  "serviceName": "my-service",
  "namespace": "default",
  "clusterId": "cluster-1",
  "canaryImage": "my-app:v2.0",
  "productionImage": "my-app:v1.0",
  "canaryReplicas": 1,
  "prodReplicas": 3,
  "trafficRatio": 10,
  "canaryApprovers": ["<EMAIL>"],
  "prodApprovers": ["<EMAIL>"],
  "approvalType": "or",
  "monitorDuration": "30m",
  "monitorMetrics": ["success_rate", "response_time"],
  "approvalTimeout": 3600,
  "stepTimeout": 1800
}
```

## 优势

### 1. 简化架构

- 删除了大量复杂的请求结构体
- 减少了代码维护成本
- 降低了系统复杂性

### 2. 统一配置来源

- 所有配置都来源于Canary模型
- 避免了配置分散和不一致的问题
- 提高了配置管理的统一性

### 3. 向后兼容

- 保留了传统模式的支持
- 现有的工作流不会受到影响
- 平滑迁移到新的架构

### 4. 更好的可维护性

- 代码结构更清晰
- 功能职责更明确
- 更容易进行功能扩展

## 迁移指南

### 对于新用户

建议直接使用Canary配置创建工作流：

1. 首先创建Canary配置
2. 使用Canary UUID创建工作流
3. 所有配置自动从Canary模型中获取

### 对于现有用户

现有的传统方式仍然支持，但建议逐步迁移：

1. 将现有配置迁移到Canary模型
2. 使用新的API创建工作流
3. 享受统一配置管理的好处

## 总结

此次简化大大降低了Workflow系统的复杂性，提高了可维护性，同时保持了向后兼容性。所有配置现在都统一来源于Canary模型，避免了配置分散和不一致的问题。
