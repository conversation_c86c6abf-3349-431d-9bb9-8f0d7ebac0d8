# Canary模型增强文档

## 概述

本文档记录了对Canary模型的增强修改，确保Canary配置与Workflow系统的完全兼容性和字段映射一致性。

## 主要修改

### 1. CanaryService结构体增强

#### 新增字段
- `Version string` - 版本号字段，用于与WorkflowServiceConfig保持一致
- `Resources map[string]string` - 资源配置字段，支持CPU、内存等资源限制
- `Dependencies []string` - 依赖服务字段，支持服务间依赖关系配置
- `Strategy string` - 部署策略字段，支持canary、blue-green、rolling等策略
- `MonitoringConfig *ServiceMonitoringConfig` - 监控配置字段，支持服务级别监控

#### 字段说明
```go
type CanaryService struct {
    ServiceName      string                      `json:"serviceName"`      // 服务名称
    Namespace        string                      `json:"namespace"`        // 命名空间
    Order            int                         `json:"order"`            // 执行顺序
    Images           map[string]string           `json:"images"`           // 多镜像支持
    Version          string                      `json:"version"`          // 版本号
    Envs             map[string]string           `json:"envs"`             // 环境变量
    Resources        map[string]string           `json:"resources"`        // 资源配置
    Dependencies     []string                    `json:"dependencies"`     // 依赖服务
    CanaryReplicas   int32                       `json:"canaryReplicas"`   // 灰度副本数
    ProdReplicas     int32                       `json:"prodReplicas"`     // 生产副本数
    TrafficRatio     int32                       `json:"trafficRatio"`     // 流量比例
    Strategy         string                      `json:"strategy"`         // 部署策略
    RoutingConfig    *ServiceRoutingConfig       `json:"routingConfig"`    // 路由配置
    MonitoringConfig *ServiceMonitoringConfig    `json:"monitoringConfig"` // 监控配置
    Policy           *CanaryServicePolicy        `json:"policy"`           // 服务策略
    DeploymentStatus string                      `json:"deploymentStatus"` // 部署状态
    HealthStatus     string                      `json:"healthStatus"`     // 健康状态
    Metrics          *ServiceMetrics             `json:"metrics"`          // 监控指标
}
```

### 2. CanaryGlobalConfig结构体增强

#### 新增字段
- `GlobalTimeout *GlobalTimeoutConfig` - 全局超时配置
- `GlobalNotification *GlobalNotificationConfig` - 全局通知配置

#### 字段说明
```go
type CanaryGlobalConfig struct {
    GlobalRoutingStrategy string                      `json:"globalRoutingStrategy"` // 全局路由策略
    GlobalHeaders         map[string]string           `json:"globalHeaders"`         // 全局请求头
    GlobalMonitoring      *GlobalMonitoringConfig     `json:"globalMonitoring"`      // 全局监控配置
    GlobalRollback        *GlobalRollbackConfig       `json:"globalRollback"`        // 全局回滚配置
    GlobalTimeout         *GlobalTimeoutConfig        `json:"globalTimeout"`         // 全局超时配置
    GlobalNotification    *GlobalNotificationConfig   `json:"globalNotification"`    // 全局通知配置
}
```

### 3. 新增配置结构体

#### ServiceMonitoringConfig - 服务监控配置
```go
type ServiceMonitoringConfig struct {
    Metrics    []string           `json:"metrics"`    // 监控指标
    Thresholds []ServiceThreshold `json:"thresholds"` // 阈值配置
    Duration   string             `json:"duration"`   // 监控持续时间
    Interval   string             `json:"interval"`   // 监控间隔
}
```

#### ServiceThreshold - 服务阈值
```go
type ServiceThreshold struct {
    Name     string  `json:"name"`     // 指标名称
    Operator string  `json:"operator"` // 操作符: >=, <=, >, <, ==
    Value    float64 `json:"value"`    // 阈值
    Unit     string  `json:"unit"`     // 单位
}
```

#### GlobalTimeoutConfig - 全局超时配置
```go
type GlobalTimeoutConfig struct {
    StepTimeout       int `json:"stepTimeout"`       // 步骤超时时间(秒)
    ApprovalTimeout   int `json:"approvalTimeout"`   // 审批超时时间(秒)
    MonitoringTimeout int `json:"monitoringTimeout"` // 监控超时时间(秒)
    RollbackTimeout   int `json:"rollbackTimeout"`   // 回滚超时时间(秒)
}
```

#### GlobalNotificationConfig - 全局通知配置
```go
type GlobalNotificationConfig struct {
    Enabled  bool     `json:"enabled"`  // 是否启用通知
    Channels []string `json:"channels"` // 通知渠道: email, slack, webhook
    Events   []string `json:"events"`   // 通知事件: start, success, failure, approval
    Template string   `json:"template"` // 通知模板
    Users    []string `json:"users"`    // 通知用户列表
}
```

### 4. WorkflowServiceConfig结构体增强

#### 新增字段
- `Images map[string]string` - 多镜像支持字段
- `Strategy string` - 部署策略字段

#### 字段说明
```go
type WorkflowServiceConfig struct {
    Name         string                      `json:"name"`         // 服务名称
    Order        int                         `json:"order"`        // 执行顺序
    Images       map[string]string           `json:"images"`       // 多镜像支持
    Image        string                      `json:"image"`        // 单镜像支持（向后兼容）
    Version      string                      `json:"version"`      // 版本号
    Replicas     int32                       `json:"replicas"`     // 副本数
    Resources    map[string]string           `json:"resources"`    // 资源配置
    Environment  map[string]string           `json:"environment"`  // 环境变量
    Strategy     string                      `json:"strategy"`     // 部署策略
    Routing      *WorkflowServiceRouting     `json:"routing"`      // 路由配置
    HealthCheck  *WorkflowServiceHealthCheck `json:"healthCheck"`  // 健康检查配置
    Monitoring   *WorkflowServiceMonitoring  `json:"monitoring"`   // 监控配置
    Dependencies []string                    `json:"dependencies"` // 依赖服务
}
```

### 5. 配置转换逻辑增强

#### 新增转换方法
- `convertCanaryMonitoringToWorkflowMonitoring` - 转换监控配置
- 增强 `buildGlobalConfigFromCanary` - 支持超时和通知配置转换
- 增强 `buildServicesFromCanary` - 支持所有新字段的转换

#### 配置映射关系
| Canary字段 | Workflow字段 | 说明 |
|-----------|-------------|------|
| `Images` | `Images` | 多镜像配置 |
| `Version` | `Version` | 版本号 |
| `Resources` | `Resources` | 资源配置 |
| `Dependencies` | `Dependencies` | 依赖服务 |
| `Strategy` | `Strategy` | 部署策略 |
| `MonitoringConfig` | `Monitoring` | 监控配置 |
| `GlobalTimeout` | `GlobalTimeout` | 全局超时配置 |
| `GlobalNotification` | `GlobalNotification` | 全局通知配置 |

## 向后兼容性

### 1. 保持现有字段
- 所有现有字段均保持不变
- 新增字段均为可选字段（omitempty）

### 2. 默认值设置
- `Version` 默认为 "latest"
- `Strategy` 默认为 "canary"
- 向后兼容单镜像配置

### 3. 配置转换
- 支持从单镜像到多镜像的自动转换
- 支持从旧配置格式到新配置格式的转换

## 使用示例

### 1. 完整的Canary服务配置
```json
{
  "serviceName": "my-service",
  "namespace": "production",
  "order": 1,
  "images": {
    "app": "my-service:v1.2.0",
    "sidecar": "sidecar:v1.0.0"
  },
  "version": "v1.2.0",
  "envs": {
    "ENVIRONMENT": "production",
    "DEBUG": "false"
  },
  "resources": {
    "cpu": "500m",
    "memory": "1Gi"
  },
  "dependencies": ["database-service"],
  "canaryReplicas": 2,
  "prodReplicas": 5,
  "trafficRatio": 20,
  "strategy": "canary",
  "routingConfig": {
    "strategy": "weight",
    "weightRouting": {
      "canaryWeight": 20,
      "stableWeight": 80
    }
  },
  "monitoringConfig": {
    "metrics": ["success_rate", "response_time", "error_rate"],
    "thresholds": [
      {
        "name": "success_rate",
        "operator": ">=",
        "value": 0.95,
        "unit": "percent"
      }
    ],
    "duration": "30m",
    "interval": "1m"
  }
}
```

### 2. 完整的Canary全局配置
```json
{
  "globalRoutingStrategy": "weight",
  "globalHeaders": {
    "X-Canary-Version": "v1.2.0",
    "X-Trace-ID": "trace-123"
  },
  "globalMonitoring": {
    "enableTracing": true,
    "traceHeaders": ["X-Trace-ID", "X-Request-ID"]
  },
  "globalRollback": {
    "autoRollback": true,
    "errorThreshold": 0.05,
    "latencyThreshold": 1000
  },
  "globalTimeout": {
    "stepTimeout": 1800,
    "approvalTimeout": 3600,
    "monitoringTimeout": 1800,
    "rollbackTimeout": 600
  },
  "globalNotification": {
    "enabled": true,
    "channels": ["email", "slack"],
    "events": ["start", "success", "failure", "approval"],
    "template": "canary-notification-template",
    "users": ["<EMAIL>", "<EMAIL>"]
  }
}
```

## 总结

通过这些增强，Canary模型现在完全支持：

1. **多镜像部署** - 支持在单个服务中部署多个容器镜像
2. **资源管理** - 支持CPU、内存等资源配置
3. **依赖管理** - 支持服务间依赖关系配置
4. **策略支持** - 支持多种部署策略（canary、blue-green、rolling）
5. **监控集成** - 支持服务级别和全局级别的监控配置
6. **超时管理** - 支持各种操作的超时配置
7. **通知系统** - 支持多渠道通知配置
8. **完整映射** - 与Workflow系统的完全字段映射一致性

这些增强确保了Canary配置可以作为Workflow系统的完整数据源，实现了真正的"所有配置来源于canary"的目标。
