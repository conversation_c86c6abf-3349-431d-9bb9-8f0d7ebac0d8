# 多镜像部署功能

## 概述

Pilot API 现在支持多镜像部署功能，允许您在单个服务中部署多个容器，每个容器可以有不同的镜像。系统会自动从canary模型中获取最新的镜像配置来更新部署，这对于需要 sidecar 容器、日志收集器、监控代理等场景非常有用。

## 功能特性

- **多容器支持**: 在单个 Kubernetes Deployment 中部署多个容器
- **镜像映射**: 通过 key-value 方式指定容器名称和对应的镜像
- **动态镜像更新**: 系统自动从canary模型中获取最新的镜像配置
- **环境变量共享**: 所有容器共享相同的环境变量配置，支持从canary配置中动态更新
- **资源配置**: 所有容器使用相同的资源限制配置

## 配置结构

### 基本配置

```json
{
  "name": "my-service",
  "images": {
    "main-app": "myapp:v1.0.0",
    "sidecar": "envoy:v1.18.0",
    "log-collector": "fluentd:v1.12.0"
  },
  "environment": {
    "ENV": "production",
    "LOG_LEVEL": "info"
  }
}
```

### 灰度发布配置

```json
{
  "name": "my-service",
  "images": {
    "main-app": "myapp:v1.0.1",
    "sidecar": "envoy:v1.18.1"
  },
  "canaryReplicas": 1,
  "replicas": 3
}
```

## 字段说明

### images (必需)
- **类型**: `map[string]string`
- **说明**: 容器名称到镜像地址的映射，系统会自动从canary模型中获取最新配置
- **示例**: `{"web": "nginx:1.21", "sidecar": "envoy:v1.18"}`
- **动态更新**: 当执行部署时，系统会自动从canary模型中获取最新的镜像配置来覆盖原始配置



### environment (可选)
- **类型**: `map[string]string`
- **说明**: 环境变量，将应用到所有容器
- **示例**: `{"ENV": "prod", "LOG_LEVEL": "info"}`

### resources (可选)
- **类型**: `map[string]string`
- **说明**: 资源配置，将应用到所有容器
- **示例**: `{"cpu_request": "100m", "memory_request": "128Mi"}`

## 使用示例

### 1. 创建canary配置

首先需要创建canary配置，其中包含服务的镜像信息：

```json
{
  "name": "web-service-canary",
  "namespace": "default",
  "clusterId": "cluster-1",
  "services": [
    {
      "serviceName": "web-service",
      "images": {
        "web-app": "nginx:1.21.1",
        "sidecar": "envoy:v1.18.1",
        "log-collector": "fluentd:v1.12.1"
      },
      "envs": {
        "ENV": "production",
        "LOG_LEVEL": "info"
      },
      "canaryReplicas": 1,
      "prodReplicas": 3,
      "trafficRatio": 50
    }
  ]
}
```

### 2. 创建多镜像服务工作流

```bash
curl -X POST http://localhost:8080/api/v1/workflows/canary \
  -H "Content-Type: application/json" \
  -d @multi-image-deployment-example.json
```

### 3. 更新canary配置中的镜像

当需要更新镜像时，只需更新canary配置中的镜像信息：

```json
{
  "services": [
    {
      "serviceName": "web-service",
      "images": {
        "web-app": "nginx:1.22.0",
        "sidecar": "envoy:v1.19.0",
        "log-collector": "fluentd:v1.13.0"
      }
    }
  ]
}
```

系统在执行部署步骤时会自动使用canary配置中的最新镜像。

### 4. 部署配置示例

```json
{
  "services": [
    {
      "name": "web-service",
      "order": 1,
         "images": {
         "web-app": "nginx:1.21.1",
         "sidecar": "envoy:v1.18.1",
         "log-collector": "fluentd:v1.12.1"
       },
      "replicas": 3,
      "canaryReplicas": 1,
      "environment": {
        "ENV": "production",
        "LOG_LEVEL": "info",
        "SIDECAR_ENABLED": "true"
      },
      "resources": {
        "cpu_request": "100m",
        "memory_request": "128Mi",
        "cpu_limit": "500m",
        "memory_limit": "512Mi"
      }
    }
  ]
}
```

## 生成的 Kubernetes 资源

### Deployment 示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-service-canary
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web-service
      version: canary
  template:
    metadata:
      labels:
        app: web-service
        version: canary
    spec:
      containers:
      - name: web-app
        image: nginx:1.21.1
        ports:
        - containerPort: 8080
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: ENV
          value: production
        - name: LOG_LEVEL
          value: info
        - name: SIDECAR_ENABLED
          value: "true"
      - name: sidecar
        image: envoy:v1.18.1
        ports:
        - containerPort: 8080
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: ENV
          value: production
        - name: LOG_LEVEL
          value: info
        - name: SIDECAR_ENABLED
          value: "true"
      - name: log-collector
        image: fluentd:v1.12.1
        ports:
        - containerPort: 8080
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: ENV
          value: production
        - name: LOG_LEVEL
          value: info
        - name: SIDECAR_ENABLED
          value: "true"
```

## 注意事项

1. **容器名称**: 容器名称必须符合 Kubernetes 命名规范
2. **镜像地址**: 确保所有镜像地址都是可访问的
3. **资源配置**: 所有容器共享相同的资源配置，请合理设置
4. **环境变量**: 所有容器共享相同的环境变量
5. **端口配置**: 当前所有容器都使用相同的端口配置 (8080)

## 最佳实践

1. **命名规范**: 使用清晰的容器名称，如 `main-app`, `sidecar`, `log-collector`
2. **镜像版本**: 使用具体的版本标签，避免使用 `latest`
3. **资源规划**: 根据实际需求合理分配 CPU 和内存资源
4. **监控配置**: 为多容器服务配置适当的监控和日志收集
5. **健康检查**: 确保主容器有适当的健康检查配置

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查镜像地址是否正确
   - 确认镜像是否存在且可访问
   - 查看容器日志获取详细错误信息

2. **资源不足**
   - 检查集群资源是否充足
   - 适当调整资源请求和限制

3. **环境变量问题**
   - 确认环境变量配置正确
   - 检查是否有敏感信息需要使用 Secret

### 调试命令

```bash
# 查看 Pod 状态
kubectl get pods -l app=web-service

# 查看 Pod 详细信息
kubectl describe pod <pod-name>

# 查看容器日志
kubectl logs <pod-name> -c <container-name>

# 进入容器调试
kubectl exec -it <pod-name> -c <container-name> -- /bin/bash
```
