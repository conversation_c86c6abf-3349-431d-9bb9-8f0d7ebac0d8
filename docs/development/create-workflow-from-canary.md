# 从Canary配置创建Workflow

## 概述

新的API端点 `/api/v1/workflow/canary/create-from-canary` 允许您直接从现有的canary配置创建工作流，无需手动配置复杂的参数。所有配置都会自动从canary模型中获取。

## API端点

```
POST /api/v1/workflow/canary/create-from-canary
```

## 请求参数

### CreateWorkflowFromCanaryRequest

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| canaryUuid | string | 是 | Canary配置的UUID |
| name | string | 否 | 工作流名称（可选，默认使用canary名称） |
| description | string | 否 | 工作流描述（可选，默认使用canary描述） |

## 使用示例

### 1. 基本用法

```bash
curl -X POST http://localhost:8080/api/v1/workflow/canary/create-from-canary \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "canaryUuid": "550e8400-e29b-41d4-a716-************"
  }'
```

### 2. 自定义名称和描述

```bash
curl -X POST http://localhost:8080/api/v1/workflow/canary/create-from-canary \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "canaryUuid": "550e8400-e29b-41d4-a716-************",
    "name": "web-service-v2-workflow",
    "description": "Web服务v2.0灰度发布工作流"
  }'
```

## 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "uuid": "workflow-uuid-here",
    "name": "web-service-canary-workflow",
    "description": "Web服务灰度发布配置 - 自动生成的工作流",
    "type": "canary",
    "serviceName": "web-service",
    "namespace": "default",
    "clusterId": "cluster-1",
    "status": "draft",
    "version": 1,
    "isTemplate": false,
    "services": "[{\"name\":\"web-service\",\"order\":1,\"version\":\"latest\",\"replicas\":3}]",
    "globalConfig": "{\"globalHeaders\":{\"X-Canary\":\"true\"}}",

    "steps": [
      {
        "id": 1,
        "workflowId": 123,
        "name": "审批web-service灰度发布",
        "type": "approval",
        "order": 1,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": false,
        "timeout": 3600,
        "status": "pending"
      },
      {
        "id": 2,
        "workflowId": 123,
        "name": "部署web-service灰度版本",
        "type": "deployment",
        "order": 2,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": true,
        "timeout": 1800,
        "retryCount": 2,
        "status": "pending"
      },
      {
        "id": 3,
        "workflowId": 123,
        "name": "配置web-service流量分发",
        "type": "traffic_split",
        "order": 3,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": true,
        "timeout": 1800,
        "status": "pending"
      },
      {
        "id": 4,
        "workflowId": 123,
        "name": "审批web-service生产发布",
        "type": "approval",
        "order": 4,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": false,
        "timeout": 3600,
        "status": "pending"
      },
      {
        "id": 5,
        "workflowId": 123,
        "name": "部署web-service生产版本",
        "type": "deployment",
        "order": 5,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": true,
        "timeout": 1800,
        "retryCount": 2,
        "status": "pending"
      },
      {
        "id": 6,
        "workflowId": 123,
        "name": "清理web-service灰度服务",
        "type": "cleanup",
        "order": 6,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": false,
        "timeout": 1800,
        "status": "pending"
      }
    ]
  }
}
```

## 自动配置映射

系统会自动从canary配置中提取以下信息来创建workflow：

### 基本信息
- **名称**: 使用canary名称 + "-workflow"后缀
- **描述**: 使用canary描述 + "- 自动生成的工作流"后缀
- **命名空间**: 直接使用canary的namespace
- **集群ID**: 直接使用canary的clusterId

### 服务配置
- **服务列表**: 从canary的services字段转换
- **镜像配置**: 使用canary服务的images字段
- **环境变量**: 使用canary服务的envs字段
- **副本数**: 使用canary服务的prodReplicas作为默认副本数
- **路由配置**: 从canary的routingConfig转换

### 审批配置
- **灰度审批**: 使用canary的canaryApprovers
- **生产审批**: 使用canary的prodApprovers
- **审批类型**: 使用canary的approvalType
- **超时时间**: 使用canary的approvalTimeout

### 全局配置
- **全局头部**: 使用canary的globalConfig.globalHeaders
- **监控配置**: 从canary的globalConfig.globalMonitoring转换
- **回滚配置**: 从canary的globalConfig.globalRollback转换

### 步骤生成
系统会为每个服务自动生成以下步骤：
1. **灰度审批步骤**: 等待审批人审批灰度发布
2. **灰度部署步骤**: 部署灰度版本到Kubernetes
3. **流量分发步骤**: 配置Istio流量分发（如果有路由配置）
4. **生产审批步骤**: 等待审批人审批生产发布
5. **生产部署步骤**: 部署生产版本到Kubernetes
6. **清理步骤**: 清理灰度环境资源

## 错误处理

### 常见错误

1. **Canary配置不存在**
```json
{
  "code": 400,
  "message": "failed to get canary config: record not found"
}
```

2. **无效的UUID格式**
```json
{
  "code": 400,
  "message": "canaryUuid is required"
}
```

3. **权限不足**
```json
{
  "code": 403,
  "message": "insufficient permissions"
}
```

## 与传统方式的对比

### 传统方式创建workflow
```json
{
  "name": "web-service-workflow",
  "description": "Web服务灰度发布工作流",
  "namespace": "default",
  "clusterId": "cluster-1",
  "services": [
    {
      "name": "web-service",
      "order": 1,
      "images": {
        "web-app": "nginx:1.21.1",
        "sidecar": "envoy:v1.18.1"
      },
      "version": "v1.0.0",
      "replicas": 3,
      "canaryReplicas": 1,
      "environment": {
        "ENV": "production"
      },
      "routing": {
        "strategy": "weight",
        "weights": [
          {"version": "canary", "weight": 50},
          {"version": "stable", "weight": 50}
        ]
      }
    }
  ],
  "steps": [
    {
      "name": "审批web-service灰度发布",
      "type": "approval",
      "order": 1,
      "serviceName": "web-service",
      "config": {
        "approvalConfig": {
          "approvers": ["<EMAIL>"],
          "conditionType": "or",
          "timeout": 3600
        }
      },
      "autoExecute": false,
      "timeout": 3600
    },
    {
      "name": "审批生产发布",
      "type": "approval",
      "order": 10,
      "config": {
        "approvalConfig": {
          "approvers": ["<EMAIL>"],
          "conditionType": "or",
          "timeout": 3600
        }
      },
      "autoExecute": false,
      "timeout": 3600
    }
  ]
}
```

### 新方式创建workflow
```json
{
  "canaryUuid": "550e8400-e29b-41d4-a716-************"
}
```

## 最佳实践

1. **预先配置Canary**: 确保canary配置已经完整设置，包括所有必要的服务、镜像和审批信息

2. **命名规范**: 如果不指定workflow名称，系统会自动生成，建议使用有意义的canary名称

3. **权限管理**: 确保操作用户有访问对应canary配置的权限

4. **配置验证**: 创建workflow前，先验证canary配置的完整性

5. **版本管理**: 定期更新canary配置中的镜像版本，workflow会自动使用最新配置

## 注意事项

- 创建的workflow会继承canary配置的所有设置
- 如果canary配置发生变化，已创建的workflow不会自动更新
- 需要更新workflow时，建议重新从canary配置创建新的workflow
- 系统会自动处理配置格式转换，无需手动调整
