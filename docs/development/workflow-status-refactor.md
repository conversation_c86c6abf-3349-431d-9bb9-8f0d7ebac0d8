# 工作流状态重构说明

## 问题背景

原有的工作流设计存在以下问题：

1. **状态混淆**：`Workflow` 和 `WorkflowExecution` 都使用同一套 `WorkflowStatus` 枚举
2. **概念不清**：工作流模板状态（如草稿、审批）与执行状态（如运行、完成）混在一起
3. **外键依赖**：模型间存在不必要的外键关联关系

## 重构方案

### 1. 状态分离

**之前（问题）：**
```go
// 混淆的状态定义
type WorkflowStatus string
const (
    WorkflowStatusDraft     = "draft"     // 草稿 - 模板状态
    WorkflowStatusPending   = "pending"   // 待审批 - 模板状态
    WorkflowStatusApproved  = "approved"  // 已审批 - 模板状态
    WorkflowStatusRunning   = "running"   // 运行中 - 执行状态
    WorkflowStatusCompleted = "completed" // 已完成 - 执行状态
    // ...
)

// 两个不同概念的模型使用同一套状态
type Workflow struct {
    Status WorkflowStatus // 混淆：既有模板状态又有执行状态
}

type WorkflowExecution struct {
    Status WorkflowStatus // 混淆：既有模板状态又有执行状态
}
```

**之后（解决）：**
```go
// 清晰的状态分离
type WorkflowTemplateStatus string
const (
    WorkflowTemplateStatusDraft    = "draft"    // 草稿
    WorkflowTemplateStatusPending  = "pending"  // 待审批
    WorkflowTemplateStatusApproved = "approved" // 已审批，可执行
    WorkflowTemplateStatusRejected = "rejected" // 已拒绝
    WorkflowTemplateStatusArchived = "archived" // 已归档
)

type WorkflowExecutionStatus string
const (
    WorkflowExecutionStatusPending   = "pending"   // 待执行
    WorkflowExecutionStatusRunning   = "running"   // 运行中
    WorkflowExecutionStatusPaused    = "paused"    // 已暂停
    WorkflowExecutionStatusCompleted = "completed" // 已完成
    WorkflowExecutionStatusFailed    = "failed"    // 失败
    WorkflowExecutionStatusCanceled  = "canceled"  // 已取消
)

// 概念清晰的模型定义
type Workflow struct {
    Status WorkflowTemplateStatus // 模板状态
}

type WorkflowExecution struct {
    Status WorkflowExecutionStatus // 执行状态
}
```

### 2. 概念澄清

| 模型 | 职责 | 状态类型 | 生命周期 |
|------|------|----------|----------|
| **Workflow** | 工作流模板/定义 | `WorkflowTemplateStatus` | 草稿 → 待审批 → 已审批/已拒绝 → 归档 |
| **WorkflowExecution** | 工作流执行实例 | `WorkflowExecutionStatus` | 待执行 → 运行中 → 完成/失败/取消 |

### 3. 移除外键关联

**之前（问题）：**
```go
type Workflow struct {
    // ...
    Executions []WorkflowExecution `gorm:"foreignKey:WorkflowID"`
}

type WorkflowExecution struct {
    // ...
    Workflow *Workflow `gorm:"foreignKey:WorkflowID"`
}
```

**之后（解决）：**
```go
type Workflow struct {
    // ... 不包含关联关系
}

type WorkflowExecution struct {
    WorkflowID int64 `gorm:"column:workflow_id;not null;comment:工作流ID"`
    // ... 只保留外键字段，不定义关联关系
}
```

**关联查询通过服务层处理：**
```go
// 在服务层进行关联查询，而不是在模型层
func (s *WorkflowService) GetWorkflowExecutions(workflowID int64) ([]models.WorkflowExecution, error) {
    var executions []models.WorkflowExecution
    return executions, s.db.Where("workflow_id = ?", workflowID).Find(&executions).Error
}
```

### 4. 向后兼容

为了保证现有代码的兼容性，保留了 `WorkflowStatus` 作为类型别名：

```go
// 向后兼容（废弃，请逐步迁移到新的状态类型）
type WorkflowStatus = WorkflowExecutionStatus

const (
    // 向后兼容常量 - 模板状态相关
    WorkflowStatusDraft    = WorkflowExecutionStatus("draft")    // 已废弃，请使用 WorkflowTemplateStatusDraft
    WorkflowStatusApproved = WorkflowExecutionStatus("approved") // 已废弃，请使用 WorkflowTemplateStatusApproved
    WorkflowStatusRejected = WorkflowExecutionStatus("rejected") // 已废弃，请使用 WorkflowTemplateStatusRejected

    // 向后兼容常量 - 执行状态相关
    WorkflowStatusPending   = WorkflowExecutionStatusPending   // 待执行
    WorkflowStatusRunning   = WorkflowExecutionStatusRunning   // 运行中
    WorkflowStatusCompleted = WorkflowExecutionStatusCompleted // 已完成
    // ...
)
```

### 5. 新增辅助方法

#### Workflow 模板状态管理
```go
func (w *Workflow) IsApproved() bool           // 检查是否已审批
func (w *Workflow) CanExecute() bool           // 检查是否可执行
func (w *Workflow) SubmitForApproval()         // 提交审批
func (w *Workflow) Approve()                   // 审批通过
func (w *Workflow) Reject()                    // 审批拒绝
func (w *Workflow) Archive()                   // 归档
```

#### WorkflowExecution 执行状态管理
```go
func (we *WorkflowExecution) IsRunning() bool      // 检查是否运行中
func (we *WorkflowExecution) IsCompleted() bool    // 检查是否已完成
func (we *WorkflowExecution) IsFailed() bool       // 检查是否失败
func (we *WorkflowExecution) IsFinished() bool     // 检查是否已结束
func (we *WorkflowExecution) Start()               // 开始执行
func (we *WorkflowExecution) Complete()            // 完成执行
func (we *WorkflowExecution) Fail(errorMessage string) // 失败执行
func (we *WorkflowExecution) Cancel()              // 取消执行
func (we *WorkflowExecution) Pause()               // 暂停执行
func (we *WorkflowExecution) Resume()              // 恢复执行
```

## 业务流程澄清

### 工作流模板生命周期
```
1. 创建工作流模板（draft）
2. 提交审批（pending）
3. 审批决策（approved/rejected）
4. 执行工作流（基于approved模板）
5. 归档模板（archived）
```

### 工作流执行生命周期
```
1. 创建执行实例（pending）
2. 开始执行（running）
3. 执行过程（running/paused）
4. 执行结束（completed/failed/canceled）
```

## 迁移指南

### 代码迁移建议

1. **新代码**：直接使用新的状态类型
   ```go
   // ✅ 推荐
   workflow.Status = models.WorkflowTemplateStatusDraft
   execution.Status = models.WorkflowExecutionStatusRunning
   ```

2. **现有代码**：逐步迁移
   ```go
   // ⚠️ 兼容但不推荐
   workflow.Status = models.WorkflowStatusDraft
   execution.Status = models.WorkflowStatusRunning
   ```

3. **关联查询**：移到服务层
   ```go
   // ❌ 避免
   workflow.Executions

   // ✅ 推荐
   service.GetWorkflowExecutions(workflowID)
   ```

### 数据库迁移

现有数据库数据不需要迁移，因为状态值本身没有改变，只是类型定义更清晰了。

## 优势总结

1. **概念清晰**：模板状态与执行状态明确分离
2. **类型安全**：编译期可以检查状态类型使用是否正确
3. **易于维护**：状态转换逻辑更清晰，便于后续扩展
4. **向后兼容**：现有代码可以继续工作
5. **无外键依赖**：避免了复杂的关联关系，提高性能
6. **业务明确**：每个状态都有明确的业务含义

这次重构解决了工作流状态管理中的核心问题，为后续的功能扩展和维护奠定了坚实的基础。
