# 统一的工作流创建API

## 概述

`POST /api/v1/workflow/canary/create` 端点现在支持两种创建工作流的方式：
1. **从canary配置创建**: 只需提供`canaryUuid`，所有配置自动从canary中获取
2. **传统方式创建**: 手动提供所有必需的配置参数

## API端点

```
POST /api/v1/workflow/canary/create
```

## 使用方式

### 方式1: 从Canary配置创建（推荐）

当提供`canaryUuid`时，系统会自动从canary配置中获取所有必要信息。

#### 请求参数

```json
{
  "canaryUuid": "550e8400-e29b-41d4-a716-************",
  "name": "可选的工作流名称",
  "description": "可选的工作流描述"
}
```

#### 完整示例

```bash
curl -X POST http://localhost:8080/api/v1/workflow/canary/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "canaryUuid": "550e8400-e29b-41d4-a716-************"
  }'
```

#### 自定义名称和描述

```bash
curl -X POST http://localhost:8080/api/v1/workflow/canary/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "canaryUuid": "550e8400-e29b-41d4-a716-************",
    "name": "web-service-v2-workflow",
    "description": "Web服务v2.0灰度发布工作流"
  }'
```

### 方式2: 传统方式创建

当不提供`canaryUuid`时，需要手动提供所有必需的配置参数。

#### 请求参数

```json
{
  "name": "web-service-workflow",
  "description": "Web服务灰度发布工作流",
  "namespace": "default",
  "clusterId": "cluster-1",
  "services": [
    {
      "name": "web-service",
      "order": 1,
      "images": {
        "web-app": "nginx:1.21.1",
        "sidecar": "envoy:v1.18.1"
      },
      "version": "v1.0.0",
      "replicas": 3,
      "canaryReplicas": 1,
      "environment": {
        "ENV": "production"
      },
      "routing": {
        "strategy": "weight",
        "weights": [
          {"version": "canary", "weight": 50},
          {"version": "stable", "weight": 50}
        ]
      }
    }
  ],
  "steps": [
    {
      "name": "审批web-service灰度发布",
      "type": "approval",
      "order": 1,
      "serviceName": "web-service",
      "config": {
        "approvalConfig": {
          "approvers": ["<EMAIL>"],
          "conditionType": "or",
          "timeout": 3600
        }
      },
      "autoExecute": false,
      "timeout": 3600
    },
    {
      "name": "审批生产发布",
      "type": "approval",
      "order": 10,
      "config": {
        "approvalConfig": {
          "approvers": ["<EMAIL>"],
          "conditionType": "or",
          "timeout": 3600
        }
      },
      "autoExecute": false,
      "timeout": 3600
    }
  ]
}
```

#### 完整示例

```bash
curl -X POST http://localhost:8080/api/v1/workflow/canary/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "web-service-workflow",
    "description": "Web服务灰度发布工作流",
    "namespace": "default",
    "clusterId": "cluster-1",
    "services": [
      {
        "name": "web-service",
        "order": 1,
        "images": {
          "web-app": "nginx:1.21.1",
          "sidecar": "envoy:v1.18.1"
        },
        "version": "v1.0.0",
        "replicas": 3,
        "canaryReplicas": 1,
        "environment": {
          "ENV": "production"
        }
      }
    ],
    "steps": [
      {
        "name": "审批web-service灰度发布",
        "type": "approval",
        "order": 1,
        "serviceName": "web-service",
        "config": {
          "approvalConfig": {
            "approvers": ["<EMAIL>"],
            "conditionType": "or",
            "timeout": 3600
          }
        },
        "autoExecute": false,
        "timeout": 3600
      },
      {
        "name": "审批生产发布",
        "type": "approval",
        "order": 10,
        "config": {
          "approvalConfig": {
            "approvers": ["<EMAIL>"],
            "conditionType": "or",
            "timeout": 3600
          }
        },
        "autoExecute": false,
        "timeout": 3600
      }
    ]
  }'
```

## 字段说明

### 从Canary配置创建时的字段

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| canaryUuid | string | 是 | Canary配置的UUID |
| name | string | 否 | 工作流名称（可选，默认使用canary名称） |
| description | string | 否 | 工作流描述（可选，默认使用canary描述） |

### 传统方式创建时的字段

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| name | string | 是 | 工作流名称 |
| description | string | 否 | 工作流描述 |
| namespace | string | 是 | 命名空间 |
| clusterId | string | 是 | 集群ID |
| services | array | 是 | 服务链路配置 |
| steps | array | 否 | 工作流步骤配置（包含审批步骤） |

## 响应格式

两种方式的响应格式完全相同：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "uuid": "workflow-uuid-here",
    "name": "web-service-workflow",
    "description": "Web服务灰度发布工作流",
    "type": "canary",
    "serviceName": "web-service",
    "namespace": "default",
    "clusterId": "cluster-1",
    "status": "draft",
    "version": 1,
    "isTemplate": false,
    "steps": [
      {
        "id": 1,
        "workflowId": 123,
        "name": "审批web-service灰度发布",
        "type": "approval",
        "order": 1,
        "serviceName": "web-service",
        "serviceOrder": 1,
        "autoExecute": false,
        "timeout": 3600,
        "status": "pending"
      }
    ]
  }
}
```

## 验证逻辑

系统会根据请求中是否包含`canaryUuid`来决定验证逻辑：

### 包含canaryUuid时
- 只验证`canaryUuid`是否存在
- 其他字段都是可选的

### 不包含canaryUuid时
- `name`、`namespace`、`clusterId`为必需字段
- 必须提供`services`配置或传统的审批人配置

## 错误处理

### 从Canary配置创建时的错误

```json
{
  "code": 400,
  "message": "failed to get canary config: record not found"
}
```

### 传统方式创建时的错误

```json
{
  "code": 400,
  "message": "name is required when canaryUuid is not provided"
}
```

## 最佳实践

1. **优先使用Canary配置创建**: 更简单、更不容易出错
2. **预先配置Canary**: 确保canary配置完整且正确
3. **合理命名**: 使用有意义的工作流名称
4. **权限管理**: 确保有访问canary配置的权限

## 对比优势

| 特性 | 从Canary配置创建 | 传统方式创建 |
|------|------------------|--------------|
| 请求复杂度 | 简单（1-3个字段） | 复杂（10+个字段） |
| 配置一致性 | 高（来源统一） | 低（手动配置） |
| 维护成本 | 低 | 高 |
| 错误概率 | 低 | 高 |
| 灵活性 | 中等 | 高 |

## 使用建议

- **新项目**: 建议使用从Canary配置创建的方式
- **已有项目**: 可以逐步迁移到Canary配置管理
- **临时测试**: 可以使用传统方式快速创建

## 注意事项

- 从Canary配置创建的工作流会继承所有canary设置
- 如果canary配置变更，已创建的工作流不会自动更新
- 两种方式创建的工作流在执行时行为完全一致
- 系统会自动处理镜像的动态更新（从canary配置中获取最新镜像）
