<mxfile host="Electron" modified="2025-07-04T05:57:20.399Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.1.0 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="7854myr9Ne12uIbw36fP" version="24.1.0" type="device" pages="2">
  <diagram name="灰度发布架构图" id="KyJhcfXHlT7iet0P_mbS">
    <mxGraphModel dx="1434" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="client-group" value="客户端" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="50" y="100" width="120" height="230" as="geometry" />
        </mxCell>
        <mxCell id="ios" value="iOS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="client-group" vertex="1">
          <mxGeometry x="20" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="android" value="Android" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="client-group" vertex="1">
          <mxGeometry x="20" y="100" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="h5" value="H5" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="client-group" vertex="1">
          <mxGeometry x="20" y="160" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gateway-group" value="网关层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="250" y="100" width="120" height="230" as="geometry" />
        </mxCell>
        <mxCell id="gateway" value="云原生" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="gateway-group" vertex="1">
          <mxGeometry x="20" y="90" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="service-a" value="A" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="450" y="100" width="160" height="230" as="geometry" />
        </mxCell>
        <mxCell id="a-gray" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;A&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#999999;strokeColor=#333333;fontColor=#ffffff;" parent="service-a" vertex="1">
          <mxGeometry x="20" y="60" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="a-gray-label" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;gray&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="service-a" vertex="1">
          <mxGeometry x="20" y="40" width="50" height="15" as="geometry" />
        </mxCell>
        <mxCell id="a-base" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;A&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dddddd;strokeColor=#333333;" parent="service-a" vertex="1">
          <mxGeometry x="20" y="150" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-1" value="Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff8c00;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-a">
          <mxGeometry x="90" y="80" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-2" value="Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff8c00;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-a">
          <mxGeometry x="90" y="170" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="service-b" value="B" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="700" y="100" width="160" height="230" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-3" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;B&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dddddd;strokeColor=#333333;" vertex="1" parent="service-b">
          <mxGeometry x="25" y="150" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-4" value="Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff8c00;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-b">
          <mxGeometry x="95" y="170" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="service-c" value="C" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="950" y="100" width="160" height="230" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-5" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;C&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dddddd;strokeColor=#333333;" vertex="1" parent="service-c">
          <mxGeometry x="25" y="150" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-6" value="Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff8c00;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-c">
          <mxGeometry x="95" y="170" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-7" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;C&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#999999;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-c">
          <mxGeometry x="25" y="60" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-8" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;gray&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="service-c">
          <mxGeometry x="25" y="40" width="50" height="15" as="geometry" />
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-9" value="Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff8c00;strokeColor=#333333;fontColor=#ffffff;" vertex="1" parent="service-c">
          <mxGeometry x="95" y="80" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ios-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.3;entryDx=0;entryDy=0;" parent="1" source="ios" target="gateway" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="android-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="android" target="gateway" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h5-to-gateway" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.7;entryDx=0;entryDy=0;" parent="1" source="h5" target="gateway" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gateway-to-a-gray" value="gray&lt;br&gt;client-id" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.3;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#82b366;" parent="1" source="gateway" target="a-gray" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gateway-to-a-base" value="base" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.7;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="gateway" target="a-base" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a-gray-to-b-gray" value="gray" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#82b366;" parent="1" source="a-gray" target="dWit22u-gnQfDIAGMdAo-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="155" as="sourcePoint" />
            <mxPoint x="720" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a-base-to-b-base" value="base" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="dWit22u-gnQfDIAGMdAo-3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="275" as="sourcePoint" />
            <mxPoint x="720" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-10" value="gray" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#82b366;" edge="1" parent="1" source="dWit22u-gnQfDIAGMdAo-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="90" as="sourcePoint" />
            <mxPoint x="975" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dWit22u-gnQfDIAGMdAo-11" value="base" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;" edge="1" parent="1" source="dWit22u-gnQfDIAGMdAo-4" target="dWit22u-gnQfDIAGMdAo-5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="270" as="sourcePoint" />
            <mxPoint x="975" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="36Nd9Yu1IszSJpk6Ef9G" name="编辑页面">
    <mxGraphModel dx="1434" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="page-title" value="灰度发布管理系统 - 横向流程" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="20" width="400" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step1-group" target="step2-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step1-group" value="步骤1：创建灰度发布" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="env-select" value="环境选择：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="env-dropdown" value="测试环境 ▼" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="65" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="name-label" value="发布名称：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="110" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="name-input" value="gray-release-v1.0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="135" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag-label" value="标签：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="180" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="tag-input" value="feature-new-ui" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="205" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="create-btn" value="创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="step1-group">
          <mxGeometry x="10" y="250" width="80" height="35" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step2-group" target="step3-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step2-group" value="步骤2：选择服务" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="service-list" value="可选服务：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step2-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="service-a-btn" value="服务A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step2-group">
          <mxGeometry x="10" y="65" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="service-b-btn" value="服务B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step2-group">
          <mxGeometry x="80" y="65" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="service-c-btn" value="服务C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step2-group">
          <mxGeometry x="150" y="65" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="add-to-group" value="添加到组 ↓" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="step2-group">
          <mxGeometry x="10" y="110" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="selected-services" value="已选服务：&#xa;A → B → C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="step2-group">
          <mxGeometry x="10" y="155" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="generate-tabs" value="生成泳道图" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="step2-group">
          <mxGeometry x="10" y="220" width="100" height="35" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step3-group" target="step4-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step3-group" value="步骤3：服务配置" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="tab-nav" value="Tab导航" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontSize=10;" vertex="1" parent="step3-group">
          <mxGeometry x="10" y="40" width="220" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tab-a" value="服务A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="tab-nav">
          <mxGeometry x="5" y="5" width="50" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tab-b" value="服务B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="tab-nav">
          <mxGeometry x="65" y="5" width="50" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tab-c" value="服务C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="tab-nav">
          <mxGeometry x="125" y="5" width="50" height="25" as="geometry" />
        </mxCell>
        <mxCell id="current-tab-content" value="当前Tab：服务A配置" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontSize=10;" vertex="1" parent="step3-group">
          <mxGeometry x="10" y="90" width="220" height="200" as="geometry" />
        </mxCell>
        <mxCell id="image-version" value="镜像版本：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="25" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="version-select" value="v1.2.3 ▼" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="45" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="traffic-ratio" value="流量占比：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="80" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="ratio-input" value="20%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="100" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="ratio-range" value="(10-100%)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="current-tab-content">
          <mxGeometry x="80" y="102" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="next-btn" value="下一步" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="140" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="confirm-btn" value="确认配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="current-tab-content">
          <mxGeometry x="80" y="140" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="default-note" value="注：不选择灰度发布时使用默认配置" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="current-tab-content">
          <mxGeometry x="10" y="170" width="180" height="20" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step4-group" target="step5-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step4-group" value="步骤4：审批流程" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="approval-status" value="审批状态：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="pending-approval" value="待审批" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="65" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="approve-btn" value="批准" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="110" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="reject-btn" value="拒绝" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step4-group">
          <mxGeometry x="80" y="110" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="approver" value="审批人：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="155" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="approver-name" value="管理员" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="180" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="approval-time" value="审批时间：&#xa;2025-01-03 10:30" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="step4-group">
          <mxGeometry x="10" y="220" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step5-group" target="step6-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-323HRXjQ4WxgvACybYr-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="step5-group" target="step3-group">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1255" y="530" />
              <mxPoint x="715" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="-323HRXjQ4WxgvACybYr-2" value="重新配置" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="-323HRXjQ4WxgvACybYr-1">
          <mxGeometry x="0.05" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step5-group" value="步骤5：监控面板" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1130" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="deploy-status" value="部署状态：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="deploying" value="部署中..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="65" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="traffic-stats" value="流量统计：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="110" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="total-requests" value="总请求：1000" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="135" width="90" height="25" as="geometry" />
        </mxCell>
        <mxCell id="gray-requests" value="灰度：200" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="step5-group">
          <mxGeometry x="110" y="135" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="client-id-label" value="Client-ID统计：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="175" width="90" height="20" as="geometry" />
        </mxCell>
        <mxCell id="client-stats" value="活跃客户端：50个" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="200" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="error-rate" value="错误率：0.1%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="235" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="response-time" value="响应时间：50ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="step5-group">
          <mxGeometry x="100" y="235" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="monitor-dashboard" value="监控面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;fontStyle=1;" vertex="1" parent="step5-group">
          <mxGeometry x="10" y="275" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="log-view" value="日志查看" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;fontStyle=1;" vertex="1" parent="step5-group">
          <mxGeometry x="100" y="275" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rr1WeHsJKgWbfOnOkR19-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="step6-group" target="step7-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step6-group" value="步骤6：生产环境审批" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1400" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="prod-approval" value="生产审批：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step6-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="prod-pending" value="待审批" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step6-group">
          <mxGeometry x="10" y="65" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-prod-btn" value="部署到生产" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="step6-group">
          <mxGeometry x="10" y="110" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rollback-btn" value="回滚" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step6-group">
          <mxGeometry x="110" y="110" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="prod-approver" value="生产审批人：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step6-group">
          <mxGeometry x="10" y="155" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="prod-approver-name" value="技术总监" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="step6-group">
          <mxGeometry x="10" y="180" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="step7-group" value="步骤7：清理灰度服务" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1670" y="100" width="250" height="380" as="geometry" />
        </mxCell>
        <mxCell id="cleanup-status" value="清理状态：" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="step7-group">
          <mxGeometry x="10" y="40" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="cleanup-ready" value="准备清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="step7-group">
          <mxGeometry x="10" y="65" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cleanup-btn" value="清理灰度服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;fontStyle=1;" vertex="1" parent="step7-group">
          <mxGeometry x="10" y="110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cleanup-confirm" value="确认清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="step7-group">
          <mxGeometry x="120" y="110" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-1" value="1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-2" value="2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="430" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-3" value="3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-4" value="4" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="970" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-5" value="5" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1240" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-6" value="6" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1510" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step-indicator-7" value="7" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1780" y="70" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="status-legend" value="状态说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="115" y="580" width="575" height="60" as="geometry" />
        </mxCell>
        <mxCell id="status-pending" value="待处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="20" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-processing" value="处理中" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="100" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-success" value="成功" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="180" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-info" value="信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="260" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-config" value="配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="340" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-warning" value="警告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="420" y="25" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="status-disabled" value="禁用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="status-legend">
          <mxGeometry x="500" y="25" width="60" height="25" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
