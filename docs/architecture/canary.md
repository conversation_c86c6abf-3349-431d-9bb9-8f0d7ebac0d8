# 基于Istio和Kubernetes的灰度发布系统设计

## 📋 系统概述

本系统基于Istio服务网格和Kubernetes容器编排平台，提供企业级的灰度发布能力。系统设计简洁易用，支持工作流管理、多种发布策略、审批功能、效果监控和用户群体控制等功能。

### 核心特性

- 🔄 **工作流管理**：可视化的发布工作流，支持步骤编排和动态调整
- 🎯 **多种发布策略**：Canary、Blue-Green、A/B测试等多种策略
- ✅ **审批机制**：多级审批流程，确保发布安全性
- 📊 **实时监控**：发布效果实时监控和自动决策
- 👥 **用户群体控制**：基于标签的精准流量控制
- 🔧 **简洁设计**：直观的用户界面，降低使用门槛
- 🌐 **全链路灰度**：支持HTTP/gRPC/WebSocket协议的端到端灰度控制
- 🔗 **协议兼容**：统一的灰度策略适配多种通信协议
- 📡 **实时通信**：WebSocket长连接的灰度路由和会话保持

---

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        灰度发布管理平台                           │
├─────────────────────────────────────────────────────────────────┤
│  工作流管理 │ 发布策略 │ 审批流程 │ 监控面板 │ 用户管理                │
├─────────────────────────────────────────────────────────────────┤
│                       控制平面 (Control Plane)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ Workflow    │ │ Approval    │ │ Strategy    │ │ Monitoring  ││
│  │ Engine      │ │ Engine      │ │ Engine      │ │ Engine      ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│                      Istio服务网格层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │VirtualService│ │DestinationRule│ │  Gateway   │ │ServiceEntry││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│                    全链路灰度协议层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │    HTTP     │ │    gRPC     │ │  WebSocket  │ │   Others    ││
│  │   Router    │ │   Router    │ │   Router    │ │   Router    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│                    Kubernetes集群                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │Service A v1 │ │Service B v1 │ │Service C v1 │               │
│  │    +        │ │    +        │ │    +        │               │
│  │Service A v2 │ │Service B v2 │ │Service C v2 │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
└─────────────────────────────────────────────────────────────────┘
```

### 全链路灰度架构图

```mermaid
graph TD
    Client[客户端] --> Gateway[API网关]
    Gateway --> |灰度决策| Router[协议路由器]

    subgraph "协议层"
        Router --> HTTPRouter[HTTP路由]
        Router --> GRPCRouter[gRPC路由]
        Router --> WSRouter[WebSocket路由]
    end

    subgraph "服务调用链"
        HTTPRouter --> ServiceA[服务A]
        GRPCRouter --> ServiceA
        WSRouter --> ServiceA
        ServiceA --> |HTTP调用| ServiceB[服务B]
        ServiceA --> |gRPC调用| ServiceC[服务C]
        ServiceB --> |WebSocket| ServiceD[服务D]
    end

    subgraph "版本控制"
        ServiceA --> ServiceA_v1[服务A-v1]
        ServiceA --> ServiceA_v2[服务A-v2]
        ServiceB --> ServiceB_v1[服务B-v1]
        ServiceB --> ServiceB_v2[服务B-v2]
        ServiceC --> ServiceC_v1[服务C-v1]
        ServiceC --> ServiceC_v2[服务C-v2]
        ServiceD --> ServiceD_v1[服务D-v1]
        ServiceD --> ServiceD_v2[服务D-v2]
    end

    subgraph "链路追踪"
        TraceCollector[链路收集器]
        ServiceA -.-> TraceCollector
        ServiceB -.-> TraceCollector
        ServiceC -.-> TraceCollector
        ServiceD -.-> TraceCollector
    end
```

---

## �� 工作流设计

### 工作流状态图

```mermaid
stateDiagram-v2
    [*] --> Draft: 创建工作流
    Draft --> PendingApproval: 提交审批
    PendingApproval --> Approved: 审批通过
    PendingApproval --> Rejected: 审批拒绝
    Rejected --> Draft: 重新编辑
    Approved --> Running: 开始执行
    Running --> Paused: 暂停执行
    Paused --> Running: 继续执行
    Running --> RollingBack: 触发回滚
    Running --> Completed: 执行完成
    RollingBack --> RolledBack: 回滚完成
    Completed --> [*]
    RolledBack --> [*]
```

### 工作流步骤定义

```json
{
  "workflow": {
    "id": "canary-workflow-001",
    "name": "用户服务灰度发布",
    "description": "用户服务从v1.0升级到v2.0的灰度发布流程",
    "steps": [
      {
        "id": "step-1",
        "name": "创建灰度版本",
        "type": "deployment",
        "config": {
          "service": "user-service",
          "version": "v2.0",
          "replicas": 2,
          "image": "user-service:v2.0"
        },
        "approvers": ["<EMAIL>"],
        "autoExecute": false
      },
      {
        "id": "step-2",
        "name": "配置用户组路由",
        "type": "usergroup-binding",
        "config": {
          "userGroups": [
            {
              "name": "beta-testers",
              "targetVersion": "v2.0",
              "priority": 100
            },
            {
              "name": "internal-staff",
              "targetVersion": "v2.0",
              "priority": 95
            }
          ]
        },
        "dependsOn": ["step-1"],
        "approvers": ["<EMAIL>"],
        "autoExecute": false
      },
      {
        "id": "step-3",
        "name": "配置流量分发",
        "type": "traffic-split",
        "config": {
          "strategy": "canary",
          "weights": [
            {"version": "v1.0", "weight": 90},
            {"version": "v2.0", "weight": 10}
          ]
        },
        "dependsOn": ["step-2"],
        "approvers": ["<EMAIL>"],
        "autoExecute": false
      },
      {
        "id": "step-4",
        "name": "监控指标检查",
        "type": "monitoring",
        "config": {
          "duration": "10m",
          "metrics": [
            {"name": "success_rate", "threshold": ">= 99%"},
            {"name": "response_time", "threshold": "<= 200ms"},
            {"name": "error_rate", "threshold": "<= 1%"}
          ]
        },
        "dependsOn": ["step-3"],
        "autoExecute": true
      },
      {
        "id": "step-5",
        "name": "扩大灰度范围",
        "type": "traffic-split",
        "config": {
          "weights": [
            {"version": "v1.0", "weight": 50},
            {"version": "v2.0", "weight": 50}
          ]
        },
        "dependsOn": ["step-4"],
        "approvers": ["<EMAIL>"],
        "autoExecute": false
      },
      {
        "id": "step-6",
        "name": "全量发布",
        "type": "traffic-split",
        "config": {
          "weights": [
            {"version": "v1.0", "weight": 0},
            {"version": "v2.0", "weight": 100}
          ]
        },
        "dependsOn": ["step-5"],
        "approvers": ["<EMAIL>"],
        "autoExecute": false
      }
    ]
  }
}
```

---

## 🎯 发布策略设计

### 1. Canary发布策略

**特点**：渐进式流量切换，风险可控
**适用场景**：大多数生产环境发布

#### 1.1 基础Canary配置

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service-canary
  namespace: production
spec:
  hosts:
  - user-service
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: user-service
        subset: v2
      weight: 100
  - route:
    - destination:
        host: user-service
        subset: v1
      weight: 90
    - destination:
        host: user-service
        subset: v2
      weight: 10
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: user-service-destination
  namespace: production
spec:
  host: user-service
  subsets:
  - name: v1
    labels:
      version: v1.0
  - name: v2
    labels:
      version: v2.0
```

#### 1.2 用户组定向Canary配置

**特点**：指定用户组访问特定服务版本，精准控制灰度范围
**适用场景**：需要对特定用户群体进行功能验证

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service-canary-usergroup
  namespace: production
spec:
  hosts:
  - user-service
  http:
  # 指定用户组路由到新版本
  - match:
    - headers:
        user-group:
          exact: "beta-testers"
    route:
    - destination:
        host: user-service
        subset: v2
      weight: 100
  - match:
    - headers:
        user-group:
          exact: "premium-users"
    route:
    - destination:
        host: user-service
        subset: v2
      weight: 100
  - match:
    - headers:
        user-group:
          exact: "internal-staff"
    route:
    - destination:
        host: user-service
        subset: v2
      weight: 100
  # 基于用户ID的灰度策略
  - match:
    - headers:
        user-id:
          regex: "^(beta|test|dev).*"
    route:
    - destination:
        host: user-service
        subset: v2
      weight: 100
  # 默认流量分发
  - route:
    - destination:
        host: user-service
        subset: v1
      weight: 90
    - destination:
        host: user-service
        subset: v2
      weight: 10

---
# 用户组服务绑定配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: usergroup-service-mapping
  namespace: production
data:
  mapping.yaml: |
    userGroupServiceMappings:
      - userGroup: "beta-testers"
        services:
          - serviceName: "user-service"
            targetVersion: "v2"
            priority: 100
          - serviceName: "order-service"
            targetVersion: "v2"
            priority: 90
      - userGroup: "premium-users"
        services:
          - serviceName: "user-service"
            targetVersion: "v2"
            priority: 100
          - serviceName: "payment-service"
            targetVersion: "v2"
            priority: 95
      - userGroup: "internal-staff"
        services:
          - serviceName: "*"  # 所有服务
            targetVersion: "v2"
            priority: 100
```

#### 1.3 基于Client-ID的灰度路由配置

**特点**：使用 client-id 作为路由键，支持前端和服务端的统一灰度控制
**适用场景**：需要前后端版本一致性的应用系统，多客户端类型的灰度发布

##### 1.3.1 前端灰度路由配置

```yaml
# 前端静态资源灰度路由
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: frontend-canary-clientid
  namespace: production
spec:
  hosts:
  - pilot-web.company.com
  http:
  # 基于client-id的前端版本路由
  - match:
    - headers:
        client-id:
          regex: ".*"
    route:
    - destination:
        host: pilot-web-service
        subset: v1
      weight: 90
    - destination:
        host: pilot-web-service
        subset: v2
      weight: 10
    # 使用client-id作为一致性哈希键
    hash:
      httpHeaderName: "client-id"

  # 默认路由到稳定版本
  - route:
    - destination:
        host: pilot-web-service
        subset: v1
      weight: 100

---
# 前端服务版本定义
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: pilot-web-destination
  namespace: production
spec:
  host: pilot-web-service
  trafficPolicy:
    # 启用基于client-id的一致性哈希
    consistentHash:
      httpHeaderName: "client-id"
  subsets:
  - name: v1
    labels:
      version: v1.0
  - name: v2
    labels:
      version: v2.0
```

##### 1.3.2 服务端API灰度路由配置

```yaml
# 服务端API灰度路由
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: pilot-api-canary-clientid
  namespace: production
spec:
  hosts:
  - pilot-api
  http:
  # 优先匹配已缓存的版本分配
  - match:
    - headers:
        x-canary-version:
          exact: "v2"
        client-id:
          regex: ".*"
    route:
    - destination:
        host: pilot-api
        subset: v2
      weight: 100

  - match:
    - headers:
        x-canary-version:
          exact: "v1"
        client-id:
          regex: ".*"
    route:
    - destination:
        host: pilot-api
        subset: v1
      weight: 100

  # 基于client-id的新分配路由
  - match:
    - headers:
        client-id:
          regex: ".*"
    route:
    - destination:
        host: pilot-api
        subset: v1
      weight: 90
      headers:
        response:
          set:
            x-canary-version: "v1"
    - destination:
        host: pilot-api
        subset: v2
      weight: 10
      headers:
        response:
          set:
            x-canary-version: "v2"
    # 使用client-id确保一致性
    hash:
      httpHeaderName: "client-id"

  # 默认路由（无client-id）
  - route:
    - destination:
        host: pilot-api
        subset: v1
      weight: 100

---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: pilot-api-destination
  namespace: production
spec:
  host: pilot-api
  trafficPolicy:
    consistentHash:
      httpHeaderName: "client-id"
  subsets:
  - name: v1
    labels:
      version: v1.0
  - name: v2
    labels:
      version: v2.0

##### 1.3.3 Client-ID生成和管理策略

**Client-ID生成规则**：
- Web客户端：`web-${deviceId}-${timestamp}`
- 移动客户端：`mobile-${deviceId}-${appVersion}`
- API客户端：`api-${serviceId}-${instanceId}`

```yaml
# Client-ID管理服务配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: client-id-config
  namespace: production
data:
  config.yaml: |
    clientIdConfig:
      # Client-ID生成策略
      generationStrategy:
        web:
          prefix: "web"
          includeDeviceId: true
          includeTimestamp: true
          format: "web-{deviceId}-{timestamp}"
        mobile:
          prefix: "mobile"
          includeDeviceId: true
          includeAppVersion: true
          format: "mobile-{deviceId}-{appVersion}"
        api:
          prefix: "api"
          includeServiceId: true
          includeInstanceId: true
          format: "api-{serviceId}-{instanceId}"

      # 版本分配策略
      versionAllocation:
        algorithm: "consistent-hash"  # consistent-hash | random | weighted
        hashFunction: "sha256"
        canaryPercentage: 10

      # 缓存配置
      cache:
        enabled: true
        ttl: "24h"
        storage: "redis"
        keyPrefix: "canary:client-id:"
```

##### 1.3.4 前端Client-ID实现

```typescript
// src/utils/clientId.ts
export class ClientIdManager {
  private static instance: ClientIdManager;
  private clientId: string | null = null;
  private readonly STORAGE_KEY = 'pilot_client_id';
  private readonly CANARY_VERSION_KEY = 'pilot_canary_version';

  static getInstance(): ClientIdManager {
    if (!ClientIdManager.instance) {
      ClientIdManager.instance = new ClientIdManager();
    }
    return ClientIdManager.instance;
  }

  /**
   * 获取或生成Client-ID
   */
  getClientId(): string {
    if (this.clientId) {
      return this.clientId;
    }

    // 尝试从localStorage获取
    const storedClientId = localStorage.getItem(this.STORAGE_KEY);
    if (storedClientId) {
      this.clientId = storedClientId;
      return this.clientId;
    }

    // 生成新的Client-ID
    this.clientId = this.generateClientId();
    localStorage.setItem(this.STORAGE_KEY, this.clientId);

    return this.clientId;
  }

  /**
   * 生成Client-ID
   */
  private generateClientId(): string {
    const deviceId = this.getDeviceId();
    const timestamp = Date.now();
    return `web-${deviceId}-${timestamp}`;
  }

  /**
   * 获取设备标识
   */
  private getDeviceId(): string {
    // 尝试获取设备指纹
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
      const fingerprint = canvas.toDataURL();
      return this.hashString(fingerprint).substring(0, 8);
    }

    // 降级方案：使用随机字符串
    return Math.random().toString(36).substring(2, 10);
  }

  /**
   * 字符串哈希
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 获取当前分配的金丝雀版本
   */
  getCanaryVersion(): string | null {
    return localStorage.getItem(this.CANARY_VERSION_KEY);
  }

  /**
   * 设置金丝雀版本
   */
  setCanaryVersion(version: string): void {
    localStorage.setItem(this.CANARY_VERSION_KEY, version);
  }

  /**
   * 清除客户端缓存
   */
  clearCache(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.CANARY_VERSION_KEY);
    this.clientId = null;
  }
}

// src/utils/axios.ts - HTTP拦截器配置
import axios from 'axios';
import { ClientIdManager } from './clientId';

// 请求拦截器：添加Client-ID头
axios.interceptors.request.use(
  (config) => {
    const clientIdManager = ClientIdManager.getInstance();
    const clientId = clientIdManager.getClientId();
    const canaryVersion = clientIdManager.getCanaryVersion();

    // 添加Client-ID头
    config.headers['client-id'] = clientId;

    // 如果已有版本分配，添加版本头
    if (canaryVersion) {
      config.headers['x-canary-version'] = canaryVersion;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器：处理版本分配
axios.interceptors.response.use(
  (response) => {
    const canaryVersion = response.headers['x-canary-version'];
    if (canaryVersion) {
      const clientIdManager = ClientIdManager.getInstance();
      clientIdManager.setCanaryVersion(canaryVersion);
    }
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);
```

##### 1.3.5 服务端Client-ID处理

```go
// internal/pkg/middleware/clientid.go
package middleware

import (
    "crypto/sha256"
    "fmt"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
)

type ClientIdMiddleware struct {
    redisClient *redis.Client
    canaryPercentage int
}

func NewClientIdMiddleware(redisClient *redis.Client, canaryPercentage int) *ClientIdMiddleware {
    return &ClientIdMiddleware{
        redisClient: redisClient,
        canaryPercentage: canaryPercentage,
    }
}

// ClientIdHandler 处理Client-ID路由
func (m *ClientIdMiddleware) ClientIdHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        clientId := c.GetHeader("client-id")
        if clientId == "" {
            // 如果没有client-id，生成一个临时的
            clientId = m.generateTempClientId(c)
        }

        // 检查是否已有版本分配
        canaryVersion := c.GetHeader("x-canary-version")
        if canaryVersion == "" {
            // 从缓存获取版本分配
            cachedVersion := m.getCachedVersion(clientId)
            if cachedVersion != "" {
                canaryVersion = cachedVersion
            } else {
                // 计算新的版本分配
                canaryVersion = m.calculateCanaryVersion(clientId)
                // 缓存版本分配
                m.cacheVersion(clientId, canaryVersion)
            }
        }

        // 设置响应头
        c.Header("x-canary-version", canaryVersion)

        // 将信息存储到context中供后续使用
        c.Set("client-id", clientId)
        c.Set("canary-version", canaryVersion)

        c.Next()
    }
}

// 生成临时Client-ID
func (m *ClientIdMiddleware) generateTempClientId(c *gin.Context) string {
    userAgent := c.GetHeader("User-Agent")
    clientIP := c.ClientIP()
    timestamp := time.Now().Unix()

    hash := sha256.Sum256([]byte(fmt.Sprintf("%s-%s-%d", userAgent, clientIP, timestamp)))
    return fmt.Sprintf("temp-%x", hash[:8])
}

// 计算金丝雀版本
func (m *ClientIdMiddleware) calculateCanaryVersion(clientId string) string {
    // 使用一致性哈希算法
    hash := sha256.Sum256([]byte(clientId))
    hashValue := int(hash[0])<<24 + int(hash[1])<<16 + int(hash[2])<<8 + int(hash[3])

    // 确保为正数
    if hashValue < 0 {
        hashValue = -hashValue
    }

    // 计算百分比
    percentage := hashValue % 100

    if percentage < m.canaryPercentage {
        return "v2"
    }
    return "v1"
}

// 从缓存获取版本
func (m *ClientIdMiddleware) getCachedVersion(clientId string) string {
    key := fmt.Sprintf("canary:client-id:%s", clientId)
    version, err := m.redisClient.Get(context.Background(), key).Result()
    if err != nil {
        return ""
    }
    return version
}

// 缓存版本分配
func (m *ClientIdMiddleware) cacheVersion(clientId, version string) {
    key := fmt.Sprintf("canary:client-id:%s", clientId)
    // 缓存24小时
    m.redisClient.Set(context.Background(), key, version, 24*time.Hour)
}

// ClientIdService 提供Client-ID相关服务
type ClientIdService struct {
    redisClient *redis.Client
}

func NewClientIdService(redisClient *redis.Client) *ClientIdService {
    return &ClientIdService{
        redisClient: redisClient,
    }
}

// GetClientIdStats 获取Client-ID统计信息
func (s *ClientIdService) GetClientIdStats() (*ClientIdStats, error) {
    ctx := context.Background()

    // 获取所有client-id键
    keys, err := s.redisClient.Keys(ctx, "canary:client-id:*").Result()
    if err != nil {
        return nil, err
    }

    stats := &ClientIdStats{
        TotalClients: len(keys),
        V1Clients:    0,
        V2Clients:    0,
        WebClients:   0,
        MobileClients: 0,
        ApiClients:   0,
    }

    // 批量获取版本信息
    if len(keys) > 0 {
        versions, err := s.redisClient.MGet(ctx, keys...).Result()
        if err != nil {
            return nil, err
        }

        for i, version := range versions {
            if version == nil {
                continue
            }

            versionStr := version.(string)
            if versionStr == "v1" {
                stats.V1Clients++
            } else if versionStr == "v2" {
                stats.V2Clients++
            }

            // 分析client-id类型
            clientId := keys[i][len("canary:client-id:"):]
            if strings.HasPrefix(clientId, "web-") {
                stats.WebClients++
            } else if strings.HasPrefix(clientId, "mobile-") {
                stats.MobileClients++
            } else if strings.HasPrefix(clientId, "api-") {
                stats.ApiClients++
            }
        }
    }

    return stats, nil
}

type ClientIdStats struct {
    TotalClients  int `json:"totalClients"`
    V1Clients     int `json:"v1Clients"`
    V2Clients     int `json:"v2Clients"`
    WebClients    int `json:"webClients"`
    MobileClients int `json:"mobileClients"`
    ApiClients    int `json:"apiClients"`
}
```

---
# 基于用户ID哈希的一致性路由配置
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service-canary-consistent-hash
  namespace: production
spec:
  hosts:
  - user-service
  http:
  # 使用一致性哈希确保同一用户始终路由到同一版本
  - match:
    - headers:
        user-id:
          regex: ".*"
    route:
    - destination:
        host: user-service
        subset: v1
      weight: 90
    - destination:
        host: user-service
        subset: v2
      weight: 10
    # 使用用户ID作为哈希键
    hash:
      httpHeaderName: "user-id"

---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: user-service-destination-sticky
  namespace: production
spec:
  host: user-service
  trafficPolicy:
    # 启用会话保持
    consistentHash:
      httpHeaderName: "user-id"
  subsets:
  - name: v1
    labels:
      version: v1.0
    trafficPolicy:
      # 连接池配置，保持连接复用
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 100
          maxRequestsPerConnection: 2
  - name: v2
    labels:
      version: v2.0
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 100
          maxRequestsPerConnection: 2

---
# EnvoyFilter - 自定义会话保持逻辑
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: user-service-session-affinity
  namespace: production
spec:
  workloadSelector:
    labels:
      app: istio-proxy
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.lua
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
          inline_code: |
            function envoy_on_request(request_handle)
              local user_id = request_handle:headers():get("user-id")
              local canary_cookie = request_handle:headers():get("cookie")

              if user_id and not (canary_cookie and string.find(canary_cookie, "canary%-version=")) then
                -- 基于用户ID计算哈希，确定版本
                local hash = 0
                for i = 1, #user_id do
                  hash = hash + string.byte(user_id, i)
                end

                local version = "v1"
                if (hash % 100) < 10 then  -- 10%的用户分配到v2
                  version = "v2"
                end

                -- 设置版本标识头
                request_handle:headers():add("x-canary-version", version)
              end
            end

            function envoy_on_response(response_handle)
              local canary_version = response_handle:headers():get("x-canary-version")
              if canary_version then
                -- 设置Cookie保持会话
                local cookie = "canary-version=" .. canary_version .. "; Path=/; Max-Age=86400; HttpOnly"
                response_handle:headers():add("set-cookie", cookie)
              end
            end
```

### 2. Blue-Green发布策略

**特点**：快速切换，零停机时间
**适用场景**：关键业务系统发布

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service-bluegreen
  namespace: production
spec:
  hosts:
  - user-service
  http:
  - match:
    - headers:
        deployment:
          exact: "green"
    route:
    - destination:
        host: user-service
        subset: green
  - route:
    - destination:
        host: user-service
        subset: blue
```

### 3. A/B测试策略

**特点**：基于用户特征的精准分流
**适用场景**：功能验证和用户体验优化

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service-abtest
  namespace: production
spec:
  hosts:
  - user-service
  http:
  - match:
    - headers:
        user-group:
          exact: "premium"
    route:
    - destination:
        host: user-service
        subset: v2
  - match:
    - headers:
        user-id:
          regex: ".*[02468]$"  # 用户ID末位为偶数
    route:
    - destination:
        host: user-service
        subset: v2
  - route:
    - destination:
        host: user-service
        subset: v1
```

---

## 🌐 全链路灰度发布方案

### 核心概念

全链路灰度是指在微服务架构中，确保一个请求在整个调用链路上都使用相同版本的服务，实现端到端的版本一致性。支持HTTP、gRPC、WebSocket等多种协议的统一灰度控制。

### 协议支持矩阵

| 协议类型 | 路由方式 | 会话保持 | 链路追踪 | 实时监控 |
|---------|---------|---------|---------|---------|
| HTTP/HTTPS | Header路由 | Cookie/Header | ✅ | ✅ |
| gRPC | Metadata路由 | 一致性哈希 | ✅ | ✅ |
| WebSocket | 握手Header | 连接绑定 | ✅ | ✅ |
| TCP | SNI/端口 | 连接保持 | 部分 | ✅ |

### 全链路灰度标识设计

#### 核心Header定义

```go
// 全链路灰度Header常量
const (
    // 主要灰度标识
    HeaderCanaryVersion = "x-canary-version"    // v1/v2
    HeaderCanaryFlag    = "x-canary-flag"       // true/false
    HeaderTraceID       = "x-trace-id"          // 链路追踪ID
    HeaderUserGroup     = "x-user-group"        // 用户组标识
    HeaderClientID      = "x-client-id"         // 客户端ID

    // 协议特定标识
    HeaderProtocolType  = "x-protocol-type"     // http/grpc/websocket
    HeaderConnectionID  = "x-connection-id"     // 连接标识
    HeaderSessionID     = "x-session-id"        // 会话标识

    // 辅助标识
    HeaderCanaryWeight  = "x-canary-weight"     // 灰度权重
    HeaderCanaryRule    = "x-canary-rule"       // 灰度规则ID
    HeaderCanarySource  = "x-canary-source"     // 灰度来源
)

// 全链路灰度上下文
type CanaryContext struct {
    Version       string            `json:"version"`       // 版本标识
    TraceID       string            `json:"traceId"`       // 链路ID
    UserGroup     string            `json:"userGroup"`     // 用户组
    ClientID      string            `json:"clientId"`      // 客户端ID
    ProtocolType  string            `json:"protocolType"`  // 协议类型
    ConnectionID  string            `json:"connectionId"`  // 连接ID
    SessionID     string            `json:"sessionId"`     // 会话ID
    Rules         []string          `json:"rules"`         // 应用的规则
    Services      map[string]string `json:"services"`      // 服务版本映射
    StartTime     time.Time         `json:"startTime"`     // 开始时间
    Metadata      map[string]string `json:"metadata"`      // 扩展元数据
}
```

### HTTP协议灰度实现

#### HTTP中间件实现

```go
// internal/pkg/middleware/http_canary.go
package middleware

import (
    "context"
    "crypto/md5"
    "fmt"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
    "gorm.io/gorm"
)

// HTTPCanaryMiddleware HTTP协议灰度中间件
type HTTPCanaryMiddleware struct {
    db          *gorm.DB
    redisClient *redis.Client
    config      *CanaryConfig
}

func NewHTTPCanaryMiddleware(db *gorm.DB, redisClient *redis.Client, config *CanaryConfig) *HTTPCanaryMiddleware {
    return &HTTPCanaryMiddleware{
        db:          db,
        redisClient: redisClient,
        config:      config,
    }
}

// HTTPCanaryHandler HTTP全链路灰度处理器
func (m *HTTPCanaryMiddleware) HTTPCanaryHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        ctx := context.Background()

        // 1. 构建灰度上下文
        canaryCtx := m.buildHTTPCanaryContext(c)

        // 2. 版本决策
        version := m.makeVersionDecision(ctx, canaryCtx)

        // 3. 设置链路传递Header
        m.setHTTPCanaryHeaders(c, canaryCtx, version)

        // 4. 缓存决策结果
        if m.config.CacheEnabled {
            m.cacheVersionDecision(ctx, canaryCtx, version)
        }

        // 5. 记录链路日志
        m.logCanaryDecision(canaryCtx, version)

        c.Next()
    }
}

// buildHTTPCanaryContext 构建HTTP灰度上下文
func (m *HTTPCanaryMiddleware) buildHTTPCanaryContext(c *gin.Context) *CanaryContext {
    return &CanaryContext{
        TraceID:      m.getOrGenerateTraceID(c),
        UserGroup:    c.GetHeader("x-user-group"),
        ClientID:     c.GetHeader("x-client-id"),
        UserID:       c.GetHeader("x-user-id"),
        ProtocolType: "http",
        SessionID:    m.extractSessionID(c),
        StartTime:    time.Now(),
        Metadata: map[string]string{
            "user-agent":   c.GetHeader("User-Agent"),
            "client-ip":    c.ClientIP(),
            "path":         c.Request.URL.Path,
            "method":       c.Request.Method,
            "content-type": c.GetHeader("Content-Type"),
        },
    }
}

// setHTTPCanaryHeaders 设置HTTP链路传递Header
func (m *HTTPCanaryMiddleware) setHTTPCanaryHeaders(c *gin.Context, canaryCtx *CanaryContext, version string) {
    // 设置核心灰度Header
    c.Header(HeaderCanaryVersion, version)
    c.Header(HeaderCanaryFlag, strconv.FormatBool(version == "v2"))
    c.Header(HeaderTraceID, canaryCtx.TraceID)
    c.Header(HeaderProtocolType, "http")

    // 传递用户标识
    if canaryCtx.UserGroup != "" {
        c.Header(HeaderUserGroup, canaryCtx.UserGroup)
    }
    if canaryCtx.ClientID != "" {
        c.Header(HeaderClientID, canaryCtx.ClientID)
    }
    if canaryCtx.SessionID != "" {
        c.Header(HeaderSessionID, canaryCtx.SessionID)
    }

    // 设置服务版本映射
    serviceVersions := m.buildServiceVersionMap(version)
    for service, serviceVersion := range serviceVersions {
        headerKey := fmt.Sprintf("x-service-%s-version", service)
        c.Header(headerKey, serviceVersion)
    }
}
```

#### HTTP客户端拦截器

```go
// internal/pkg/client/http_client.go
package client

import (
    "context"
    "net/http"
)

// CanaryHTTPClient 支持全链路灰度的HTTP客户端
type CanaryHTTPClient struct {
    client *http.Client
}

func NewCanaryHTTPClient() *CanaryHTTPClient {
    client := &http.Client{
        Transport: &CanaryHTTPTransport{
            base: http.DefaultTransport,
        },
    }
    return &CanaryHTTPClient{client: client}
}

// CanaryHTTPTransport HTTP灰度传输层
type CanaryHTTPTransport struct {
    base http.RoundTripper
}

func (t *CanaryHTTPTransport) RoundTrip(req *http.Request) (*http.Response, error) {
    // 从上下文获取灰度信息
    canaryCtx := GetCanaryContextFromRequest(req)
    if canaryCtx != nil {
        // 传递灰度Header
        t.propagateHTTPCanaryHeaders(req, canaryCtx)
    }

    return t.base.RoundTrip(req)
}

func (t *CanaryHTTPTransport) propagateHTTPCanaryHeaders(req *http.Request, canaryCtx *CanaryContext) {
    // 传递核心灰度Header
    req.Header.Set(HeaderCanaryVersion, canaryCtx.Version)
    req.Header.Set(HeaderTraceID, canaryCtx.TraceID)
    req.Header.Set(HeaderCanaryFlag, strconv.FormatBool(canaryCtx.Version == "v2"))
    req.Header.Set(HeaderProtocolType, "http")

    // 传递用户标识
    if canaryCtx.UserGroup != "" {
        req.Header.Set(HeaderUserGroup, canaryCtx.UserGroup)
    }
    if canaryCtx.ClientID != "" {
        req.Header.Set(HeaderClientID, canaryCtx.ClientID)
    }
    if canaryCtx.SessionID != "" {
        req.Header.Set(HeaderSessionID, canaryCtx.SessionID)
    }

    // 传递目标服务版本
    targetService := extractServiceFromURL(req.URL.String())
    if targetVersion, exists := canaryCtx.Services[targetService]; exists {
        req.Header.Set(fmt.Sprintf("x-service-%s-version", targetService), targetVersion)
    }
}
```

### gRPC协议灰度实现

#### gRPC拦截器

```go
// internal/pkg/client/grpc_canary.go
package client

import (
    "context"
    "fmt"

    "google.golang.org/grpc"
    "google.golang.org/grpc/metadata"
)

// CanaryUnaryInterceptor gRPC一元调用灰度拦截器
func CanaryUnaryInterceptor(ctx context.Context, method string, req, reply interface{},
    cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {

    // 从上下文获取灰度信息
    canaryCtx := GetCanaryContextFromContext(ctx)
    if canaryCtx != nil {
        // 构建gRPC metadata
        md := metadata.New(map[string]string{
            HeaderCanaryVersion: canaryCtx.Version,
            HeaderTraceID:      canaryCtx.TraceID,
            HeaderUserGroup:    canaryCtx.UserGroup,
            HeaderClientID:     canaryCtx.ClientID,
            HeaderProtocolType: "grpc",
            HeaderSessionID:    canaryCtx.SessionID,
        })

        // 添加服务版本映射
        for service, version := range canaryCtx.Services {
            headerKey := fmt.Sprintf("x-service-%s-version", service)
            md.Set(headerKey, version)
        }

        // 将metadata附加到上下文
        ctx = metadata.NewOutgoingContext(ctx, md)
    }

    return invoker(ctx, method, req, reply, cc, opts...)
}

// CanaryStreamInterceptor gRPC流调用灰度拦截器
func CanaryStreamInterceptor(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn,
    method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {

    // 从上下文获取灰度信息
    canaryCtx := GetCanaryContextFromContext(ctx)
    if canaryCtx != nil {
        // 构建gRPC metadata
        md := metadata.New(map[string]string{
            HeaderCanaryVersion: canaryCtx.Version,
            HeaderTraceID:      canaryCtx.TraceID,
            HeaderUserGroup:    canaryCtx.UserGroup,
            HeaderClientID:     canaryCtx.ClientID,
            HeaderProtocolType: "grpc",
            HeaderSessionID:    canaryCtx.SessionID,
        })

        // 添加服务版本映射
        for service, version := range canaryCtx.Services {
            headerKey := fmt.Sprintf("x-service-%s-version", service)
            md.Set(headerKey, version)
        }

        // 将metadata附加到上下文
        ctx = metadata.NewOutgoingContext(ctx, md)
    }

    return streamer(ctx, desc, cc, method, opts...)
}

// gRPC服务端拦截器
func CanaryServerUnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo,
    handler grpc.UnaryHandler) (interface{}, error) {

    // 从metadata提取灰度信息
    if md, ok := metadata.FromIncomingContext(ctx); ok {
        canaryCtx := &CanaryContext{}

        if versions := md.Get(HeaderCanaryVersion); len(versions) > 0 {
            canaryCtx.Version = versions[0]
        }
        if traceIDs := md.Get(HeaderTraceID); len(traceIDs) > 0 {
            canaryCtx.TraceID = traceIDs[0]
        }
        if userGroups := md.Get(HeaderUserGroup); len(userGroups) > 0 {
            canaryCtx.UserGroup = userGroups[0]
        }
        if clientIDs := md.Get(HeaderClientID); len(clientIDs) > 0 {
            canaryCtx.ClientID = clientIDs[0]
        }
        if sessionIDs := md.Get(HeaderSessionID); len(sessionIDs) > 0 {
            canaryCtx.SessionID = sessionIDs[0]
        }

        canaryCtx.ProtocolType = "grpc"

        // 将灰度上下文存储到context
        ctx = context.WithValue(ctx, "canary-context", canaryCtx)
    }

    return handler(ctx, req)
}
```

### WebSocket协议灰度实现

#### WebSocket灰度处理器

```go
// internal/pkg/websocket/canary_handler.go
package websocket

import (
    "context"
    "fmt"
    "net/http"
    "sync"
    "time"

    "github.com/gorilla/websocket"
)

// WebSocketCanaryManager WebSocket灰度管理器
type WebSocketCanaryManager struct {
    connections map[string]*CanaryConnection  // 连接映射
    mutex       sync.RWMutex                  // 读写锁
    upgrader    websocket.Upgrader            // WebSocket升级器
}

type CanaryConnection struct {
    Conn         *websocket.Conn  `json:"conn"`
    CanaryCtx    *CanaryContext   `json:"canaryCtx"`
    ServiceVersion string         `json:"serviceVersion"`
    CreatedAt    time.Time        `json:"createdAt"`
    LastActivity time.Time        `json:"lastActivity"`
}

func NewWebSocketCanaryManager() *WebSocketCanaryManager {
    return &WebSocketCanaryManager{
        connections: make(map[string]*CanaryConnection),
        upgrader: websocket.Upgrader{
            CheckOrigin: func(r *http.Request) bool {
                return true // 在生产环境中应该进行适当的检查
            },
        },
    }
}

// HandleCanaryWebSocket 处理WebSocket灰度连接
func (m *WebSocketCanaryManager) HandleCanaryWebSocket(w http.ResponseWriter, r *http.Request) {
    // 1. 从HTTP请求头提取灰度信息
    canaryCtx := m.extractCanaryContextFromRequest(r)

    // 2. 版本决策
    version := m.makeWebSocketVersionDecision(canaryCtx)
    canaryCtx.Version = version

    // 3. 生成连接ID
    connectionID := m.generateConnectionID(canaryCtx)
    canaryCtx.ConnectionID = connectionID

    // 4. 升级HTTP连接为WebSocket
    conn, err := m.upgrader.Upgrade(w, r, http.Header{
        HeaderCanaryVersion: []string{version},
        HeaderTraceID:      []string{canaryCtx.TraceID},
        HeaderConnectionID: []string{connectionID},
        HeaderProtocolType: []string{"websocket"},
    })
    if err != nil {
        return
    }
    defer conn.Close()

    // 5. 注册连接
    canaryConn := &CanaryConnection{
        Conn:           conn,
        CanaryCtx:      canaryCtx,
        ServiceVersion: version,
        CreatedAt:      time.Now(),
        LastActivity:   time.Now(),
    }

    m.registerConnection(connectionID, canaryConn)
    defer m.unregisterConnection(connectionID)

    // 6. 处理消息
    m.handleMessages(canaryConn)
}

// extractCanaryContextFromRequest 从HTTP请求提取灰度上下文
func (m *WebSocketCanaryManager) extractCanaryContextFromRequest(r *http.Request) *CanaryContext {
    return &CanaryContext{
        TraceID:      r.Header.Get(HeaderTraceID),
        UserGroup:    r.Header.Get(HeaderUserGroup),
        ClientID:     r.Header.Get(HeaderClientID),
        SessionID:    r.Header.Get(HeaderSessionID),
        ProtocolType: "websocket",
        StartTime:    time.Now(),
        Metadata: map[string]string{
            "user-agent": r.Header.Get("User-Agent"),
            "origin":     r.Header.Get("Origin"),
            "host":       r.Host,
        },
    }
}

// handleMessages 处理WebSocket消息
func (m *WebSocketCanaryManager) handleMessages(canaryConn *CanaryConnection) {
    for {
        // 读取消息
        messageType, message, err := canaryConn.Conn.ReadMessage()
        if err != nil {
            break
        }

        // 更新活动时间
        canaryConn.LastActivity = time.Now()

        // 处理消息（根据灰度版本路由到不同的处理器）
        response := m.processMessage(canaryConn.CanaryCtx, message)

        // 发送响应
        err = canaryConn.Conn.WriteMessage(messageType, response)
        if err != nil {
            break
        }
    }
}

// processMessage 处理消息（根据灰度版本）
func (m *WebSocketCanaryManager) processMessage(canaryCtx *CanaryContext, message []byte) []byte {
    // 根据灰度版本选择不同的处理逻辑
    if canaryCtx.Version == "v2" {
        return m.processMessageV2(canaryCtx, message)
    }
    return m.processMessageV1(canaryCtx, message)
}

// registerConnection 注册连接
func (m *WebSocketCanaryManager) registerConnection(connectionID string, conn *CanaryConnection) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    m.connections[connectionID] = conn
}

// unregisterConnection 注销连接
func (m *WebSocketCanaryManager) unregisterConnection(connectionID string) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    delete(m.connections, connectionID)
}

// GetConnectionStats 获取连接统计
func (m *WebSocketCanaryManager) GetConnectionStats() map[string]interface{} {
    m.mutex.RLock()
    defer m.mutex.RUnlock()

    stats := map[string]interface{}{
        "total_connections": len(m.connections),
        "v1_connections":    0,
        "v2_connections":    0,
        "by_user_group":     make(map[string]int),
    }

    for _, conn := range m.connections {
        if conn.ServiceVersion == "v1" {
            stats["v1_connections"] = stats["v1_connections"].(int) + 1
        } else if conn.ServiceVersion == "v2" {
            stats["v2_connections"] = stats["v2_connections"].(int) + 1
        }

        if conn.CanaryCtx.UserGroup != "" {
            userGroupStats := stats["by_user_group"].(map[string]int)
            userGroupStats[conn.CanaryCtx.UserGroup]++
        }
    }

    return stats
}
```

### Istio配置自动生成

#### 全协议VirtualService配置

```yaml
# 全协议支持的VirtualService模板
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{.ServiceName}}-fullchain-canary
  namespace: {{.Namespace}}
spec:
  hosts:
  - {{.ServiceName}}

  # HTTP路由规则
  http:
  # 1. 明确的服务版本指定
  - match:
    - headers:
        x-service-{{.ServiceName}}-version:
          exact: "v2"
    route:
    - destination:
        host: {{.ServiceName}}
        subset: v2
      weight: 100

  # 2. 基于协议类型的路由
  - match:
    - headers:
        x-protocol-type:
          exact: "http"
        x-canary-version:
          exact: "v2"
    route:
    - destination:
        host: {{.ServiceName}}
        subset: v2
      weight: 100

  # 3. WebSocket协议路由
  - match:
    - headers:
        x-protocol-type:
          exact: "websocket"
        x-canary-version:
          exact: "v2"
    route:
    - destination:
        host: {{.ServiceName}}
        subset: v2
      weight: 100

  # 4. 基于用户组的路由
  {{range .UserGroupMappings}}
  - match:
    - headers:
        x-user-group:
          exact: "{{.UserGroup}}"
    route:
    - destination:
        host: {{$.ServiceName}}
        subset: {{.TargetVersion}}
      weight: 100
  {{end}}

  # 5. 基于Client-ID的一致性哈希路由
  - match:
    - headers:
        x-client-id:
          regex: ".*"
    route:
    - destination:
        host: {{.ServiceName}}
        subset: v1
      weight: {{.V1Weight}}
    - destination:
        host: {{.ServiceName}}
        subset: v2
      weight: {{.V2Weight}}
    hash:
      httpHeaderName: "x-client-id"

  # 6. 默认路由
  - route:
    - destination:
        host: {{.ServiceName}}
        subset: v1
      weight: 100

---
# DestinationRule配置
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{.ServiceName}}-fullchain-destination
  namespace: {{.Namespace}}
spec:
  host: {{.ServiceName}}
  trafficPolicy:
    consistentHash:
      httpHeaderName: "x-client-id"
  subsets:
  - name: v1
    labels:
      version: v1
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 100
          maxRequestsPerConnection: 2
          h2UpgradePolicy: UPGRADE  # 支持HTTP/2升级
  - name: v2
    labels:
      version: v2
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 100
          maxRequestsPerConnection: 2
          h2UpgradePolicy: UPGRADE
```

#### EnvoyFilter增强配置

```yaml
# 全协议灰度EnvoyFilter
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: fullchain-canary-enhancement
  namespace: {{.Namespace}}
spec:
  configPatches:
  # HTTP协议增强
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.lua
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
          inline_code: |
            function envoy_on_request(request_handle)
              -- 获取协议类型
              local protocol_type = request_handle:headers():get("x-protocol-type") or "http"
              local canary_version = request_handle:headers():get("x-canary-version")
              local trace_id = request_handle:headers():get("x-trace-id")

              -- 记录协议类型和版本信息
              request_handle:logInfo("Protocol: " .. protocol_type ..
                                   " Version: " .. (canary_version or "unknown") ..
                                   " TraceID: " .. (trace_id or "none"))

              -- 处理WebSocket升级
              local upgrade = request_handle:headers():get("upgrade")
              if upgrade and string.lower(upgrade) == "websocket" then
                request_handle:headers():add("x-protocol-type", "websocket")
                request_handle:logInfo("WebSocket upgrade detected")
              end

              -- 处理gRPC请求
              local content_type = request_handle:headers():get("content-type")
              if content_type and string.find(content_type, "application/grpc") then
                request_handle:headers():add("x-protocol-type", "grpc")
                request_handle:logInfo("gRPC request detected")
              end
            end

            function envoy_on_response(response_handle)
              -- 在响应中添加协议和版本信息
              local protocol_type = response_handle:headers():get("x-protocol-type")
              local canary_version = response_handle:headers():get("x-canary-version")

              if protocol_type and canary_version then
                response_handle:headers():add("x-served-by", protocol_type .. "-" .. canary_version)
              end
            end

  # TCP代理增强（用于gRPC和其他TCP协议）
  - applyTo: NETWORK_FILTER
    match:
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.tcp_proxy"
    patch:
      operation: MERGE
      value:
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
          access_log:
          - name: envoy.access_loggers.file
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              path: "/dev/stdout"
              format: |
                [%START_TIME%] "%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%"
                %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT%
                %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%"
                "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"
                "canary_version:%REQ(X-CANARY-VERSION)%" "protocol_type:%REQ(X-PROTOCOL-TYPE)%"
```

### 数据模型扩展

```go
// FullChainCanaryConfig 全链路灰度配置
type FullChainCanaryConfig struct {
    ID                 uint                     `json:"id" gorm:"primarykey"`
    Name               string                   `json:"name" gorm:"comment:配置名称"`
    Namespace          string                   `json:"namespace" gorm:"comment:命名空间"`
    ProtocolSupport    []string                 `json:"protocolSupport" gorm:"type:json;comment:支持的协议列表"`
    GlobalVersion      string                   `json:"globalVersion" gorm:"comment:全局版本"`
    CanaryPercentage   int                      `json:"canaryPercentage" gorm:"comment:灰度流量百分比"`
    ServiceMappings    []ServiceVersionMapping  `json:"serviceMappings" gorm:"foreignKey:ConfigID"`
    UserGroupRules     []UserGroupCanaryRule    `json:"userGroupRules" gorm:"foreignKey:ConfigID"`
    ProtocolConfigs    []ProtocolConfig         `json:"protocolConfigs" gorm:"foreignKey:ConfigID"`
    ConsistentHash     bool                     `json:"consistentHash" gorm:"comment:是否启用一致性哈希"`
    HashKey            string                   `json:"hashKey" gorm:"comment:哈希键(client-id/user-id)"`
    TraceEnabled       bool                     `json:"traceEnabled" gorm:"comment:是否启用链路追踪"`
    Status             string                   `json:"status" gorm:"comment:状态(active/inactive)"`
    CreatedAt          time.Time                `json:"createdAt"`
    UpdatedAt          time.Time                `json:"updatedAt"`
}

// ProtocolConfig 协议特定配置
type ProtocolConfig struct {
    ID                uint      `json:"id" gorm:"primarykey"`
    ConfigID          uint      `json:"configId" gorm:"comment:全链路配置ID"`
    ProtocolType      string    `json:"protocolType" gorm:"comment:协议类型(http/grpc/websocket/tcp)"`
    RoutingStrategy   string    `json:"routingStrategy" gorm:"comment:路由策略"`
    SessionAffinity   bool      `json:"sessionAffinity" gorm:"comment:是否启用会话保持"`
    ConnectionTimeout int       `json:"connectionTimeout" gorm:"comment:连接超时(秒)"`
    KeepAlive         bool      `json:"keepAlive" gorm:"comment:是否保持连接"`
    MaxConnections    int       `json:"maxConnections" gorm:"comment:最大连接数"`
    LoadBalancer      string    `json:"loadBalancer" gorm:"comment:负载均衡算法"`
    HealthCheck       bool      `json:"healthCheck" gorm:"comment:是否启用健康检查"`
    RetryPolicy       string    `json:"retryPolicy" gorm:"type:json;comment:重试策略配置"`
    CircuitBreaker    string    `json:"circuitBreaker" gorm:"type:json;comment:熔断器配置"`
    CreatedAt         time.Time `json:"createdAt"`
    UpdatedAt         time.Time `json:"updatedAt"`
}

// CanaryChainTrace 全链路灰度追踪
type CanaryChainTrace struct {
    ID           uint                   `json:"id" gorm:"primarykey"`
    TraceID      string                 `json:"traceId" gorm:"uniqueIndex;comment:链路追踪ID"`
    ProtocolType string                 `json:"protocolType" gorm:"comment:协议类型"`
    UserGroup    string                 `json:"userGroup" gorm:"comment:用户组"`
    ClientID     string                 `json:"clientId" gorm:"comment:客户端ID"`
    ConnectionID string                 `json:"connectionId" gorm:"comment:连接ID"`
    SessionID    string                 `json:"sessionId" gorm:"comment:会话ID"`
    Version      string                 `json:"version" gorm:"comment:全局版本"`
    StartTime    time.Time              `json:"startTime" gorm:"comment:开始时间"`
    EndTime      *time.Time             `json:"endTime" gorm:"comment:结束时间"`
    Duration     int64                  `json:"duration" gorm:"comment:总耗时(ms)"`
    ServiceCalls []CanaryServiceCall    `json:"serviceCalls" gorm:"foreignKey:TraceID;references:TraceID"`
    Status       string                 `json:"status" gorm:"comment:状态(success/error)"`
    ErrorMsg     string                 `json:"errorMsg" gorm:"comment:错误信息"`
    CreatedAt    time.Time              `json:"createdAt"`
    UpdatedAt    time.Time              `json:"updatedAt"`
}

// CanaryServiceCall 服务调用记录
type CanaryServiceCall struct {
    ID             uint      `json:"id" gorm:"primarykey"`
    TraceID        string    `json:"traceId" gorm:"comment:链路追踪ID"`
    ServiceName    string    `json:"serviceName" gorm:"comment:服务名称"`
    ServiceVersion string    `json:"serviceVersion" gorm:"comment:服务版本"`
    ProtocolType   string    `json:"protocolType" gorm:"comment:协议类型"`
    Method         string    `json:"method" gorm:"comment:调用方法"`
    StartTime      time.Time `json:"startTime" gorm:"comment:开始时间"`
    EndTime        time.Time `json:"endTime" gorm:"comment:结束时间"`
    Duration       int64     `json:"duration" gorm:"comment:耗时(ms)"`
    StatusCode     int       `json:"statusCode" gorm:"comment:状态码"`
    Success        bool      `json:"success" gorm:"comment:是否成功"`
    ErrorMsg       string    `json:"errorMsg" gorm:"comment:错误信息"`
    RequestSize    int64     `json:"requestSize" gorm:"comment:请求大小(bytes)"`
    ResponseSize   int64     `json:"responseSize" gorm:"comment:响应大小(bytes)"`
    Order          int       `json:"order" gorm:"comment:调用顺序"`
    CreatedAt      time.Time `json:"createdAt"`
}

// WebSocketConnection WebSocket连接记录
type WebSocketConnection struct {
    ID             uint      `json:"id" gorm:"primarykey"`
    ConnectionID   string    `json:"connectionId" gorm:"uniqueIndex;comment:连接ID"`
    TraceID        string    `json:"traceId" gorm:"comment:链路追踪ID"`
    ServiceName    string    `json:"serviceName" gorm:"comment:服务名称"`
    ServiceVersion string    `json:"serviceVersion" gorm:"comment:服务版本"`
    UserGroup      string    `json:"userGroup" gorm:"comment:用户组"`
    ClientID       string    `json:"clientId" gorm:"comment:客户端ID"`
    SessionID      string    `json:"sessionId" gorm:"comment:会话ID"`
    ConnectedAt    time.Time `json:"connectedAt" gorm:"comment:连接时间"`
    DisconnectedAt *time.Time `json:"disconnectedAt" gorm:"comment:断开时间"`
    LastActivity   time.Time `json:"lastActivity" gorm:"comment:最后活动时间"`
    MessageCount   int64     `json:"messageCount" gorm:"comment:消息数量"`
    BytesReceived  int64     `json:"bytesReceived" gorm:"comment:接收字节数"`
    BytesSent      int64     `json:"bytesSent" gorm:"comment:发送字节数"`
    Status         string    `json:"status" gorm:"comment:状态(active/disconnected)"`
    DisconnectReason string  `json:"disconnectReason" gorm:"comment:断开原因"`
    CreatedAt      time.Time `json:"createdAt"`
    UpdatedAt      time.Time `json:"updatedAt"`
}
```

### 监控指标扩展

```yaml
# 全链路灰度Prometheus指标
- name: canary_fullchain_requests_total
  help: "全链路灰度请求总数"
  type: counter
  labels: [protocol_type, trace_id, version, user_group, service_chain]

- name: canary_fullchain_duration_seconds
  help: "全链路灰度请求耗时"
  type: histogram
  labels: [protocol_type, trace_id, version, user_group, service_chain]
  buckets: [0.1, 0.25, 0.5, 1, 2.5, 5, 10]

- name: canary_protocol_distribution
  help: "协议类型分布"
  type: gauge
  labels: [protocol_type, version, namespace]

- name: canary_websocket_connections_active
  help: "活跃WebSocket连接数"
  type: gauge
  labels: [service_name, version, user_group]

- name: canary_grpc_calls_total
  help: "gRPC调用总数"
  type: counter
  labels: [service_name, method, version, status]

- name: canary_service_version_consistency
  help: "服务版本一致性指标"
  type: gauge
  labels: [trace_id, expected_version, actual_version, service_name, protocol_type]

- name: canary_chain_success_rate
  help: "全链路灰度成功率"
  type: gauge
  labels: [protocol_type, version, user_group, service_chain]

- name: canary_connection_duration_seconds
  help: "连接持续时间"
  type: histogram
  labels: [protocol_type, service_name, version]
  buckets: [1, 10, 60, 300, 1800, 3600]
```

---

## 📋 泳道图设计

### 灰度发布完整流程泳道图

```mermaid
graph TB
    subgraph "开发人员"
        A1[创建工作流] --> A2[配置发布策略]
        A2 --> A3[提交审批]
    end

    subgraph "审批人员"
        B1[接收审批通知] --> B2[审查发布计划]
        B2 --> B3{审批决定}
        B3 -->|通过| B4[批准发布]
        B3 -->|拒绝| B5[拒绝并反馈]
    end

    subgraph "系统自动化"
        C1[创建灰度Deployment] --> C2[配置Istio规则]
        C2 --> C3[开始流量分发]
        C3 --> C4[监控关键指标]
        C4 --> C5{指标是否正常}
        C5 -->|正常| C6[继续下一步]
        C5 -->|异常| C7[自动回滚]
        C6 --> C8[逐步增加流量]
        C8 --> C9[全量发布]
    end

    subgraph "运维人员"
        D1[监控发布进度] --> D2[查看实时指标]
        D2 --> D3{是否需要干预}
        D3 -->|需要| D4[手动暂停/回滚]
        D3 -->|不需要| D5[持续观察]
    end

    subgraph "最终用户"
        E1[访问服务] --> E2[体验新功能]
        E2 --> E3[反馈使用体验]
    end

    A3 --> B1
    B4 --> C1
    B5 --> A2
    C4 --> D1
    C3 --> E1
    D4 --> C7
```

### 用户群体控制流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as 网关
    participant I as Istio
    participant S1 as 服务V1
    participant S2 as 服务V2

    U->>G: 发送请求 (带用户标签)
    G->>I: 转发请求

    alt 用户属于灰度群体
        I->>I: 匹配灰度规则
        I->>S2: 路由到新版本
        S2->>I: 返回响应
    else 用户不属于灰度群体
        I->>I: 匹配默认规则
        I->>S1: 路由到稳定版本
        S1->>I: 返回响应
    end

    I->>G: 返回响应
    G->>U: 返回最终响应
```

---

## 🔧 数据模型设计

### 工作流模型

```go
// Workflow 工作流定义
type Workflow struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    Name        string    `json:"name" gorm:"comment:工作流名称"`
    Description string    `json:"description" gorm:"comment:工作流描述"`
    ServiceName string    `json:"serviceName" gorm:"comment:目标服务名"`
    Namespace   string    `json:"namespace" gorm:"comment:命名空间"`
    Status      string    `json:"status" gorm:"comment:状态(draft/pending/approved/running/completed/failed)"`
    Config      string    `json:"config" gorm:"type:text;comment:工作流配置JSON"`
    CreatorID   uint      `json:"creatorId" gorm:"comment:创建者ID"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`

    Steps       []WorkflowStep     `json:"steps" gorm:"foreignKey:WorkflowID"`
    Executions  []WorkflowExecution `json:"executions" gorm:"foreignKey:WorkflowID"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    WorkflowID  uint      `json:"workflowId"`
    Name        string    `json:"name" gorm:"comment:步骤名称"`
    Type        string    `json:"type" gorm:"comment:步骤类型(deployment/traffic-split/monitoring/approval)"`
    Order       int       `json:"order" gorm:"comment:执行顺序"`
    Config      string    `json:"config" gorm:"type:text;comment:步骤配置JSON"`
    DependsOn   string    `json:"dependsOn" gorm:"comment:依赖步骤ID列表"`
    Approvers   string    `json:"approvers" gorm:"comment:审批人列表"`
    AutoExecute bool      `json:"autoExecute" gorm:"comment:是否自动执行"`
    Status      string    `json:"status" gorm:"comment:状态(pending/running/completed/failed/skipped)"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`
}

// WorkflowExecution 工作流执行记录
type WorkflowExecution struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    WorkflowID  uint      `json:"workflowId"`
    Status      string    `json:"status" gorm:"comment:执行状态"`
    StartTime   time.Time `json:"startTime" gorm:"comment:开始时间"`
    EndTime     *time.Time `json:"endTime" gorm:"comment:结束时间"`
    ExecutorID  uint      `json:"executorId" gorm:"comment:执行者ID"`
    Logs        string    `json:"logs" gorm:"type:text;comment:执行日志"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`

    StepExecutions []StepExecution `json:"stepExecutions" gorm:"foreignKey:ExecutionID"`
}

// StepExecution 步骤执行记录
type StepExecution struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    ExecutionID uint      `json:"executionId"`
    StepID      uint      `json:"stepId"`
    Status      string    `json:"status" gorm:"comment:执行状态"`
    StartTime   time.Time `json:"startTime"`
    EndTime     *time.Time `json:"endTime"`
    Output      string    `json:"output" gorm:"type:text;comment:执行输出"`
    Error       string    `json:"error" gorm:"type:text;comment:错误信息"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`
}
```

### 审批模型

```go
// Approval 审批记录
type Approval struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    WorkflowID  uint      `json:"workflowId"`
    StepID      uint      `json:"stepId"`
    ApproverID  uint      `json:"approverId" gorm:"comment:审批人ID"`
    Status      string    `json:"status" gorm:"comment:审批状态(pending/approved/rejected)"`
    Comment     string    `json:"comment" gorm:"comment:审批意见"`
    ApprovedAt  *time.Time `json:"approvedAt" gorm:"comment:审批时间"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`
}

// UserGroup 用户群体定义
type UserGroup struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    Name        string    `json:"name" gorm:"comment:群体名称"`
    Description string    `json:"description" gorm:"comment:群体描述"`
    Rules       string    `json:"rules" gorm:"type:text;comment:匹配规则JSON"`
    Status      string    `json:"status" gorm:"comment:状态(active/inactive)"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`

    // 关联的服务绑定
    ServiceBindings []UserGroupServiceBinding `json:"serviceBindings" gorm:"foreignKey:UserGroupID"`
}

// UserGroupServiceBinding 用户组服务绑定
type UserGroupServiceBinding struct {
    ID            uint      `json:"id" gorm:"primarykey"`
    UserGroupID   uint      `json:"userGroupId" gorm:"comment:用户组ID"`
    ServiceName   string    `json:"serviceName" gorm:"comment:服务名称"`
    Namespace     string    `json:"namespace" gorm:"comment:命名空间"`
    TargetVersion string    `json:"targetVersion" gorm:"comment:目标版本"`
    Priority      int       `json:"priority" gorm:"comment:优先级(数值越大优先级越高)"`
    Status        string    `json:"status" gorm:"comment:状态(active/inactive)"`
    StartTime     *time.Time `json:"startTime" gorm:"comment:生效开始时间"`
    EndTime       *time.Time `json:"endTime" gorm:"comment:生效结束时间"`
    CreatedAt     time.Time `json:"createdAt"`
    UpdatedAt     time.Time `json:"updatedAt"`

    // 关联关系
    UserGroup     UserGroup `json:"userGroup" gorm:"foreignKey:UserGroupID"`
}

// UserGroupMember 用户组成员
type UserGroupMember struct {
    ID          uint      `json:"id" gorm:"primarykey"`
    UserGroupID uint      `json:"userGroupId" gorm:"comment:用户组ID"`
    UserID      string    `json:"userId" gorm:"comment:用户ID"`
    UserEmail   string    `json:"userEmail" gorm:"comment:用户邮箱"`
    JoinedAt    time.Time `json:"joinedAt" gorm:"comment:加入时间"`
    Status      string    `json:"status" gorm:"comment:状态(active/inactive)"`
    CreatedAt   time.Time `json:"createdAt"`
    UpdatedAt   time.Time `json:"updatedAt"`

    // 关联关系
    UserGroup   UserGroup `json:"userGroup" gorm:"foreignKey:UserGroupID"`
}

// SessionAffinity 会话一致性配置
type SessionAffinity struct {
    ID            uint      `json:"id" gorm:"primarykey"`
    ServiceName   string    `json:"serviceName" gorm:"comment:服务名称"`
    Namespace     string    `json:"namespace" gorm:"comment:命名空间"`
    AffinityType  string    `json:"affinityType" gorm:"comment:一致性类型(cookie/header/consistent-hash)"`
    HashKey       string    `json:"hashKey" gorm:"comment:哈希键(user-id/session-id等)"`
    CookieName    string    `json:"cookieName" gorm:"comment:Cookie名称"`
    CookieMaxAge  int       `json:"cookieMaxAge" gorm:"comment:Cookie过期时间(秒)"`
    HeaderName    string    `json:"headerName" gorm:"comment:Header名称"`
    Enabled       bool      `json:"enabled" gorm:"comment:是否启用"`
    CreatedAt     time.Time `json:"createdAt"`
    UpdatedAt     time.Time `json:"updatedAt"`
}

// UserSessionMapping 用户会话映射
type UserSessionMapping struct {
    ID           uint      `json:"id" gorm:"primarykey"`
    UserID       string    `json:"userId" gorm:"comment:用户ID"`
    SessionID    string    `json:"sessionId" gorm:"comment:会话ID"`
    ServiceName  string    `json:"serviceName" gorm:"comment:服务名称"`
    ServiceVersion string  `json:"serviceVersion" gorm:"comment:服务版本"`
    AssignedAt   time.Time `json:"assignedAt" gorm:"comment:分配时间"`
    ExpiresAt    time.Time `json:"expiresAt" gorm:"comment:过期时间"`
    LastAccessAt time.Time `json:"lastAccessAt" gorm:"comment:最后访问时间"`
    Status       string    `json:"status" gorm:"comment:状态(active/expired)"`
    CreatedAt    time.Time `json:"createdAt"`
    UpdatedAt    time.Time `json:"updatedAt"`

    // 索引优化
    Index        string    `json:"-" gorm:"index:idx_user_service,unique"`
}

// CanaryTrafficRule 金丝雀流量规则
type CanaryTrafficRule struct {
    ID                uint      `json:"id" gorm:"primarykey"`
    ServiceName       string    `json:"serviceName" gorm:"comment:服务名称"`
    Namespace         string    `json:"namespace" gorm:"comment:命名空间"`
    RuleName          string    `json:"ruleName" gorm:"comment:规则名称"`
    RuleType          string    `json:"ruleType" gorm:"comment:规则类型(weight/header/usergroup/client-id)"`
    SourceVersion     string    `json:"sourceVersion" gorm:"comment:源版本"`
    TargetVersion     string    `json:"targetVersion" gorm:"comment:目标版本"`
    TrafficWeight     int       `json:"trafficWeight" gorm:"comment:流量权重百分比"`
    SessionAffinity   bool      `json:"sessionAffinity" gorm:"comment:是否启用会话一致性"`
    AffinityConfig    string    `json:"affinityConfig" gorm:"type:text;comment:一致性配置JSON"`
    MatchConditions   string    `json:"matchConditions" gorm:"type:text;comment:匹配条件JSON"`
    Priority          int       `json:"priority" gorm:"comment:优先级"`
    Status            string    `json:"status" gorm:"comment:状态(active/inactive)"`
    CreatedAt         time.Time `json:"createdAt"`
    UpdatedAt         time.Time `json:"updatedAt"`
}

// ClientIdAllocation Client-ID版本分配记录
type ClientIdAllocation struct {
    ID             uint      `json:"id" gorm:"primarykey"`
    ClientId       string    `json:"clientId" gorm:"uniqueIndex;comment:客户端ID"`
    ClientType     string    `json:"clientType" gorm:"comment:客户端类型(web/mobile/api)"`
    ServiceName    string    `json:"serviceName" gorm:"comment:服务名称"`
    ServiceVersion string    `json:"serviceVersion" gorm:"comment:分配的服务版本"`
    HashValue      uint32    `json:"hashValue" gorm:"comment:哈希值"`
    AssignedAt     time.Time `json:"assignedAt" gorm:"comment:分配时间"`
    LastAccessAt   time.Time `json:"lastAccessAt" gorm:"comment:最后访问时间"`
    AccessCount    int64     `json:"accessCount" gorm:"comment:访问次数"`
    Status         string    `json:"status" gorm:"comment:状态(active/expired)"`
    ExpiresAt      time.Time `json:"expiresAt" gorm:"comment:过期时间"`
    CreatedAt      time.Time `json:"createdAt"`
    UpdatedAt      time.Time `json:"updatedAt"`

    // 索引优化
    Index          string    `json:"-" gorm:"index:idx_client_service,unique"`
}

// ClientIdConfig Client-ID配置
type ClientIdConfig struct {
    ID                 uint      `json:"id" gorm:"primarykey"`
    ServiceName        string    `json:"serviceName" gorm:"comment:服务名称"`
    Namespace          string    `json:"namespace" gorm:"comment:命名空间"`
    CanaryPercentage   int       `json:"canaryPercentage" gorm:"comment:金丝雀流量百分比"`
    HashAlgorithm      string    `json:"hashAlgorithm" gorm:"comment:哈希算法(sha256/md5)"`
    CacheEnabled       bool      `json:"cacheEnabled" gorm:"comment:是否启用缓存"`
    CacheTTL           int       `json:"cacheTtl" gorm:"comment:缓存TTL(秒)"`
    ClientIdPattern    string    `json:"clientIdPattern" gorm:"comment:Client-ID匹配模式"`
    AllowTempClientId  bool      `json:"allowTempClientId" gorm:"comment:是否允许临时Client-ID"`
    Status             string    `json:"status" gorm:"comment:状态(active/inactive)"`
    CreatedAt          time.Time `json:"createdAt"`
    UpdatedAt          time.Time `json:"updatedAt"`
}

// ClientIdMetrics Client-ID指标统计
type ClientIdMetrics struct {
    ID               uint      `json:"id" gorm:"primarykey"`
    ServiceName      string    `json:"serviceName" gorm:"comment:服务名称"`
    ClientType       string    `json:"clientType" gorm:"comment:客户端类型"`
    TotalClients     int64     `json:"totalClients" gorm:"comment:总客户端数"`
    V1Clients        int64     `json:"v1Clients" gorm:"comment:V1版本客户端数"`
    V2Clients        int64     `json:"v2Clients" gorm:"comment:V2版本客户端数"`
    NewClientsToday  int64     `json:"newClientsToday" gorm:"comment:今日新增客户端"`
    ActiveClients    int64     `json:"activeClients" gorm:"comment:活跃客户端数"`
    TotalRequests    int64     `json:"totalRequests" gorm:"comment:总请求数"`
    V1Requests       int64     `json:"v1Requests" gorm:"comment:V1版本请求数"`
    V2Requests       int64     `json:"v2Requests" gorm:"comment:V2版本请求数"`
    MetricDate       time.Time `json:"metricDate" gorm:"comment:统计日期"`
    CreatedAt        time.Time `json:"createdAt"`
    UpdatedAt        time.Time `json:"updatedAt"`
}
```

### 监控模型

```go
// DeploymentMetrics 发布指标
type DeploymentMetrics struct {
    ID           uint      `json:"id" gorm:"primarykey"`
    WorkflowID   uint      `json:"workflowId"`
    ExecutionID  uint      `json:"executionId"`
    ServiceName  string    `json:"serviceName"`
    Version      string    `json:"version"`
    Timestamp    time.Time `json:"timestamp"`
    SuccessRate  float64   `json:"successRate" gorm:"comment:成功率"`
    ResponseTime float64   `json:"responseTime" gorm:"comment:响应时间(ms)"`
    ErrorRate    float64   `json:"errorRate" gorm:"comment:错误率"`
    ThroughputQPS float64   `json:"throughputQps" gorm:"comment:QPS"`
    CPUUsage     float64   `json:"cpuUsage" gorm:"comment:CPU使用率"`
    MemoryUsage  float64   `json:"memoryUsage" gorm:"comment:内存使用率"`
    CreatedAt    time.Time `json:"createdAt"`
}
```

---

## 🚀 API设计

### 工作流管理API

```go
// 工作流路由注册
func RegisterWorkflowRoutes(r *gin.RouterGroup) {
    workflows := r.Group("/workflows")
    {
        workflows.GET("", listWorkflows)           // 获取工作流列表
        workflows.POST("", createWorkflow)         // 创建工作流
        workflows.GET("/:id", getWorkflow)         // 获取工作流详情
        workflows.PUT("/:id", updateWorkflow)      // 更新工作流
        workflows.DELETE("/:id", deleteWorkflow)   // 删除工作流

        // 工作流执行相关
        workflows.POST("/:id/execute", executeWorkflow)     // 执行工作流
        workflows.POST("/:id/pause", pauseWorkflow)         // 暂停工作流
        workflows.POST("/:id/resume", resumeWorkflow)       // 恢复工作流
        workflows.POST("/:id/rollback", rollbackWorkflow)   // 回滚工作流

        // 工作流步骤相关
        workflows.GET("/:id/steps", getWorkflowSteps)       // 获取步骤列表
        workflows.POST("/:id/steps/:stepId/approve", approveStep)  // 审批步骤
        workflows.POST("/:id/steps/:stepId/reject", rejectStep)    // 拒绝步骤

        // 监控相关
        workflows.GET("/:id/metrics", getWorkflowMetrics)   // 获取工作流指标
        workflows.GET("/:id/logs", getWorkflowLogs)         // 获取执行日志
    }
}
```

### 用户群体管理API

```go
// 用户群体路由注册
func RegisterUserGroupRoutes(r *gin.RouterGroup) {
    userGroups := r.Group("/user-groups")
    {
        userGroups.GET("", listUserGroups)         // 获取用户群体列表
        userGroups.POST("", createUserGroup)       // 创建用户群体
        userGroups.GET("/:id", getUserGroup)       // 获取用户群体详情
        userGroups.PUT("/:id", updateUserGroup)    // 更新用户群体
        userGroups.DELETE("/:id", deleteUserGroup) // 删除用户群体

        // 用户群体测试
        userGroups.POST("/:id/test", testUserGroup)     // 测试用户是否匹配群体
        userGroups.GET("/:id/users", getUsersInGroup)   // 获取群体内用户列表

        // 用户群体成员管理
        userGroups.POST("/:id/members", addUserGroupMember)       // 添加用户到群体
        userGroups.DELETE("/:id/members/:userId", removeUserGroupMember) // 从群体移除用户
        userGroups.GET("/:id/members", listUserGroupMembers)      // 获取群体成员列表

        // 服务绑定管理
        userGroups.GET("/:id/services", listUserGroupServices)        // 获取用户组绑定的服务列表
        userGroups.POST("/:id/services", bindUserGroupToService)      // 绑定用户组到服务
        userGroups.PUT("/:id/services/:bindingId", updateServiceBinding) // 更新服务绑定
        userGroups.DELETE("/:id/services/:bindingId", unbindUserGroupFromService) // 解绑服务

        // 服务路由预览
        userGroups.GET("/:id/preview-routes", previewUserGroupRoutes) // 预览用户组的路由规则
    }
}

// 服务管理API
func RegisterServiceRoutes(r *gin.RouterGroup) {
    services := r.Group("/services")
    {
        services.GET("", listServices)                    // 获取服务列表
        services.GET("/:name/user-groups", getServiceUserGroups) // 获取服务关联的用户组
        services.GET("/:name/routing-rules", getServiceRoutingRules) // 获取服务的路由规则
        services.POST("/:name/apply-routes", applyServiceRoutes)     // 应用服务路由规则

        // 会话一致性管理
        services.GET("/:name/session-affinity", getSessionAffinityConfig)    // 获取会话一致性配置
        services.POST("/:name/session-affinity", setSessionAffinityConfig)   // 设置会话一致性配置
        services.PUT("/:name/session-affinity", updateSessionAffinityConfig) // 更新会话一致性配置
        services.DELETE("/:name/session-affinity", deleteSessionAffinityConfig) // 删除会话一致性配置

        // 用户会话管理
        services.GET("/:name/user-sessions", getUserSessions)        // 获取用户会话映射
        services.POST("/:name/user-sessions/cleanup", cleanupExpiredSessions) // 清理过期会话
        services.GET("/:name/session-stats", getSessionStats)        // 获取会话统计信息
    }
}

// 流量规则管理API
func RegisterTrafficRuleRoutes(r *gin.RouterGroup) {
    trafficRules := r.Group("/traffic-rules")
    {
        trafficRules.GET("", listTrafficRules)           // 获取流量规则列表
        trafficRules.POST("", createTrafficRule)         // 创建流量规则
        trafficRules.GET("/:id", getTrafficRule)         // 获取流量规则详情
        trafficRules.PUT("/:id", updateTrafficRule)      // 更新流量规则
        trafficRules.DELETE("/:id", deleteTrafficRule)   // 删除流量规则

        // 规则应用和验证
        trafficRules.POST("/:id/apply", applyTrafficRule)     // 应用流量规则
        trafficRules.POST("/:id/validate", validateTrafficRule) // 验证流量规则
        trafficRules.GET("/:id/preview", previewTrafficRule)    // 预览流量规则效果
    }
}

// Client-ID管理API
func RegisterClientIdRoutes(r *gin.RouterGroup) {
    clientIds := r.Group("/client-ids")
    {
        // Client-ID配置管理
        clientIds.GET("/config", getClientIdConfig)              // 获取Client-ID配置
        clientIds.POST("/config", createClientIdConfig)          // 创建Client-ID配置
        clientIds.PUT("/config/:id", updateClientIdConfig)       // 更新Client-ID配置
        clientIds.DELETE("/config/:id", deleteClientIdConfig)    // 删除Client-ID配置

        // Client-ID分配管理
        clientIds.GET("/allocations", listClientIdAllocations)   // 获取分配记录列表
        clientIds.GET("/allocations/:clientId", getClientIdAllocation) // 获取特定Client-ID分配
        clientIds.POST("/allocations/batch-update", batchUpdateAllocations) // 批量更新分配
        clientIds.DELETE("/allocations/:clientId", deleteClientIdAllocation) // 删除分配记录

        // Client-ID统计和监控
        clientIds.GET("/stats", getClientIdStats)                // 获取统计信息
        clientIds.GET("/metrics", getClientIdMetrics)            // 获取指标数据
        clientIds.GET("/metrics/history", getClientIdMetricsHistory) // 获取历史指标

        // Client-ID测试和验证
        clientIds.POST("/test-allocation", testClientIdAllocation) // 测试Client-ID分配
        clientIds.POST("/simulate", simulateClientIdDistribution) // 模拟分配分布

        // 缓存管理
        clientIds.POST("/cache/clear", clearClientIdCache)       // 清理缓存
        clientIds.GET("/cache/stats", getClientIdCacheStats)     // 获取缓存统计

        // 导入导出
        clientIds.POST("/export", exportClientIdData)            // 导出Client-ID数据
        clientIds.POST("/import", importClientIdData)            // 导入Client-ID数据
    }
}
```

### 用户群体服务绑定API示例

```go
// 绑定用户组到服务的请求结构
type BindUserGroupToServiceRequest struct {
    ServiceName   string     `json:"serviceName" binding:"required"`
    Namespace     string     `json:"namespace" binding:"required"`
    TargetVersion string     `json:"targetVersion" binding:"required"`
    Priority      int        `json:"priority"`
    StartTime     *time.Time `json:"startTime"`
    EndTime       *time.Time `json:"endTime"`
}

// 绑定用户组到服务
func bindUserGroupToService(c *gin.Context) {
    userGroupID := c.Param("id")
    var req BindUserGroupToServiceRequest

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }

    // 创建服务绑定
    binding := UserGroupServiceBinding{
        UserGroupID:   parseUint(userGroupID),
        ServiceName:   req.ServiceName,
        Namespace:     req.Namespace,
        TargetVersion: req.TargetVersion,
        Priority:      req.Priority,
        Status:        "active",
        StartTime:     req.StartTime,
        EndTime:       req.EndTime,
    }

    if err := db.Create(&binding).Error; err != nil {
        c.JSON(500, gin.H{"error": "创建服务绑定失败"})
        return
    }

    // 自动生成并应用Istio路由规则
    if err := generateAndApplyRoutingRules(binding); err != nil {
        c.JSON(500, gin.H{"error": "应用路由规则失败"})
        return
    }

    c.JSON(200, gin.H{"data": binding})
}

// 预览用户组路由规则
func previewUserGroupRoutes(c *gin.Context) {
    userGroupID := c.Param("id")

    // 获取用户组及其服务绑定
    var userGroup UserGroup
    if err := db.Preload("ServiceBindings").First(&userGroup, userGroupID).Error; err != nil {
        c.JSON(404, gin.H{"error": "用户组不存在"})
        return
    }

    // 生成路由规则预览
    routes := generateRoutingRulesPreview(userGroup)

    c.JSON(200, gin.H{"data": routes})
}

// 会话一致性配置
func setSessionAffinityConfig(c *gin.Context) {
    serviceName := c.Param("name")
    var req SessionAffinityRequest

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }

    // 创建或更新会话一致性配置
    affinity := SessionAffinity{
        ServiceName:  serviceName,
        Namespace:    req.Namespace,
        AffinityType: req.AffinityType,
        HashKey:      req.HashKey,
        CookieName:   req.CookieName,
        CookieMaxAge: req.CookieMaxAge,
        HeaderName:   req.HeaderName,
        Enabled:      req.Enabled,
    }

    if err := db.Create(&affinity).Error; err != nil {
        c.JSON(500, gin.H{"error": "创建会话一致性配置失败"})
        return
    }

    // 生成并应用Istio配置
    if err := generateSessionAffinityRules(affinity); err != nil {
        c.JSON(500, gin.H{"error": "应用会话一致性规则失败"})
        return
    }

    c.JSON(200, gin.H{"data": affinity})
}

// 获取用户会话映射
func getUserSessions(c *gin.Context) {
    serviceName := c.Param("name")
    page := c.DefaultQuery("page", "1")
    size := c.DefaultQuery("size", "20")

    var sessions []UserSessionMapping
    var total int64

    query := db.Where("service_name = ? AND status = ?", serviceName, "active")
    query.Count(&total)

    offset := (parseInt(page) - 1) * parseInt(size)
    if err := query.Offset(offset).Limit(parseInt(size)).Find(&sessions).Error; err != nil {
        c.JSON(500, gin.H{"error": "获取用户会话失败"})
        return
    }

    c.JSON(200, gin.H{
        "data": sessions,
        "total": total,
        "page": parseInt(page),
        "size": parseInt(size),
    })
}

// 清理过期会话
func cleanupExpiredSessions(c *gin.Context) {
    serviceName := c.Param("name")

    result := db.Where("service_name = ? AND expires_at < ?", serviceName, time.Now()).
              Update("status", "expired")

    if result.Error != nil {
        c.JSON(500, gin.H{"error": "清理过期会话失败"})
        return
    }

    c.JSON(200, gin.H{
        "message": "清理完成",
        "cleaned_count": result.RowsAffected,
    })
}

// 会话统计信息
func getSessionStats(c *gin.Context) {
    serviceName := c.Param("name")

    var stats struct {
        TotalSessions    int64 `json:"totalSessions"`
        ActiveSessions   int64 `json:"activeSessions"`
        ExpiredSessions  int64 `json:"expiredSessions"`
        V1Sessions       int64 `json:"v1Sessions"`
        V2Sessions       int64 `json:"v2Sessions"`
        AvgSessionDuration float64 `json:"avgSessionDuration"`
    }

    // 统计总会话数
    db.Model(&UserSessionMapping{}).Where("service_name = ?", serviceName).Count(&stats.TotalSessions)

    // 统计活跃会话数
    db.Model(&UserSessionMapping{}).Where("service_name = ? AND status = ?", serviceName, "active").Count(&stats.ActiveSessions)

    // 统计过期会话数
    db.Model(&UserSessionMapping{}).Where("service_name = ? AND status = ?", serviceName, "expired").Count(&stats.ExpiredSessions)

    // 统计各版本会话数
    db.Model(&UserSessionMapping{}).Where("service_name = ? AND service_version = ? AND status = ?", serviceName, "v1", "active").Count(&stats.V1Sessions)
    db.Model(&UserSessionMapping{}).Where("service_name = ? AND service_version = ? AND status = ?", serviceName, "v2", "active").Count(&stats.V2Sessions)

    // 计算平均会话时长
    var avgDuration sql.NullFloat64
    db.Model(&UserSessionMapping{}).
        Where("service_name = ? AND status = ?", serviceName, "expired").
        Select("AVG(TIMESTAMPDIFF(SECOND, assigned_at, expires_at))").
        Scan(&avgDuration)

    if avgDuration.Valid {
        stats.AvgSessionDuration = avgDuration.Float64
    }

    c.JSON(200, gin.H{"data": stats})
}

// 会话一致性请求结构
type SessionAffinityRequest struct {
    Namespace     string `json:"namespace" binding:"required"`
    AffinityType  string `json:"affinityType" binding:"required"`
    HashKey       string `json:"hashKey"`
    CookieName    string `json:"cookieName"`
    CookieMaxAge  int    `json:"cookieMaxAge"`
    HeaderName    string `json:"headerName"`
    Enabled       bool   `json:"enabled"`
}
```

---

## 📊 监控与告警

### 关键指标监控

```yaml
# Prometheus监控规则
groups:
- name: canary-deployment
  rules:
  - alert: CanaryHighErrorRate
    expr: |
      (
        sum(rate(istio_requests_total{destination_service_name="user-service",destination_version="v2",response_code!~"2.."}[5m])) /
        sum(rate(istio_requests_total{destination_service_name="user-service",destination_version="v2"}[5m]))
      ) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "金丝雀版本错误率过高"
      description: "服务 {{ $labels.destination_service_name }} 的金丝雀版本 {{ $labels.destination_version }} 错误率超过5%"

  - alert: CanaryHighLatency
    expr: |
      histogram_quantile(0.99,
        sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name="user-service",destination_version="v2"}[5m]))
        by (le)
      ) > 500
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "金丝雀版本延迟过高"
      description: "服务 {{ $labels.destination_service_name }} 的金丝雀版本 {{ $labels.destination_version }} P99延迟超过500ms"
```

### Grafana监控面板

```json
{
  "dashboard": {
    "title": "灰度发布监控面板",
    "panels": [
      {
        "title": "请求成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(istio_requests_total{destination_service_name=\"$service\",response_code=~\"2..\"}[5m])) / sum(rate(istio_requests_total{destination_service_name=\"$service\"}[5m]))",
            "legendFormat": "成功率"
          }
        ]
      },
      {
        "title": "版本流量分布",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum(rate(istio_requests_total{destination_service_name=\"$service\"}[5m])) by (destination_version)",
            "legendFormat": "{{destination_version}}"
          }
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name=\"$service\"}[5m])) by (le, destination_version)",
            "legendFormat": "{{destination_version}}"
          }
        ]
      }
    ]
  }
}
```

---

## 🎨 用户界面设计

### 工作流创建界面

```vue
<template>
  <div class="workflow-creator">
    <a-card title="创建灰度发布工作流">
      <a-form :model="workflowForm" layout="vertical">
        <!-- 基本信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="工作流名称" required>
              <a-input v-model:value="workflowForm.name" placeholder="请输入工作流名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="目标服务" required>
              <a-select v-model:value="workflowForm.serviceName" placeholder="选择目标服务">
                <a-select-option v-for="service in services" :key="service.name" :value="service.name">
                  {{ service.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 发布策略选择 -->
        <a-form-item label="发布策略" required>
          <a-radio-group v-model:value="workflowForm.strategy">
            <a-radio value="canary">
              <div class="strategy-option">
                <div class="strategy-title">金丝雀发布</div>
                <div class="strategy-desc">渐进式流量切换，风险可控</div>
              </div>
            </a-radio>
            <a-radio value="blue-green">
              <div class="strategy-option">
                <div class="strategy-title">蓝绿发布</div>
                <div class="strategy-desc">快速切换，零停机时间</div>
              </div>
            </a-radio>
            <a-radio value="ab-test">
              <div class="strategy-option">
                <div class="strategy-title">A/B测试</div>
                <div class="strategy-desc">基于用户特征的精准分流</div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 步骤配置 -->
        <a-form-item label="发布步骤">
          <workflow-step-builder
            v-model:steps="workflowForm.steps"
            :strategy="workflowForm.strategy"
          />
        </a-form-item>

        <!-- 用户群体配置 -->
        <a-form-item label="目标用户群体">
          <user-group-selector
            v-model:selected="workflowForm.userGroups"
            :service="workflowForm.serviceName"
          />
        </a-form-item>

        <!-- 监控配置 -->
        <a-form-item label="监控阈值">
          <monitoring-config
            v-model:config="workflowForm.monitoring"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="createWorkflow">创建工作流</a-button>
          <a-button style="margin-left: 8px" @click="previewWorkflow">预览配置</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>
```

### 发布监控面板

```vue
<template>
  <div class="deployment-monitor">
    <a-row :gutter="16">
      <!-- 实时指标 -->
      <a-col :span="6">
        <a-card title="成功率" size="small">
          <div class="metric-value" :class="successRateClass">
            {{ successRate }}%
          </div>
          <div class="metric-trend">
            <a-icon :type="successRateTrend.icon" />
            {{ successRateTrend.text }}
          </div>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card title="响应时间" size="small">
          <div class="metric-value">
            {{ responseTime }}ms
          </div>
          <div class="metric-trend">
            <a-icon :type="responseTimeTrend.icon" />
            {{ responseTimeTrend.text }}
          </div>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card title="错误率" size="small">
          <div class="metric-value" :class="errorRateClass">
            {{ errorRate }}%
          </div>
          <div class="metric-trend">
            <a-icon :type="errorRateTrend.icon" />
            {{ errorRateTrend.text }}
          </div>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card title="QPS" size="small">
          <div class="metric-value">
            {{ qps }}
          </div>
          <div class="metric-trend">
            <a-icon :type="qpsTrend.icon" />
            {{ qpsTrend.text }}
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 流量分布图 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="版本流量分布">
          <traffic-distribution-chart :data="trafficData" />
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="发布进度">
          <deployment-progress :workflow="currentWorkflow" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 控制面板 -->
    <a-card title="发布控制" style="margin-top: 16px">
      <a-space>
        <a-button
          type="primary"
          :disabled="!canPromote"
          @click="promoteDeployment"
        >
          晋级到下一步
        </a-button>

        <a-button
          type="default"
          :disabled="!canPause"
          @click="pauseDeployment"
        >
          暂停发布
        </a-button>

        <a-button
          type="danger"
          :disabled="!canRollback"
          @click="rollbackDeployment"
        >
          立即回滚
        </a-button>
      </a-space>
    </a-card>
  </div>
</template>
```

### 用户组服务绑定管理界面

```vue
<template>
  <div class="usergroup-service-binding">
    <a-card title="用户组服务绑定管理">
      <template #extra>
        <a-button type="primary" @click="showBindingModal = true">
          <a-icon type="plus" />
          新增绑定
        </a-button>
      </template>

      <!-- 用户组列表 -->
      <a-table
        :columns="userGroupColumns"
        :data-source="userGroups"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #action="{ record }">
          <a-space>
            <a-button size="small" @click="viewBindings(record)">查看绑定</a-button>
            <a-button size="small" @click="editUserGroup(record)">编辑</a-button>
            <a-popconfirm
              title="确定删除此用户组吗？"
              @confirm="deleteUserGroup(record.id)"
            >
              <a-button size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>

        <template #serviceBindings="{ record }">
          <a-tag
            v-for="binding in record.serviceBindings"
            :key="binding.id"
            :color="binding.status === 'active' ? 'green' : 'red'"
          >
            {{ binding.serviceName }}:{{ binding.targetVersion }}
          </a-tag>
        </template>
      </a-table>
    </a-card>

    <!-- 服务绑定详情抽屉 -->
    <a-drawer
      title="服务绑定详情"
      :width="720"
      :visible="bindingDrawerVisible"
      @close="bindingDrawerVisible = false"
    >
      <div v-if="selectedUserGroup">
        <a-descriptions title="用户组信息" :column="2">
          <a-descriptions-item label="名称">{{ selectedUserGroup.name }}</a-descriptions-item>
          <a-descriptions-item label="描述">{{ selectedUserGroup.description }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge :status="selectedUserGroup.status === 'active' ? 'success' : 'error'" />
            {{ selectedUserGroup.status }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ selectedUserGroup.createdAt }}</a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <div class="service-bindings-section">
          <div class="section-header">
            <h3>服务绑定</h3>
            <a-button type="primary" size="small" @click="showAddBindingModal">
              <a-icon type="plus" />
              添加服务绑定
            </a-button>
          </div>

          <a-table
            :columns="bindingColumns"
            :data-source="selectedUserGroup.serviceBindings"
            size="small"
          >
            <template #status="{ record }">
              <a-badge
                :status="record.status === 'active' ? 'success' : 'error'"
                :text="record.status"
              />
            </template>

            <template #action="{ record }">
              <a-space>
                <a-button size="small" @click="editBinding(record)">编辑</a-button>
                <a-button size="small" @click="previewRoutes(record)">预览路由</a-button>
                <a-popconfirm
                  title="确定解绑此服务吗？"
                  @confirm="unbindService(record.id)"
                >
                  <a-button size="small" danger>解绑</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 新增/编辑绑定模态框 -->
    <a-modal
      :title="bindingModalTitle"
      :visible="showBindingModal"
      @ok="handleBindingSubmit"
      @cancel="showBindingModal = false"
      :width="600"
    >
      <a-form :model="bindingForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户组" required>
              <a-select
                v-model:value="bindingForm.userGroupId"
                placeholder="选择用户组"
                :disabled="!!editingBinding"
              >
                <a-select-option v-for="group in userGroups" :key="group.id" :value="group.id">
                  {{ group.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="服务名称" required>
              <a-select
                v-model:value="bindingForm.serviceName"
                placeholder="选择服务"
                show-search
              >
                <a-select-option v-for="service in services" :key="service.name" :value="service.name">
                  {{ service.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="命名空间" required>
              <a-select v-model:value="bindingForm.namespace" placeholder="选择命名空间">
                <a-select-option v-for="ns in namespaces" :key="ns" :value="ns">
                  {{ ns }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="目标版本" required>
              <a-input v-model:value="bindingForm.targetVersion" placeholder="如: v2.0" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="优先级">
              <a-input-number
                v-model:value="bindingForm.priority"
                :min="1"
                :max="100"
                placeholder="数值越大优先级越高"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态">
              <a-select v-model:value="bindingForm.status">
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="inactive">停用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="生效开始时间">
              <a-date-picker
                v-model:value="bindingForm.startTime"
                show-time
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生效结束时间">
              <a-date-picker
                v-model:value="bindingForm.endTime"
                show-time
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 路由预览模态框 -->
    <a-modal
      title="Istio路由规则预览"
      :visible="showRoutePreview"
      @cancel="showRoutePreview = false"
      :width="800"
      :footer="null"
    >
      <pre class="route-preview-content">{{ routePreviewContent }}</pre>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'UserGroupServiceBinding',
  data() {
    return {
      userGroups: [],
      services: [],
      namespaces: ['default', 'production', 'staging'],
      selectedUserGroup: null,
      bindingDrawerVisible: false,
      showBindingModal: false,
      showRoutePreview: false,
      routePreviewContent: '',
      editingBinding: null,
      bindingForm: {
        userGroupId: null,
        serviceName: '',
        namespace: 'production',
        targetVersion: '',
        priority: 50,
        status: 'active',
        startTime: null,
        endTime: null
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      userGroupColumns: [
        { title: '用户组名称', dataIndex: 'name', key: 'name' },
        { title: '描述', dataIndex: 'description', key: 'description' },
        { title: '服务绑定', key: 'serviceBindings', scopedSlots: { customRender: 'serviceBindings' } },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' } }
      ],
      bindingColumns: [
        { title: '服务名称', dataIndex: 'serviceName', key: 'serviceName' },
        { title: '命名空间', dataIndex: 'namespace', key: 'namespace' },
        { title: '目标版本', dataIndex: 'targetVersion', key: 'targetVersion' },
        { title: '优先级', dataIndex: 'priority', key: 'priority' },
        { title: '状态', key: 'status', scopedSlots: { customRender: 'status' } },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' } }
      ]
    }
  },
  computed: {
    bindingModalTitle() {
      return this.editingBinding ? '编辑服务绑定' : '新增服务绑定'
    }
  },
  methods: {
    async loadUserGroups() {
      // 加载用户组列表
      const response = await this.$api.get('/user-groups', {
        params: {
          page: this.pagination.current,
          size: this.pagination.pageSize
        }
      })
      this.userGroups = response.data.items
      this.pagination.total = response.data.total
    },

    async viewBindings(userGroup) {
      this.selectedUserGroup = userGroup
      this.bindingDrawerVisible = true
    },

    async showAddBindingModal() {
      this.editingBinding = null
      this.bindingForm = {
        userGroupId: this.selectedUserGroup?.id,
        serviceName: '',
        namespace: 'production',
        targetVersion: '',
        priority: 50,
        status: 'active',
        startTime: null,
        endTime: null
      }
      this.showBindingModal = true
    },

    async handleBindingSubmit() {
      try {
        if (this.editingBinding) {
          await this.$api.put(`/user-groups/${this.bindingForm.userGroupId}/services/${this.editingBinding.id}`, this.bindingForm)
        } else {
          await this.$api.post(`/user-groups/${this.bindingForm.userGroupId}/services`, this.bindingForm)
        }

        this.$message.success('操作成功')
        this.showBindingModal = false
        this.loadUserGroups()
      } catch (error) {
        this.$message.error('操作失败: ' + error.message)
      }
    },

    async previewRoutes(binding) {
      try {
        const response = await this.$api.get(`/user-groups/${binding.userGroupId}/preview-routes`)
        this.routePreviewContent = response.data
        this.showRoutePreview = true
      } catch (error) {
        this.$message.error('获取路由预览失败: ' + error.message)
      }
    }
  },

  mounted() {
    this.loadUserGroups()
  }
}
</script>

<style scoped>
.service-bindings-section {
  margin-top: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.route-preview-content {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}
</style>
```

### Client-ID灰度管理界面

```vue
<template>
  <div class="client-id-management">
    <a-card title="Client-ID灰度发布管理">
      <template #extra>
        <a-space>
          <a-button @click="refreshData">
            <a-icon type="reload" />
            刷新
          </a-button>
          <a-button type="primary" @click="showConfigModal = true">
            <a-icon type="setting" />
            配置管理
          </a-button>
        </a-space>
      </template>

      <!-- 统计概览 -->
      <a-row :gutter="16" class="stats-overview">
        <a-col :span="6">
          <a-statistic title="总客户端数" :value="stats.totalClients" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="V1版本客户端" :value="stats.v1Clients" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="V2版本客户端" :value="stats.v2Clients" />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="金丝雀流量比例"
            :value="canaryPercentage"
            suffix="%"
          />
        </a-col>
      </a-row>

      <a-divider />

      <!-- 客户端类型分布 -->
      <a-row :gutter="16" class="client-type-stats">
        <a-col :span="8">
          <a-card size="small" title="Web客户端">
            <client-type-chart
              :data="clientTypeData.web"
              type="web"
            />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card size="small" title="移动客户端">
            <client-type-chart
              :data="clientTypeData.mobile"
              type="mobile"
            />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card size="small" title="API客户端">
            <client-type-chart
              :data="clientTypeData.api"
              type="api"
            />
          </a-card>
        </a-col>
      </a-row>

      <a-divider />

      <!-- Client-ID分配记录表格 -->
      <div class="allocation-table-section">
        <div class="section-header">
          <h3>Client-ID分配记录</h3>
          <a-space>
            <a-input-search
              v-model:value="searchClientId"
              placeholder="搜索Client-ID"
              @search="handleSearch"
              style="width: 200px"
            />
            <a-select
              v-model:value="filterClientType"
              placeholder="客户端类型"
              style="width: 120px"
              @change="handleFilter"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="web">Web</a-select-option>
              <a-select-option value="mobile">Mobile</a-select-option>
              <a-select-option value="api">API</a-select-option>
            </a-select>
            <a-select
              v-model:value="filterVersion"
              placeholder="服务版本"
              style="width: 120px"
              @change="handleFilter"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="v1">V1</a-select-option>
              <a-select-option value="v2">V2</a-select-option>
            </a-select>
          </a-space>
        </div>

        <a-table
          :columns="allocationColumns"
          :data-source="allocations"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #clientType="{ record }">
            <a-tag :color="getClientTypeColor(record.clientType)">
              {{ record.clientType.toUpperCase() }}
            </a-tag>
          </template>

          <template #serviceVersion="{ record }">
            <a-tag :color="record.serviceVersion === 'v2' ? 'blue' : 'green'">
              {{ record.serviceVersion }}
            </a-tag>
          </template>

          <template #status="{ record }">
            <a-badge
              :status="record.status === 'active' ? 'success' : 'error'"
              :text="record.status"
            />
          </template>

          <template #accessCount="{ record }">
            <a-tooltip :title="`最后访问: ${record.lastAccessAt}`">
              {{ record.accessCount }}
            </a-tooltip>
          </template>

          <template #action="{ record }">
            <a-space>
              <a-button size="small" @click="viewClientDetails(record)">详情</a-button>
              <a-button size="small" @click="changeClientVersion(record)">切换版本</a-button>
              <a-popconfirm
                title="确定删除此分配记录吗？"
                @confirm="deleteAllocation(record.clientId)"
              >
                <a-button size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- Client-ID配置模态框 -->
    <a-modal
      title="Client-ID配置管理"
      :visible="showConfigModal"
      @ok="handleConfigSubmit"
      @cancel="showConfigModal = false"
      :width="800"
    >
      <a-form :model="configForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="服务名称" required>
              <a-select v-model:value="configForm.serviceName" placeholder="选择服务">
                <a-select-option v-for="service in services" :key="service.name" :value="service.name">
                  {{ service.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="命名空间" required>
              <a-select v-model:value="configForm.namespace" placeholder="选择命名空间">
                <a-select-option v-for="ns in namespaces" :key="ns" :value="ns">
                  {{ ns }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="金丝雀流量百分比" required>
              <a-slider
                v-model:value="configForm.canaryPercentage"
                :min="0"
                :max="100"
                :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="哈希算法">
              <a-select v-model:value="configForm.hashAlgorithm">
                <a-select-option value="sha256">SHA256</a-select-option>
                <a-select-option value="md5">MD5</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="缓存TTL(秒)">
              <a-input-number
                v-model:value="configForm.cacheTtl"
                :min="300"
                :max="86400"
                placeholder="默认86400秒"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Client-ID匹配模式">
              <a-input
                v-model:value="configForm.clientIdPattern"
                placeholder="如: web-*,mobile-*"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="configForm.cacheEnabled">启用缓存</a-checkbox>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="configForm.allowTempClientId">允许临时Client-ID</a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 分配模拟测试 -->
      <a-divider>分配模拟测试</a-divider>
      <div class="simulation-section">
        <a-space>
          <a-input
            v-model:value="testClientId"
            placeholder="输入测试Client-ID"
            style="width: 200px"
          />
          <a-button @click="testAllocation">测试分配</a-button>
          <a-button @click="simulateDistribution">模拟分布</a-button>
        </a-space>

        <div v-if="testResult" class="test-result">
          <a-alert
            :message="`Client-ID: ${testResult.clientId} 将被分配到版本: ${testResult.version}`"
            :type="testResult.version === 'v2' ? 'info' : 'success'"
            show-icon
          />
        </div>
      </div>
    </a-modal>

    <!-- 客户端详情抽屉 -->
    <a-drawer
      title="Client-ID详情"
      :width="600"
      :visible="clientDetailVisible"
      @close="clientDetailVisible = false"
    >
      <div v-if="selectedClient">
        <a-descriptions title="基本信息" :column="2">
          <a-descriptions-item label="Client-ID">{{ selectedClient.clientId }}</a-descriptions-item>
          <a-descriptions-item label="客户端类型">
            <a-tag :color="getClientTypeColor(selectedClient.clientType)">
              {{ selectedClient.clientType.toUpperCase() }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="服务版本">
            <a-tag :color="selectedClient.serviceVersion === 'v2' ? 'blue' : 'green'">
              {{ selectedClient.serviceVersion }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="哈希值">{{ selectedClient.hashValue }}</a-descriptions-item>
          <a-descriptions-item label="分配时间">{{ selectedClient.assignedAt }}</a-descriptions-item>
          <a-descriptions-item label="最后访问">{{ selectedClient.lastAccessAt }}</a-descriptions-item>
          <a-descriptions-item label="访问次数">{{ selectedClient.accessCount }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge
              :status="selectedClient.status === 'active' ? 'success' : 'error'"
              :text="selectedClient.status"
            />
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <div class="client-actions">
          <a-space>
            <a-button
              type="primary"
              @click="changeClientVersion(selectedClient)"
            >
              切换版本
            </a-button>
            <a-button @click="refreshClientData(selectedClient.clientId)">
              刷新数据
            </a-button>
            <a-popconfirm
              title="确定删除此Client-ID分配吗？"
              @confirm="deleteAllocation(selectedClient.clientId)"
            >
              <a-button danger>删除分配</a-button>
            </a-popconfirm>
          </a-space>
        </div>
      </div>
    </a-drawer>

    <!-- 版本切换模态框 -->
    <a-modal
      title="切换服务版本"
      :visible="showVersionModal"
      @ok="handleVersionChange"
      @cancel="showVersionModal = false"
    >
      <div v-if="changingClient">
        <p>当前Client-ID: <strong>{{ changingClient.clientId }}</strong></p>
        <p>当前版本: <a-tag :color="changingClient.serviceVersion === 'v2' ? 'blue' : 'green'">{{ changingClient.serviceVersion }}</a-tag></p>

        <a-form-item label="切换到版本:">
          <a-radio-group v-model:value="newVersion">
            <a-radio value="v1">V1 (稳定版本)</a-radio>
            <a-radio value="v2">V2 (金丝雀版本)</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-alert
          message="注意：版本切换将立即生效，请确认操作。"
          type="warning"
          show-icon
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'ClientIdManagement',
  data() {
    return {
      stats: {
        totalClients: 0,
        v1Clients: 0,
        v2Clients: 0
      },
      clientTypeData: {
        web: { v1: 0, v2: 0 },
        mobile: { v1: 0, v2: 0 },
        api: { v1: 0, v2: 0 }
      },
      allocations: [],
      services: [],
      namespaces: ['default', 'production', 'staging'],
      canaryPercentage: 10,
      searchClientId: '',
      filterClientType: '',
      filterVersion: '',
      loading: false,
      showConfigModal: false,
      clientDetailVisible: false,
      showVersionModal: false,
      selectedClient: null,
      changingClient: null,
      newVersion: 'v1',
      testClientId: '',
      testResult: null,
      configForm: {
        serviceName: '',
        namespace: 'production',
        canaryPercentage: 10,
        hashAlgorithm: 'sha256',
        cacheEnabled: true,
        cacheTtl: 86400,
        clientIdPattern: '',
        allowTempClientId: true
      },
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      allocationColumns: [
        { title: 'Client-ID', dataIndex: 'clientId', key: 'clientId', width: 200 },
        { title: '类型', key: 'clientType', scopedSlots: { customRender: 'clientType' }, width: 80 },
        { title: '服务版本', key: 'serviceVersion', scopedSlots: { customRender: 'serviceVersion' }, width: 100 },
        { title: '分配时间', dataIndex: 'assignedAt', key: 'assignedAt', width: 150 },
        { title: '访问次数', key: 'accessCount', scopedSlots: { customRender: 'accessCount' }, width: 100 },
        { title: '状态', key: 'status', scopedSlots: { customRender: 'status' }, width: 80 },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, width: 200 }
      ]
    }
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        // 加载统计信息
        const statsResponse = await this.$api.get('/client-ids/stats')
        this.stats = statsResponse.data

        // 加载分配记录
        const allocationsResponse = await this.$api.get('/client-ids/allocations', {
          params: {
            page: this.pagination.current,
            size: this.pagination.pageSize,
            clientId: this.searchClientId,
            clientType: this.filterClientType,
            version: this.filterVersion
          }
        })
        this.allocations = allocationsResponse.data.data
        this.pagination.total = allocationsResponse.data.total

        // 加载客户端类型分布
        await this.loadClientTypeData()
      } catch (error) {
        this.$message.error('加载数据失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    async loadClientTypeData() {
      try {
        const response = await this.$api.get('/client-ids/metrics')
        // 处理客户端类型数据
        this.clientTypeData = response.data
      } catch (error) {
        console.error('加载客户端类型数据失败:', error)
      }
    },

    async testAllocation() {
      if (!this.testClientId) {
        this.$message.warning('请输入测试Client-ID')
        return
      }

      try {
        const response = await this.$api.post('/client-ids/test-allocation', {
          clientId: this.testClientId,
          canaryPercentage: this.configForm.canaryPercentage
        })
        this.testResult = response.data
      } catch (error) {
        this.$message.error('测试失败: ' + error.message)
      }
    },

    async simulateDistribution() {
      try {
        const response = await this.$api.post('/client-ids/simulate', {
          sampleSize: 10000,
          canaryPercentage: this.configForm.canaryPercentage
        })

        this.$modal.info({
          title: '分配分布模拟结果',
          content: `
            模拟样本数: ${response.data.sampleSize}
            V1版本分配: ${response.data.v1Count} (${response.data.v1Percentage}%)
            V2版本分配: ${response.data.v2Count} (${response.data.v2Percentage}%)
            期望金丝雀比例: ${this.configForm.canaryPercentage}%
            实际金丝雀比例: ${response.data.v2Percentage}%
          `
        })
      } catch (error) {
        this.$message.error('模拟失败: ' + error.message)
      }
    },

    getClientTypeColor(type) {
      const colors = {
        web: 'blue',
        mobile: 'green',
        api: 'orange'
      }
      return colors[type] || 'default'
    },

    viewClientDetails(client) {
      this.selectedClient = client
      this.clientDetailVisible = true
    },

    changeClientVersion(client) {
      this.changingClient = client
      this.newVersion = client.serviceVersion === 'v1' ? 'v2' : 'v1'
      this.showVersionModal = true
    },

    async handleVersionChange() {
      try {
        await this.$api.post('/client-ids/allocations/batch-update', {
          updates: [{
            clientId: this.changingClient.clientId,
            serviceVersion: this.newVersion
          }]
        })

        this.$message.success('版本切换成功')
        this.showVersionModal = false
        this.loadData()
      } catch (error) {
        this.$message.error('版本切换失败: ' + error.message)
      }
    },

    async deleteAllocation(clientId) {
      try {
        await this.$api.delete(`/client-ids/allocations/${clientId}`)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        this.$message.error('删除失败: ' + error.message)
      }
    },

    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },

    handleFilter() {
      this.pagination.current = 1
      this.loadData()
    },

    handleTableChange(pagination) {
      this.pagination = pagination
      this.loadData()
    },

    refreshData() {
      this.loadData()
    }
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.stats-overview {
  margin-bottom: 24px;
}

.client-type-stats {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.simulation-section {
  margin-top: 16px;
}

.test-result {
  margin-top: 16px;
}

.client-actions {
  margin-top: 16px;
}
</style>
```

### 会话一致性配置界面

```vue
<template>
  <div class="session-affinity-config">
    <a-card title="会话一致性配置">
      <template #extra>
        <a-space>
          <a-button @click="refreshConfig">
            <a-icon type="reload" />
            刷新
          </a-button>
          <a-button type="primary" @click="showConfigModal = true">
            <a-icon type="setting" />
            配置会话一致性
          </a-button>
        </a-space>
      </template>

      <!-- 服务列表 -->
      <a-table
        :columns="serviceColumns"
        :data-source="services"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #sessionAffinity="{ record }">
          <a-tag
            :color="record.sessionAffinity?.enabled ? 'green' : 'red'"
          >
            {{ record.sessionAffinity?.enabled ? '已启用' : '未启用' }}
          </a-tag>
          <span v-if="record.sessionAffinity?.enabled" class="affinity-type">
            ({{ getAffinityTypeText(record.sessionAffinity.affinityType) }})
          </span>
        </template>

        <template #sessionStats="{ record }">
          <div v-if="record.sessionStats">
            <div>活跃会话: {{ record.sessionStats.activeSessions }}</div>
            <div>V1: {{ record.sessionStats.v1Sessions }} | V2: {{ record.sessionStats.v2Sessions }}</div>
          </div>
          <span v-else>-</span>
        </template>

        <template #action="{ record }">
          <a-space>
            <a-button size="small" @click="configureAffinity(record)">配置</a-button>
            <a-button size="small" @click="viewSessions(record)">查看会话</a-button>
            <a-button size="small" @click="viewStats(record)">统计</a-button>
            <a-popconfirm
              title="确定清理过期会话吗？"
              @confirm="cleanupSessions(record.name)"
            >
              <a-button size="small">清理</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 会话一致性配置模态框 -->
    <a-modal
      title="配置会话一致性"
      :visible="showConfigModal"
      @ok="handleConfigSubmit"
      @cancel="showConfigModal = false"
      :width="600"
    >
      <a-form :model="configForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="服务名称" required>
              <a-select
                v-model:value="configForm.serviceName"
                placeholder="选择服务"
                :disabled="!!editingService"
              >
                <a-select-option v-for="service in services" :key="service.name" :value="service.name">
                  {{ service.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="命名空间" required>
              <a-select v-model:value="configForm.namespace" placeholder="选择命名空间">
                <a-select-option v-for="ns in namespaces" :key="ns" :value="ns">
                  {{ ns }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="一致性类型" required>
          <a-radio-group v-model:value="configForm.affinityType">
            <a-radio value="cookie">
              <div class="affinity-option">
                <div class="option-title">Cookie会话保持</div>
                <div class="option-desc">基于Cookie实现会话一致性</div>
              </div>
            </a-radio>
            <a-radio value="consistent-hash">
              <div class="affinity-option">
                <div class="option-title">一致性哈希</div>
                <div class="option-desc">基于用户ID哈希实现一致性路由</div>
              </div>
            </a-radio>
            <a-radio value="header">
              <div class="affinity-option">
                <div class="option-title">Header会话保持</div>
                <div class="option-desc">基于请求头实现会话一致性</div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- Cookie配置 -->
        <div v-if="configForm.affinityType === 'cookie'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="Cookie名称" required>
                <a-input v-model:value="configForm.cookieName" placeholder="如: canary-version" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="过期时间(秒)">
                <a-input-number
                  v-model:value="configForm.cookieMaxAge"
                  :min="300"
                  :max="86400"
                  placeholder="默认86400秒"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 一致性哈希配置 -->
        <div v-if="configForm.affinityType === 'consistent-hash'">
          <a-form-item label="哈希键" required>
            <a-select v-model:value="configForm.hashKey" placeholder="选择哈希键">
              <a-select-option value="user-id">用户ID</a-select-option>
              <a-select-option value="session-id">会话ID</a-select-option>
              <a-select-option value="client-ip">客户端IP</a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <!-- Header配置 -->
        <div v-if="configForm.affinityType === 'header'">
          <a-form-item label="Header名称" required>
            <a-input v-model:value="configForm.headerName" placeholder="如: X-Session-ID" />
          </a-form-item>
        </div>

        <a-form-item>
          <a-checkbox v-model:checked="configForm.enabled">启用会话一致性</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 用户会话详情抽屉 -->
    <a-drawer
      title="用户会话详情"
      :width="800"
      :visible="sessionDrawerVisible"
      @close="sessionDrawerVisible = false"
    >
      <div v-if="selectedService">
        <a-descriptions title="服务信息" :column="3">
          <a-descriptions-item label="服务名称">{{ selectedService.name }}</a-descriptions-item>
          <a-descriptions-item label="命名空间">{{ selectedService.namespace }}</a-descriptions-item>
          <a-descriptions-item label="会话一致性">
            <a-badge
              :status="selectedService.sessionAffinity?.enabled ? 'success' : 'error'"
              :text="selectedService.sessionAffinity?.enabled ? '已启用' : '未启用'"
            />
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <div class="session-list-section">
          <div class="section-header">
            <h3>活跃会话列表</h3>
            <a-space>
              <a-button size="small" @click="refreshSessions">
                <a-icon type="reload" />
                刷新
              </a-button>
              <a-button size="small" type="danger" @click="cleanupExpiredSessions">
                <a-icon type="delete" />
                清理过期会话
              </a-button>
            </a-space>
          </div>

          <a-table
            :columns="sessionColumns"
            :data-source="userSessions"
            size="small"
            :scroll="{ y: 400 }"
          >
            <template #status="{ record }">
              <a-badge
                :status="record.status === 'active' ? 'success' : 'error'"
                :text="record.status"
              />
            </template>

            <template #duration="{ record }">
              {{ calculateDuration(record.assignedAt, record.lastAccessAt) }}
            </template>

            <template #version="{ record }">
              <a-tag :color="record.serviceVersion === 'v2' ? 'blue' : 'green'">
                {{ record.serviceVersion }}
              </a-tag>
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 会话统计面板 -->
    <a-modal
      title="会话统计信息"
      :visible="showStatsModal"
      @cancel="showStatsModal = false"
      :width="800"
      :footer="null"
    >
      <div v-if="sessionStats">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="总会话数" :value="sessionStats.totalSessions" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="活跃会话" :value="sessionStats.activeSessions" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="过期会话" :value="sessionStats.expiredSessions" />
          </a-col>
        </a-row>

        <a-divider />

        <a-row :gutter="16">
          <a-col :span="12">
            <a-statistic title="V1版本会话" :value="sessionStats.v1Sessions" />
          </a-col>
          <a-col :span="12">
            <a-statistic title="V2版本会话" :value="sessionStats.v2Sessions" />
          </a-col>
        </a-row>

        <a-divider />

        <a-statistic
          title="平均会话时长"
          :value="sessionStats.avgSessionDuration"
          suffix="秒"
          :precision="2"
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'SessionAffinityConfig',
  data() {
    return {
      services: [],
      userSessions: [],
      sessionStats: null,
      selectedService: null,
      editingService: null,
      showConfigModal: false,
      sessionDrawerVisible: false,
      showStatsModal: false,
      namespaces: ['default', 'production', 'staging'],
      configForm: {
        serviceName: '',
        namespace: 'production',
        affinityType: 'cookie',
        hashKey: 'user-id',
        cookieName: 'canary-version',
        cookieMaxAge: 86400,
        headerName: '',
        enabled: true
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      serviceColumns: [
        { title: '服务名称', dataIndex: 'name', key: 'name' },
        { title: '命名空间', dataIndex: 'namespace', key: 'namespace' },
        { title: '会话一致性', key: 'sessionAffinity', scopedSlots: { customRender: 'sessionAffinity' } },
        { title: '会话统计', key: 'sessionStats', scopedSlots: { customRender: 'sessionStats' } },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' } }
      ],
      sessionColumns: [
        { title: '用户ID', dataIndex: 'userId', key: 'userId' },
        { title: '会话ID', dataIndex: 'sessionId', key: 'sessionId' },
        { title: '服务版本', key: 'version', scopedSlots: { customRender: 'version' } },
        { title: '分配时间', dataIndex: 'assignedAt', key: 'assignedAt' },
        { title: '会话时长', key: 'duration', scopedSlots: { customRender: 'duration' } },
        { title: '状态', key: 'status', scopedSlots: { customRender: 'status' } }
      ]
    }
  },
  methods: {
    async loadServices() {
      try {
        const response = await this.$api.get('/services')
        this.services = response.data

        // 加载每个服务的会话一致性配置和统计信息
        for (let service of this.services) {
          await this.loadServiceConfig(service)
          await this.loadServiceStats(service)
        }
      } catch (error) {
        this.$message.error('加载服务列表失败: ' + error.message)
      }
    },

    async loadServiceConfig(service) {
      try {
        const response = await this.$api.get(`/services/${service.name}/session-affinity`)
        service.sessionAffinity = response.data
      } catch (error) {
        service.sessionAffinity = { enabled: false }
      }
    },

    async loadServiceStats(service) {
      try {
        const response = await this.$api.get(`/services/${service.name}/session-stats`)
        service.sessionStats = response.data
      } catch (error) {
        service.sessionStats = null
      }
    },

    async configureAffinity(service) {
      this.editingService = service
      this.configForm = {
        serviceName: service.name,
        namespace: service.namespace || 'production',
        affinityType: service.sessionAffinity?.affinityType || 'cookie',
        hashKey: service.sessionAffinity?.hashKey || 'user-id',
        cookieName: service.sessionAffinity?.cookieName || 'canary-version',
        cookieMaxAge: service.sessionAffinity?.cookieMaxAge || 86400,
        headerName: service.sessionAffinity?.headerName || '',
        enabled: service.sessionAffinity?.enabled || false
      }
      this.showConfigModal = true
    },

    async handleConfigSubmit() {
      try {
        await this.$api.post(`/services/${this.configForm.serviceName}/session-affinity`, this.configForm)
        this.$message.success('配置保存成功')
        this.showConfigModal = false
        this.loadServices()
      } catch (error) {
        this.$message.error('配置保存失败: ' + error.message)
      }
    },

    async viewSessions(service) {
      this.selectedService = service
      this.sessionDrawerVisible = true
      await this.loadUserSessions(service.name)
    },

    async loadUserSessions(serviceName) {
      try {
        const response = await this.$api.get(`/services/${serviceName}/user-sessions`)
        this.userSessions = response.data.data
      } catch (error) {
        this.$message.error('加载用户会话失败: ' + error.message)
      }
    },

    async viewStats(service) {
      try {
        const response = await this.$api.get(`/services/${service.name}/session-stats`)
        this.sessionStats = response.data
        this.showStatsModal = true
      } catch (error) {
        this.$message.error('加载统计信息失败: ' + error.message)
      }
    },

    async cleanupSessions(serviceName) {
      try {
        await this.$api.post(`/services/${serviceName}/user-sessions/cleanup`)
        this.$message.success('清理完成')
        this.loadServices()
      } catch (error) {
        this.$message.error('清理失败: ' + error.message)
      }
    },

    getAffinityTypeText(type) {
      const types = {
        'cookie': 'Cookie',
        'consistent-hash': '一致性哈希',
        'header': 'Header'
      }
      return types[type] || type
    },

    calculateDuration(startTime, endTime) {
      const start = new Date(startTime)
      const end = new Date(endTime)
      const duration = Math.floor((end - start) / 1000)

      if (duration < 60) return `${duration}秒`
      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`
      return `${Math.floor(duration / 3600)}小时`
    }
  },

  mounted() {
    this.loadServices()
  }
}
</script>

<style scoped>
.affinity-option {
  margin-left: 8px;
}

.option-title {
  font-weight: 500;
}

.option-desc {
  font-size: 12px;
  color: #666;
}

.affinity-type {
  margin-left: 8px;
  color: #666;
  font-size: 12px;
}

.session-list-section {
  margin-top: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>
```

---

## 🔐 安全与权限

### RBAC权限模型

```yaml
# 工作流创建者权限
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: workflow-creator
rules:
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "create", "update"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules"]
  verbs: ["get", "list", "create", "update", "delete"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]

---
# 工作流执行者权限
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: workflow-executor
rules:
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "update", "patch"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules"]
  verbs: ["get", "list", "update", "patch"]

---
# 监控查看者权限
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: workflow-viewer
rules:
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list"]
- apiGroups: ["networking.istio.io"]
  resources: ["virtualservices", "destinationrules"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list"]
```

---

## 🚀 实施步骤

### 第一阶段：基础平台搭建（4周）

1. **Week 1-2: 核心模型和API开发**
   - 实现工作流、步骤、执行记录等核心数据模型
   - 开发工作流管理的基础API接口
   - 集成现有的Kubernetes和Istio客户端

2. **Week 3-4: 基础UI开发**
   - 实现工作流创建和编辑界面
   - 开发基础的监控面板
   - 集成现有的前端框架和组件

### 第二阶段：核心功能实现（6周）

1. **Week 5-6: 发布策略引擎**
   - 实现Canary、Blue-Green、A/B测试策略
   - 开发Istio配置自动生成逻辑
   - 实现流量权重动态调整

2. **Week 7-8: 审批流程**
   - 实现多级审批工作流
   - 开发审批通知和界面
   - 集成企业级认证系统

3. **Week 9-10: 监控和告警**
   - 集成Prometheus指标收集
   - 实现自动决策逻辑
   - 开发告警通知机制

### 第三阶段：全链路灰度实现（6周）

1. **Week 11-12: 协议支持开发**
   - 实现HTTP/gRPC/WebSocket协议的灰度中间件
   - 开发协议自动识别和路由逻辑
   - 实现全链路Header传递机制

2. **Week 13-14: 链路追踪和监控**
   - 集成分布式链路追踪系统
   - 实现全链路版本一致性检测
   - 开发协议级别的监控指标

3. **Week 15-16: 用户群体和会话管理**
   - 实现用户群体定义和匹配
   - 开发WebSocket连接管理
   - 实现跨协议的会话保持

### 第四阶段：系统优化和测试（4周）

1. **Week 17-18: 性能优化**
   - 全链路灰度性能调优
   - 大规模并发测试
   - 协议切换性能优化

2. **Week 19-20: 系统完善**
   - 安全加固和权限完善
   - 故障恢复和容错机制
   - 文档编写和用户培训

---

## 📈 预期效果

### 业务价值
- **发布风险降低90%**：通过渐进式发布和自动回滚
- **发布效率提升50%**：通过工作流自动化和并行处理
- **故障恢复时间缩短80%**：通过快速检测和自动回滚
- **用户体验提升**：通过精准的用户群体控制

### 技术价值
- **标准化发布流程**：统一的工作流模板和最佳实践
- **可观测性增强**：全方位的监控和链路追踪
- **运维自动化**：减少人工干预，提升运维效率
- **平台化能力**：支持多团队、多服务的统一管理
- **全链路一致性**：确保HTTP/gRPC/WebSocket全协议的版本一致性
- **协议无关性**：统一的灰度策略适配多种通信协议
- **实时通信支持**：WebSocket长连接的灰度路由和会话保持

---

## 📝 总结

本灰度发布系统设计基于Istio和Kubernetes，提供了完整的工作流管理、多种发布策略、审批机制、实时监控和用户群体控制能力。特别是全链路灰度功能，支持HTTP、gRPC、WebSocket等多种协议的统一灰度控制，确保端到端的版本一致性。系统设计简洁易用，通过可视化界面和自动化流程，大大降低了灰度发布的复杂度和风险。

系统的核心优势在于：
1. **工作流驱动**：通过可视化工作流编排，让发布过程更加清晰可控
2. **策略丰富**：支持多种发布策略，适应不同业务场景
3. **安全可靠**：多级审批和自动监控，确保发布安全
4. **用户友好**：简洁的界面设计，降低学习和使用成本
5. **全链路支持**：统一的灰度策略覆盖HTTP/gRPC/WebSocket等多种协议
6. **协议无关**：灰度决策逻辑与具体通信协议解耦，提高系统扩展性
7. **实时通信**：特别针对WebSocket长连接场景优化，支持连接级别的灰度控制

### 🌐 全链路灰度核心价值

全链路灰度发布方案的引入，为现代微服务架构提供了以下关键能力：

#### 技术创新点
1. **协议统一抽象**：将HTTP、gRPC、WebSocket等不同协议的灰度控制统一到同一套框架中
2. **链路版本一致性**：通过Header/Metadata传递确保整个调用链路的版本一致性
3. **实时连接管理**：针对WebSocket等长连接协议提供连接级别的灰度路由
4. **智能协议识别**：自动识别请求协议类型并应用相应的灰度策略

#### 业务价值提升
- **降低风险**：全链路版本一致性避免了协议间的版本冲突
- **提升效率**：统一的灰度策略减少了多协议场景下的配置复杂度
- **增强体验**：WebSocket等实时通信场景的灰度支持保证了用户体验的连续性
- **扩展性强**：协议无关的设计为未来新协议的支持奠定了基础

### 🔄 全链路灰度流程示例

以下是一个完整的全链路灰度发布流程示例：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant ServiceA as 服务A(HTTP)
    participant ServiceB as 服务B(gRPC)
    participant ServiceC as 服务C(WebSocket)

    Note over Client,ServiceC: 全链路灰度发布流程

    Client->>Gateway: HTTP请求 (x-user-group: beta-testers)
    Gateway->>Gateway: 灰度决策 (version=v2)
    Gateway->>ServiceA: 转发请求 (x-canary-version: v2, x-trace-id: abc123)

    Note over ServiceA: 根据灰度版本选择v2处理逻辑
    ServiceA->>ServiceB: gRPC调用 (metadata: x-canary-version=v2)

    Note over ServiceB: 从metadata提取版本信息，使用v2逻辑
    ServiceB->>ServiceC: WebSocket连接 (headers: x-canary-version=v2)

    Note over ServiceC: 建立v2版本的WebSocket连接
    ServiceC-->>ServiceB: WebSocket响应 (v2版本)
    ServiceB-->>ServiceA: gRPC响应 (v2版本)
    ServiceA-->>Gateway: HTTP响应 (x-served-by: http-v2)
    Gateway-->>Client: 最终响应 (全链路v2版本)

    Note over Client,ServiceC: 整个调用链路保持版本一致性
```

#### 关键特性展示

1. **版本一致性传递**
   ```
   用户请求 → 灰度决策(v2) → HTTP服务(v2) → gRPC服务(v2) → WebSocket服务(v2)
   ```

2. **协议自适应**
   - HTTP: 通过Header传递版本信息
   - gRPC: 通过Metadata传递版本信息
   - WebSocket: 通过握手Header确定版本

3. **链路追踪**
   - 全链路使用统一的TraceID
   - 每个服务调用记录协议类型和版本信息
   - 支持版本一致性验证

通过分阶段实施，可以逐步构建完整的全链路灰度发布能力，为企业的数字化转型和现代化微服务架构提供强有力的技术支撑。

### 🎯 用户组服务绑定功能亮点

本次设计新增的用户组服务绑定功能，为灰度发布系统带来了以下重要能力：

#### 核心特性
1. **精准用户定向**：支持将特定用户组绑定到指定服务版本，实现精准的灰度控制
2. **多服务支持**：单个用户组可以绑定多个服务，支持复杂的微服务架构场景
3. **优先级管理**：通过优先级设置，解决用户属于多个组时的路由冲突问题
4. **时间控制**：支持设置绑定的生效时间范围，实现定时灰度发布
5. **实时预览**：提供Istio路由规则预览功能，确保配置正确性

#### 使用场景
- **内部员工优先体验**：将内部员工组绑定到新版本，率先体验新功能
- **VIP用户专享**：为高价值用户提供新功能的优先访问权
- **地域性发布**：基于用户地理位置进行分批发布
- **A/B测试**：为不同用户群体提供不同版本的功能对比测试

#### 技术优势
- **自动化路由生成**：系统自动生成对应的Istio VirtualService和DestinationRule
- **动态配置更新**：支持在线修改用户组绑定，无需重启服务
- **冲突检测**：智能检测和解决用户组之间的路由冲突
- **监控集成**：与现有监控系统无缝集成，提供用户组维度的指标分析

这一功能的加入，使得灰度发布系统能够更好地满足企业级应用的复杂发布需求，提供更加精细化和智能化的流量控制能力。

### 🔄 基于Client-ID的灰度发布方案

为了实现前后端统一的灰度控制，系统提供了基于 Client-ID 的完整解决方案：

#### 核心设计理念

1. **统一标识**：使用 Client-ID 作为前端和服务端的统一路由键
2. **一致性保障**：通过一致性哈希算法确保同一客户端始终访问相同版本
3. **多端支持**：支持 Web、移动端、API 等多种客户端类型
4. **缓存优化**：Redis 缓存分配结果，提升路由性能

#### 技术实现特点

**前端实现**：
- **设备指纹生成**：基于 Canvas 指纹和设备信息生成唯一标识
- **本地存储**：LocalStorage 持久化存储 Client-ID 和版本信息
- **HTTP 拦截器**：自动在请求头中添加 Client-ID 和版本标识
- **版本缓存**：缓存服务端分配的版本信息，减少重复计算

**服务端实现**：
- **中间件处理**：Gin 中间件统一处理 Client-ID 路由逻辑
- **一致性哈希**：SHA256 哈希算法确保分配的一致性和均匀性
- **Redis 缓存**：24小时缓存分配结果，支持高并发访问
- **降级机制**：无 Client-ID 时自动生成临时标识

**Istio 配置**：
- **VirtualService**：基于 client-id header 的流量路由规则
- **DestinationRule**：一致性哈希配置确保会话保持
- **权重分配**：支持动态调整金丝雀流量比例

#### 核心优势

1. **前后端一致性**
   - 同一 Client-ID 的前端和 API 请求路由到相同版本
   - 避免前后端版本不匹配导致的兼容性问题
   - 保证用户体验的完整性和一致性

2. **精准流量控制**
   - 基于哈希算法的精确流量分配
   - 支持按客户端类型（Web/Mobile/API）分别统计
   - 实时监控各版本的客户端分布情况

3. **高可用性设计**
   - Redis 缓存提升性能，支持高并发场景
   - 缓存失效时自动重新计算，无单点故障
   - 支持临时 Client-ID，兼容各种客户端环境

4. **运维友好**
   - 可视化管理界面，支持 Client-ID 查询和版本切换
   - 分配模拟测试，验证流量分布的准确性
   - 详细的统计报表，支持灰度效果分析

#### 应用场景

- **Web 应用灰度**：前端静态资源和 API 服务的同步灰度
- **移动应用发布**：App 版本和后端服务的协调发布
- **微服务架构**：多服务间的版本一致性保障
- **A/B 测试**：基于客户端特征的精准实验分组

#### 实施效果

- **版本一致性 100%**：同一客户端前后端版本完全一致
- **流量分配精度 ±1%**：哈希算法保证流量分配的准确性
- **性能提升 50%**：Redis 缓存大幅减少计算开销
- **运维效率提升**：自动化分配和可视化管理

通过基于 Client-ID 的灰度发布方案，系统实现了前后端的统一灰度控制，为复杂的微服务架构提供了可靠的版本管理能力。


1. 策略
2. 现有基建的状况（后端、前端）
