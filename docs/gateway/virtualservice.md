# 探索如何更高效稳定的配置网关路由

## 相关背景

1. 目前新增和修改以及删除对外应用路由的配置都是手工的方式去修改，每次需要修改多个环境的文件和apply，繁琐且容易出错，效率比较低，没有将流程进行自动化。
2. 针对于开发人员无法看到具体的域名路由网关配置，直接查看yaml不够直观或者看了不一定理解。

## 预期效果

1. 交由需求方（开发人员）提交工单申请，确认增加路由到指定集群和位置，由AI或者运维开发人员进行审核，审核通过自动应用到线上。
2. 配置支持回滚，如果配置有问题可以选定版本进行回滚。

## 实现方案

### 难点分析

1. 可视化编辑（将现有的VirtualService进行可视化，提供业务方选择需要调整的位置和内容）。
2. 修改配置后如何有效的apply上去, 切能保证不影响其他的配置（如istio VirtualService中的新增/修改match项）。

### 详细设计

1. 展示 virtual_service 列表, 提供修改和新增的按钮，弹开抽屉，提供可视化的方式进行配置，配置完成可以预览修改的yaml。
2. dry-run 通过后方可提交由 AI 或者运维开发人员进行审核，审核通过自动应用到线上。
3. 列表中的数据项提供回滚按钮，点击后提供历史版本选择，指定版本进行回滚。

#### 数据库表

1. virtual_service: 保存线上生效的配置，或者新增但是未上线的配置，状态为pending
2. virtual_service_editor: 保存编辑中的配置, 不记录状态
3. virtual_service_history: 保存历史版本的配置

#### 查询

1. 同步集群资源到数据库，单一数据源方便查询关联。
2. 只查询未删除的记录，deleted 为 false 的记录。

#### 新增

1. 状态变更：新增pending->approved/rejected审批->上线active(应用到k8s)->保存历史版本
2. 数据流转：插入virtual_service表, 状态为pending。
3. 是否需要插入virtual_service_editor表 ？
   1. 如果插入那再次修改的话就得两个表(virtual_service和virtual_service_editor)
   2. 如果不插入那到virtual_service那详情看到的就会是更之前的版本的（未上线）
4. 前端保存需要能预览变更的yaml对比功能

#### 修改

1. 修改逻辑:
   1. 新增后进行修改: 需要修改virtual_service和virtual_service_editor两个表
   2. 已上线的进行修改: 如果没有编辑版本的记录（virtual_service_editor表）则取virtual_service的记录进行修改存入virtual_service_editor表。
   3. 修改后 virtual_service 的状态需要更新为pending。
2. 用户提交修改申请，通过飞书发送通知到审批群。

#### 删除

1. 更新编辑版本为删除状态（如果存在）
2. 通过api删除k8s资源（需要管理员确认后才执行）

#### 上线

1. 管理员进行审核通过，审核通过后自动应用到k8s（需要对比版本号，如果版本号不一致则通过失败因为线上的数据已经被修改了）,监听资源同步到数据库，自动变更为active。
2. 管理员审核不通过，输入原因，编辑记录更新状态变更为rejected，同时发送通知给申请人。
3. 在审核时需要展示当前版本和显示版本的yaml diff差异，使用已有的yaml-diff组件。
4. 在审核时管理员可以按需进行修改yaml。

#### 同步

1. 监听k8s的virtualservice资源变化，自动同步到virtual_service表，状态为active。但是如果是编辑状态怎么处理 ？
   1. 其实修改virtual_service表的状态主要在分页的时候显示，如果我们连表virtual_service_editor其实就可以。
   2. 控制更新virtual_service表的状态逻辑，只有在合适的条件下才更新：
      1. 在应用的时候先更新数据库virtual_service表状态，再提交到k8s, 在监听到k8s资源变化时更新状态只有为active才更新。
      2. 在首次同步的时候不存在记录才会更新为active。

## 相关资料

1. Cilium Network Policy Editor：https://editor.networkpolicy.io/
2. Yaml 可视化编辑器：https://github.com/AykutSarac/jsoncrack.com
