# 域名管理功能设计文档

## 概述

提供统一管理各个云平台域名解析的功能，支持多云平台的DNS管理、域名解析配置、以及自动化的域名记录同步。

- 域名解析管理
- 支持各种记录类型 (A, AAAA, CNAME, MX, TXT等)

### 支持的云平台

1. **阿里云 (Alibaba Cloud)**
   - DNS服务

2. **微软云 (Azure)**
   - Azure DNS服务

3. **Cloudflare**
   - DNS服务

## 功能特性

### 1. 多云平台统一管理

- **云平台账户配置**：支持配置多个云平台的API密钥
- **统一接口操作**：通过统一的API接口管理不同云平台的域名
- **批量操作**：支持批量添加、修改、删除域名记录

### 2. 域名记录管理

- **记录类型支持**：
  - A记录：IPv4地址解析
  - AAAA记录：IPv6地址解析
  - CNAME记录：别名解析
  - MX记录：邮件服务器记录
  - TXT记录：文本记录，支持SSL证书验证
  - SRV记录：服务记录
  - NS记录：域名服务器记录
  - 等等所有的类型

## 数据模型设计

1. **云平台配置表 (domain_provider)**
2. **域名表 (domain)**
3. **域名记录表 (domain_record)**
4. **域名记录编辑表 (domain_record_editor)**
5. **域名记录历史表 (domain_record_history)**
6. **域名解析历史表 (domain_resolution_history)**

相关的数据结构通过pkg/migrate包进行迁移

## API接口设计

### 1. 域名记录管理接口
```
POST /api/v1/gateway/domain/list               # 获取域名列表,提供给前端下拉框选择,默认按照创建时间进行排序,前端列表下拉框默认选中第一个
POST /api/v1/gateway/domain/record/list        # 获取域名记录列表(status必填,如果是online则是通过api去云平台查询,其他则从本地数据库查询,默认为online)
POST /api/v1/gateway/domain/record/info        # 获取域名记录详情
POST /api/v1/gateway/domain/record/add         # 添加域名记录, 只添加到本地数据库, 审批通过后才会应用到云平台
PUT  /api/v1/gateway/domain/record/update      # 更新域名记录, 只更新到本地数据库, 审批通过后才会应用到云平台
DELETE /api/v1/gateway/domain/record/delete    # 删除域名记录, 只删除到本地数据库, 审批通过后才会应用到云平台
```

### 3. 审批流程接口
```
POST /api/v1/gateway/domain/record/approval    # 执行审批,通过参数判断是通过还是拒绝
```

### 4. 历史版本接口
```
POST /api/v1/gateway/domain/record/history       # 获取历史版本
POST /api/v1/gateway/domain/record/history/rollback   # 回滚到指定版本
```

## 前端界面设计

### 1. 域名记录列表页面

- **功能特性**：
-
  - 支持按域名、记录类型、状态(默认为online)等条件筛选
  - 快速搜索和排序

- **操作按钮**：
  - 新增记录

### 2. 域名记录编辑抽屉

  - 域名自动选中列表的域名
  - 子域名输入
  - 记录类型选择
  - 记录值配置
  - TTL和优先级设置

### 3. 审批管理页面

- 如果记录处于pending状态则显示审批按钮，点击后弹出审批抽屉，如果是修改则需要展示修改前后的对比。
- 审批通过后需要自动应用到云平台。

### 4. 历史版本页面

- **版本列表**：显示所有历史版本
- **版本对比**：支持任意两个版本的配置对比
- **版本回滚**：一键回滚到指定版本


# 相关文档


阿里云DNS：https://api.aliyun.com/document/Alidns/2015-01-09/DescribeDomains
cloudflare: https://developers.cloudflare.com/api/resources/dns/
azure: https://pkg.go.dev/github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/dns/armdns@v1.2.0

阿里云

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "alidns:DeleteDomainRecord",
        "alidns:AddDomainRecord",
        "alidns:UpdateDomainRecord",
        "alidns:DescribeDomains"
      ],
      "Resource": "*"
    }
  ]
}
```

Azure

```json

```
