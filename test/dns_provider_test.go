package test

import (
	"encoding/json"
	"pilot-api/config"
	"pilot-api/internal/pkg/dns"
	"pilot-api/internal/pkg/util"
	"pilot-api/pkg/models"
	"pilot-api/pkg/repos"
	"testing"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"gorm.io/gorm/logger"
)

const (
	testProviderUpdatedName = "阿里云DNS测试-已更新"
)

// TestInsertDNSProvider 测试插入DNS Provider的完整用例
func TestInsertDNSProvider(t *testing.T) {
	t.Skip()
	// 1. 加载配置
	config.Load()

	// 2. 初始化数据库连接
	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	defer mysql.Close()

	// 3. 获取数据库连接
	db := mysql.GetDB()

	// 4. 创建Repository
	providerRepo := repos.NewDomainProviderRepo(db)

	// 5. 准备测试数据 - 阿里云DNS Provider
	alibabaConfig := &dns.AlibabaConfig{
		AccessKeyId:     "LTAI5tSoaHdJk8X3rZcqetpy",
		AccessKeySecret: "******************************",
		RegionId:        "cn-hangzhou",
		Endpoint:        "dns.aliyuncs.com",
	}

	// 将配置转换为JSON字符串
	configJSON, err := json.Marshal(alibabaConfig)
	if err != nil {
		t.Fatalf("Failed to marshal config: %v", err)
	}

	// 加密配置
	encryptedConfig, err := util.DefaultEncrypt(string(configJSON))
	if err != nil {
		t.Fatalf("Failed to encrypt config: %v", err)
	}

	// 6. 创建DomainProvider实例
	provider := &models.DomainProvider{
		Name:   "阿里云DNS测试",
		Type:   models.ProviderTypeAlibaba,
		Config: encryptedConfig,
		Status: models.ProviderStatusActive,
	}

	// 7. 设置BaseModel字段
	provider.Create("test-user")

	// 8. 插入数据库
	err = providerRepo.Create(provider)
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}

	t.Logf("Successfully created DNS provider with ID: %d, UUID: %s", provider.ID, provider.UUID)

	// 9. 验证插入结果
	createdProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get created provider: %v", err)
	}

	if createdProvider == nil {
		t.Fatal("Created provider is nil")
	}

	// 验证基本信息
	if createdProvider.Name != "阿里云DNS测试" {
		t.Errorf("Expected name '阿里云DNS测试', got '%s'", createdProvider.Name)
	}

	if createdProvider.Type != models.ProviderTypeAlibaba {
		t.Errorf("Expected type 'alibaba', got '%s'", createdProvider.Type)
	}

	if createdProvider.Status != models.ProviderStatusActive {
		t.Errorf("Expected status 'active', got '%s'", createdProvider.Status)
	}

	// 验证配置解密
	decryptedConfig, err := util.DefaultDecrypt(createdProvider.Config)
	if err != nil {
		t.Fatalf("Failed to decrypt config: %v", err)
	}

	var decryptedAlibabaConfig dns.AlibabaConfig
	err = json.Unmarshal([]byte(decryptedConfig), &decryptedAlibabaConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal decrypted config: %v", err)
	}

	if decryptedAlibabaConfig.AccessKeyId != "LTAI5tSoaHdJk8X3rZcqetpy" {
		t.Errorf("Expected accessKeyId 'LTAI5tSoaHdJk8X3rZcqetpy', got '%s'", decryptedAlibabaConfig.AccessKeyId)
	}

	t.Logf("Provider config decrypted successfully: %+v", decryptedAlibabaConfig)

	// 10. 测试查询功能
	activeProviders, err := providerRepo.ListByStatus(models.ProviderStatusActive)
	if err != nil {
		t.Fatalf("Failed to list active providers: %v", err)
	}

	found := false
	for _, p := range activeProviders {
		if p.UUID == provider.UUID {
			found = true
			break
		}
	}

	if !found {
		t.Error("Created provider not found in active providers list")
	}

	t.Logf("Found %d active providers", len(activeProviders))

	// 11. 测试按类型查询
	alibabaProvider, err := providerRepo.GetByType(models.ProviderTypeAlibaba)
	if err != nil {
		t.Fatalf("Failed to get alibaba provider: %v", err)
	}

	if alibabaProvider == nil {
		t.Error("Alibaba provider not found")
	} else {
		t.Logf("Found alibaba provider: %s", alibabaProvider.Name)
	}

	// 12. 测试更新功能
	provider.Name = testProviderUpdatedName
	provider.Update("test-user-update")

	err = providerRepo.Save(provider)
	if err != nil {
		t.Fatalf("Failed to update provider: %v", err)
	}

	// 验证更新结果
	updatedProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get updated provider: %v", err)
	}

	if updatedProvider.Name != testProviderUpdatedName {
		t.Errorf("Expected updated name '%s', got '%s'", testProviderUpdatedName, updatedProvider.Name)
	}

	t.Logf("Provider updated successfully")

	// 13. 清理测试数据（可选）
	// 注意：在生产环境中，你可能不想删除测试数据
	// err = providerRepo.Delete(provider.ID)
	// if err != nil {
	//     t.Logf("Failed to delete test provider: %v", err)
	// }

	t.Log("DNS Provider test completed successfully")
}

// TestInsertAzureDNSProvider 测试插入Azure DNS Provider的完整用例
func TestInsertAzureDNSProvider(t *testing.T) {
	t.Skip()
	// 1. 加载配置
	config.Load()

	// 2. 初始化数据库连接
	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	defer mysql.Close()

	// 3. 获取数据库连接
	db := mysql.GetDB()

	// 4. 创建Repository
	providerRepo := repos.NewDomainProviderRepo(db)

	// 5. 准备测试数据 - Azure DNS Provider
	azureConfig := &dns.AzureConfig{
		SubscriptionID: "cffa1e86-2643-4ebe-a0b3-8dfa662a44d4",
		ResourceGroup:  "dns",
		TenantID:       "f785faed-a5b9-488f-8f80-b8c95af42d7a",
		ClientID:       "8093197b-f4fe-4366-9656-0b6bb2d421da",
		ClientSecret:   "4td8Q~cg1ilPHg6TWaUUzrzKxqJretCXynikpapvd",
	}

	// 将配置转换为JSON字符串
	configJSON, err := json.Marshal(azureConfig)
	if err != nil {
		t.Fatalf("Failed to marshal Azure config: %v", err)
	}

	// 加密配置
	encryptedConfig, err := util.DefaultEncrypt(string(configJSON))
	if err != nil {
		t.Fatalf("Failed to encrypt Azure config: %v", err)
	}

	// 6. 创建DomainProvider实例
	provider := &models.DomainProvider{
		Name:   "Azure DNS测试",
		Type:   models.ProviderTypeAzure,
		Config: encryptedConfig,
		Status: models.ProviderStatusActive,
	}

	// 7. 设置BaseModel字段
	provider.Create("test-user")

	// 8. 插入数据库
	err = providerRepo.Create(provider)
	if err != nil {
		t.Fatalf("Failed to create Azure provider: %v", err)
	}

	t.Logf("Successfully created Azure DNS provider with ID: %d, UUID: %s", provider.ID, provider.UUID)

	// 9. 验证插入结果
	createdProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get created Azure provider: %v", err)
	}

	if createdProvider == nil {
		t.Fatal("Created Azure provider is nil")
	}

	// 验证基本信息
	if createdProvider.Name != "Azure DNS测试" {
		t.Errorf("Expected name 'Azure DNS测试', got '%s'", createdProvider.Name)
	}

	if createdProvider.Type != models.ProviderTypeAzure {
		t.Errorf("Expected type 'azure', got '%s'", createdProvider.Type)
	}

	if createdProvider.Status != models.ProviderStatusActive {
		t.Errorf("Expected status 'active', got '%s'", createdProvider.Status)
	}

	// 验证配置解密
	decryptedConfig, err := util.DefaultDecrypt(createdProvider.Config)
	if err != nil {
		t.Fatalf("Failed to decrypt Azure config: %v", err)
	}

	var decryptedAzureConfig dns.AzureConfig
	err = json.Unmarshal([]byte(decryptedConfig), &decryptedAzureConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal decrypted Azure config: %v", err)
	}

	if decryptedAzureConfig.SubscriptionID != "12345678-1234-1234-1234-123456789012" {
		t.Errorf("Expected subscriptionId '12345678-1234-1234-1234-123456789012', got '%s'", decryptedAzureConfig.SubscriptionID)
	}

	t.Logf("Azure provider config decrypted successfully: %+v", decryptedAzureConfig)

	// 10. 测试按类型查询
	azureProvider, err := providerRepo.GetByType(models.ProviderTypeAzure)
	if err != nil {
		t.Fatalf("Failed to get azure provider: %v", err)
	}

	if azureProvider == nil {
		t.Error("Azure provider not found")
	} else {
		t.Logf("Found azure provider: %s", azureProvider.Name)
	}

	// 11. 测试更新功能
	provider.Name = "Azure DNS测试-已更新" // nolint
	provider.Update("test-user-update")

	err = providerRepo.Save(provider)
	if err != nil {
		t.Fatalf("Failed to update Azure provider: %v", err)
	}

	// 验证更新结果
	updatedProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get updated Azure provider: %v", err)
	}

	if updatedProvider.Name != "Azure DNS测试-已更新" {
		t.Errorf("Expected updated name 'Azure DNS测试-已更新', got '%s'", updatedProvider.Name)
	}

	t.Logf("Azure Provider updated successfully")

	t.Log("Azure DNS Provider test completed successfully")
}

// TestInsertCloudflareDNSProvider 测试插入Cloudflare DNS Provider的完整用例
func TestInsertCloudflareDNSProvider(t *testing.T) {
	t.Skip()
	// 1. 加载配置
	config.Load()

	// 2. 初始化数据库连接
	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	defer mysql.Close()

	// 3. 获取数据库连接
	db := mysql.GetDB()

	// 4. 创建Repository
	providerRepo := repos.NewDomainProviderRepo(db)

	// 5. 准备测试数据 - Cloudflare DNS Provider
	cloudflareConfig := &dns.CloudflareConfig{
		APIKey:   "test-api-key-1234567890abcdef",
		Email:    "<EMAIL>",
		APIToken: "test-api-token-xyz789",
	}

	// 将配置转换为JSON字符串
	configJSON, err := json.Marshal(cloudflareConfig)
	if err != nil {
		t.Fatalf("Failed to marshal Cloudflare config: %v", err)
	}

	// 加密配置
	encryptedConfig, err := util.DefaultEncrypt(string(configJSON))
	if err != nil {
		t.Fatalf("Failed to encrypt Cloudflare config: %v", err)
	}

	// 6. 创建DomainProvider实例
	provider := &models.DomainProvider{
		Name:   "Cloudflare DNS测试",
		Type:   models.ProviderTypeCloudflare,
		Config: encryptedConfig,
		Status: models.ProviderStatusActive,
	}

	// 7. 设置BaseModel字段
	provider.Create("test-user")

	// 8. 插入数据库
	err = providerRepo.Create(provider)
	if err != nil {
		t.Fatalf("Failed to create Cloudflare provider: %v", err)
	}

	t.Logf("Successfully created Cloudflare DNS provider with ID: %d, UUID: %s", provider.ID, provider.UUID)

	// 9. 验证插入结果
	createdProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get created Cloudflare provider: %v", err)
	}

	if createdProvider == nil {
		t.Fatal("Created Cloudflare provider is nil")
	}

	// 验证基本信息
	if createdProvider.Name != "Cloudflare DNS测试" {
		t.Errorf("Expected name 'Cloudflare DNS测试', got '%s'", createdProvider.Name)
	}

	if createdProvider.Type != models.ProviderTypeCloudflare {
		t.Errorf("Expected type 'cloudflare', got '%s'", createdProvider.Type)
	}

	if createdProvider.Status != models.ProviderStatusActive {
		t.Errorf("Expected status 'active', got '%s'", createdProvider.Status)
	}

	// 验证配置解密
	decryptedConfig, err := util.DefaultDecrypt(createdProvider.Config)
	if err != nil {
		t.Fatalf("Failed to decrypt Cloudflare config: %v", err)
	}

	var decryptedCloudflareConfig dns.CloudflareConfig
	err = json.Unmarshal([]byte(decryptedConfig), &decryptedCloudflareConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal decrypted Cloudflare config: %v", err)
	}

	if decryptedCloudflareConfig.APIKey != "test-api-key-1234567890abcdef" {
		t.Errorf("Expected apiKey 'test-api-key-1234567890abcdef', got '%s'", decryptedCloudflareConfig.APIKey)
	}

	if decryptedCloudflareConfig.Email != "<EMAIL>" {
		t.Errorf("Expected email '<EMAIL>', got '%s'", decryptedCloudflareConfig.Email)
	}

	t.Logf("Cloudflare provider config decrypted successfully: %+v", decryptedCloudflareConfig)

	// 10. 测试按类型查询
	cloudflareProvider, err := providerRepo.GetByType(models.ProviderTypeCloudflare)
	if err != nil {
		t.Fatalf("Failed to get cloudflare provider: %v", err)
	}

	if cloudflareProvider == nil {
		t.Error("Cloudflare provider not found")
	} else {
		t.Logf("Found cloudflare provider: %s", cloudflareProvider.Name)
	}

	// 11. 测试更新功能
	provider.Name = "Cloudflare DNS测试-已更新" // nolint
	provider.Update("test-user-update")

	err = providerRepo.Save(provider)
	if err != nil {
		t.Fatalf("Failed to update Cloudflare provider: %v", err)
	}

	// 验证更新结果
	updatedProvider, err := providerRepo.GetByID(provider.ID)
	if err != nil {
		t.Fatalf("Failed to get updated Cloudflare provider: %v", err)
	}

	if updatedProvider.Name != "Cloudflare DNS测试-已更新" {
		t.Errorf("Expected updated name 'Cloudflare DNS测试-已更新', got '%s'", updatedProvider.Name)
	}

	t.Logf("Cloudflare Provider updated successfully")

	t.Log("Cloudflare DNS Provider test completed successfully")
}
