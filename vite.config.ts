import vue from '@vitejs/plugin-vue';
import * as path from 'path';
import { join } from 'path';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import qiankun from 'vite-plugin-qiankun';

const OUT_DIR = {
  development: 'dist/dev',
  test: 'dist/test',
  production: 'dist/prod',
};

export default defineConfig(({ mode }) => {
  const outDir = OUT_DIR[mode];
  return {
    resolve: {
      alias: {
        '@': join(__dirname, './src'),
        '@c': join(__dirname, './src/components'),
      },
    },
    plugins: [
      vue(),
      qiankun('pilot', {
        useDevMode: true,
      }),
      Components({
        resolvers: [
          AntDesignVueResolver({
            importStyle: false, // css in js
          }),
        ],
      }),
      createSvgIconsPlugin({
        // 指定缓存文件
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',
      }),
      viteCommonjs({
        exclude: ['ali-oss'],
        include: ['jsoneditor/dist/jsoneditor.min.js'],
      }),
    ],
    build: {
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      outDir,
    },
    define: {
      'process.env': process.env,
    },

    base: getBase(),

    optimizeDeps: {
      include: [],
    },
    server: {
      port: 7006,
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    },
  };
});

function getBase() {
  // @ts-ignore
  return process.env.MODE === 'localhost' ? '/' : '/micro/pilot/';
}
