{"name": "pilot", "version": "1.0.0", "engines": {"node": ">=16", "npm": ">=8"}, "scripts": {"prepare": "husky install", "localhost": "NODE_ENV=development MODE=localhost vite --mode localhost", "dev": "vite", "build": "vue-tsc --noEmit --skipLibCheck && vite build", "build-development": "vue-tsc --noEmit --skipLibCheck && NODE_ENV=development vite build --mode development", "build-test": "vue-tsc --noEmit --skipLibCheck && vite build --mode test", "build-production": "vue-tsc --noEmit --skipLibCheck && vite build --mode production", "preview": "vite preview", "typecheck": "vue-tsc --skipLib<PERSON><PERSON><PERSON> --noEmit -p tsconfig.json", "lint": "eslint ./ --ext ts,vue --fix", "test": "jest --passWithNoTests", "test:coverage": "jest --coverage --passWithNoTests", "type": "module", "update:client": "npm i -S @makeblock/passport-client@latest"}, "lint-staged": {"**/*.{js,ts,vue}": ["bash -c \"npm run typecheck\"", "npm run lint", "prettier --write --ignore-unknown"]}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@azure/storage-blob": "12.17.0", "@makeblock/canvas-grid": "1.0.18", "@makeblock/passport-client": "^2.2.0", "@makeblock/upload": "^0.11.2", "@originjs/vite-plugin-commonjs": "^1.0.3", "@purge-icons/generated": "^0.10.0", "@stagewise-plugins/vue": "^0.6.2", "@stagewise/toolbar-vue": "^0.6.2", "@types/js-cookie": "^3.0.6", "@vueup/vue-quill": "^1.0.0", "@vueuse/core": "^10.7.1", "ali-oss": "^6.17.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.2", "dayjs": "^1.11.3", "diff": "^8.0.2", "echarts": "^5.3.2", "fuse.js": "^6.6.2", "js-cookie": "^3.0.1", "js-yaml": "^4.1.0", "json-editor-vue3": "^1.0.6", "less": "^4.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "marked": "^4.0.18", "md-editor-v3": "^4.1.1", "moment": "^2.29.2", "monaco-editor": "^0.45.0", "nprogress": "^0.2.0", "spark-md5": "^3.0.2", "uuid": "^9.0.0", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.2.25", "vue-echarts": "^6.0.3", "vue-router": "^4.0.14", "vue-types": "^5.1.1", "vuex": "^4.0.2", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@babel/preset-env": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "@commitlint/cli": "^16.0.2", "@commitlint/config-conventional": "^16.0.0", "@testing-library/vue": "^6.4.2", "@types/ali-oss": "^6.16.3", "@types/diff": "^7.0.2", "@types/jest": "^27.5.2", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.14.190", "@types/lodash-es": "^4.17.12", "@types/marked": "^4.0.3", "@types/node": "^20.14.2", "@types/nprogress": "^0.2.3", "@types/spark-md5": "^3.0.2", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.3.3", "@vue/test-utils": "^2.0.0-rc.18", "@vue/vue3-jest": "^27.0.0-alpha.4", "babel-core": "^7.0.0-bridge.0", "babel-preset-vite": "^1.0.4", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.3.0", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.6", "jest-html-reporters": "^3.1.7", "lint-staged": "^12.1.7", "postcss-html": "^1.3.0", "prettier": "^2.5.1", "sass": "^1.50.0", "stylelint": "^14.2.0", "stylelint-config-html": "^1.0.0", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-recommended-vue": "^1.1.0", "stylelint-config-standard": "^24.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-order": "^5.0.0", "ts-jest": "^27.1.2", "typescript": "4.9.5", "unplugin-vue-components": "^0.19.9", "vite": "^2.9.9", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^8.2.0", "vue-tsc": "^0.34.7", "vuex-persistedstate": "^4.1.0"}, "packageManager": "pnpm@9.9.0+sha512.60c18acd138bff695d339be6ad13f7e936eea6745660d4cc4a776d5247c540d0edee1a563695c183a66eb917ef88f2b4feb1fc25f32a7adcadc7aaf3438e99c1"}