package models

import (
	"encoding/json"
	"time"
)

// WorkflowTemplateStatus 工作流模板状态（用于Workflow）
type WorkflowTemplateStatus string

const (
	WorkflowTemplateStatusDraft    WorkflowTemplateStatus = "draft"    // 草稿
	WorkflowTemplateStatusPending  WorkflowTemplateStatus = "pending"  // 待审批
	WorkflowTemplateStatusApproved WorkflowTemplateStatus = "approved" // 已审批，可执行
	WorkflowTemplateStatusRejected WorkflowTemplateStatus = "rejected" // 已拒绝
	WorkflowTemplateStatusArchived WorkflowTemplateStatus = "archived" // 已归档
)

// WorkflowExecutionStatus 工作流执行状态（用于WorkflowExecution）
type WorkflowExecutionStatus string

const (
	WorkflowExecutionStatusPending   WorkflowExecutionStatus = "pending"   // 待执行
	WorkflowExecutionStatusRunning   WorkflowExecutionStatus = "running"   // 运行中
	WorkflowExecutionStatusPaused    WorkflowExecutionStatus = "paused"    // 已暂停
	WorkflowExecutionStatusCompleted WorkflowExecutionStatus = "completed" // 已完成
	WorkflowExecutionStatusFailed    WorkflowExecutionStatus = "failed"    // 失败
	WorkflowExecutionStatusCanceled  WorkflowExecutionStatus = "canceled"  // 已取消
)

// WorkflowStatus 保留用于向后兼容（废弃，请使用 WorkflowExecutionStatus）
type WorkflowStatus = WorkflowExecutionStatus

const (
	// 向后兼容常量 - 模板状态相关
	WorkflowStatusDraft    = WorkflowExecutionStatus("draft")    // 已废弃，请使用 WorkflowTemplateStatusDraft
	WorkflowStatusApproved = WorkflowExecutionStatus("approved") // 已废弃，请使用 WorkflowTemplateStatusApproved
	WorkflowStatusRejected = WorkflowExecutionStatus("rejected") // 已废弃，请使用 WorkflowTemplateStatusRejected

	// 向后兼容常量 - 执行状态相关
	WorkflowStatusPending   = WorkflowExecutionStatusPending   // 待执行
	WorkflowStatusRunning   = WorkflowExecutionStatusRunning   // 运行中
	WorkflowStatusPaused    = WorkflowExecutionStatusPaused    // 已暂停
	WorkflowStatusCompleted = WorkflowExecutionStatusCompleted // 已完成
	WorkflowStatusFailed    = WorkflowExecutionStatusFailed    // 失败
	WorkflowStatusCanceled  = WorkflowExecutionStatusCanceled  // 已取消
)

// StepStatus 步骤状态
type StepStatus string

const (
	StepStatusPending   StepStatus = "pending"   // 待执行
	StepStatusRunning   StepStatus = "running"   // 执行中
	StepStatusCompleted StepStatus = "completed" // 已完成
	StepStatusFailed    StepStatus = "failed"    // 失败
	StepStatusSkipped   StepStatus = "skipped"   // 已跳过
	StepStatusWaiting   StepStatus = "waiting"   // 等待审批
	StepStatusApproved  StepStatus = "approved"  // 已审批通过
	StepStatusRejected  StepStatus = "rejected"  // 已拒绝
)

// StepType 步骤类型
type StepType string

const (
	StepTypeDeployment   StepType = "deployment"    // 部署步骤
	StepTypeTrafficSplit StepType = "traffic-split" // 流量分发
	StepTypeMonitoring   StepType = "monitoring"    // 监控检查
	StepTypeApproval     StepType = "approval"      // 审批步骤
	StepTypeCleanup      StepType = "cleanup"       // 清理步骤
	StepTypeUserGroup    StepType = "usergroup"     // 用户组绑定 (未实现)
	StepTypeNotification StepType = "notification"  // 通知步骤 (未实现)
	StepTypeRollback     StepType = "rollback"      // 回滚步骤
)

// WorkflowType 工作流类型
type WorkflowType string

const (
	WorkflowTypeCanary    WorkflowType = "canary"     // 灰度发布
	WorkflowTypeBlueGreen WorkflowType = "blue-green" // 蓝绿部署 (未实现)
	WorkflowTypeRolling   WorkflowType = "rolling"    // 滚动更新 (未实现)
	WorkflowTypeABTest    WorkflowType = "ab-test"    // A/B测试 (未实现)
)

// TriggerType 触发类型
type TriggerType string

const (
	TriggerTypeManual   TriggerType = "manual"   // 手动触发
	TriggerTypeAuto     TriggerType = "auto"     // 自动触发
	TriggerTypeSchedule TriggerType = "schedule" // 定时触发
)

// Workflow 工作流定义（模板）
type Workflow struct {
	BaseModel
	Name        string                 `gorm:"column:name;not null;comment:工作流名称" json:"name"`
	Description string                 `gorm:"column:description;comment:工作流描述" json:"description"`
	Type        WorkflowType           `gorm:"column:type;not null;default:canary;comment:工作流类型" json:"type"`
	Namespace   string                 `gorm:"column:namespace;not null;comment:命名空间" json:"namespace"`
	ClusterID   string                 `gorm:"column:cluster_id;not null;comment:集群ID" json:"clusterId"`
	Status      WorkflowTemplateStatus `gorm:"column:status;not null;default:draft;comment:模板状态" json:"status"`
	Config      string                 `gorm:"column:config;type:text;comment:工作流配置JSON" json:"config"`
	Version     int                    `gorm:"column:version;not null;default:1;comment:版本号" json:"version"`

	// 全链路灰度发布扩展字段
	Services     string `gorm:"column:services;type:text;comment:服务链路配置JSON" json:"services"`
	GlobalConfig string `gorm:"column:global_config;type:text;comment:全局配置JSON" json:"globalConfig"`
	Dependencies string `gorm:"column:dependencies;type:text;comment:服务依赖关系JSON" json:"dependencies"`

	// 监控相关
	MonitoringConfig string `gorm:"column:monitoring_config;type:text;comment:监控配置JSON" json:"monitoringConfig"`
}

func (w *Workflow) TableName() string {
	return "workflow"
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	BaseModel
	WorkflowID  int64      `gorm:"column:workflow_id;not null;comment:工作流ID" json:"workflowId"`
	Name        string     `gorm:"column:name;not null;comment:步骤名称" json:"name"`
	Type        StepType   `gorm:"column:type;not null;comment:步骤类型" json:"type"`
	Order       int        `gorm:"column:order;not null;comment:执行顺序" json:"order"`
	Config      string     `gorm:"column:config;type:text;comment:步骤配置JSON" json:"config"`
	DependsOn   string     `gorm:"column:depends_on;comment:依赖步骤ID列表(JSON数组)" json:"dependsOn"`
	Approvers   string     `gorm:"column:approvers;comment:审批人列表(JSON数组)" json:"approvers"`
	AutoExecute bool       `gorm:"column:auto_execute;not null;default:false;comment:是否自动执行" json:"autoExecute"`
	Timeout     int        `gorm:"column:timeout;comment:超时时间(秒)" json:"timeout"`
	RetryCount  int        `gorm:"column:retry_count;default:0;comment:重试次数" json:"retryCount"`
	Status      StepStatus `gorm:"column:status;not null;default:pending;comment:状态" json:"status"`

	// 全链路灰度发布扩展字段
	ServiceName   string `gorm:"column:service_name;comment:关联服务名" json:"serviceName"`
	ServiceOrder  int    `gorm:"column:service_order;comment:服务在链路中的顺序" json:"serviceOrder"`
	ParallelGroup string `gorm:"column:parallel_group;comment:并行执行组标识" json:"parallelGroup"`
	Condition     string `gorm:"column:condition;comment:执行条件JSON" json:"condition"`

	// 监控相关
	HealthCheck string `gorm:"column:health_check;type:text;comment:健康检查配置JSON" json:"healthCheck"`
	Metrics     string `gorm:"column:metrics;type:text;comment:监控指标配置JSON" json:"metrics"`
}

func (w *WorkflowStep) TableName() string {
	return "workflow_step"
}

// WorkflowExecution 工作流执行记录
type WorkflowExecution struct {
	BaseModel
	WorkflowID   int64                   `gorm:"column:workflow_id;not null;comment:工作流ID" json:"workflowId"`
	Status       WorkflowExecutionStatus `gorm:"column:status;not null;comment:执行状态" json:"status"`
	StartTime    time.Time               `gorm:"column:start_time;not null;comment:开始时间" json:"startTime"`
	EndTime      *time.Time              `gorm:"column:end_time;comment:结束时间" json:"endTime"`
	ExecutorID   string                  `gorm:"column:executor_id;not null;comment:执行者ID" json:"executorId"`
	ExecutorName string                  `gorm:"column:executor_name;not null;comment:执行者名称" json:"executorName"`
	TriggerType  TriggerType             `gorm:"column:trigger_type;not null;comment:触发类型(manual/auto/schedule)" json:"triggerType"`
	Context      string                  `gorm:"column:context;type:text;comment:执行上下文JSON" json:"context"`
	Logs         string                  `gorm:"column:logs;type:longtext;comment:执行日志" json:"logs"`
	ErrorMessage string                  `gorm:"column:error_message;type:text;comment:错误信息" json:"errorMessage"`

	// 全链路灰度发布扩展字段
	ServiceExecutions string `gorm:"column:service_executions;type:text;comment:服务执行状态JSON(ServiceExecutionStatus数组)" json:"serviceExecutions"`
	GlobalMetrics     string `gorm:"column:global_metrics;type:text;comment:全局监控指标JSON" json:"globalMetrics"`
	RollbackInfo      string `gorm:"column:rollback_info;type:text;comment:回滚信息JSON" json:"rollbackInfo"`
}

func (w *WorkflowExecution) TableName() string {
	return "workflow_execution"
}

// StepExecution 步骤执行记录
type StepExecution struct {
	BaseModel
	ExecutionID  int64      `gorm:"column:execution_id;not null;comment:工作流执行ID" json:"executionId"`
	StepID       int64      `gorm:"column:step_id;not null;comment:步骤ID" json:"stepId"`
	Status       StepStatus `gorm:"column:status;not null;comment:执行状态" json:"status"`
	StartTime    time.Time  `gorm:"column:start_time;not null;comment:开始时间" json:"startTime"`
	EndTime      *time.Time `gorm:"column:end_time;comment:结束时间" json:"endTime"`
	ExecutorID   string     `gorm:"column:executor_id;not null;comment:执行者ID" json:"executorId"`
	ExecutorName string     `gorm:"column:executor_name;not null;comment:执行者名称" json:"executorName"`
	Input        string     `gorm:"column:input;type:text;comment:输入参数JSON" json:"input"`
	Output       string     `gorm:"column:output;type:text;comment:执行输出JSON" json:"output"`
	Logs         string     `gorm:"column:logs;type:longtext;comment:执行日志" json:"logs"`
	ErrorMessage string     `gorm:"column:error_message;type:text;comment:错误信息" json:"errorMessage"`
	RetryCount   int        `gorm:"column:retry_count;default:0;comment:重试次数" json:"retryCount"`

	// 全链路灰度发布扩展字段
	ServiceName    string `gorm:"column:service_name;comment:关联服务名" json:"serviceName"`
	ServiceMetrics string `gorm:"column:service_metrics;type:text;comment:服务监控指标JSON" json:"serviceMetrics"`
	HealthStatus   string `gorm:"column:health_status;comment:健康状态" json:"healthStatus"`
}

func (w *StepExecution) TableName() string {
	return "step_execution"
}

// ServiceExecutionStatus 服务执行状态
type ServiceExecutionStatus struct {
	ServiceName      string          `json:"serviceName"`      // 服务名称
	Namespace        string          `json:"namespace"`        // 命名空间
	DeploymentStatus string          `json:"deploymentStatus"` // 部署状态：pending/deploying/deployed/failed
	HealthStatus     string          `json:"healthStatus"`     // 健康状态：unknown/healthy/unhealthy
	Metrics          *ServiceMetrics `json:"metrics"`          // 服务监控指标
	LastUpdated      string          `json:"lastUpdated"`      // 最后更新时间
	ErrorMessage     string          `json:"errorMessage"`     // 错误信息
}

func (s *ServiceExecutionStatus) TableName() string {
	return "service_execution_status"
}

// ServiceMetrics 服务监控指标（从 canary.go 移动过来）
type ServiceMetrics struct {
	// 请求统计
	TotalRequests  int64 `json:"totalRequests"`  // 总请求数
	CanaryRequests int64 `json:"canaryRequests"` // 灰度请求数

	// 响应时间
	AvgResponseTime float64 `json:"avgResponseTime"` // 平均响应时间(ms)
	P95ResponseTime float64 `json:"p95ResponseTime"` // P95响应时间(ms)
	P99ResponseTime float64 `json:"p99ResponseTime"` // P99响应时间(ms)

	// 错误率
	ErrorRate   float64 `json:"errorRate"`   // 错误率(%)
	SuccessRate float64 `json:"successRate"` // 成功率(%)

	// 资源使用
	CPUUsage    float64 `json:"cpuUsage"`    // CPU使用率(%)
	MemoryUsage float64 `json:"memoryUsage"` // 内存使用率(%)

	// 更新时间
	LastUpdated string `json:"lastUpdated"` // 最后更新时间
}

// WorkflowServiceConfig 工作流服务配置结构体
type WorkflowServiceConfig struct {
	Name         string                      `json:"name"`                   // 服务名称
	Order        int                         `json:"order"`                  // 执行顺序，用于确定服务部署的先后顺序
	Images       map[string]string           `json:"images,omitempty"`       // 多镜像支持，key是容器名称，value是镜像地址
	Version      string                      `json:"version"`                // 版本号，用于标识当前部署的版本
	Replicas     int32                       `json:"replicas"`               // 副本数，指定服务的实例数量
	Resources    map[string]string           `json:"resources,omitempty"`    // 资源配置，包括CPU、内存等资源限制
	Environment  map[string]string           `json:"environment,omitempty"`  // 环境变量，key-value形式的环境变量配置
	Strategy     string                      `json:"strategy,omitempty"`     // 部署策略: canary, blue-green, rolling
	Routing      *WorkflowServiceRouting     `json:"routing,omitempty"`      // 路由配置，定义服务的流量路由规则
	HealthCheck  *WorkflowServiceHealthCheck `json:"healthCheck,omitempty"`  // 健康检查配置，用于检测服务健康状态
	Monitoring   *WorkflowServiceMonitoring  `json:"monitoring,omitempty"`   // 监控配置，定义监控指标和阈值
	Dependencies []string                    `json:"dependencies,omitempty"` // 依赖服务列表，指定当前服务依赖的其他服务
}

// WorkflowServiceRouting 工作流服务路由配置
type WorkflowServiceRouting struct {
	Strategy string                  `json:"strategy"` // header, weight, cookie
	Rules    []WorkflowServiceRule   `json:"rules"`
	Weights  []WorkflowServiceWeight `json:"weights,omitempty"`
	Headers  []WorkflowServiceHeader `json:"headers,omitempty"`
	Cookies  []WorkflowServiceCookie `json:"cookies,omitempty"`
}

// WorkflowServiceRule 工作流服务规则
type WorkflowServiceRule struct {
	Type     string `json:"type"` // header, parameter, user
	Key      string `json:"key"`
	Value    string `json:"value"`
	Operator string `json:"operator"` // equals, contains, regex
}

// WorkflowServiceWeight 工作流服务权重
type WorkflowServiceWeight struct {
	Version string `json:"version"`
	Weight  int32  `json:"weight"`
}

// WorkflowServiceHeader 工作流服务头部路由
type WorkflowServiceHeader struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Match string `json:"match"` // exact, prefix, regex
}

// WorkflowServiceCookie 工作流服务Cookie路由
type WorkflowServiceCookie struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Match string `json:"match"` // exact, prefix, regex
}

// WorkflowServiceHealthCheck 工作流服务健康检查
type WorkflowServiceHealthCheck struct {
	Path                string `json:"path"`
	Port                int    `json:"port"`
	InitialDelaySeconds int    `json:"initialDelaySeconds"`
	PeriodSeconds       int    `json:"periodSeconds"`
	TimeoutSeconds      int    `json:"timeoutSeconds"`
	SuccessThreshold    int    `json:"successThreshold"`
	FailureThreshold    int    `json:"failureThreshold"`
}

// WorkflowServiceMonitoring 工作流服务监控配置
type WorkflowServiceMonitoring struct {
	Metrics    []string                   `json:"metrics"`
	Thresholds []WorkflowServiceThreshold `json:"thresholds"`
	Duration   string                     `json:"duration"`
	Interval   string                     `json:"interval"`
}

// WorkflowServiceThreshold 工作流服务阈值
type WorkflowServiceThreshold struct {
	Name     string  `json:"name"`
	Operator string  `json:"operator"` // >=, <=, >, <, ==
	Value    float64 `json:"value"`
	Unit     string  `json:"unit,omitempty"`
}

// WorkflowGlobalConfig 工作流全局配置
type WorkflowGlobalConfig struct {
	// 全局路由配置
	GlobalHeaders map[string]string `json:"globalHeaders,omitempty"`

	// 全局监控配置
	GlobalMonitoring *WorkflowGlobalMonitoring `json:"globalMonitoring,omitempty"`

	// 全局回滚配置
	GlobalRollback *WorkflowGlobalRollback `json:"globalRollback,omitempty"`

	// 全局超时配置
	GlobalTimeout *WorkflowGlobalTimeout `json:"globalTimeout,omitempty"`

	// 全局通知配置
	GlobalNotification *WorkflowGlobalNotification `json:"globalNotification,omitempty"`
}

// WorkflowGlobalMonitoring 工作流全局监控配置
type WorkflowGlobalMonitoring struct {
	Enabled      bool     `json:"enabled"`
	Duration     string   `json:"duration"`
	Interval     string   `json:"interval"`
	Metrics      []string `json:"metrics"`
	AlertRules   []string `json:"alertRules,omitempty"`
	DashboardURL string   `json:"dashboardUrl,omitempty"`
}

// WorkflowGlobalRollback 工作流全局回滚配置
type WorkflowGlobalRollback struct {
	Enabled         bool   `json:"enabled"`
	AutoRollback    bool   `json:"autoRollback"`
	RollbackTrigger string `json:"rollbackTrigger"` // manual, auto, threshold
	ThresholdConfig string `json:"thresholdConfig,omitempty"`
}

// WorkflowGlobalTimeout 工作流全局超时配置
type WorkflowGlobalTimeout struct {
	StepTimeout       int `json:"stepTimeout"`
	ApprovalTimeout   int `json:"approvalTimeout"`
	MonitoringTimeout int `json:"monitoringTimeout"`
	RollbackTimeout   int `json:"rollbackTimeout"`
}

// WorkflowGlobalNotification 工作流全局通知配置
type WorkflowGlobalNotification struct {
	Enabled  bool     `json:"enabled"`
	Channels []string `json:"channels"` // email, slack, webhook
	Events   []string `json:"events"`   // start, success, failure, approval
	Template string   `json:"template,omitempty"`
	Users    []string `json:"users,omitempty"`
}

// StepConfig 步骤配置结构体（用于JSON序列化）
type StepConfig struct {
	// 通用配置
	Timeout     int    `json:"timeout,omitempty"`
	RetryCount  int    `json:"retryCount,omitempty"`
	Description string `json:"description,omitempty"`
	Condition   string `json:"condition,omitempty"`

	// 部署配置
	DeploymentConfig *DeploymentConfig `json:"deploymentConfig,omitempty"`

	// 流量分发配置
	TrafficConfig *TrafficConfig `json:"trafficConfig,omitempty"`

	// 监控配置
	MonitorConfig *MonitorConfig `json:"monitorConfig,omitempty"`

	// 审批配置
	ApprovalConfig *ApprovalConfig `json:"approvalConfig,omitempty"`

	// 用户组配置
	UserGroupConfig *UserGroupConfig `json:"userGroupConfig,omitempty"`

	// 通知配置
	NotificationConfig *NotificationConfig `json:"notificationConfig,omitempty"`

	// 回滚配置
	RollbackConfig *RollbackConfig `json:"rollbackConfig,omitempty"`
}

// DeploymentConfig 部署配置
type DeploymentConfig struct {
	ServiceName string            `json:"serviceName"`
	Images      map[string]string `json:"images"` // 多镜像支持，key是容器名称，value是镜像地址
	Version     string            `json:"version"`
	Replicas    int32             `json:"replicas"`
	Resources   map[string]string `json:"resources,omitempty"`
	Environment map[string]string `json:"environment,omitempty"`

	// 全链路灰度发布扩展
	Strategy       string                      `json:"strategy,omitempty"` // canary, blue-green, rolling
	CanaryReplicas int32                       `json:"canaryReplicas,omitempty"`
	HealthCheck    *WorkflowServiceHealthCheck `json:"healthCheck,omitempty"`
	Dependencies   []string                    `json:"dependencies,omitempty"`
}

// TrafficConfig 流量配置
type TrafficConfig struct {
	ServiceName string          `json:"serviceName"`
	Strategy    string          `json:"strategy"` // canary, blue-green, ab-test
	Weights     []TrafficWeight `json:"weights"`
	Rules       []TrafficRule   `json:"rules,omitempty"`

	// 全链路灰度发布扩展
	ChainConfig *TrafficChainConfig `json:"chainConfig,omitempty"`
}

// TrafficChainConfig 流量链路配置
type TrafficChainConfig struct {
	ChainServices []string             `json:"chainServices"`
	ChainRules    []TrafficChainRule   `json:"chainRules"`
	ChainWeights  []TrafficChainWeight `json:"chainWeights"`
}

// TrafficChainRule 流量链路规则
type TrafficChainRule struct {
	ServiceName string `json:"serviceName"`
	Type        string `json:"type"` // header, parameter, user
	Key         string `json:"key"`
	Value       string `json:"value"`
	Operator    string `json:"operator"` // equals, contains, regex
}

// TrafficChainWeight 流量链路权重
type TrafficChainWeight struct {
	ServiceName string `json:"serviceName"`
	Version     string `json:"version"`
	Weight      int32  `json:"weight"`
}

type TrafficWeight struct {
	Version string `json:"version"`
	Weight  int32  `json:"weight"`
}

type TrafficRule struct {
	Type     string `json:"type"` // header, parameter, user
	Key      string `json:"key"`
	Value    string `json:"value"`
	Operator string `json:"operator"` // equals, contains, regex
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	ServiceName string             `json:"serviceName"`
	Duration    string             `json:"duration"`
	Metrics     []string           `json:"metrics"` // 监控指标名称列表
	Thresholds  []MonitorThreshold `json:"thresholds"`

	// 全链路灰度发布扩展
	ChainMonitoring *MonitorChainConfig `json:"chainMonitoring,omitempty"`
}

// MonitorChainConfig 监控链路配置
type MonitorChainConfig struct {
	ChainServices []string             `json:"chainServices"`
	ChainMetrics  []MonitorChainMetric `json:"chainMetrics"`
	ChainAlerts   []MonitorChainAlert  `json:"chainAlerts"`
}

// MonitorChainMetric 监控链路指标
type MonitorChainMetric struct {
	ServiceName string   `json:"serviceName"`
	Metrics     []string `json:"metrics"`
}

// MonitorChainAlert 监控链路告警
type MonitorChainAlert struct {
	ServiceName string             `json:"serviceName"`
	Thresholds  []MonitorThreshold `json:"thresholds"`
}

type MonitorThreshold struct {
	Name     string  `json:"name"`
	Operator string  `json:"operator"` // >=, <=, >, <, ==
	Value    float64 `json:"value"`
	Unit     string  `json:"unit,omitempty"`
}

// ApprovalConfig 审批配置
type ApprovalConfig struct {
	Approvers     []string `json:"approvers"`
	ConditionType string   `json:"conditionType"` // and, or
	Timeout       int      `json:"timeout,omitempty"`

	// 全链路灰度发布扩展
	ServiceApprovals []ServiceApproval `json:"serviceApprovals,omitempty"`
}

// ServiceApproval 服务审批配置
type ServiceApproval struct {
	ServiceName   string   `json:"serviceName"`
	Approvers     []string `json:"approvers"`
	ConditionType string   `json:"conditionType"` // and, or
	Required      bool     `json:"required"`
}

// UserGroupConfig 用户组配置
type UserGroupConfig struct {
	UserGroups []UserGroupBinding `json:"userGroups"`
}

type UserGroupBinding struct {
	Name          string `json:"name"`
	TargetVersion string `json:"targetVersion"`
	Priority      int    `json:"priority"`
	ServiceName   string `json:"serviceName,omitempty"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Channels []string `json:"channels"` // email, slack, webhook
	Template string   `json:"template,omitempty"`
	Users    []string `json:"users,omitempty"`

	// 全链路灰度发布扩展
	ServiceNotifications []ServiceNotification `json:"serviceNotifications,omitempty"`
}

// ServiceNotification 服务通知配置
type ServiceNotification struct {
	ServiceName string   `json:"serviceName"`
	Events      []string `json:"events"`
	Channels    []string `json:"channels"`
	Users       []string `json:"users"`
}

// RollbackConfig 回滚配置
type RollbackConfig struct {
	ServiceName     string `json:"serviceName"`
	Strategy        string `json:"strategy"` // immediate, gradual, manual
	AutoRollback    bool   `json:"autoRollback"`
	RollbackTrigger string `json:"rollbackTrigger"` // manual, auto, threshold
	ThresholdConfig string `json:"thresholdConfig,omitempty"`

	// 全链路灰度发布扩展
	ChainRollback *RollbackChainConfig `json:"chainRollback,omitempty"`
}

// RollbackChainConfig 回滚链路配置
type RollbackChainConfig struct {
	ChainServices []string            `json:"chainServices"`
	ChainStrategy string              `json:"chainStrategy"` // sequential, parallel, reverse
	ChainRules    []RollbackChainRule `json:"chainRules"`
}

// RollbackChainRule 回滚链路规则
type RollbackChainRule struct {
	ServiceName string `json:"serviceName"`
	Condition   string `json:"condition"`
	Action      string `json:"action"`
}

// ServiceExecutionStatus 相关的辅助方法

// GetServiceStatuses 获取服务执行状态列表
func (we *WorkflowExecution) GetServiceStatuses() ([]ServiceExecutionStatus, error) {
	if we.ServiceExecutions == "" {
		return []ServiceExecutionStatus{}, nil
	}

	var statuses []ServiceExecutionStatus
	if err := json.Unmarshal([]byte(we.ServiceExecutions), &statuses); err != nil {
		return nil, err
	}
	return statuses, nil
}

// SetServiceStatuses 设置服务执行状态列表
func (we *WorkflowExecution) SetServiceStatuses(statuses []ServiceExecutionStatus) error {
	data, err := json.Marshal(statuses)
	if err != nil {
		return err
	}
	we.ServiceExecutions = string(data)
	return nil
}

// UpdateServiceStatus 更新指定服务的执行状态
func (we *WorkflowExecution) UpdateServiceStatus(serviceName string, status ServiceExecutionStatus) error {
	statuses, err := we.GetServiceStatuses()
	if err != nil {
		return err
	}

	// 查找并更新指定服务的状态
	found := false
	for i, s := range statuses {
		if s.ServiceName == serviceName && s.Namespace == status.Namespace {
			statuses[i] = status
			found = true
			break
		}
	}

	// 如果没有找到，则添加新的状态
	if !found {
		statuses = append(statuses, status)
	}

	return we.SetServiceStatuses(statuses)
}

// GetServiceStatus 获取指定服务的执行状态
func (we *WorkflowExecution) GetServiceStatus(serviceName, namespace string) (*ServiceExecutionStatus, error) {
	statuses, err := we.GetServiceStatuses()
	if err != nil {
		return nil, err
	}

	for _, status := range statuses {
		if status.ServiceName == serviceName && status.Namespace == namespace {
			return &status, nil
		}
	}

	return nil, nil // 未找到
}

// GetServiceMetrics 获取指定服务的监控指标
func (se *StepExecution) GetServiceMetrics() (*ServiceMetrics, error) {
	if se.ServiceMetrics == "" {
		return nil, nil
	}

	var metrics ServiceMetrics
	if err := json.Unmarshal([]byte(se.ServiceMetrics), &metrics); err != nil {
		return nil, err
	}
	return &metrics, nil
}

// SetServiceMetrics 设置服务监控指标
func (se *StepExecution) SetServiceMetrics(metrics *ServiceMetrics) error {
	if metrics == nil {
		se.ServiceMetrics = ""
		return nil
	}

	data, err := json.Marshal(metrics)
	if err != nil {
		return err
	}
	se.ServiceMetrics = string(data)
	return nil
}

// Workflow 状态管理辅助方法

// IsApproved 检查工作流模板是否已审批
func (w *Workflow) IsApproved() bool {
	return w.Status == WorkflowTemplateStatusApproved
}

// CanExecute 检查工作流模板是否可以执行
func (w *Workflow) CanExecute() bool {
	return w.Status == WorkflowTemplateStatusApproved
}

// SubmitForApproval 提交审批
func (w *Workflow) SubmitForApproval() {
	w.Status = WorkflowTemplateStatusPending
}

// Approve 审批通过
func (w *Workflow) Approve() {
	w.Status = WorkflowTemplateStatusApproved
}

// Reject 审批拒绝
func (w *Workflow) Reject() {
	w.Status = WorkflowTemplateStatusRejected
}

// Archive 归档工作流模板
func (w *Workflow) Archive() {
	w.Status = WorkflowTemplateStatusArchived
}

// 注意：获取执行记录需要通过服务层查询，不在模型层提供关联

// WorkflowExecution 状态管理辅助方法

// IsRunning 检查执行是否正在运行
func (we *WorkflowExecution) IsRunning() bool {
	return we.Status == WorkflowExecutionStatusRunning
}

// IsCompleted 检查执行是否已完成
func (we *WorkflowExecution) IsCompleted() bool {
	return we.Status == WorkflowExecutionStatusCompleted
}

// IsFailed 检查执行是否失败
func (we *WorkflowExecution) IsFailed() bool {
	return we.Status == WorkflowExecutionStatusFailed
}

// IsFinished 检查执行是否已结束（完成、失败或取消）
func (we *WorkflowExecution) IsFinished() bool {
	return we.Status == WorkflowExecutionStatusCompleted ||
		we.Status == WorkflowExecutionStatusFailed ||
		we.Status == WorkflowExecutionStatusCanceled
}

// Start 开始执行
func (we *WorkflowExecution) Start() {
	we.Status = WorkflowExecutionStatusRunning
	we.StartTime = time.Now()
}

// Complete 完成执行
func (we *WorkflowExecution) Complete() {
	we.Status = WorkflowExecutionStatusCompleted
	now := time.Now()
	we.EndTime = &now
}

// Fail 标记执行失败
func (we *WorkflowExecution) Fail(errorMsg string) {
	we.Status = WorkflowExecutionStatusFailed
	we.ErrorMessage = errorMsg
	now := time.Now()
	we.EndTime = &now
}

// Cancel 取消执行
func (we *WorkflowExecution) Cancel() {
	we.Status = WorkflowExecutionStatusCanceled
	now := time.Now()
	we.EndTime = &now
}

// Pause 暂停执行
func (we *WorkflowExecution) Pause() {
	we.Status = WorkflowExecutionStatusPaused
}

// Resume 恢复执行
func (we *WorkflowExecution) Resume() {
	we.Status = WorkflowExecutionStatusRunning
}
