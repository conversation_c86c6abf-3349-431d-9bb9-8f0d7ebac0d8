package models

import (
	"database/sql/driver"
	"fmt"
)

// DomainProvider represents cloud DNS provider configuration
type DomainProvider struct {
	BaseModel
	Name   string               `gorm:"type:varchar(100);not null" json:"name"` // 名称
	Type   DomainProviderType   `gorm:"type:enum('alibaba','azure','cloudflare');not null" json:"type"`
	Config string               `gorm:"type:longtext;not null" json:"config"` // Encrypted JSON config
	Status DomainProviderStatus `gorm:"type:enum('active','inactive');default:'active'" json:"status"`
}

// TableName returns the table name for DomainProvider
func (DomainProvider) TableName() string {
	return "domain_provider"
}

//type DomainInfo struct {
//	BaseModel
//	Domain       string `gorm:"type:varchar(100);not null" json:"domain"`
//	ProviderUUID string `gorm:"type:varchar(100);not null" json:"providerUUID"`
//}
//
//func (DomainInfo) TableName() string {
//	return "domain_info"
//}

// DomainRecord represents a DNS record
type DomainRecord struct {
	BaseModel
	Domain       string             `gorm:"type:varchar(255);not null" json:"domain"`       // 域名字符串
	ProviderUUID string             `gorm:"type:varchar(100);not null" json:"providerUUID"` // 服务商UUID
	Subdomain    string             `gorm:"type:varchar(255)" json:"subdomain"`
	RecordType   DomainRecordType   `gorm:"type:enum('A','AAAA','CNAME','MX','TXT','SRV','NS');not null" json:"recordType"`
	Value        string             `gorm:"type:text;not null" json:"value"`
	TTL          int                `gorm:"type:int;default:600" json:"ttl"`
	Priority     int                `gorm:"type:int;default:0" json:"priority"`
	Proxied      *bool              `gorm:"type:boolean;default:false" json:"proxied"` // Cloudflare DNS Proxy状态
	Status       DomainRecordStatus `gorm:"type:enum('active','pending','failed','deleted');default:'pending'" json:"status"`
	Action       OperationType      `gorm:"type:enum('create','update','delete');not null" json:"action"` // 操作类型：新增、修改、删除
	Comment      string             `gorm:"type:text" json:"comment"`
	Reason       string             `gorm:"type:text" json:"reason"`     // 申请理由
	PrevRecord   string             `gorm:"type:text" json:"prevRecord"` // JSON格式的原记录信息
}

// TableName returns the table name for DomainRecord
func (DomainRecord) TableName() string {
	return "domain_record"
}

// DomainProviderType Domain provider type enumeration
type DomainProviderType string

const (
	ProviderTypeAlibaba    DomainProviderType = "alibaba"
	ProviderTypeAzure      DomainProviderType = "azure"
	ProviderTypeCloudflare DomainProviderType = "cloudflare"
)

// Value implements the driver Valuer interface
func (dpt DomainProviderType) Value() (driver.Value, error) {
	return string(dpt), nil
}

// Scan implements the sql Scanner interface
func (dpt *DomainProviderType) Scan(value interface{}) error {
	if value == nil {
		*dpt = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*dpt = DomainProviderType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*dpt = DomainProviderType(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into DomainProviderType", value)
}

type DomainProviderStatus string

const (
	ProviderStatusActive   DomainProviderStatus = "active"
	ProviderStatusInactive DomainProviderStatus = "inactive"
)

// Value implements the driver Valuer interface
func (dps DomainProviderStatus) Value() (driver.Value, error) {
	return string(dps), nil
}

// Scan implements the sql Scanner interface
func (dps *DomainProviderStatus) Scan(value interface{}) error {
	if value == nil {
		*dps = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*dps = DomainProviderStatus(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*dps = DomainProviderStatus(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into DomainProviderStatus", value)
}

type DomainRecordType string

const (
	RecordTypeA     DomainRecordType = "A"
	RecordTypeAAAA  DomainRecordType = "AAAA"
	RecordTypeCNAME DomainRecordType = "CNAME"
	RecordTypeMX    DomainRecordType = "MX"
	RecordTypeTXT   DomainRecordType = "TXT"
	RecordTypeSRV   DomainRecordType = "SRV"
	RecordTypeNS    DomainRecordType = "NS"
)

// Value implements the driver Valuer interface
func (drt DomainRecordType) Value() (driver.Value, error) {
	return string(drt), nil
}

// Scan implements the sql Scanner interface
func (drt *DomainRecordType) Scan(value interface{}) error {
	if value == nil {
		*drt = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*drt = DomainRecordType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*drt = DomainRecordType(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into DomainRecordType", value)
}

type DomainRecordStatus string

const (
	RecordStatusActive  DomainRecordStatus = "active"
	RecordStatusPending DomainRecordStatus = "pending"
	RecordStatusFailed  DomainRecordStatus = "failed"
	RecordStatusDeleted DomainRecordStatus = "deleted"
)

// Value implements the driver Valuer interface
func (drs DomainRecordStatus) Value() (driver.Value, error) {
	return string(drs), nil
}

// Scan implements the sql Scanner interface
func (drs *DomainRecordStatus) Scan(value interface{}) error {
	if value == nil {
		*drs = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*drs = DomainRecordStatus(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*drs = DomainRecordStatus(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into DomainRecordStatus", value)
}

type OperationType string

const (
	OperationTypeCreate OperationType = "create"
	OperationTypeUpdate OperationType = "update"
	OperationTypeDelete OperationType = "delete"
)

// Value implements the driver Valuer interface
func (ot OperationType) Value() (driver.Value, error) {
	return string(ot), nil
}

// Scan implements the sql Scanner interface
func (ot *OperationType) Scan(value interface{}) error {
	if value == nil {
		*ot = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*ot = OperationType(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*ot = OperationType(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into OperationType", value)
}

type ApprovalStatus string

const (
	ApprovalStatusPending  ApprovalStatus = "pending"
	ApprovalStatusApproved ApprovalStatus = "approved"
	ApprovalStatusRejected ApprovalStatus = "rejected"
)

// Value implements the driver Valuer interface
func (as ApprovalStatus) Value() (driver.Value, error) {
	return string(as), nil
}

// Scan implements the sql Scanner interface
func (as *ApprovalStatus) Scan(value interface{}) error {
	if value == nil {
		*as = ""
		return nil
	}
	if s, ok := value.(string); ok {
		*as = ApprovalStatus(s)
		return nil
	}
	if b, ok := value.([]byte); ok {
		*as = ApprovalStatus(b)
		return nil
	}
	return fmt.Errorf("cannot scan %T into ApprovalStatus", value)
}
