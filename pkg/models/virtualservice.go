package models

type VirtualServiceStatus string

const (
	Active   VirtualServiceStatus = "active"
	Pending  VirtualServiceStatus = "pending"
	Failed   VirtualServiceStatus = "failed"
	Rejected VirtualServiceStatus = "rejected"
	Deleted  VirtualServiceStatus = "deleted"
)

type VirtualService struct {
	BaseModel
	// 基础配置
	Name            string `json:"name" gorm:"type:varchar(128);not null;index;uniqueIndex:idx_cluster_namespace_name"`
	Namespace       string `json:"namespace" gorm:"type:varchar(128);not null;index;uniqueIndex:idx_cluster_namespace_name"`
	Cluster         string `json:"cluster" gorm:"type:varchar(128);not null;index;uniqueIndex:idx_cluster_namespace_name"`
	Gateways        string `json:"gateways" gorm:"type:text"`                     // 方便查询
	Hosts           string `json:"hosts" gorm:"type:text"`                        // 方便查询
	Content         string `json:"content" gorm:"type:text"`                      // YAML原文
	ResourceVersion string `json:"resourceVersion" gorm:"type:varchar(64);index"` // Kubernetes 资源版本
	// 审批相关
	Status  VirtualServiceStatus `json:"status" gorm:"type:varchar(32);default:'pending'"`
	Action  OperationType        `json:"action" gorm:"type:enum('create','update','delete');not null"` // 操作类型：新增、修改、删除
	Comment string               `json:"comment" gorm:"type:text"`                                     // 审批意见
	Reason  string               `json:"reason" gorm:"type:text"`                                      // 申请理由
}

func (VirtualService) TableName() string {
	return "virtual_service"
}

type VirtualServiceHistory struct {
	BaseModel
	Name               string               `json:"name" gorm:"type:varchar(128);not null;"`
	Namespace          string               `json:"namespace" gorm:"type:varchar(128);not null;"`
	Cluster            string               `json:"cluster" gorm:"type:varchar(128);not null;"`
	Gateways           string               `json:"gateways" gorm:"type:text"`                     // 方便查询
	Hosts              string               `json:"hosts" gorm:"type:text"`                        // 方便查询
	Content            string               `json:"content" gorm:"type:text"`                      // YAML原文
	ResourceVersion    string               `json:"resourceVersion" gorm:"type:varchar(64);index"` // Kubernetes 资源版本
	VirtualServiceUUID string               `json:"virtualServiceUUID" gorm:"type:varchar(36);not null;index"`
	Version            int                  `json:"version" gorm:"type:int;not null"`                             // 版本号
	Action             OperationType        `json:"action" gorm:"type:enum('create','update','delete');not null"` // 操作类型
	Status             VirtualServiceStatus `json:"status" gorm:"type:varchar(32)"`                               // 备份时的状态
	Operator           string               `json:"operator" gorm:"type:varchar(255);not null"`                   // 操作人
	Comment            string               `json:"comment" gorm:"type:text"`                                     // 操作说明
}

func (VirtualServiceHistory) TableName() string {
	return "virtual_service_history"
}
