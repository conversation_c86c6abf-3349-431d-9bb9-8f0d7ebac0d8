package models

import (
	"gopkg.in/yaml.v3"
)

// SecurityRule 安全规则模型
type SecurityRule struct {
	BaseModel
	Name        string `gorm:"type:varchar(255);not null;index" json:"name"`
	Namespace   string `gorm:"type:varchar(255);not null;index" json:"namespace"`
	Cluster     string `gorm:"type:varchar(255);not null;index" json:"cluster"`
	Type        string `gorm:"type:varchar(50);not null;index" json:"type"` // blacklist, whitelist, ratelimit
	Content     string `gorm:"type:text;not null" json:"-"`                 // YAML格式存储规则配置
	Enabled     bool   `gorm:"type:bool;default:true" json:"enabled"`
	Description string `gorm:"type:varchar(1024)" json:"description"`
	AuditStatus string `gorm:"type:varchar(50);default:'pending'" json:"auditStatus"` // pending, approved, rejected, applied
}

// TableName 返回表名
func (SecurityRule) TableName() string {
	return "security_rules"
}

// GetRules 获取规则配置
func (s *SecurityRule) GetRules() (map[string]interface{}, error) {
	var rules map[string]interface{}
	if s.Content != "" {
		err := yaml.Unmarshal([]byte(s.Content), &rules)
		if err != nil {
			return nil, err
		}
	}
	return rules, nil
}

// SetRules 设置规则配置
func (s *SecurityRule) SetRules(rules map[string]interface{}) error {
	yamlBytes, err := yaml.Marshal(rules)
	if err != nil {
		return err
	}
	s.Content = string(yamlBytes)
	return nil
}

// GetContent 获取YAML格式的规则配置
func (s *SecurityRule) GetContent() string {
	return s.Content
}

// SetContentFromYAML 从YAML字符串设置规则配置
func (s *SecurityRule) SetContentFromYAML(yamlContent string) error {
	var rules map[string]interface{}

	// 验证YAML格式
	err := yaml.Unmarshal([]byte(yamlContent), &rules)
	if err != nil {
		return err
	}

	s.Content = yamlContent
	return nil
}

// SecurityRuleHistory 安全规则历史记录模型
type SecurityRuleHistory struct {
	BaseModel
	RuleID   string `gorm:"type:varchar(36);not null;index" json:"ruleId"`
	Version  int    `gorm:"type:int;not null" json:"version"`
	Action   string `gorm:"type:varchar(50);not null" json:"action"` // create, update, delete, apply
	Changes  string `gorm:"type:text" json:"changes"`                // 变更内容的JSON格式
	Operator string `gorm:"type:varchar(255);not null" json:"operator"`
	Content  string `gorm:"type:text" json:"-"` // 规则快照的YAML格式
}

// TableName 返回表名
func (SecurityRuleHistory) TableName() string {
	return "security_rule_histories"
}

// GetRuleData 获取规则快照
func (s *SecurityRuleHistory) GetRuleData() (map[string]interface{}, error) {
	var ruleData map[string]interface{}
	if s.Content != "" {
		err := yaml.Unmarshal([]byte(s.Content), &ruleData)
		if err != nil {
			return nil, err
		}
	}
	return ruleData, nil
}

// SetRuleData 设置规则快照
func (s *SecurityRuleHistory) SetRuleData(ruleData map[string]interface{}) error {
	ruleDataBytes, err := yaml.Marshal(ruleData)
	if err != nil {
		return err
	}
	s.Content = string(ruleDataBytes)
	return nil
}

// SecurityTemplate 安全策略模板模型
type SecurityTemplate struct {
	BaseModel
	Name        string `gorm:"type:varchar(255);not null;uniqueIndex" json:"name"`
	Type        string `gorm:"type:varchar(50);not null" json:"type"` // blacklist, whitelist, ratelimit
	Description string `gorm:"type:varchar(1024)" json:"description"`
	Content     string `gorm:"type:text;not null" json:"-"` // 模板配置的YAML格式
	IsBuiltIn   bool   `gorm:"type:bool;default:false" json:"isBuiltIn"`
}

// TableName 返回表名
func (SecurityTemplate) TableName() string {
	return "security_templates"
}

// GetTemplate 获取模板配置
func (s *SecurityTemplate) GetTemplate() (map[string]interface{}, error) {
	var template map[string]interface{}
	if s.Content != "" {
		err := yaml.Unmarshal([]byte(s.Content), &template)
		if err != nil {
			return nil, err
		}
	}
	return template, nil
}

// SetTemplate 设置模板配置
func (s *SecurityTemplate) SetTemplate(template map[string]interface{}) error {
	templateBytes, err := yaml.Marshal(template)
	if err != nil {
		return err
	}
	s.Content = string(templateBytes)
	return nil
}
