package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"pilot-api/internal/pkg/response"
	"pilot-api/internal/pkg/util"
	"strings"
	"time"

	"gorm.io/gorm"
)

type BaseModel struct {
	ID          int64          `gorm:"column:id" db:"id" json:"id" form:"id"`                                       //  主键
	UUID        string         `gorm:"column:uuid" db:"uuid" json:"uuid" form:"uuid"`                               //  uuid
	Creator     string         `gorm:"column:creator" db:"creator" json:"creator" form:"creator"`                   //  创建人
	Modifier    string         `gorm:"column:modifier" db:"modifier" json:"modifier" form:"modifier"`               //  备注
	Remark      string         `gorm:"column:remark" db:"remark" json:"remark" form:"remark"`                       //  修改人
	GmtCreate   *time.Time     `gorm:"column:gmt_create" db:"gmt_create" json:"gmtCreate" form:"gmtCreate"`         //  创建时间
	GmtModified *time.Time     `gorm:"column:gmt_modified" db:"gmt_modified" json:"gmtModified" form:"gmtModified"` //  修改时间
	DeletedAt   gorm.DeletedAt `gorm:"comment:删除时间;index" json:"-"`
}

func (b *BaseModel) Create(ops string) {
	now := time.Now()
	b.Creator = ops
	b.GmtCreate = &now
	b.GmtModified = &now
	b.UUID = util.UUID()
}

func (b *BaseModel) Update(ops string) {
	now := time.Now()
	b.Modifier = ops
	b.GmtModified = &now
}

func (b *BaseModel) Delete(ops string) {
	now := time.Now()
	b.Modifier = ops
	b.GmtModified = &now
	b.DeletedAt = gorm.DeletedAt{Time: now, Valid: true}
}

type Time time.Time

func (t *Time) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}
	var err error
	//前端接收的时间字符串
	str := string(data)
	//去除接收的str收尾多余的"
	timeStr := strings.Trim(str, "\"")
	t1, err := time.Parse(util.DateTimeFormat, timeStr)
	*t = Time(t1)
	return err
}

func (t Time) MarshalJSON() ([]byte, error) {
	formatted := fmt.Sprintf("\"%v\"", time.Time(t).Format(util.DateTimeFormat))
	return []byte(formatted), nil
}

func (t Time) Value() (driver.Value, error) {
	// MyTime 转换成 time.Time 类型
	tTime := time.Time(t)
	return tTime.Format(util.DateTimeFormat), nil
}

func (t *Time) Scan(v any) error {
	switch vt := v.(type) {
	case time.Time:
		// 字符串转成 time.Time 类型
		*t = Time(vt)
	default:
		return errors.New("类型处理错误")
	}
	return nil
}

func (t *Time) String() string {
	return fmt.Sprintf("time : %s", time.Time(*t).String())
}

const MaxPageSize = 1000

// CheckVersionConflict 检查版本冲突
func CheckVersionConflict(requestGmtModified, currentGmtModified *time.Time) error {
	// 如果任一时间为 nil，则不进行版本检查
	if requestGmtModified == nil || currentGmtModified == nil {
		return nil
	}
	// 比较时间戳是否一致
	if *requestGmtModified != *currentGmtModified {
		return response.VersionConflictError("")
	}
	return nil
}

type User struct {
	UserName string `json:"userName" form:"userName"` //  账号
	Name     string `json:"name" form:"name"`         //  名字
	Email    string `json:"email" form:"email"`       //  邮箱
	// 适配效能平台的用户字段
	Value  string `json:"value"`
	Value1 string `json:"value1"`
}
