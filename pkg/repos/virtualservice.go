package repos

import (
	"pilot-api/pkg/models"

	"gorm.io/gorm"
)

type VirtualServiceRepo struct {
	db *gorm.DB
}

func NewVirtualServiceRepo(db *gorm.DB) *VirtualServiceRepo {
	return &VirtualServiceRepo{db: db}
}

func (r *VirtualServiceRepo) Create(m *models.VirtualService) error {
	return r.db.Create(m).Error
}

func (r *VirtualServiceRepo) Update(m *models.VirtualService) error {
	return r.db.Where("uuid = ?", m.UUID).Updates(m).Error
}

func (r *VirtualServiceRepo) Save(m *models.VirtualService) error {
	return r.db.Save(m).Error
}

func (r *VirtualServiceRepo) CreateHistory(history *models.VirtualServiceHistory) error {
	return r.db.Create(history).Error
}

func (r *VirtualServiceRepo) GetFirst(cond models.VirtualService) (*models.VirtualService, error) {
	var m models.VirtualService
	err := r.db.Where(&cond).First(&m).Error
	if err != nil {
		return nil, err
	}
	return &m, nil
}

func (r *VirtualServiceRepo) GetPage(cond *models.VirtualService, order string, page, pageSize int) ([]*models.VirtualService, error) {
	var list []*models.VirtualService
	err := r.db.Where(cond).Order(order).Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return list, err
}

func (r *VirtualServiceRepo) GetHistoryPage(cond *models.VirtualServiceHistory, order string, page, pageSize int) ([]*models.VirtualServiceHistory, error) {
	var list []*models.VirtualServiceHistory
	err := r.db.Where(cond).Order(order).Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return list, err
}

func (r *VirtualServiceRepo) GetPageWithNameFilter(cond *models.VirtualService, nameFilter string, order string, page, pageSize int) ([]*models.VirtualService, error) {
	query := r.db.Where(cond)
	if nameFilter != "" {
		query = query.Where("name LIKE ?", "%"+nameFilter+"%")
	}
	var list []*models.VirtualService
	err := query.Order(order).Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return list, err
}

func (r *VirtualServiceRepo) Count() (int64, error) {
	var count int64
	err := r.db.Model(&models.VirtualService{}).Count(&count).Error
	return count, err
}

func (r *VirtualServiceRepo) CountWithFilter(cond *models.VirtualService, nameFilter string) (int64, error) {
	query := r.db.Model(&models.VirtualService{}).Where(cond)
	if nameFilter != "" {
		query = query.Where("name LIKE ?", "%"+nameFilter+"%")
	}
	var count int64
	err := query.Count(&count).Error
	return count, err
}

func (r *VirtualServiceRepo) DeleteInColumn(tx *gorm.DB, column string, values []string, softDelete bool) error {
	db := r.db
	if tx != nil {
		db = tx
	}
	if softDelete {
		return db.Where(column+" IN ?", values).Delete(&models.VirtualService{}).Error
	}
	return db.Unscoped().Where(column+" IN ?", values).Delete(&models.VirtualService{}).Error
}

func (r *VirtualServiceRepo) CountHistory(cond *models.VirtualServiceHistory) (int64, error) {
	var count int64
	err := r.db.Model(&models.VirtualServiceHistory{}).Where(cond).Count(&count).Error
	return count, err
}

func (r *VirtualServiceRepo) GetHistoryByVersion(cond models.VirtualServiceHistory) (*models.VirtualServiceHistory, error) {
	var history models.VirtualServiceHistory
	err := r.db.Where(&cond).First(&history).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

func (r *VirtualServiceRepo) GetMaxVersion(cluster, namespace, name string) (int, error) {
	var maxVersion int
	err := r.db.Model(&models.VirtualServiceHistory{}).
		Where("cluster = ? AND namespace = ? AND name = ?", cluster, namespace, name).
		Select("COALESCE(MAX(version), 0)").
		Scan(&maxVersion).Error
	return maxVersion, err
}

func (r *VirtualServiceRepo) GetNextVersion(cluster, namespace, name string) (int, error) {
	maxVersion, err := r.GetMaxVersion(cluster, namespace, name)
	if err != nil {
		return 0, err
	}
	return maxVersion + 1, nil
}

type VirtualServiceHistoryRepo struct {
	db *gorm.DB
}

func NewVirtualServiceHistoryRepo(db *gorm.DB) *VirtualServiceHistoryRepo {
	return &VirtualServiceHistoryRepo{db: db}
}

func (r *VirtualServiceHistoryRepo) Create(m *models.VirtualServiceHistory) error {
	return r.db.Create(m).Error
}
