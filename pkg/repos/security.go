package repos

import (
	"pilot-api/pkg/models"

	"gorm.io/gorm"
)

type SecurityRuleRepo struct {
	baseRepo[models.SecurityRule]
}

func NewSecurityRuleRepo(db *gorm.DB) *SecurityRuleRepo {
	return &SecurityRuleRepo{
		baseRepo: NewBaseRepo[models.SecurityRule](db),
	}
}

// GetByClusterAndNamespace 根据集群和命名空间获取规则列表
func (r *SecurityRuleRepo) GetByClusterAndNamespace(cluster, namespace string, page, pageSize int) ([]*models.SecurityRule, int64, error) {
	var rules []*models.SecurityRule
	var total int64

	query := r.db.Model(&models.SecurityRule{}).Where("cluster = ?", cluster)
	if namespace != "" {
		query = query.Where("namespace = ?", namespace)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("gmt_create DESC").Find(&rules).Error; err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// GetByTypeAndCluster 根据类型和集群获取规则列表
func (r *SecurityRuleRepo) GetByTypeAndCluster(ruleType, cluster string, page, pageSize int) ([]*models.SecurityRule, int64, error) {
	var rules []*models.SecurityRule
	var total int64

	query := r.db.Model(&models.SecurityRule{}).Where("type = ? AND cluster = ?", ruleType, cluster)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("gmt_create DESC").Find(&rules).Error; err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// SearchByName 根据名称搜索规则
func (r *SecurityRuleRepo) SearchByName(cluster, name string, page, pageSize int) ([]*models.SecurityRule, int64, error) {
	var rules []*models.SecurityRule
	var total int64

	query := r.db.Model(&models.SecurityRule{}).Where("cluster = ? AND name LIKE ?", cluster, "%"+name+"%")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("gmt_create DESC").Find(&rules).Error; err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// GetByClusterNamespaceAndName 根据集群、命名空间和名称获取规则
func (r *SecurityRuleRepo) GetByClusterNamespaceAndName(cluster, namespace, name string) (*models.SecurityRule, error) {
	var rule models.SecurityRule
	err := r.db.Where("cluster = ? AND namespace = ? AND name = ?", cluster, namespace, name).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

type SecurityRuleHistoryRepo struct {
	baseRepo[models.SecurityRuleHistory]
}

func NewSecurityRuleHistoryRepo(db *gorm.DB) *SecurityRuleHistoryRepo {
	return &SecurityRuleHistoryRepo{
		baseRepo: NewBaseRepo[models.SecurityRuleHistory](db),
	}
}

// GetByRuleID 根据规则ID获取历史记录
func (r *SecurityRuleHistoryRepo) GetByRuleID(ruleID string, page, pageSize int) ([]*models.SecurityRuleHistory, int64, error) {
	var histories []*models.SecurityRuleHistory
	var total int64

	query := r.db.Model(&models.SecurityRuleHistory{}).Where("rule_id = ?", ruleID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("version DESC").Find(&histories).Error; err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// GetLatestVersionByRuleID 获取规则的最新版本号
func (r *SecurityRuleHistoryRepo) GetLatestVersionByRuleID(ruleID string) (int, error) {
	var maxVersion int
	err := r.db.Model(&models.SecurityRuleHistory{}).Where("rule_id = ?", ruleID).Select("COALESCE(MAX(version), 0)").Scan(&maxVersion).Error
	return maxVersion, err
}

type SecurityTemplateRepo struct {
	baseRepo[models.SecurityTemplate]
}

func NewSecurityTemplateRepo(db *gorm.DB) *SecurityTemplateRepo {
	return &SecurityTemplateRepo{
		baseRepo: NewBaseRepo[models.SecurityTemplate](db),
	}
}

// GetByType 根据类型获取模板列表
func (r *SecurityTemplateRepo) GetByType(templateType string) ([]*models.SecurityTemplate, error) {
	var templates []*models.SecurityTemplate
	query := r.db.Model(&models.SecurityTemplate{})

	if templateType != "" {
		query = query.Where("type = ?", templateType)
	}

	err := query.Order("is_built_in DESC, gmt_create DESC").Find(&templates).Error
	return templates, err
}

// GetBuiltInTemplates 获取内置模板
func (r *SecurityTemplateRepo) GetBuiltInTemplates() ([]*models.SecurityTemplate, error) {
	var templates []*models.SecurityTemplate
	err := r.db.Where("is_built_in = ?", true).Order("gmt_create DESC").Find(&templates).Error
	return templates, err
}
