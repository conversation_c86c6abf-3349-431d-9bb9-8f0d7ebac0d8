package repos

import (
	"pilot-api/internal/pkg/response"
	"pilot-api/pkg/models"

	"gorm.io/gorm"
)

// DomainProviderRepo provides database operations for DomainProvider
type DomainProviderRepo struct {
	baseRepo[models.DomainProvider]
}

// NewDomainProviderRepo creates a new DomainProviderRepo instance
func NewDomainProviderRepo(db *gorm.DB) *DomainProviderRepo {
	return &DomainProviderRepo{
		baseRepo: NewBaseRepo[models.DomainProvider](db),
	}
}

// GetByType gets domain provider by type
func (r *DomainProviderRepo) GetByType(providerType models.DomainProviderType) (*models.DomainProvider, error) {
	var provider models.DomainProvider
	err := r.db.Where("type = ? AND status = ?", providerType, models.ProviderStatusActive).First(&provider).Error
	if err != nil {
		return nil, err
	}
	return &provider, nil
}

// ListByStatus gets domain providers by status
func (r *DomainProviderRepo) ListByStatus(status models.DomainProviderStatus) ([]models.DomainProvider, error) {
	var providers []models.DomainProvider
	err := r.db.Where("status = ?", status).Find(&providers).Error
	return providers, err
}

func (r *DomainProviderRepo) GetByUUID(uuid string) (*models.DomainProvider, error) {
	return r.GetFirst(models.DomainProvider{BaseModel: models.BaseModel{UUID: uuid}})
}

// GetPageWithFilter gets domain providers with pagination and filters
func (r *DomainProviderRepo) GetPageWithFilter(
	page, pageSize int, name string, providerType models.DomainProviderType, status models.DomainProviderStatus,
) (*response.PageModel[models.DomainProvider], error) {
	query := r.db.Model(&models.DomainProvider{})

	// Apply filters
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if providerType != "" {
		query = query.Where("type = ?", providerType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	var providers []models.DomainProvider
	offset := (page - 1) * pageSize
	err = query.Limit(pageSize).Offset(offset).Order("gmt_create DESC").Find(&providers).Error
	if err != nil {
		return nil, err
	}

	return &response.PageModel[models.DomainProvider]{
		List:     providers,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// DomainRecordRepo provides database operations for DomainRecord
type DomainRecordRepo struct {
	baseRepo[models.DomainRecord]
}

// NewDomainRecordRepo creates a new DomainRecordRepo instance
func NewDomainRecordRepo(db *gorm.DB) *DomainRecordRepo {
	return &DomainRecordRepo{
		baseRepo: NewBaseRepo[models.DomainRecord](db),
	}
}

// GetPageWithFilter gets domain records with pagination and filters
func (r *DomainRecordRepo) GetPageWithFilter(
	page, pageSize int, domain, subdomain, value string,
	recordType models.DomainRecordType, status models.DomainRecordStatus, providerUUID string,
) (*response.PageModel[models.DomainRecord], error) {
	query := r.db.Model(&models.DomainRecord{})

	// Apply filters
	if domain != "" {
		query = query.Where("domain = ?", domain)
	}
	if providerUUID != "" {
		query = query.Where("provider_uuid = ?", providerUUID)
	}
	if subdomain != "" {
		query = query.Where("subdomain LIKE ?", "%"+subdomain+"%")
	}
	if value != "" {
		query = query.Where("value LIKE ?", "%"+value+"%")
	}
	if recordType != "" {
		query = query.Where("record_type = ?", recordType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	var records []models.DomainRecord
	offset := (page - 1) * pageSize
	err = query.Limit(pageSize).Offset(offset).Order("gmt_create DESC").Find(&records).Error
	if err != nil {
		return nil, err
	}

	return &response.PageModel[models.DomainRecord]{
		List:     records,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// GetByDomainUUIDAndType gets domain record by domain UUID and record type
func (r *DomainRecordRepo) GetByDomainUUIDAndType(domainUUID, subdomain string, recordType models.DomainRecordType) (*models.DomainRecord, error) {
	var record models.DomainRecord
	err := r.db.Where("domain_uuid = ? AND subdomain = ? AND record_type = ? AND status != ?",
		domainUUID, subdomain, recordType, models.RecordStatusDeleted).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetByUUID gets domain record by UUID
func (r *DomainRecordRepo) GetByUUID(uuid string) (*models.DomainRecord, error) {
	var record models.DomainRecord
	err := r.db.Where("uuid = ?", uuid).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}
