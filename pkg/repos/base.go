package repos

import (
	"errors"
	"fmt"
	"pilot-api/internal/pkg/util"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	NumberHundred = 100
)

func IsErrRecordNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

type baseRepo[T any] struct {
	db *gorm.DB
}

func NewBaseRepo[T any](db *gorm.DB) baseRepo[T] {
	return baseRepo[T]{db: db}
}

func (r *baseRepo[T]) GetDB() *gorm.DB {
	return r.db
}

// WithTx 使用事务创建新的repo实例
func (r *baseRepo[T]) WithTx(tx *gorm.DB) baseRepo[T] {
	return baseRepo[T]{db: tx}
}

func (r *baseRepo[T]) GetByID(id int64, preloads ...string) (*T, error) {
	var result T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.First(&result, id).Error; err != nil {
		if IsErrRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r *baseRepo[T]) GetByIds(ids []int64, preloads ...string) (list []*T, err error) {
	if len(ids) == 0 {
		return nil, nil
	}
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	err = db.Unscoped().Where("id IN ?", ids).Find(&list).Error
	return
}

func (r *baseRepo[T]) GetListInColumn(cond *T, column string, values any, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if cond != nil {
		db = db.Where(cond)
	}

	where := fmt.Sprintf("%s IN ?", column)
	if err := db.Where(where, values).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetListInColumnAndOrder(cond *T, column string, values any, order string, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if cond != nil {
		db = db.Where(cond)
	}

	where := fmt.Sprintf("%s IN ?", column)
	if err := db.Where(where, values).Order(order).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetFirst(cond T, preloads ...string) (*T, error) {
	var result T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.First(&result, cond).Error; err != nil {
		if IsErrRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r *baseRepo[T]) GetOneWithOrder(cond T, order string, preloads ...string) (*T, error) {
	var result T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Order(order).First(&result, cond).Error; err != nil {
		if IsErrRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r *baseRepo[T]) GetOneInColumnAndOrder(cond *T, column string, values any, order string, preloads ...string) (*T, error) {
	var result T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if cond != nil {
		db = db.Where(cond)
	}

	where := fmt.Sprintf("%s IN ?", column)
	if err := db.Where(where, values).Order(order).First(&result).Error; err != nil {
		if IsErrRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

// GetList GetList
func (r *baseRepo[T]) GetList(cond T, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Find(&list, cond).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetListByWhere(where string, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Where(where).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// GetListWithOrder GetListWithOrder
func (r *baseRepo[T]) GetListWithOrder(cond T, order string, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Order(order).Find(&list, cond).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetPage(cond *T, order string, pageNo, pageSize int, preloads ...string) ([]*T, error) {
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}

	if cond != nil {
		db = db.Where(cond)
	}

	// set default value
	pageSize = util.Coalesce(pageSize, 10)
	pageNo = util.Coalesce(pageNo, 1)

	offset := (pageNo - 1) * pageSize
	db = db.Order(order).Offset(offset).Limit(pageSize)

	var list []*T
	if err := db.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// GetIdPage 按id分页查询
func (r *baseRepo[T]) GetIdPage(cond *T, columns []string, keyword string, id, pageSize int, isAsc bool, preloads ...string) ([]*T, error) {
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}

	if cond != nil {
		db = db.Where(cond)
	}

	if keyword != "" && len(columns) > 0 {
		like := "%" + keyword + "%"
		tx := r.db
		for _, column := range columns {
			query := fmt.Sprintf("%s like ?", column)
			tx = tx.Or(query, like)
		}
		db = db.Where(tx)
	}

	if isAsc {
		db = db.Where("id > ?", id).Order("id asc")
	} else {
		if id > 0 {
			db = db.Where("id < ?", id)
		}
		db = db.Order("id desc")
	}

	var list []*T
	if err := db.Limit(pageSize).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) Like(cond *T, columns []string, keyword, order string, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	if cond != nil {
		db = db.Where(cond)
	}
	for _, preload := range preloads {
		db = db.Preload(preload)
	}

	if keyword != "" && len(columns) > 0 {
		like := "%" + keyword + "%"
		tx := r.db
		for _, column := range columns {
			query := fmt.Sprintf("%s like ?", column)
			tx = tx.Or(query, like)
		}
		db = db.Where(tx)
	}

	if err := db.Order(order).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetAll(preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetAllWithOrder(order string, preloads ...string) ([]*T, error) {
	var list []*T
	db := r.db.Order(order)
	for _, preload := range preloads {
		db = db.Preload(preload)
	}
	if err := db.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r *baseRepo[T]) GetIds(cond T) ([]int64, error) {
	var ids []int64
	model := new(T)
	if err := r.db.Model(model).Where(cond).Pluck("id", &ids).Error; err != nil {
		return nil, err
	}
	return ids, nil
}

func (r *baseRepo[T]) Pluck(cond T, column string) ([]any, error) {
	var dest []any
	model := new(T)
	if err := r.db.Model(model).Where(cond).Pluck(column, &dest).Error; err != nil {
		return nil, err
	}
	return dest, nil
}

func (r *baseRepo[T]) Save(m *T, omitColumn ...string) error {
	db := r.db
	for _, column := range omitColumn {
		db = db.Omit(column)
	}
	if err := db.Save(m).Error; err != nil {
		return err
	}
	return nil
}

func (r *baseRepo[T]) SaveList(list []*T) error {
	if len(list) == 0 {
		return nil
	}
	if err := r.db.Save(list).Error; err != nil {
		return err
	}
	return nil
}

func (r *baseRepo[T]) Create(m *T, ignoreConflict ...bool) error {
	db := r.db
	if len(ignoreConflict) > 0 && ignoreConflict[0] {
		db = r.db.Clauses(clause.Insert{Modifier: "IGNORE"})
	}
	return db.Create(m).Error
}

func (r *baseRepo[T]) FirstOrCreate(m *T) error {
	return r.db.FirstOrCreate(m).Error
}

func (r *baseRepo[T]) CreateInBatches(list []*T, ignoreConflict ...bool) error {
	if len(list) == 0 {
		return nil
	}
	db := r.db
	if len(ignoreConflict) > 0 && ignoreConflict[0] {
		db = db.Clauses(clause.Insert{Modifier: "IGNORE"})
	}
	err := db.CreateInBatches(list, NumberHundred).Error
	return err
}

func (r *baseRepo[T]) CreateOrUpdate(m *T, column ...string) error {
	var conflictColumns clause.OnConflict
	if len(column) == 0 {
		conflictColumns.UpdateAll = true
	} else {
		conflictColumns.DoUpdates = clause.AssignmentColumns(column)
	}
	err := r.db.Clauses(conflictColumns).Create(&m).Error
	return err
}

func (r *baseRepo[T]) Updates(updateInfo any) error {
	return r.db.Updates(updateInfo).Error
}

func (r *baseRepo[T]) UpdateById(column string, value any, id ...int64) error {
	if len(id) == 0 {
		return nil
	}

	m := new(T)
	return r.db.Model(m).Where("id", id).Update(column, value).Error
}

func (r *baseRepo[T]) UpdatesById(updateInfo any, id ...int64) error {
	if len(id) == 0 {
		return nil
	}

	m := new(T)
	return r.db.Model(m).Where("id", id).Updates(updateInfo).Error
}

func (r *baseRepo[T]) UpdateByCond(cond T, column string, value any) error {
	m := new(T)
	return r.db.Model(m).Where(cond).Update(column, value).Error
}

func (r *baseRepo[T]) UpdatesByCond(cond T, updateInfo any) error {
	m := new(T)
	return r.db.Model(m).Where(cond).Updates(updateInfo).Error
}

func (r *baseRepo[T]) DeleteByID(id int64, force ...bool) error {
	m := new(T)
	session := r.db.Model(m)
	if len(force) > 0 && force[0] {
		session = session.Unscoped()
	}
	return session.Delete(m, id).Error
}

// DeleteBatch DeleteBatch
func (r *baseRepo[T]) DeleteBatch(ids []int64, force ...bool) error {
	if len(ids) == 0 {
		return nil
	}
	m := new(T)
	session := r.db.Where("id IN ?", ids)
	if len(force) > 0 && force[0] {
		session = session.Unscoped()
	}
	return session.Delete(m).Error
}

func (r *baseRepo[T]) DeleteWith(cond *T, force ...bool) error {
	m := new(T)
	session := r.db.Where(cond)
	if len(force) > 0 && force[0] {
		session = session.Unscoped()
	}
	return session.Delete(m).Error
}

func (r *baseRepo[T]) DeleteInColumn(cond *T, column string, values any, force ...bool) error {
	if values == nil {
		return nil
	}

	m := new(T)
	where := fmt.Sprintf("%s IN ?", column)
	session := r.db.Where(where, values)
	if cond != nil {
		session = session.Where(cond)
	}

	if len(force) > 0 && force[0] {
		session = session.Unscoped()
	}
	return session.Delete(m).Error
}

func (r *baseRepo[T]) Count(cond ...*T) (int64, error) {
	var num int64
	m := new(T)
	session := r.db.Model(m)
	if len(cond) != 0 {
		session = session.Where(cond[0])
	}
	err := session.Count(&num).Error
	return num, err
}
