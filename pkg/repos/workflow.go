package repos

import (
	"pilot-api/pkg/models"

	"gorm.io/gorm"
)

type WorkflowRepo struct {
	baseRepo[models.Workflow]
}

func NewWorkflowRepo(db *gorm.DB) *WorkflowRepo {
	return &WorkflowRepo{
		baseRepo: NewBaseRepo[models.Workflow](db),
	}
}

// WorkflowExecutionRepo 工作流执行repo
type WorkflowExecutionRepo struct {
	baseRepo[models.WorkflowExecution]
}

func NewWorkflowExecutionRepo(db *gorm.DB) *WorkflowExecutionRepo {
	return &WorkflowExecutionRepo{
		baseRepo: NewBaseRepo[models.WorkflowExecution](db),
	}
}

// WithTx 使用事务创建新的WorkflowExecutionRepo实例
func (r *WorkflowExecutionRepo) WithTx(tx *gorm.DB) *WorkflowExecutionRepo {
	return &WorkflowExecutionRepo{
		baseRepo: r.baseRepo.WithTx(tx),
	}
}

// StepExecutionRepo 步骤执行repo
type StepExecutionRepo struct {
	baseRepo[models.StepExecution]
}

func NewStepExecutionRepo(db *gorm.DB) *StepExecutionRepo {
	return &StepExecutionRepo{
		baseRepo: NewBaseRepo[models.StepExecution](db),
	}
}

// WithTx 使用事务创建新的StepExecutionRepo实例
func (r *StepExecutionRepo) WithTx(tx *gorm.DB) *StepExecutionRepo {
	return &StepExecutionRepo{
		baseRepo: r.baseRepo.WithTx(tx),
	}
}
