# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.1.0":
  version "2.1.2"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.0"

"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@ant-design%2fcolors/-/colors-6.0.0.tgz"
  integrity sha1-m5NmJXz/zEfbQrnQIDu1ksE8Apg=
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/icons-svg@^4.2.1":
  version "4.2.1"

"@ant-design/icons-vue@^6.1.0":
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@ant-design%2ficons-vue/-/icons-vue-6.1.0.tgz"
  integrity sha512-EX6bYm56V+ZrKN7+3MT/ubDkvJ5rK/O2t380WFRflDcVFgsvl3NLH7Wxeau6R8DbrO5jWR6DSTC3B6gYFp77AA==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.2.1"

"@ant-design/icons-vue@^7.0.0":
  version "7.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@ant-design/icons-vue/-/icons-vue-7.0.1.tgz"
  integrity sha512-eCqY2unfZK6Fe02AwFlDHLfoyEFreP6rBwAZMIJ1LugmfMiVgwWDYlp1YsRugaPtICYOabV1iWxXdP12u9U43Q==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.2.1"

"@antfu/utils@^0.5.2":
  version "0.5.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@antfu%2futils/-/utils-0.5.2.tgz"
  integrity sha512-CQkeV+oJxUazwjlHD0/3ZD08QWKuGQkhnrKo3e6ly5pd48VUpXbb77q0xMU4+vc2CkJnDS02Eq/M9ugyX20XZA==

"@azure/abort-controller@^1.0.0", "@azure/abort-controller@^1.1.0":
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fabort-controller/-/abort-controller-1.1.0.tgz"
  integrity sha512-TrRLIoSQVzfAJX9H1JeFjzAoDGcoK1IYX1UImfceTZpsyYfWr09Ss1aHW1y5TrrR3iq6RZLBwJ3E24uwPhwahw==
  dependencies:
    tslib "^2.2.0"

"@azure/abort-controller@^2.0.0":
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fabort-controller/-/abort-controller-2.0.0.tgz"
  integrity sha512-RP/mR/WJchR+g+nQFJGOec+nzeN/VvjlwbinccoqfhTsTHbb8X5+mLDp48kHT0ueyum0BNSwGm0kX0UZuIqTGg==
  dependencies:
    tslib "^2.2.0"

"@azure/core-auth@^1.3.0":
  version "1.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fcore-auth/-/core-auth-1.6.0.tgz"
  integrity sha512-3X9wzaaGgRaBCwhLQZDtFp5uLIXCPrGbwJNWPPugvL4xbIGgScv77YzzxToKGLAKvG9amDoofMoP+9hsH1vs1w==
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-util" "^1.1.0"
    tslib "^2.2.0"

"@azure/core-http@^3.0.0":
  version "3.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fcore-http/-/core-http-3.0.4.tgz"
  integrity sha512-Fok9VVhMdxAFOtqiiAtg74fL0UJkt0z3D+ouUUxcRLzZNBioPRAMJFVxiWoJljYpXsRi4GDQHzQHDc9AiYaIUQ==
  dependencies:
    "@azure/abort-controller" "^1.0.0"
    "@azure/core-auth" "^1.3.0"
    "@azure/core-tracing" "1.0.0-preview.13"
    "@azure/core-util" "^1.1.1"
    "@azure/logger" "^1.0.0"
    "@types/node-fetch" "^2.5.0"
    "@types/tunnel" "^0.0.3"
    form-data "^4.0.0"
    node-fetch "^2.6.7"
    process "^0.11.10"
    tslib "^2.2.0"
    tunnel "^0.0.6"
    uuid "^8.3.0"
    xml2js "^0.5.0"

"@azure/core-lro@^2.2.0":
  version "2.2.4"
  dependencies:
    "@azure/abort-controller" "^1.0.0"
    "@azure/core-tracing" "1.0.0-preview.13"
    "@azure/logger" "^1.0.0"
    tslib "^2.2.0"

"@azure/core-paging@^1.1.1":
  version "1.3.0"
  dependencies:
    tslib "^2.2.0"

"@azure/core-tracing@1.0.0-preview.13":
  version "1.0.0-preview.13"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fcore-tracing/-/core-tracing-1.0.0-preview.13.tgz"
  integrity sha512-KxDlhXyMlh2Jhj2ykX6vNEU0Vou4nHr025KoSEiz7cS3BNiHNaZcdECk/DmLkEB0as5T7b/TpRcehJ5yV6NeXQ==
  dependencies:
    "@opentelemetry/api" "^1.0.1"
    tslib "^2.2.0"

"@azure/core-util@^1.1.0", "@azure/core-util@^1.1.1":
  version "1.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fcore-util/-/core-util-1.7.0.tgz"
  integrity sha512-Zq2i3QO6k9DA8vnm29mYM4G8IE9u1mhF1GUabVEqPNX8Lj833gdxQ2NAFxt2BZsfAL+e9cT8SyVN7dFVJ/Hf0g==
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    tslib "^2.2.0"

"@azure/logger@^1.0.0":
  version "1.0.3"
  dependencies:
    tslib "^2.2.0"

"@azure/storage-blob@12.17.0":
  version "12.17.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@azure%2fstorage-blob/-/storage-blob-12.17.0.tgz"
  integrity sha512-sM4vpsCpcCApagRW5UIjQNlNylo02my2opgp0Emi8x888hZUvJ3dN69Oq20cEGXkMUWnoCrBaB0zyS3yeB87sQ==
  dependencies:
    "@azure/abort-controller" "^1.0.0"
    "@azure/core-http" "^3.0.0"
    "@azure/core-lro" "^2.2.0"
    "@azure/core-paging" "^1.1.1"
    "@azure/core-tracing" "1.0.0-preview.13"
    "@azure/logger" "^1.0.0"
    events "^3.0.0"
    tslib "^2.2.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.8", "@babel/compat-data@^7.17.0", "@babel/compat-data@^7.17.7":
  version "7.17.7"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0", "@babel/core@^7.12.0", "@babel/core@^7.12.3", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0", "@babel/core@^7.7.2", "@babel/core@^7.8.0", "@babel/core@>=7.0.0-beta.0 <8", "@babel/core@7.x":
  version "7.17.9"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.17.9"
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-module-transforms" "^7.17.7"
    "@babel/helpers" "^7.17.9"
    "@babel/parser" "^7.17.9"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.9"
    "@babel/types" "^7.17.0"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/generator@^7.17.9", "@babel/generator@^7.7.2":
  version "7.17.9"
  dependencies:
    "@babel/types" "^7.17.0"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7", "@babel/helper-compilation-targets@^7.17.7":
  version "7.17.7"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-validator-option" "^7.16.7"
    browserslist "^4.17.5"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.10", "@babel/helper-create-class-features-plugin@^7.16.7", "@babel/helper-create-class-features-plugin@^7.17.6":
  version "7.17.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.17.9"
    "@babel/helper-member-expression-to-functions" "^7.17.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7":
  version "7.17.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    regexpu-core "^5.0.1"

"@babel/helper-define-polyfill-provider@^0.3.1":
  version "0.3.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7", "@babel/helper-function-name@^7.17.9":
  version "7.17.9"
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.17.0"

"@babel/helper-hoist-variables@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7", "@babel/helper-member-expression-to-functions@^7.17.7":
  version "7.17.7"
  dependencies:
    "@babel/types" "^7.17.0"

"@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.16.7", "@babel/helper-module-transforms@^7.17.7":
  version "7.17.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.17.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.3"
    "@babel/types" "^7.17.0"

"@babel/helper-optimise-call-expression@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.20.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fhelper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz"
  integrity sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==

"@babel/helper-remap-async-to-generator@^7.16.8":
  version "7.16.8"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helper-replace-supers@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.17.7":
  version "7.17.7"
  dependencies:
    "@babel/types" "^7.17.0"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  version "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"

"@babel/helper-validator-option@^7.16.7":
  version "7.16.7"

"@babel/helper-wrap-function@^7.16.8":
  version "7.16.8"
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helpers@^7.17.9":
  version "7.17.9"
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.9"
    "@babel/types" "^7.17.0"

"@babel/highlight@^7.16.7":
  version "7.17.9"
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.15.8", "@babel/parser@^7.16.7", "@babel/parser@^7.17.9", "@babel/parser@^7.20.15", "@babel/parser@^7.21.3":
  version "7.22.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fparser/-/parser-7.22.5.tgz"
  integrity sha512-DFZMC9LJUG9PLOclRC32G63UXwzqS2koQC8dkx+PLdmt1xSePYpbT/NbsrJy8Q/muXz7o/h/d4A7Fuyixm559Q==

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"

"@babel/plugin-proposal-async-generator-functions@^7.16.8":
  version "7.16.8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-class-static-block@^7.16.7":
  version "7.17.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.6"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.7":
  version "7.17.3"
  dependencies:
    "@babel/compat-data" "^7.17.0"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.7"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.11":
  version "7.16.11"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.10"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-private-property-in-object@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.7", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13", "@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5", "@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@babel%2fplugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.16.7", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-arrow-functions@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-async-to-generator@^7.16.8":
  version "7.16.8"
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-classes@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-destructuring@^7.16.7":
  version "7.17.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-for-of@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-function-name@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.8", "@babel/plugin-transform-modules-commonjs@^7.2.0":
  version "7.17.9"
  dependencies:
    "@babel/helper-module-transforms" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-simple-access" "^7.17.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.7":
  version "7.17.8"
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.8":
  version "7.16.8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"

"@babel/plugin-transform-new-target@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-object-super@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-property-literals@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.16.7":
  version "7.17.9"
  dependencies:
    regenerator-transform "^0.15.0"

"@babel/plugin-transform-reserved-words@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typeof-symbol@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typescript@^7.16.7":
  version "7.16.8"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-typescript" "^7.16.7"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@^7.16.7":
  version "7.16.11"
  dependencies:
    "@babel/compat-data" "^7.16.8"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.7"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.8"
    "@babel/plugin-proposal-class-properties" "^7.16.7"
    "@babel/plugin-proposal-class-static-block" "^7.16.7"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.7"
    "@babel/plugin-proposal-json-strings" "^7.16.7"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.7"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.7"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-private-methods" "^7.16.11"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.7"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.7"
    "@babel/plugin-transform-async-to-generator" "^7.16.8"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.16.7"
    "@babel/plugin-transform-classes" "^7.16.7"
    "@babel/plugin-transform-computed-properties" "^7.16.7"
    "@babel/plugin-transform-destructuring" "^7.16.7"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.16.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.16.7"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.16.7"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.16.7"
    "@babel/plugin-transform-modules-commonjs" "^7.16.8"
    "@babel/plugin-transform-modules-systemjs" "^7.16.7"
    "@babel/plugin-transform-modules-umd" "^7.16.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.8"
    "@babel/plugin-transform-new-target" "^7.16.7"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.16.7"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.16.7"
    "@babel/plugin-transform-reserved-words" "^7.16.7"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.16.7"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.16.7"
    "@babel/plugin-transform-typeof-symbol" "^7.16.7"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.8"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.5.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    core-js-compat "^3.20.2"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-typescript@^7.16.7":
  version "7.16.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-typescript" "^7.16.7"

"@babel/runtime@^7.10.5", "@babel/runtime@^7.12.5", "@babel/runtime@^7.13.9", "@babel/runtime@^7.15.4", "@babel/runtime@^7.8.4":
  version "7.17.9"
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.16.7", "@babel/template@^7.3.3":
  version "7.16.7"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.15.4", "@babel/traverse@^7.16.7", "@babel/traverse@^7.16.8", "@babel/traverse@^7.17.3", "@babel/traverse@^7.17.9", "@babel/traverse@^7.7.2":
  version "7.17.9"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.17.9"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.17.9"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.17.9"
    "@babel/types" "^7.17.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.16.8", "@babel/types@^7.17.0", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.4.4":
  version "7.17.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@bcoe%2fv8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@codemirror/autocomplete@^6.0.0", "@codemirror/autocomplete@^6.3.2":
  version "6.8.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2fautocomplete/-/autocomplete-6.8.0.tgz"
  integrity sha512-nTimZYnTYaZ5skAt+zlk8BD41GvjpWgtDni2K+BritA7Ed9A0aJWwo1ohTvwUEfHfhIVtcFSLEddVPkegw8C/Q==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.6.0"
    "@lezer/common" "^1.0.0"

"@codemirror/commands@^6.0.0":
  version "6.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2fcommands/-/commands-6.1.2.tgz"
  integrity sha512-sO3jdX1s0pam6lIdeSJLMN3DQ6mPEbM4yLvyKkdqtmd/UDwhXA5+AwFJ89rRXm6vTeOXBsE5cAmlos/t7MJdgg==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"

"@codemirror/lang-angular@^0.1.0":
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-angular/-/lang-angular-0.1.1.tgz"
  integrity sha512-DVDTwL8rMqzPmBJ5XnstdEMTroiniPCzfP2rp65d39XKohPVSxGA55zC26YiyQtTVNFezTiOOSPTm11oIui5dg==
  dependencies:
    "@codemirror/lang-html" "^6.0.0"
    "@codemirror/lang-javascript" "^6.1.2"
    "@codemirror/language" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.3.3"

"@codemirror/lang-cpp@^6.0.0":
  version "6.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-cpp/-/lang-cpp-6.0.2.tgz"
  integrity sha512-6oYEYUKHvrnacXxWxYa6t4puTlbN3dgV662BDfSH8+MfjQjVmP697/KYTDOqpxgerkvoNm7q5wlFMBeX8ZMocg==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@lezer/cpp" "^1.0.0"

"@codemirror/lang-css@^6.0.0", "@codemirror/lang-css@^6.2.0":
  version "6.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-css/-/lang-css-6.2.0.tgz"
  integrity sha512-oyIdJM29AyRPM3+PPq1I2oIk8NpUfEN3kAM05XWDDs6o3gSneIKaVJifT2P+fqONLou2uIgXynFyMUDQvo/szA==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@lezer/common" "^1.0.2"
    "@lezer/css" "^1.0.0"

"@codemirror/lang-html@^6.0.0":
  version "6.4.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-html/-/lang-html-6.4.4.tgz"
  integrity sha512-NbrqEp0GUOSvhZbG6BxVcS4SzM4SvN5vkkD2sEoETHIyHLZDb9pO1z+r1L2heb6LuF4bUeBCXKjHXoSeDJHO1w==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/lang-css" "^6.0.0"
    "@codemirror/lang-javascript" "^6.0.0"
    "@codemirror/language" "^6.4.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.2.2"
    "@lezer/common" "^1.0.0"
    "@lezer/css" "^1.1.0"
    "@lezer/html" "^1.3.0"

"@codemirror/lang-java@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-java/-/lang-java-6.0.1.tgz"
  integrity sha512-OOnmhH67h97jHzCuFaIEspbmsT98fNdhVhmA3zCxW0cn7l8rChDhZtwiwJ/JOKXgfm4J+ELxQihxaI7bj7mJRg==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@lezer/java" "^1.0.0"

"@codemirror/lang-javascript@^6.0.0", "@codemirror/lang-javascript@^6.1.2":
  version "6.1.9"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-javascript/-/lang-javascript-6.1.9.tgz"
  integrity sha512-z3jdkcqOEBT2txn2a87A0jSy6Te3679wg/U8QzMeftFt+4KA6QooMwfdFzJiuC3L6fXKfTXZcDocoaxMYfGz0w==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/language" "^6.6.0"
    "@codemirror/lint" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/javascript" "^1.0.0"

"@codemirror/lang-json@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-json/-/lang-json-6.0.1.tgz"
  integrity sha512-+T1flHdgpqDDlJZ2Lkil/rLiRy684WMLc74xUnjJH48GQdfJo/pudlTRreZmKwzP8/tGdKf83wlbAdOCzlJOGQ==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@lezer/json" "^1.0.0"

"@codemirror/lang-less@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-less/-/lang-less-6.0.1.tgz"
  integrity sha512-ABcsKBjLbyPZwPR5gePpc8jEKCQrFF4pby2WlMVdmJOOr7OWwwyz8DZonPx/cKDE00hfoSLc8F7yAcn/d6+rTQ==
  dependencies:
    "@codemirror/lang-css" "^6.2.0"
    "@codemirror/language" "^6.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@codemirror/lang-markdown@^6.0.0", "@codemirror/lang-markdown@^6.1.1":
  version "6.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-markdown/-/lang-markdown-6.1.1.tgz"
  integrity sha512-n87Ms6Y5UYb1UkFu8sRzTLfq/yyF1y2AYiWvaVdbBQi5WDj1tFk5N+AKA+WC0Jcjc1VxvrCCM0iizjdYYi9sFQ==
  dependencies:
    "@codemirror/lang-html" "^6.0.0"
    "@codemirror/language" "^6.3.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/markdown" "^1.0.0"

"@codemirror/lang-php@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-php/-/lang-php-6.0.1.tgz"
  integrity sha512-ublojMdw/PNWa7qdN5TMsjmqkNuTBD3k6ndZ4Z0S25SBAiweFGyY68AS3xNcIOlb6DDFDvKlinLQ40vSLqf8xA==
  dependencies:
    "@codemirror/lang-html" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/php" "^1.0.0"

"@codemirror/lang-python@^6.0.0":
  version "6.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-python/-/lang-python-6.1.3.tgz"
  integrity sha512-S9w2Jl74hFlD5nqtUMIaXAq9t5WlM0acCkyuQWUUSvZclk1sV+UfnpFiZzuZSG+hfEaOmxKR5UxY/Uxswn7EhQ==
  dependencies:
    "@codemirror/autocomplete" "^6.3.2"
    "@codemirror/language" "^6.8.0"
    "@lezer/python" "^1.1.4"

"@codemirror/lang-rust@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-rust/-/lang-rust-6.0.1.tgz"
  integrity sha512-344EMWFBzWArHWdZn/NcgkwMvZIWUR1GEBdwG8FEp++6o6vT6KL9V7vGs2ONsKxxFUPXKI0SPcWhyYyl2zPYxQ==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@lezer/rust" "^1.0.0"

"@codemirror/lang-sass@^6.0.0":
  version "6.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-sass/-/lang-sass-6.0.2.tgz"
  integrity sha512-l/bdzIABvnTo1nzdY6U+kPAC51czYQcOErfzQ9zSm9D8GmNPD0WTW8st/CJwBTPLO8jlrbyvlSEcN20dc4iL0Q==
  dependencies:
    "@codemirror/lang-css" "^6.2.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@lezer/common" "^1.0.2"
    "@lezer/sass" "^1.0.0"

"@codemirror/lang-sql@^6.0.0":
  version "6.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-sql/-/lang-sql-6.5.0.tgz"
  integrity sha512-ztJ+5lk0yWf4E7sQQqsidPYJa0a/511Ln/IaI3A+fGv6z0SrGDG0Lu6SAehczcehrhgNwMhPlerJMeXw7vZs2g==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@codemirror/lang-vue@^0.1.1":
  version "0.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-vue/-/lang-vue-0.1.2.tgz"
  integrity sha512-D4YrefiRBAr+CfEIM4S3yvGSbYW+N69mttIfGMEf7diHpRbmygDxS+R/5xSqjgtkY6VO6qmUrre1GkRcWeZa9A==
  dependencies:
    "@codemirror/lang-html" "^6.0.0"
    "@codemirror/lang-javascript" "^6.1.2"
    "@codemirror/language" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.3.1"

"@codemirror/lang-wast@^6.0.0":
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-wast/-/lang-wast-6.0.1.tgz"
  integrity sha512-sQLsqhRjl2MWG3rxZysX+2XAyed48KhLBHLgq9xcKxIJu3npH/G+BIXW5NM5mHeDUjG0jcGh9BcjP0NfMStuzA==
  dependencies:
    "@codemirror/language" "^6.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@codemirror/lang-xml@^6.0.0":
  version "6.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flang-xml/-/lang-xml-6.0.2.tgz"
  integrity sha512-JQYZjHL2LAfpiZI2/qZ/qzDuSqmGKMwyApYmEUUCTxLM4MWS7sATUEfIguZQr9Zjx/7gcdnewb039smF6nC2zw==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/language" "^6.4.0"
    "@codemirror/state" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/xml" "^1.0.0"

"@codemirror/language-data@^6.3.0":
  version "6.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flanguage-data/-/language-data-6.3.1.tgz"
  integrity sha512-p6jhJmvhGe1TG1EGNhwH7nFWWFSTJ8NDKnB2fVx5g3t+PpO0+63R7GJNxjS0TmmH3cdMxZbzejsik+rlEh1EyQ==
  dependencies:
    "@codemirror/lang-angular" "^0.1.0"
    "@codemirror/lang-cpp" "^6.0.0"
    "@codemirror/lang-css" "^6.0.0"
    "@codemirror/lang-html" "^6.0.0"
    "@codemirror/lang-java" "^6.0.0"
    "@codemirror/lang-javascript" "^6.0.0"
    "@codemirror/lang-json" "^6.0.0"
    "@codemirror/lang-less" "^6.0.0"
    "@codemirror/lang-markdown" "^6.0.0"
    "@codemirror/lang-php" "^6.0.0"
    "@codemirror/lang-python" "^6.0.0"
    "@codemirror/lang-rust" "^6.0.0"
    "@codemirror/lang-sass" "^6.0.0"
    "@codemirror/lang-sql" "^6.0.0"
    "@codemirror/lang-vue" "^0.1.1"
    "@codemirror/lang-wast" "^6.0.0"
    "@codemirror/lang-xml" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/legacy-modes" "^6.1.0"

"@codemirror/language@^6.0.0", "@codemirror/language@^6.3.0", "@codemirror/language@^6.4.0", "@codemirror/language@^6.6.0", "@codemirror/language@^6.8.0":
  version "6.8.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flanguage/-/language-6.8.0.tgz"
  integrity sha512-r1paAyWOZkfY0RaYEZj3Kul+MiQTEbDvYqf8gPGaRvNneHXCmfSaAVFjwRUPlgxS8yflMxw2CTu6uCMp8R8A2g==
  dependencies:
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"
    style-mod "^4.0.0"

"@codemirror/legacy-modes@^6.1.0":
  version "6.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flegacy-modes/-/legacy-modes-6.3.2.tgz"
  integrity sha512-ki5sqNKWzKi5AKvpVE6Cna4Q+SgxYuYVLAZFSsMjGBWx5qSVa+D+xipix65GS3f2syTfAD9pXKMX4i4p49eneQ==
  dependencies:
    "@codemirror/language" "^6.0.0"

"@codemirror/lint@^6.0.0":
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2flint/-/lint-6.0.0.tgz"
  integrity sha512-nUUXcJW1Xp54kNs+a1ToPLK8MadO0rMTnJB8Zk4Z8gBdrN0kqV7uvUraU/T2yqg+grDNR38Vmy/MrhQN/RgwiA==
  dependencies:
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    crelt "^1.0.5"

"@codemirror/search@^6.0.0":
  version "6.5.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2fsearch/-/search-6.5.5.tgz"
  integrity sha512-PIEN3Ke1buPod2EHbJsoQwlbpkz30qGZKcnmH1eihq9+bPQx8gelauUwLYaY4vBOuBAuEhmpDLii4rj/uO0yMA==
  dependencies:
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"
    crelt "^1.0.5"

"@codemirror/state@^6.0.0", "@codemirror/state@^6.1.4":
  version "6.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2fstate/-/state-6.2.1.tgz"
  integrity sha512-RupHSZ8+OjNT38zU9fKH2sv+Dnlr8Eb8sl4NOnnqz95mCFTZUaiRP8Xv5MeeaG0px2b8Bnfe7YGwCV3nsBhbuw==

"@codemirror/view@^6.0.0", "@codemirror/view@^6.2.2", "@codemirror/view@^6.6.0":
  version "6.13.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@codemirror%2fview/-/view-6.13.2.tgz"
  integrity sha512-XA/jUuu1H+eTja49654QkrQwx2CuDMdjciHcdqyasfTVo4HRlvj87rD/Qmm4HfnhwX8234FQSSA8HxEzxihX/Q==
  dependencies:
    "@codemirror/state" "^6.1.4"
    style-mod "^4.0.0"
    w3c-keyname "^2.2.4"

"@commitlint/cli@^16.0.2":
  version "16.2.3"
  dependencies:
    "@commitlint/format" "^16.2.1"
    "@commitlint/lint" "^16.2.1"
    "@commitlint/load" "^16.2.3"
    "@commitlint/read" "^16.2.1"
    "@commitlint/types" "^16.2.1"
    lodash "^4.17.19"
    resolve-from "5.0.0"
    resolve-global "1.0.0"
    yargs "^17.0.0"

"@commitlint/config-conventional@^16.0.0":
  version "16.2.1"
  dependencies:
    conventional-changelog-conventionalcommits "^4.3.1"

"@commitlint/config-validator@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fconfig-validator/-/config-validator-16.2.1.tgz"
  integrity sha512-hogSe0WGg7CKmp4IfNbdNES3Rq3UEI4XRPB8JL4EPgo/ORq5nrGTVzxJh78omibNuB8Ho4501Czb1Er1MoDWpw==
  dependencies:
    "@commitlint/types" "^16.2.1"
    ajv "^6.12.6"

"@commitlint/ensure@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fensure/-/ensure-16.2.1.tgz"
  integrity sha512-/h+lBTgf1r5fhbDNHOViLuej38i3rZqTQnBTk+xEg+ehOwQDXUuissQ5GsYXXqI5uGy+261ew++sT4EA3uBJ+A==
  dependencies:
    "@commitlint/types" "^16.2.1"
    lodash "^4.17.19"

"@commitlint/execute-rule@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fexecute-rule/-/execute-rule-16.2.1.tgz"
  integrity sha512-oSls82fmUTLM6cl5V3epdVo4gHhbmBFvCvQGHBRdQ50H/690Uq1Dyd7hXMuKITCIdcnr9umyDkr8r5C6HZDF3g==

"@commitlint/format@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fformat/-/format-16.2.1.tgz"
  integrity sha512-Yyio9bdHWmNDRlEJrxHKglamIk3d6hC0NkEUW6Ti6ipEh2g0BAhy8Od6t4vLhdZRa1I2n+gY13foy+tUgk0i1Q==
  dependencies:
    "@commitlint/types" "^16.2.1"
    chalk "^4.0.0"

"@commitlint/is-ignored@^16.2.1":
  version "16.2.1"
  dependencies:
    "@commitlint/types" "^16.2.1"
    semver "7.3.5"

"@commitlint/lint@^16.2.1":
  version "16.2.1"
  dependencies:
    "@commitlint/is-ignored" "^16.2.1"
    "@commitlint/parse" "^16.2.1"
    "@commitlint/rules" "^16.2.1"
    "@commitlint/types" "^16.2.1"

"@commitlint/load@^16.2.3":
  version "16.2.3"
  dependencies:
    "@commitlint/config-validator" "^16.2.1"
    "@commitlint/execute-rule" "^16.2.1"
    "@commitlint/resolve-extends" "^16.2.1"
    "@commitlint/types" "^16.2.1"
    "@types/node" ">=12"
    chalk "^4.0.0"
    cosmiconfig "^7.0.0"
    cosmiconfig-typescript-loader "^1.0.0"
    lodash "^4.17.19"
    resolve-from "^5.0.0"
    typescript "^4.4.3"

"@commitlint/message@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fmessage/-/message-16.2.1.tgz"
  integrity sha512-2eWX/47rftViYg7a3axYDdrgwKv32mxbycBJT6OQY/MJM7SUfYNYYvbMFOQFaA4xIVZt7t2Alyqslbl6blVwWw==

"@commitlint/parse@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fparse/-/parse-16.2.1.tgz"
  integrity sha512-2NP2dDQNL378VZYioLrgGVZhWdnJO4nAxQl5LXwYb08nEcN+cgxHN1dJV8OLJ5uxlGJtDeR8UZZ1mnQ1gSAD/g==
  dependencies:
    "@commitlint/types" "^16.2.1"
    conventional-changelog-angular "^5.0.11"
    conventional-commits-parser "^3.2.2"

"@commitlint/read@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fread/-/read-16.2.1.tgz"
  integrity sha512-tViXGuaxLTrw2r7PiYMQOFA2fueZxnnt0lkOWqKyxT+n2XdEMGYcI9ID5ndJKXnfPGPppD0w/IItKsIXlZ+alw==
  dependencies:
    "@commitlint/top-level" "^16.2.1"
    "@commitlint/types" "^16.2.1"
    fs-extra "^10.0.0"
    git-raw-commits "^2.0.0"

"@commitlint/resolve-extends@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fresolve-extends/-/resolve-extends-16.2.1.tgz"
  integrity sha512-NbbCMPKTFf2J805kwfP9EO+vV+XvnaHRcBy6ud5dF35dxMsvdJqke54W3XazXF1ZAxC4a3LBy4i/GNVBAthsEg==
  dependencies:
    "@commitlint/config-validator" "^16.2.1"
    "@commitlint/types" "^16.2.1"
    import-fresh "^3.0.0"
    lodash "^4.17.19"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"

"@commitlint/rules@^16.2.1":
  version "16.2.1"
  dependencies:
    "@commitlint/ensure" "^16.2.1"
    "@commitlint/message" "^16.2.1"
    "@commitlint/to-lines" "^16.2.1"
    "@commitlint/types" "^16.2.1"
    execa "^5.0.0"

"@commitlint/to-lines@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2fto-lines/-/to-lines-16.2.1.tgz"
  integrity sha512-9/VjpYj5j1QeY3eiog1zQWY6axsdWAc0AonUUfyZ7B0MVcRI0R56YsHAfzF6uK/g/WwPZaoe4Lb1QCyDVnpVaQ==

"@commitlint/top-level@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2ftop-level/-/top-level-16.2.1.tgz"
  integrity sha512-lS6GSieHW9y6ePL73ied71Z9bOKyK+Ib9hTkRsB8oZFAyQZcyRwq2w6nIa6Fngir1QW51oKzzaXfJL94qwImyw==
  dependencies:
    find-up "^5.0.0"

"@commitlint/types@^16.2.1":
  version "16.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@commitlint%2ftypes/-/types-16.2.1.tgz"
  integrity sha512-7/z7pA7BM0i8XvMSBynO7xsB3mVQPUZbVn6zMIlp/a091XJ3qAXRXc+HwLYhiIdzzS5fuxxNIHZMGHVD4HJxdA==
  dependencies:
    chalk "^4.0.0"

"@cspotcode/source-map-consumer@0.8.0":
  version "0.8.0"

"@cspotcode/source-map-support@0.7.0":
  version "0.7.0"
  dependencies:
    "@cspotcode/source-map-consumer" "0.8.0"

"@csstools/selector-specificity@^2.0.2":
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@csstools%2fselector-specificity/-/selector-specificity-2.0.2.tgz"
  integrity sha512-IkpVW/ehM1hWKln4fCA3NzJU8KwD+kIOvPZA4cqxoJHtE21CCzjyp+Kxbu0i5I4tBNOlXPL9mjwnWlL0VEG4Fg==

"@ctrl/tinycolor@^3.4.0", "@ctrl/tinycolor@^3.5.0":
  version "3.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@emotion/hash@^0.9.0":
  version "0.9.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==

"@emotion/unitless@^0.8.0":
  version "0.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@emotion/unitless/-/unitless-0.8.1.tgz"
  integrity sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==

"@eslint/eslintrc@^1.2.1":
  version "1.2.1"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.3.1"
    globals "^13.9.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@humanwhocodes/config-array@^0.9.2":
  version "0.9.5"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.1":
  version "1.2.1"

"@hutson/parse-repository-url@^3.0.0":
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@hutson%2fparse-repository-url/-/parse-repository-url-3.0.2.tgz"
  integrity sha1-mMI8lQo9m2yPDa7QbabDrwaYE0A=

"@iconify/iconify@>=3.1.1":
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@iconify%2ficonify/-/iconify-3.1.1.tgz"
  integrity sha512-1nemfyD/OJzh9ALepH7YfuuP8BdEB24Skhd8DXWh0hzcOxImbb1ZizSZkpCzAwSZSGcJFmscIBaBQu+yLyWaxQ==
  dependencies:
    "@iconify/types" "^2.0.0"

"@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@iconify%2ftypes/-/types-2.0.0.tgz"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@istanbuljs%2fload-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@istanbuljs%2fschema/-/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2fconsole/-/console-27.5.1.tgz"
  integrity sha512-kZ/tNpS3NXn0mlXXXPNuDZnb4c0oZ20r4K5eemM2k30ZC3G0T02nXUvyhf5YdbXWHPEJLc9qGLxEZ216MdL+Zg==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^27.5.1"
    jest-util "^27.5.1"
    slash "^3.0.0"

"@jest/core@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2fcore/-/core-27.5.1.tgz"
  integrity sha512-AK6/UTrvQD0Cd24NSqmIA6rKsu0tKIxfiCducZvqxYdmMisOYAsdItspT+fQDQYARPf8XgjAFZi0ogW2agH5nQ==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/reporters" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.8.1"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^27.5.1"
    jest-config "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-message-util "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-resolve-dependencies "^27.5.1"
    jest-runner "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    jest-watcher "^27.5.1"
    micromatch "^4.0.4"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2fenvironment/-/environment-27.5.1.tgz"
  integrity sha512-/WQjhPJe3/ghaol/4Bq480JKXV/Rfw8nQdN7f41fM8VDHLcxKXou6QyXAh3EFr9/bVG3x74z1NWDkP87EiY8gA==
  dependencies:
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"

"@jest/fake-timers@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2ffake-timers/-/fake-timers-27.5.1.tgz"
  integrity sha512-/aPowoolwa07k7/oM3aASneNeBGCmGQsc3ugN4u6s4C/+s5M64MFo/+djTdiwcbQlRfFElGuDXWzaWj6QgKObQ==
  dependencies:
    "@jest/types" "^27.5.1"
    "@sinonjs/fake-timers" "^8.0.1"
    "@types/node" "*"
    jest-message-util "^27.5.1"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"

"@jest/globals@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2fglobals/-/globals-27.5.1.tgz"
  integrity sha512-ZEJNB41OBQQgGzgyInAv0UUfDDj3upmHydjieSxFvTRuZElrx7tXg/uVQ5hYVEwiXs3+aMsAeEc9X7xiSKCm4Q==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/types" "^27.5.1"
    expect "^27.5.1"

"@jest/reporters@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2freporters/-/reporters-27.5.1.tgz"
  integrity sha512-cPXh9hWIlVJMQkVk84aIvXuBB4uQQmFqZiacloFuGiP3ah1sbCxCosidXFDfqG8+6fO1oR2dTJTlsOy4VFmUfw==
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-haste-map "^27.5.1"
    jest-resolve "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^8.1.0"

"@jest/source-map@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2fsource-map/-/source-map-27.5.1.tgz"
  integrity sha512-y9NIHUYF3PJRlHk98NdC/N1gl88BL08aQQgu4k4ZopQkCw9t9cV8mtl3TV8b/YCB8XaVTFrmUTAJvjsntDireg==
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.9"
    source-map "^0.6.0"

"@jest/test-result@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2ftest-result/-/test-result-27.5.1.tgz"
  integrity sha512-EW35l2RYFUcUQxFJz5Cv5MTOxlJIQs4I7gxzi2zVU7PJhOwfYq1MdC5nhSmYjX1gmMmLPvB3sIaC+BkcHRBfag==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2ftest-sequencer/-/test-sequencer-27.5.1.tgz"
  integrity sha512-LCheJF7WB2+9JuCS7VB/EmGIdQuhtqjRNI9A43idHv3E4KltCTsPsLxvdaubFHSYwY/fNjMWjl6vNRhDiN7vpQ==
  dependencies:
    "@jest/test-result" "^27.5.1"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-runtime "^27.5.1"

"@jest/transform@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2ftransform/-/transform-27.5.1.tgz"
  integrity sha512-ipON6WtYgl/1329g5AIJVbUuEh0wZVbdpGwC99Jw4LwuoBNS95MVphU6zOeD9pDkon+LLbFL7lOQRapbB8SCHw==
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^27.5.1"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-util "^27.5.1"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^27.5.1":
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@jest%2ftypes/-/types-27.5.1.tgz"
  integrity sha512-Cx46iJ9QpwQTjIdq5VJu2QTMMs3QlEjI0x1QbBP5W1+nMzyc2XmimiRR/CbX9TO0cPTeUlxWMOu8mslYsJ8DEw==
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@jridgewell%2fresolve-uri/-/resolve-uri-3.1.0.tgz"
  integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==

"@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "http://repository.makeblock.com/repository/npm-group/@jridgewell%2fsourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==

"@jridgewell/trace-mapping@^0.3.0":
  version "0.3.17"
  resolved "http://repository.makeblock.com/repository/npm-group/@jridgewell%2ftrace-mapping/-/trace-mapping-0.3.17.tgz"
  integrity sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@lezer/common@^1.0.0", "@lezer/common@^1.0.2":
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fcommon/-/common-1.0.3.tgz"
  integrity sha512-JH4wAXCgUOcCGNekQPLhVeUtIqjH0yPBs7vvUdSjyQama9618IOKFJwkv2kcqdhF0my8hQEgCTEJU0GIgnahvA==

"@lezer/cpp@^1.0.0":
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fcpp/-/cpp-1.1.0.tgz"
  integrity sha512-zUHrjNFuY/DOZCkOBJ6qItQIkcopHM/Zv/QOE0a4XNG3HDNahxTNu5fQYl8dIuKCpxCqRdMl5cEwl5zekFc7BA==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/css@^1.0.0", "@lezer/css@^1.1.0":
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fcss/-/css-1.1.2.tgz"
  integrity sha512-5TKMAReXukfEmIiZprDlGfZVfOOCyEStFi1YLzxclm9H3G/HHI49/2wzlRT6bQw5r7PoZVEtjTItEkb/UuZQyg==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/highlight@^1.0.0", "@lezer/highlight@^1.1.3":
  version "1.1.6"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fhighlight/-/highlight-1.1.6.tgz"
  integrity sha512-cmSJYa2us+r3SePpRCjN5ymCqCPv+zyXmDl0ciWtVaNiORT/MxM7ZgOMQZADD0o51qOaOg24qc/zBViOIwAjJg==
  dependencies:
    "@lezer/common" "^1.0.0"

"@lezer/html@^1.3.0":
  version "1.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fhtml/-/html-1.3.4.tgz"
  integrity sha512-HdJYMVZcT4YsMo7lW3ipL4NoyS2T67kMPuSVS5TgLGqmaCjEU/D6xv7zsa1ktvTK5lwk7zzF1e3eU6gBZIPm5g==
  dependencies:
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/java@^1.0.0":
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fjava/-/java-1.0.3.tgz"
  integrity sha512-kKN17wmgP1cgHb8juR4pwVSPMKkDMzY/lAPbBsZ1fpXwbk2sg3N1kIrf0q+LefxgrANaQb/eNO7+m2QPruTFng==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/javascript@^1.0.0":
  version "1.4.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fjavascript/-/javascript-1.4.3.tgz"
  integrity sha512-k7Eo9z9B1supZ5cCD4ilQv/RZVN30eUQL+gGbr6ybrEY3avBAL5MDiYi2aa23Aj0A79ry4rJRvPAwE2TM8bd+A==
  dependencies:
    "@lezer/highlight" "^1.1.3"
    "@lezer/lr" "^1.3.0"

"@lezer/json@^1.0.0":
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fjson/-/json-1.0.0.tgz"
  integrity sha512-zbAuUY09RBzCoCA3lJ1+ypKw5WSNvLqGMtasdW6HvVOqZoCpPr8eWrsGnOVWGKGn8Rh21FnrKRVlJXrGAVUqRw==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/lr@^1.0.0", "@lezer/lr@^1.1.0", "@lezer/lr@^1.3.0", "@lezer/lr@^1.3.1", "@lezer/lr@^1.3.3":
  version "1.3.6"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2flr/-/lr-1.3.6.tgz"
  integrity sha512-IDhcWjfxwWACnatUi0GzWBCbochfqxo3LZZlS27LbJh8RVYYXXyR5Ck9659IhkWkhSW/kZlaaiJpUO+YZTUK+Q==
  dependencies:
    "@lezer/common" "^1.0.0"

"@lezer/markdown@^1.0.0":
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fmarkdown/-/markdown-1.0.2.tgz"
  integrity sha512-8CY0OoZ6V5EzPjSPeJ4KLVbtXdLBd8V6sRCooN5kHnO28ytreEGTyrtU/zUwo/XLRzGr/e1g44KlzKi3yWGB5A==
  dependencies:
    "@lezer/common" "^1.0.0"
    "@lezer/highlight" "^1.0.0"

"@lezer/php@^1.0.0":
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fphp/-/php-1.0.1.tgz"
  integrity sha512-aqdCQJOXJ66De22vzdwnuC502hIaG9EnPK2rSi+ebXyUd+j7GAX1mRjWZOVOmf3GST1YUfUCu6WXDiEgDGOVwA==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.1.0"

"@lezer/python@^1.1.4":
  version "1.1.7"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fpython/-/python-1.1.7.tgz"
  integrity sha512-RbhKQ9+Y/r/Xv6OcJmETEM5tBFdpdAJRqrgi3akJkWBLCuiAaLP/jKdYzu+ICljaSXPCQeznrv+r9HUEnjq3HQ==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/rust@^1.0.0":
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2frust/-/rust-1.0.0.tgz"
  integrity sha512-IpGAxIjNxYmX9ra6GfQTSPegdCAWNeq23WNmrsMMQI7YNSvKtYxO4TX5rgZUmbhEucWn0KTBMeDEPXg99YKtTA==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/sass@^1.0.0":
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fsass/-/sass-1.0.1.tgz"
  integrity sha512-S/aYAzABzMqWLfKKqV89pCWME4yjZYC6xzD02l44wbmb0sHxmN9/8aE4GULrKFzFaGazHdXcGEbPZ4zzB6yqwQ==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@lezer/xml@^1.0.0":
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@lezer%2fxml/-/xml-1.0.1.tgz"
  integrity sha512-jMDXrV953sDAUEMI25VNrI9dz94Ai96FfeglytFINhhwQ867HKlCE2jt3AwZTCT7M528WxdDWv/Ty8e9wizwmQ==
  dependencies:
    "@lezer/highlight" "^1.0.0"
    "@lezer/lr" "^1.0.0"

"@makeblock/broswer-storage@^0.2.0":
  version "0.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@makeblock%2fbroswer-storage/-/broswer-storage-0.2.2.tgz"
  integrity sha1-9vq3BDwHRbZ4S5Qm1ej2YSp8L0k=
  dependencies:
    "@types/js-cookie" "^2.2.5"
    js-cookie "^2.2.1"

"@makeblock/canvas-grid@1.0.18":
  version "1.0.18"
  resolved "http://repository.makeblock.com/repository/npm-group/@makeblock%2fcanvas-grid/-/canvas-grid-1.0.18.tgz"
  integrity sha512-4i/hYCs3qRS8OZNoZlXPqVy/QI2bKIuU7bWtquFP8dQPx8D59mjZwgzeMyjlwCIVJJVSTe03cUf91tGIWMZr5Q==
  dependencies:
    eventemitter3 "^4.0.7"
    lodash "^4.17.21"

"@makeblock/passport-client@^2.2.0":
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@makeblock%2fpassport-client/-/passport-client-2.2.0.tgz"
  integrity sha512-cXtiMlc8+fDC7GkBHRtKeWMgM+WkYNfJES3rWezbUx0gLEsLYK1CVUGO3CvU07Zr7+P+eu678elGvxdEDrQrPg==
  dependencies:
    "@makeblock/broswer-storage" "^0.2.0"
    axios "^1.5.0"
    standard-version "^9.5.0"

"@makeblock/upload@^0.11.2":
  version "0.11.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@makeblock/upload/-/upload-0.11.2.tgz"
  integrity sha512-g8TzaETyVRaLofVasb2qD/UY9Aeek4JzQgeCYxK9vzmmS7HllNmuyMLab5mdQOIT10dsoSrpg9U95MPtdFtpwA==
  dependencies:
    "@azure/abort-controller" "^1.1.0"
    "@azure/storage-blob" "12.17.0"
    ali-oss "^6.17.1"
    axios "^0.19.2"
    mime "^3.0.0"
    spark-md5 "^3.0.2"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@nodelib%2ffs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@nodelib%2ffs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://repository.makeblock.com/repository/npm-group/@nodelib%2ffs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@opentelemetry/api@^1.0.1":
  version "1.1.0"

"@originjs/vite-plugin-commonjs@^1.0.3":
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@originjs%2fvite-plugin-commonjs/-/vite-plugin-commonjs-1.0.3.tgz"
  integrity sha512-KuEXeGPptM2lyxdIEJ4R11+5ztipHoE7hy8ClZt3PYaOVQ/pyngd2alaSrPnwyFeOW1UagRBaQ752aA1dTMdOQ==
  dependencies:
    esbuild "^0.14.14"

"@purge-icons/generated@^0.10.0":
  version "0.10.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@purge-icons%2fgenerated/-/generated-0.10.0.tgz"
  integrity sha512-I+1yN7/yDy/eZzfhAZqKF8Z6FM8D/O1vempbPrHJ0m9HlZwvf8sWXOArPJ2qRQGB6mJUVSpaXkoGBuoz1GQX5A==
  dependencies:
    "@iconify/iconify" ">=3.1.1"

"@rollup/pluginutils@^4.2.1":
  version "4.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@rollup%2fpluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@simonwep/pickr@~1.8.0":
  version "1.8.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@simonwep%2fpickr/-/pickr-1.8.2.tgz"
  integrity sha1-ltyGZ1lA18rWPWnCIIPdHLuXl8s=
  dependencies:
    core-js "^3.15.1"
    nanopop "^2.1.0"

"@sinonjs/commons@^1.7.0":
  version "1.8.6"
  resolved "http://repository.makeblock.com/repository/npm-group/@sinonjs%2fcommons/-/commons-1.8.6.tgz"
  integrity sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^8.0.1":
  version "8.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@sinonjs%2ffake-timers/-/fake-timers-8.1.0.tgz"
  integrity sha1-P9wrbLWJNbIb+40WJesTAEhDFuc=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@sphinxxxx/color-conversion@^2.2.2":
  version "2.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@sphinxxxx%2fcolor-conversion/-/color-conversion-2.2.2.tgz"
  integrity sha512-XExJS3cLqgrmNBIP3bBw6+1oQ1ksGjFh0+oClDKFYpCCqx/hlqwWO5KO/S63fzUo67SxI9dMrF0y5T/Ey7h8Zw==

"@stagewise-plugins/vue@^0.6.2":
  version "0.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@stagewise-plugins/vue/-/vue-0.6.2.tgz"
  integrity sha512-QaBDRtL1fdMz/l3skcHTU7SwKe39hJx+LfmwhsMs+94xu9rg531jTiVVp9iLxrcEQQX2BzvpLQAOJcdGoyyacQ==

"@stagewise/toolbar-vue@^0.6.2":
  version "0.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@stagewise/toolbar-vue/-/toolbar-vue-0.6.2.tgz"
  integrity sha512-dmEQEWn3hXB6NogoQmBmkyPzaFs72Xvh/abrIvmYSjLze9kILOt7ijaKV/ENqnq6GfPGfXTPKqGKESpBecyFVQ==
  dependencies:
    "@stagewise/toolbar" "0.6.2"

"@stagewise/toolbar@0.6.2":
  version "0.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@stagewise/toolbar/-/toolbar-0.6.2.tgz"
  integrity sha512-WN7PWaOT6YQKjJYL4/85V5UU0eZEws+/UBT/J4wJOEbFxoluLuchqh7xVTmUZTtw0q0xpzlgX8Vb0kAZf/pjmw==

"@testing-library/dom@^8.5.0":
  version "8.13.0"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^4.2.0"
    aria-query "^5.0.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.4.4"
    pretty-format "^27.0.2"

"@testing-library/vue@^6.4.2":
  version "6.5.1"
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@testing-library/dom" "^8.5.0"
    "@vue/test-utils" "^2.0.0-rc.18"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@tootallnate%2fonce/-/once-1.1.2.tgz"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@trysound%2fsax/-/sax-0.2.0.tgz"
  integrity sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=

"@tsconfig/node10@^1.0.7":
  version "1.0.8"

"@tsconfig/node12@^1.0.7":
  version "1.0.9"

"@tsconfig/node14@^1.0.0":
  version "1.0.1"

"@tsconfig/node16@^1.0.2":
  version "1.0.2"

"@types/ali-oss@^6.16.3":
  version "6.16.3"

"@types/aria-query@^4.2.0":
  version "4.2.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.12", "@types/babel__core@^7.1.14":
  version "7.1.19"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.4"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.14.2"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/diff@^7.0.2":
  version "7.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@types/diff/-/diff-7.0.2.tgz"
  integrity sha512-JSWRMozjFKsGlEjiiKajUjIJVKuKdE3oVy2DNtK+fUo8q82nhFZ2CPQwicAIkXrofahDXrWJ7mjelvZphMS98Q==

"@types/graceful-fs@^4.1.2":
  version "4.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fgraceful-fs/-/graceful-fs-4.1.5.tgz"
  integrity sha1-If+6DZjaQ1DbZIkfkqnl2zzbThU=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fistanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz"
  integrity sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fistanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fistanbul-reports/-/istanbul-reports-3.0.1.tgz"
  integrity sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^27.0.0", "@types/jest@^27.5.2":
  version "27.5.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fjest/-/jest-27.5.2.tgz"
  integrity sha512-mpT8LJJ4CMeeahobofYWIjFo0xonRS/HfxnVEPMPFSQdGUt1uHCnoPT7Zhb+sjDU2wz0oKV0OLUR0WzrHNgfeA==
  dependencies:
    jest-matcher-utils "^27.0.0"
    pretty-format "^27.0.0"

"@types/js-cookie@^2.2.5":
  version "2.2.7"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fjs-cookie/-/js-cookie-2.2.7.tgz"
  integrity sha1-ImqeMWgINaYYjoh/OYjmDATT9qM=

"@types/js-cookie@^3.0.6":
  version "3.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fjs-cookie/-/js-cookie-3.0.6.tgz"
  integrity sha512-wkw9yd1kEXOPnvEeEV1Go1MmxtBJL0RR79aOTAApecWFVu7w0NNXNqhcWgvw2YgZDYadliXkl14pa3WXw5jlCQ==

"@types/js-yaml@^4.0.9":
  version "4.0.9"
  resolved "http://repository.makeblock.com/repository/npm-group/@types/js-yaml/-/js-yaml-4.0.9.tgz"
  integrity sha512-k4MGaQl5TGo/iipqb2UDG2UwjXziSWkh0uysQelTlJpX1qGlpUZYm8PnO4DxG1qBomtJUdYJ6qR6xdIah10JLg==

"@types/json-schema@^7.0.9":
  version "7.0.11"

"@types/linkify-it@*":
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2flinkify-it/-/linkify-it-3.0.2.tgz"
  integrity sha1-/SzS7bqn6qx+fzwXSLUqGRQ4Rsk=

"@types/lodash-es@^4.17.12":
  version "4.17.12"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2flodash-es/-/lodash-es-4.17.12.tgz"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.190":
  version "4.14.190"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2flodash/-/lodash-4.14.190.tgz"
  integrity sha512-5iJ3FBJBvQHQ8sFhEhJfjUP+G+LalhavTkYyrAYqz5MEJG+erSv0k9KJLb6q7++17Lafk1scaTIFXcMJlwK8Mw==

"@types/markdown-it@^12.2.3":
  version "12.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fmarkdown-it/-/markdown-it-12.2.3.tgz"
  integrity sha1-DW9uXkE/jaqiZSKQRZe+PWzZO1E=
  dependencies:
    "@types/linkify-it" "*"
    "@types/mdurl" "*"

"@types/marked@^4.0.3":
  version "4.0.3"

"@types/mdurl@*":
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fmdurl/-/mdurl-1.0.2.tgz"
  integrity sha1-4s6dg6YTus8oTHvn1JGUXjnh+Ok=

"@types/minimist@^1.2.0":
  version "1.2.2"

"@types/node-fetch@^2.5.0":
  version "2.6.11"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fnode-fetch/-/node-fetch-2.6.11.tgz"
  integrity sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*", "@types/node@^20.14.2", "@types/node@>=12":
  version "20.14.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@types/node/-/node-20.14.2.tgz"
  integrity sha512-xyu6WAMVwv6AKFLB+e/7ySZVr/0zLCzOa7rSpq6jNwpqOrUbcACDWC+53d4n2QHOnDou0fbIsg8wZu/sxrnI4Q==
  dependencies:
    undici-types "~5.26.4"

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"

"@types/nprogress@^0.2.3":
  version "0.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@types/nprogress/-/nprogress-0.2.3.tgz"
  integrity sha512-k7kRA033QNtC+gLc4VPlfnue58CM1iQLgn1IMAU8VPHGOj7oIHPp9UlhedEnD/Gl8evoCjwkZjlBORtZ3JByUA==

"@types/parse-json@^4.0.0":
  version "4.0.0"

"@types/prettier@^2.1.5":
  version "2.7.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fprettier/-/prettier-2.7.1.tgz"
  integrity sha512-ri0UmynRRvZiiUJdiz38MmIblKK+oH30MztdBVR95dv/Ubw6neWSb8u1XpRb72L4qsZOhz+L+z9JD40SJmfWow==

"@types/spark-md5@^3.0.2":
  version "3.0.2"

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fstack-utils/-/stack-utils-2.0.1.tgz"
  integrity sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=

"@types/strip-bom@^3.0.0":
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fstrip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-FKjsOVbC6B7bdSB5CuzyHCkK69I=

"@types/strip-json-comments@0.0.30":
  version "0.0.30"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fstrip-json-comments/-/strip-json-comments-0.0.30.tgz"
  integrity sha1-mqMMBNshKpoGSdaub9UKzMQHSKE=

"@types/svgo@^2.6.1":
  version "2.6.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fsvgo/-/svgo-2.6.4.tgz"
  integrity sha512-l4cmyPEckf8moNYHdJ+4wkHvFxjyW6ulm9l4YGaOxeyBWPhBOT0gvni1InpFPdzx1dKf/2s62qGITwxNWnPQng==
  dependencies:
    "@types/node" "*"

"@types/tunnel@^0.0.3":
  version "0.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2ftunnel/-/tunnel-0.0.3.tgz"
  integrity sha1-8QnnMLBysxNjR1YfxVjJNYu4xuk=
  dependencies:
    "@types/node" "*"

"@types/uuid@^9.0.0":
  version "9.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fuuid/-/uuid-9.0.0.tgz"
  integrity sha512-kr90f+ERiQtKWMz5rP32ltJ/BtULDI5RVO0uavn1HQUOwjx0R1h0rnDYNL0CepF1zL5bSY6FISAfd9tOdDhU5Q==

"@types/web-bluetooth@^0.0.20":
  version "0.0.20"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fweb-bluetooth/-/web-bluetooth-0.0.20.tgz"
  integrity sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fyargs-parser/-/yargs-parser-21.0.0.tgz"
  integrity sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==

"@types/yargs@^16.0.0":
  version "16.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@types%2fyargs/-/yargs-16.0.4.tgz"
  integrity sha1-JqrZjdLCo45CEIbqmtQrnlFkKXc=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.9.1":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.19.0"
    "@typescript-eslint/type-utils" "5.19.0"
    "@typescript-eslint/utils" "5.19.0"
    debug "^4.3.2"
    functional-red-black-tree "^1.0.1"
    ignore "^5.1.8"
    regexpp "^3.2.0"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.9.1":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.19.0"
    "@typescript-eslint/types" "5.19.0"
    "@typescript-eslint/typescript-estree" "5.19.0"
    debug "^4.3.2"

"@typescript-eslint/scope-manager@5.19.0":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/types" "5.19.0"
    "@typescript-eslint/visitor-keys" "5.19.0"

"@typescript-eslint/type-utils@5.19.0":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/utils" "5.19.0"
    debug "^4.3.2"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.19.0":
  version "5.19.0"

"@typescript-eslint/typescript-estree@5.19.0":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/types" "5.19.0"
    "@typescript-eslint/visitor-keys" "5.19.0"
    debug "^4.3.2"
    globby "^11.0.4"
    is-glob "^4.0.3"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.19.0":
  version "5.19.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.19.0"
    "@typescript-eslint/types" "5.19.0"
    "@typescript-eslint/typescript-estree" "5.19.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/visitor-keys@5.19.0":
  version "5.19.0"
  dependencies:
    "@typescript-eslint/types" "5.19.0"
    eslint-visitor-keys "^3.0.0"

"@vavt/util@^1.1.1":
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/@vavt%2futil/-/util-1.1.1.tgz"
  integrity sha512-0x+Acegd8k6lFJc7SN4kVhO669fBiMtBjq/WqX2C8Uz5mSgS+nRvfXs0BK2aiV67T0rauBoAmEkQfURQjI2+4Q==

"@vitejs/plugin-vue@^2.3.3":
  version "2.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vitejs%2fplugin-vue/-/plugin-vue-2.3.4.tgz"
  integrity sha512-IfFNbtkbIm36O9KB8QodlwwYvTEsJb4Lll4c2IwB3VHc2gie2mSPtSzL0eYay7X2jd/2WX02FjSGTWR6OPr/zg==

"@volar/code-gen@0.34.17":
  version "0.34.17"
  resolved "http://repository.makeblock.com/repository/npm-group/@volar%2fcode-gen/-/code-gen-0.34.17.tgz"
  integrity sha512-rHR7BA71BJ/4S7xUOPMPiB7uk6iU9oTWpEMZxFi5VGC9iJmDncE82WzU5iYpcbOBCVHsOjMh0+5CGMgdO6SaPA==
  dependencies:
    "@volar/source-map" "0.34.17"

"@volar/source-map@0.34.17":
  version "0.34.17"
  resolved "http://repository.makeblock.com/repository/npm-group/@volar%2fsource-map/-/source-map-0.34.17.tgz"
  integrity sha512-3yn1IMXJGGWB/G817/VFlFMi8oh5pmE7VzUqvgMZMrppaZpKj6/juvJIEiXNxRsgWc0RxIO8OSp4htdPUg1Raw==

"@volar/vue-code-gen@0.34.17":
  version "0.34.17"
  resolved "http://repository.makeblock.com/repository/npm-group/@volar%2fvue-code-gen/-/vue-code-gen-0.34.17.tgz"
  integrity sha512-17pzcK29fyFWUc+C82J3JYSnA+jy3QNrIldb9kPaP9Itbik05ZjEIyEue9FjhgIAuHeYSn4LDM5s6nGjxyfhsQ==
  dependencies:
    "@volar/code-gen" "0.34.17"
    "@volar/source-map" "0.34.17"
    "@vue/compiler-core" "^3.2.36"
    "@vue/compiler-dom" "^3.2.36"
    "@vue/shared" "^3.2.36"

"@volar/vue-typescript@0.34.17":
  version "0.34.17"
  resolved "http://repository.makeblock.com/repository/npm-group/@volar%2fvue-typescript/-/vue-typescript-0.34.17.tgz"
  integrity sha512-U0YSVIBPRWVPmgJHNa4nrfq88+oS+tmyZNxmnfajIw9A/GOGZQiKXHC0k09SVvbYXlsjgJ6NIjhm9NuAhGRQjg==
  dependencies:
    "@volar/code-gen" "0.34.17"
    "@volar/source-map" "0.34.17"
    "@volar/vue-code-gen" "0.34.17"
    "@vue/compiler-sfc" "^3.2.36"
    "@vue/reactivity" "^3.2.36"

"@vue/compiler-core@^3.2.36", "@vue/compiler-core@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fcompiler-core/-/compiler-core-3.3.4.tgz"
  integrity sha512-cquyDNvZ6jTbf/+x+AgM2Arrp6G4Dzbb0R64jiG804HRMfRiFXWI6kqUVqZ6ZR0bQhIoQjB4+2bhNtVwndW15g==
  dependencies:
    "@babel/parser" "^7.21.3"
    "@vue/shared" "3.3.4"
    estree-walker "^2.0.2"
    source-map-js "^1.0.2"

"@vue/compiler-dom@^3.2.36", "@vue/compiler-dom@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fcompiler-dom/-/compiler-dom-3.3.4.tgz"
  integrity sha512-wyM+OjOVpuUukIq6p5+nwHYtj9cFroz9cwkfmP9O1nzH68BenTTv0u7/ndggT8cIQlnBeOo6sUT/gvHcIkLA5w==
  dependencies:
    "@vue/compiler-core" "3.3.4"
    "@vue/shared" "3.3.4"

"@vue/compiler-sfc@^3.2.36", "@vue/compiler-sfc@>= 3", "@vue/compiler-sfc@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fcompiler-sfc/-/compiler-sfc-3.3.4.tgz"
  integrity sha512-6y/d8uw+5TkCuzBkgLS0v3lSM3hJDntFEiUORM11pQ/hKvkhSKZrXW6i69UyXlJQisJxuUEJKAWEqWbWsLeNKQ==
  dependencies:
    "@babel/parser" "^7.20.15"
    "@vue/compiler-core" "3.3.4"
    "@vue/compiler-dom" "3.3.4"
    "@vue/compiler-ssr" "3.3.4"
    "@vue/reactivity-transform" "3.3.4"
    "@vue/shared" "3.3.4"
    estree-walker "^2.0.2"
    magic-string "^0.30.0"
    postcss "^8.1.10"
    source-map-js "^1.0.2"

"@vue/compiler-ssr@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fcompiler-ssr/-/compiler-ssr-3.3.4.tgz"
  integrity sha512-m0v6oKpup2nMSehwA6Uuu+j+wEwcy7QmwMkVNVfrV9P2qE5KshC6RwOCq8fjGS/Eak/uNb8AaWekfiXxbBB6gQ==
  dependencies:
    "@vue/compiler-dom" "3.3.4"
    "@vue/shared" "3.3.4"

"@vue/devtools-api@^6.0.0", "@vue/devtools-api@^6.0.0-beta.11":
  version "6.1.4"

"@vue/reactivity-transform@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2freactivity-transform/-/reactivity-transform-3.3.4.tgz"
  integrity sha512-MXgwjako4nu5WFLAjpBnCj/ieqcjE2aJBINUNQzkZQfzIZA4xn+0fV1tIYBJvvva3N3OvKGofRLvQIwEQPpaXw==
  dependencies:
    "@babel/parser" "^7.20.15"
    "@vue/compiler-core" "3.3.4"
    "@vue/shared" "3.3.4"
    estree-walker "^2.0.2"
    magic-string "^0.30.0"

"@vue/reactivity@^3.2.36", "@vue/reactivity@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2freactivity/-/reactivity-3.3.4.tgz"
  integrity sha512-kLTDLwd0B1jG08NBF3R5rqULtv/f8x3rOFByTDz4J53ttIQEDmALqKqXY0J+XQeN0aV2FBxY8nJDf88yvOPAqQ==
  dependencies:
    "@vue/shared" "3.3.4"

"@vue/runtime-core@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fruntime-core/-/runtime-core-3.3.4.tgz"
  integrity sha512-R+bqxMN6pWO7zGI4OMlmvePOdP2c93GsHFM/siJI7O2nxFRzj55pLwkpCedEY+bTMgp5miZ8CxfIZo3S+gFqvA==
  dependencies:
    "@vue/reactivity" "3.3.4"
    "@vue/shared" "3.3.4"

"@vue/runtime-dom@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fruntime-dom/-/runtime-dom-3.3.4.tgz"
  integrity sha512-Aj5bTJ3u5sFsUckRghsNjVTtxZQ1OyMWCr5dZRAPijF/0Vy4xEoRCwLyHXcj4D0UFbJ4lbx3gPTgg06K/GnPnQ==
  dependencies:
    "@vue/runtime-core" "3.3.4"
    "@vue/shared" "3.3.4"
    csstype "^3.1.1"

"@vue/server-renderer@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fserver-renderer/-/server-renderer-3.3.4.tgz"
  integrity sha512-Q6jDDzR23ViIb67v+vM1Dqntu+HUexQcsWKhhQa4ARVzxOY2HbC7QRW/ggkDBd5BU+uM1sV6XOAP0b216o34JQ==
  dependencies:
    "@vue/compiler-ssr" "3.3.4"
    "@vue/shared" "3.3.4"

"@vue/shared@^3.2.36", "@vue/shared@3.3.4":
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fshared/-/shared-3.3.4.tgz"
  integrity sha512-7OjdcV8vQ74eiz1TZLzZP4JwqM5fA94K6yntPS5Z25r9HDuGNzaGdgvwKYq6S+MxwF0TFRwe50fIR/MYnakdkQ==

"@vue/test-utils@^2.0.0-rc.18":
  version "2.2.6"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2ftest-utils/-/test-utils-2.2.6.tgz"
  integrity sha512-64zHtJZdG7V/U2L0j/z3Pt5bSygccI3xs+Kl7LB73AZK4MQ8WONJhqDQPK8leUFFA9CmmoJygeky7zcl2hX10A==

"@vue/vue3-jest@^27.0.0-alpha.4":
  version "27.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@vue%2fvue3-jest/-/vue3-jest-27.0.0.tgz"
  integrity sha512-VL61CgZBoQqayXfzlZJHHpZuX4lsT8dmdZMJzADhdAJjKu26JBpypHr/2ppevxItljPiuALQW4MKhhCXZRXnLg==
  dependencies:
    "@babel/plugin-transform-modules-commonjs" "^7.2.0"
    chalk "^2.1.0"
    convert-source-map "^1.6.0"
    css-tree "^2.0.1"
    source-map "0.5.6"
    tsconfig "^7.0.0"

"@vueup/vue-quill@^1.0.0":
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/@vueup%2fvue-quill/-/vue-quill-1.0.0.tgz"
  integrity sha512-WWdas2LxyHIk8yz41i8sRpGb/qM0jJ+ymo7vOMcyCs0ZW1k/hcl8n/KAUfxY48VAeixyqTGiOZypb7OISzOKpQ==
  dependencies:
    quill "^1.3.7"
    quill-delta "^4.2.2"

"@vueuse/core@^10.7.1":
  version "10.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@vueuse%2fcore/-/core-10.7.2.tgz"
  integrity sha512-AOyAL2rK0By62Hm+iqQn6Rbu8bfmbgaIMXcE3TSr7BdQ42wnSFlwIdPjInO62onYsEMK/yDMU8C6oGfDAtZ2qQ==
  dependencies:
    "@types/web-bluetooth" "^0.0.20"
    "@vueuse/metadata" "10.7.2"
    "@vueuse/shared" "10.7.2"
    vue-demi ">=0.14.6"

"@vueuse/metadata@10.7.2":
  version "10.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@vueuse%2fmetadata/-/metadata-10.7.2.tgz"
  integrity sha512-kCWPb4J2KGrwLtn1eJwaJD742u1k5h6v/St5wFe8Quih90+k2a0JP8BS4Zp34XUuJqS2AxFYMb1wjUL8HfhWsQ==

"@vueuse/shared@10.7.2":
  version "10.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/@vueuse%2fshared/-/shared-10.7.2.tgz"
  integrity sha512-qFbXoxS44pi2FkgFjPvF4h7c9oMDutpyBdcJdMYIMg9XyXli2meFMuaKn+UMgsClo//Th6+beeCgqweT/79BVA==
  dependencies:
    vue-demi ">=0.14.6"

abab@^2.0.3, abab@^2.0.5:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/abab/-/abab-2.0.6.tgz"
  integrity sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==

ace-builds@^1.4.14:
  version "1.5.0"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/acorn-globals/-/acorn-globals-6.0.0.tgz"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.3.1:
  version "5.3.2"

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/acorn-walk/-/acorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn-walk@^8.1.1, acorn-walk@^8.2.0:
  version "8.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/acorn-walk/-/acorn-walk-8.2.0.tgz"
  integrity sha1-dBIQ8uJCZFRQiFOi9E0KuDt/acE=

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.2.4, acorn@^8.4.1, acorn@^8.7.0, acorn@^8.7.1:
  version "8.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/acorn/-/acorn-8.8.1.tgz"
  integrity sha512-7zFpHzhnqYKrkYdUjF1HI1bzd0VygEGX8lFk4k5zVMqHEoES+P+7TKI+EvLO9WVMJ8eekdO0aDEK044xTXwPPA==

acorn@^7.1.1:
  version "7.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/acorn/-/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

add-stream@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/add-stream/-/add-stream-1.0.0.tgz"
  integrity sha1-anmQQ3ynNtXhKI25K9MmbV9csqo=

address@^1.0.0, address@>=0.0.1:
  version "1.2.0"

agent-base@^6.0.0, agent-base@^6.0.2, agent-base@6:
  version "6.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

agentkeepalive@^3.4.1:
  version "3.5.2"
  resolved "http://repository.makeblock.com/repository/npm-group/agentkeepalive/-/agentkeepalive-3.5.2.tgz"
  integrity sha1-oROSTdP6JKC8O3gQjEUMKr7gD2c=
  dependencies:
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.6:
  version "6.12.6"
  resolved "http://repository.makeblock.com/repository/npm-group/ajv/-/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.11.2"
  resolved "http://repository.makeblock.com/repository/npm-group/ajv/-/ajv-8.11.2.tgz"
  integrity sha512-E4bfmKAhGiSTvMfL1Myyycaub+cUEU2/IvpylXkUu7CHBkBj1f/ikdzbD7YQ6FKUbixDxeYvB/xY4fvyroDlQg==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ali-oss@^6.17.1:
  version "6.17.1"
  dependencies:
    address "^1.0.0"
    agentkeepalive "^3.4.1"
    bowser "^1.6.0"
    copy-to "^2.0.1"
    dateformat "^2.0.0"
    debug "^2.2.0"
    destroy "^1.0.4"
    end-or-error "^1.0.1"
    get-ready "^1.0.0"
    humanize-ms "^1.2.0"
    is-type-of "^1.0.0"
    js-base64 "^2.5.2"
    jstoxml "^2.0.0"
    merge-descriptors "^1.0.1"
    mime "^2.4.5"
    mz-modules "^2.1.0"
    platform "^1.3.1"
    pump "^3.0.0"
    sdk-base "^2.0.1"
    stream-http "2.8.2"
    stream-wormhole "^1.0.4"
    urllib "^2.33.1"
    utility "^1.8.0"
    xml2js "^0.4.16"

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-regex/-/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-regex/-/ansi-regex-6.0.1.tgz"
  integrity sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-styles/-/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.0.0:
  version "6.1.0"

ant-design-vue@^4.2.6:
  version "4.2.6"
  resolved "http://repository.makeblock.com/repository/npm-group/ant-design-vue/-/ant-design-vue-4.2.6.tgz"
  integrity sha512-t7eX13Yj3i9+i5g9lqFyYneoIb3OzTvQjq9Tts1i+eiOd3Eva/6GagxBSXM1fOCjqemIu0FYVE1ByZ/38epR3Q==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-vue" "^7.0.0"
    "@babel/runtime" "^7.10.5"
    "@ctrl/tinycolor" "^3.5.0"
    "@emotion/hash" "^0.9.0"
    "@emotion/unitless" "^0.8.0"
    "@simonwep/pickr" "~1.8.0"
    array-tree-filter "^2.1.0"
    async-validator "^4.0.0"
    csstype "^3.1.1"
    dayjs "^1.10.5"
    dom-align "^1.12.1"
    dom-scroll-into-view "^2.0.0"
    lodash "^4.17.21"
    lodash-es "^4.17.15"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    shallow-equal "^1.0.0"
    stylis "^4.1.3"
    throttle-debounce "^5.0.0"
    vue-types "^3.0.0"
    warning "^4.0.0"

any-promise@^1.0.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.2"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^4.1.0:
  version "4.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/arg/-/arg-4.1.3.tgz"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://repository.makeblock.com/repository/npm-group/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/argparse/-/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@^5.0.0:
  version "5.0.0"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/arr-diff/-/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/arr-flatten/-/arr-flatten-1.1.0.tgz"
  integrity sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/arr-union/-/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-ify@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/array-ify/-/array-ify-1.0.0.tgz"
  integrity sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/array-union/-/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/array-unique/-/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/arrify/-/arrify-1.0.1.tgz"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/assign-symbols/-/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types@^0.13.2:
  version "0.13.4"
  dependencies:
    tslib "^2.0.1"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/astral-regex/-/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-validator@^4.0.0:
  version "4.0.7"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/atob/-/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

axios@^0.19.2:
  version "0.19.2"
  resolved "http://repository.makeblock.com/repository/npm-group/axios/-/axios-0.19.2.tgz"
  integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
  dependencies:
    follow-redirects "1.5.10"

axios@^1.5.0, axios@^1.7.2:
  version "1.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/axios/-/axios-1.7.2.tgz"
  integrity sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-core@^7.0.0-bridge.0:
  version "7.0.0-bridge.0"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-core/-/babel-core-7.0.0-bridge.0.tgz"
  integrity sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg==

babel-jest@^27.5.1, "babel-jest@>=27.0.0 <28", babel-jest@27.x:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-jest/-/babel-jest-27.5.1.tgz"
  integrity sha512-cdQ5dXjGRd0IBRATiQ4mZGlGlRE8kJpjPOixdNRdT+m3UcNqmYWN6rK6nvtXYfY3D76cb8s/O1Ss8ea24PIwcg==
  dependencies:
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^27.5.1"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  dependencies:
    object.assign "^4.1.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.5.1.tgz"
  integrity sha512-50wCwD5EMNW4aRpOwtqzyZHIewTYNxLA4nhB+09d8BIssfNfzBRhkBIHiaPv1Si226TQSvp8gxAJm2iY2qs2hQ==
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-polyfill-corejs2@^0.3.0:
  version "0.3.1"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.5.0:
  version "0.5.2"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    core-js-compat "^3.21.0"

babel-plugin-polyfill-regenerator@^0.3.0:
  version "0.3.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"

babel-plugin-transform-vite-meta-env@1.0.3:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-plugin-transform-vite-meta-env/-/babel-plugin-transform-vite-meta-env-1.0.3.tgz"
  integrity sha1-y/gb7MlbcdzBcO5IY8t/aRntmbs=
  dependencies:
    "@babel/runtime" "^7.13.9"
    "@types/babel__core" "^7.1.12"

babel-plugin-transform-vite-meta-glob@1.0.3:
  version "1.0.3"
  dependencies:
    "@babel/runtime" "^7.13.9"
    "@types/babel__core" "^7.1.12"
    glob "^7.1.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/babel-preset-jest/-/babel-preset-jest-27.5.1.tgz"
  integrity sha512-Nptf2FzlPCWYuJg41HBqXVT8ym6bXOevuCTbhxlUpjwtysGaIWFvDEjp4y+G7fl13FgOdjs7P/DmErqH7da0Ag==
  dependencies:
    babel-plugin-jest-hoist "^27.5.1"
    babel-preset-current-node-syntax "^1.0.0"

babel-preset-vite@^1.0.4:
  version "1.0.4"
  dependencies:
    "@babel/runtime" "^7.13.9"
    "@types/babel__core" "^7.1.12"
    babel-plugin-transform-vite-meta-env "1.0.3"
    babel-plugin-transform-vite-meta-glob "1.0.3"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/balanced-match/-/balanced-match-2.0.0.tgz"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

base@^0.11.1:
  version "0.11.2"
  resolved "http://repository.makeblock.com/repository/npm-group/base/-/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/big.js/-/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bluebird@^3.5.0:
  version "3.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

bowser@^1.6.0:
  version "1.9.4"
  resolved "http://repository.makeblock.com/repository/npm-group/bowser/-/bowser-1.9.4.tgz"
  integrity sha1-iQxYooE6nTJDcEM0+oG5alwVDJo=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://repository.makeblock.com/repository/npm-group/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^2.2.2:
  version "2.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/braces/-/braces-2.3.2.tgz"
  integrity sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/braces/-/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browserslist@^4.17.5, browserslist@^4.19.1:
  version "4.20.2"
  dependencies:
    caniuse-lite "^1.0.30001317"
    electron-to-chromium "^1.4.84"
    escalade "^3.1.1"
    node-releases "^2.0.2"
    picocolors "^1.0.0"

bs-logger@0.x:
  version "0.2.6"
  resolved "http://repository.makeblock.com/repository/npm-group/bs-logger/-/bs-logger-0.2.6.tgz"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/bser/-/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.1.2:
  version "3.1.2"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/cache-base/-/cache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/callsites/-/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001317:
  version "1.0.30001331"

chalk@^1.1.3:
  version "1.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/chalk/-/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.1.0, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/chalk/-/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/chalk/-/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.0:
  version "4.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/chalk/-/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

cheerio-select@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cheerio-select/-/cheerio-select-2.1.0.tgz"
  integrity sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==
  dependencies:
    boolbase "^1.0.0"
    css-select "^5.1.0"
    css-what "^6.1.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"

cheerio@^1.0.0-rc.10:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cheerio/-/cheerio-1.0.0.tgz"
  integrity sha512-quS9HgjQpdaXOvsZz82Oz7uxtXiy6UIsIQcpBj7HRw2M63Skasm9qlDocAM7jNuaxdhpPU7c4kJN+gA5MCu4ww==
  dependencies:
    cheerio-select "^2.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.3"
    domutils "^3.1.0"
    encoding-sniffer "^0.2.0"
    htmlparser2 "^9.1.0"
    parse5 "^7.1.2"
    parse5-htmlparser2-tree-adapter "^7.0.0"
    parse5-parser-stream "^7.1.2"
    undici "^6.19.5"
    whatwg-mimetype "^4.0.0"

chokidar@^3.5.3, "chokidar@>=3.0.0 <4.0.0":
  version "3.5.3"
  resolved "http://repository.makeblock.com/repository/npm-group/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

ci-info@^3.2.0:
  version "3.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/ci-info/-/ci-info-3.7.0.tgz"
  integrity sha512-2CpRNYmImPx+RXKLq6jko/L07phmS9I02TyqkcNU20GCF/GgaWvc58hPtjxDX8lPpkdwc9sNh72V9k00S7ezog==

cjs-module-lexer@^1.0.0:
  version "1.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz"
  integrity sha1-n4S6MkSlEvOlTlJ36O70xImGTkA=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://repository.makeblock.com/repository/npm-group/class-utils/-/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cli-truncate/-/cli-truncate-2.1.0.tgz"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cli-truncate@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cli-truncate/-/cli-truncate-3.1.0.tgz"
  integrity sha1-PyOrElNePXPoObtD5zyd5IfbE4k=
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/cliui/-/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone@^2.1.1:
  version "2.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/clone/-/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

co@^4.6.0:
  version "4.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/co/-/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

codemirror@^6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/codemirror/-/codemirror-6.0.1.tgz"
  integrity sha512-J8j+nZ+CdWmIeFIGXEFbFPtpiYacFMDR8GlHK3IyHQJMCaVRfGx9NT+Hxivv1ckLWPvNdZqndbr/7lVhrf/Svg==
  dependencies:
    "@codemirror/autocomplete" "^6.0.0"
    "@codemirror/commands" "^6.0.0"
    "@codemirror/language" "^6.0.0"
    "@codemirror/lint" "^6.0.0"
    "@codemirror/search" "^6.0.0"
    "@codemirror/state" "^6.0.0"
    "@codemirror/view" "^6.0.0"

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/collection-visit/-/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://repository.makeblock.com/repository/npm-group/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/color-name/-/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-name@1.1.3:
  version "1.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/color-name/-/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

colord@^2.9.3:
  version "2.9.3"
  resolved "http://repository.makeblock.com/repository/npm-group/colord/-/colord-2.9.3.tgz"
  integrity sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==

colorette@^2.0.16:
  version "2.0.16"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^7.2.0:
  version "7.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/commander/-/commander-7.2.0.tgz"
  integrity sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=

commander@^8.3.0:
  version "8.3.0"

compare-func@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/compare-func/-/compare-func-2.0.0.tgz"
  integrity sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^5.1.0"

component-emitter@^1.2.1:
  version "1.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/component-emitter/-/component-emitter-1.3.1.tgz"
  integrity sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==

compute-scroll-into-view@^1.0.17:
  version "1.0.17"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/concat-stream/-/concat-stream-2.0.0.tgz"
  integrity sha1-QUz1r3kKSMYKub5FJ9VtXkETPLE=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.0.2"
    typedarray "^0.0.6"

content-type@^1.0.2:
  version "1.0.4"

conventional-changelog-angular@^5.0.11, conventional-changelog-angular@^5.0.12:
  version "5.0.13"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-angular/-/conventional-changelog-angular-5.0.13.tgz"
  integrity sha1-iWiF1juRSnDUk0tZ0v573hgysow=
  dependencies:
    compare-func "^2.0.0"
    q "^1.5.1"

conventional-changelog-atom@^2.0.8:
  version "2.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-atom/-/conventional-changelog-atom-2.0.8.tgz"
  integrity sha1-p1nsYcItHBGWkl/KiP466J/X2N4=
  dependencies:
    q "^1.5.1"

conventional-changelog-codemirror@^2.0.8:
  version "2.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-codemirror/-/conventional-changelog-codemirror-2.0.8.tgz"
  integrity sha1-OY6VMPCM407EZAr5jurzAi6x99w=
  dependencies:
    q "^1.5.1"

conventional-changelog-config-spec@2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-config-spec/-/conventional-changelog-config-spec-2.1.0.tgz"
  integrity sha1-h0pjUofvi1gf2FWFMr9lXU+1ny0=

conventional-changelog-conventionalcommits@^4.3.1, conventional-changelog-conventionalcommits@^4.5.0, conventional-changelog-conventionalcommits@4.6.3:
  version "4.6.3"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-4.6.3.tgz"
  integrity sha512-LTTQV4fwOM4oLPad317V/QNQ1FY4Hju5qeBIM1uTHbrnCE+Eg4CdRZ3gO2pUeR+tzWdp80M2j3qFFEDWVqOV4g==
  dependencies:
    compare-func "^2.0.0"
    lodash "^4.17.15"
    q "^1.5.1"

conventional-changelog-core@^4.2.1:
  version "4.2.4"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-core/-/conventional-changelog-core-4.2.4.tgz"
  integrity sha1-5Q0Efo66z2P6w9xnv5GBdwAeHp8=
  dependencies:
    add-stream "^1.0.0"
    conventional-changelog-writer "^5.0.0"
    conventional-commits-parser "^3.2.0"
    dateformat "^3.0.0"
    get-pkg-repo "^4.0.0"
    git-raw-commits "^2.0.8"
    git-remote-origin-url "^2.0.0"
    git-semver-tags "^4.1.1"
    lodash "^4.17.15"
    normalize-package-data "^3.0.0"
    q "^1.5.1"
    read-pkg "^3.0.0"
    read-pkg-up "^3.0.0"
    through2 "^4.0.0"

conventional-changelog-ember@^2.0.9:
  version "2.0.9"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-ember/-/conventional-changelog-ember-2.0.9.tgz"
  integrity sha1-YZs37HCL6edKIg9Nz3khKuHJKWI=
  dependencies:
    q "^1.5.1"

conventional-changelog-eslint@^3.0.9:
  version "3.0.9"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-eslint/-/conventional-changelog-eslint-3.0.9.tgz"
  integrity sha1-aJvQpHDgL3uq/iGklYgN7qGLfNs=
  dependencies:
    q "^1.5.1"

conventional-changelog-express@^2.0.6:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-express/-/conventional-changelog-express-2.0.6.tgz"
  integrity sha1-QgydkqNHtyqRVEdQv/qTh2Zabug=
  dependencies:
    q "^1.5.1"

conventional-changelog-jquery@^3.0.11:
  version "3.0.11"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-jquery/-/conventional-changelog-jquery-3.0.11.tgz"
  integrity sha1-0UIgdAD1HJ5btYhZZZjiS7qJlL8=
  dependencies:
    q "^1.5.1"

conventional-changelog-jshint@^2.0.9:
  version "2.0.9"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-jshint/-/conventional-changelog-jshint-2.0.9.tgz"
  integrity sha1-8tfyPmrNSSeiOFVdksCbUP44Uv8=
  dependencies:
    compare-func "^2.0.0"
    q "^1.5.1"

conventional-changelog-preset-loader@^2.3.4:
  version "2.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-preset-loader/-/conventional-changelog-preset-loader-2.3.4.tgz"
  integrity sha1-FKhVq7/9WQJ/1gJYHx802YYupEw=

conventional-changelog-writer@^5.0.0:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog-writer/-/conventional-changelog-writer-5.0.1.tgz"
  integrity sha512-5WsuKUfxW7suLblAbFnxAcrvf6r+0b7GvNaWUwUIk0bXMnENP/PEieGKVUQrjPqwPT4o3EPAASBXiY6iHooLOQ==
  dependencies:
    conventional-commits-filter "^2.0.7"
    dateformat "^3.0.0"
    handlebars "^4.7.7"
    json-stringify-safe "^5.0.1"
    lodash "^4.17.15"
    meow "^8.0.0"
    semver "^6.0.0"
    split "^1.0.0"
    through2 "^4.0.0"

conventional-changelog@3.1.25:
  version "3.1.25"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-changelog/-/conventional-changelog-3.1.25.tgz"
  integrity sha512-ryhi3fd1mKf3fSjbLXOfK2D06YwKNic1nC9mWqybBHdObPd8KJ2vjaXZfYj1U23t+V8T8n0d7gwnc9XbIdFbyQ==
  dependencies:
    conventional-changelog-angular "^5.0.12"
    conventional-changelog-atom "^2.0.8"
    conventional-changelog-codemirror "^2.0.8"
    conventional-changelog-conventionalcommits "^4.5.0"
    conventional-changelog-core "^4.2.1"
    conventional-changelog-ember "^2.0.9"
    conventional-changelog-eslint "^3.0.9"
    conventional-changelog-express "^2.0.6"
    conventional-changelog-jquery "^3.0.11"
    conventional-changelog-jshint "^2.0.9"
    conventional-changelog-preset-loader "^2.3.4"

conventional-commits-filter@^2.0.7:
  version "2.0.7"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-commits-filter/-/conventional-commits-filter-2.0.7.tgz"
  integrity sha1-+Nm08YL84Aya9xOdpJNlsTbIoLM=
  dependencies:
    lodash.ismatch "^4.4.0"
    modify-values "^1.0.0"

conventional-commits-parser@^3.2.0, conventional-commits-parser@^3.2.2:
  version "3.2.4"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-commits-parser/-/conventional-commits-parser-3.2.4.tgz"
  integrity sha512-nK7sAtfi+QXbxHCYfhpZsfRtaitZLIA6889kFIouLvz6repszQDgxBu7wf2WbU+Dco7sAnNCJYERCwt54WPC2Q==
  dependencies:
    is-text-path "^1.0.1"
    JSONStream "^1.0.4"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

conventional-recommended-bump@6.1.0:
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/conventional-recommended-bump/-/conventional-recommended-bump-6.1.0.tgz"
  integrity sha1-z6YjKF0d5VQBLy/95w2ciiIjH1U=
  dependencies:
    concat-stream "^2.0.0"
    conventional-changelog-preset-loader "^2.3.4"
    conventional-commits-filter "^2.0.7"
    conventional-commits-parser "^3.2.0"
    git-raw-commits "^2.0.8"
    git-semver-tags "^4.1.1"
    meow "^8.0.0"
    q "^1.5.1"

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.8.0"
  dependencies:
    safe-buffer "~5.1.1"

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/copy-anything/-/copy-anything-2.0.6.tgz"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "http://repository.makeblock.com/repository/npm-group/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  integrity sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==
  dependencies:
    toggle-selection "^1.0.6"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/copy-to/-/copy-to-2.0.1.tgz"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js-compat@^3.20.2, core-js-compat@^3.21.0:
  version "3.21.1"
  dependencies:
    browserslist "^4.19.1"
    semver "7.0.0"

core-js@^3.15.1:
  version "3.21.1"

core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cors@^2.8.5:
  version "2.8.5"
  resolved "http://repository.makeblock.com/repository/npm-group/cors/-/cors-2.8.5.tgz"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig-typescript-loader@^1.0.0:
  version "1.0.9"
  dependencies:
    cosmiconfig "^7"
    ts-node "^10.7.0"

cosmiconfig@^7, cosmiconfig@^7.0.0, cosmiconfig@^7.1.0:
  version "7.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/create-require/-/create-require-1.1.1.tgz"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

crelt@^1.0.5:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/crelt/-/crelt-1.0.5.tgz"
  integrity sha512-+BO9wPPi+DWTDcNYhr/W90myha8ptzftZT+LwcmUbbok0rcP/fequmFYCw8NMoH7pkAZQzU78b3kYrlua5a9eA==

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-functions-list@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/css-functions-list/-/css-functions-list-3.1.0.tgz"
  integrity sha512-/9lCvYZaUbBGvYUgYGFJ4dcYiyqdhSjG7IPVluoV8A1ILjkF7ilmhp1OGUz8n+nmBcu0RNrQAzgD8B6FJbrt2w==

css-select@^4.1.3:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/css-select/-/css-select-4.3.0.tgz"
  integrity sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-select@^5.1.0:
  version "5.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/css-select/-/css-select-5.1.0.tgz"
  integrity sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-tree@^1.1.3:
  version "1.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-tree@^2.0.1:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/css-tree/-/css-tree-2.3.0.tgz"
  integrity sha512-1rg0LiK2MFi4R3/lVvnRokEWTZb30ljSAe5x+0HHkZ+OqZaAeiP8g8Eh91VmkyCtQn9vMgQRiaTDYgLBt+2Qyw==
  dependencies:
    mdn-data "2.0.30"
    source-map-js "^1.0.1"

css-what@^6.0.1, css-what@^6.1.0:
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/css-what/-/css-what-6.1.0.tgz"
  integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csso@^4.2.0:
  version "4.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/csso/-/csso-4.2.0.tgz"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

cssom@^0.4.4:
  version "0.4.4"
  resolved "http://repository.makeblock.com/repository/npm-group/cssom/-/cssom-0.4.4.tgz"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/cssom/-/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/cssstyle/-/cssstyle-2.3.0.tgz"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

csstype@^3.1.1:
  version "3.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/csstype/-/csstype-3.1.2.tgz"
  integrity sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==

dargs@^7.0.0:
  version "7.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dargs/-/dargs-7.0.0.tgz"
  integrity sha1-BAFcQd4Ly2nshAUPPZvgyvjW1cw=

data-uri-to-buffer@3:
  version "3.0.1"

data-urls@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/data-urls/-/data-urls-2.0.0.tgz"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

dateformat@^2.0.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dateformat/-/dateformat-2.2.0.tgz"
  integrity sha1-QGXiATz5+5Ft39gu+1Bq1MZ2kGI=

dateformat@^3.0.0:
  version "3.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/dateformat/-/dateformat-3.0.3.tgz"
  integrity sha1-puN0maTZqc+F71hyBE1ikByYia4=

dayjs@^1.10.5, dayjs@^1.11.3:
  version "1.11.3"

debug@^2.2.0:
  version "2.6.9"
  resolved "http://repository.makeblock.com/repository/npm-group/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.3.3:
  version "2.6.9"
  resolved "http://repository.makeblock.com/repository/npm-group/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.6.9:
  version "2.6.9"
  resolved "http://repository.makeblock.com/repository/npm-group/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@4:
  version "4.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@=3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

decamelize-keys@^1.1.0:
  version "1.1.0"
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.2.1:
  version "10.4.3"
  resolved "http://repository.makeblock.com/repository/npm-group/decimal.js/-/decimal.js-10.4.3.tgz"
  integrity sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==

decode-uri-component@^0.2.0:
  version "0.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/decode-uri-component/-/decode-uri-component-0.2.2.tgz"
  integrity sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dedent/-/dedent-0.7.0.tgz"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/deep-equal/-/deep-equal-1.1.1.tgz"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.2.2"

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/default-user-agent/-/default-user-agent-1.0.0.tgz"
  integrity sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=
  dependencies:
    os-name "~1.0.3"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.1.3:
  version "1.1.3"
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://repository.makeblock.com/repository/npm-group/define-property/-/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/define-property/-/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/define-property/-/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

degenerator@^3.0.2:
  version "3.0.2"
  dependencies:
    ast-types "^0.13.2"
    escodegen "^1.8.1"
    esprima "^4.0.0"
    vm2 "^3.9.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@2.0.0:
  version "2.0.0"

destroy@^1.0.4:
  version "1.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-indent@^6.0.0:
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/detect-indent/-/detect-indent-6.1.0.tgz"
  integrity sha1-WSSF67v2s7GrK+F1yDk9BMoNV+Y=

detect-newline@^3.0.0, detect-newline@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

diff-sequences@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/diff-sequences/-/diff-sequences-27.5.1.tgz"
  integrity sha512-k1gCAXAsNgLwEL+Y8Wvl+M6oEFj5bgazfZULpS5CneoPPXRaCCW7dm+q21Ky2VEE5X+VeRDBVg1Pcvvsr4TtNQ==

diff@^4.0.1:
  version "4.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

diff@^8.0.2:
  version "8.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/diff/-/diff-8.0.2.tgz"
  integrity sha512-sSuxWU5j5SR9QQji/o2qMvqRNYRDOcBTgsJ/DeCf4iSN4gW+gNMXM7wFIP+fdXZxoNiAnHUTGjCr+TSWXdRDKg==

digest-header@^0.0.1:
  version "0.0.1"
  dependencies:
    utility "0.1.11"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.13"

dom-align@^1.12.1:
  version "1.12.2"

dom-scroll-into-view@^2.0.0:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/dom-scroll-into-view/-/dom-scroll-into-view-2.0.1.tgz"
  integrity sha1-DezIUigB/Y0/HGujVadNOCxfmJs=

dom-serializer@^1.0.1:
  version "1.4.1"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dom-serializer/-/dom-serializer-2.0.0.tgz"
  integrity sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom-serializer@0:
  version "0.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/dom-serializer/-/dom-serializer-0.2.2.tgz"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domelementtype@^1.3.1, domelementtype@1:
  version "1.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/domelementtype/-/domelementtype-1.3.1.tgz"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1, domelementtype@^2.2.0, domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/domelementtype/-/domelementtype-2.3.0.tgz"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domexception@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/domexception/-/domexception-2.0.1.tgz"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/domhandler/-/domhandler-2.4.2.tgz"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domhandler@^4.2.0, domhandler@^4.2.2, domhandler@^4.3.1:
  version "4.3.1"
  dependencies:
    domelementtype "^2.2.0"

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/domhandler/-/domhandler-5.0.3.tgz"
  integrity sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
  dependencies:
    domelementtype "^2.3.0"

domutils@^1.5.1:
  version "1.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/domutils/-/domutils-1.7.0.tgz"
  integrity sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.8.0:
  version "2.8.0"
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

domutils@^3.0.1:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/domutils/-/domutils-3.1.0.tgz"
  integrity sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

domutils@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/domutils/-/domutils-3.1.0.tgz"
  integrity sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dot-prop@^5.1.0:
  version "5.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dot-prop/-/dot-prop-5.3.0.tgz"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotgitignore@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/dotgitignore/-/dotgitignore-2.1.0.tgz"
  integrity sha1-pLFaTk7zzzg1mKrx36SgS8wIm3s=
  dependencies:
    find-up "^3.0.0"
    minimatch "^3.0.4"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

echarts@^5.1.2, echarts@^5.3.2:
  version "5.3.2"
  dependencies:
    tslib "2.3.0"
    zrender "5.3.1"

ee-first@~1.1.1:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.4.84:
  version "1.4.107"

emittery@^0.8.1:
  version "0.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/emittery/-/emittery-0.8.1.tgz"
  integrity sha1-uyPMhtA7MKp1p/c0gZ3uLhunCGA=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encoding-sniffer@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/encoding-sniffer/-/encoding-sniffer-0.2.0.tgz"
  integrity sha512-ju7Wq1kg04I3HtiYIOrUrdfdDvkyO9s5XM8QAj/bN61Yo/Vb4vgJxy5vi4Yxk01gWHbrofpPtpxM8bKger9jhg==
  dependencies:
    iconv-lite "^0.6.3"
    whatwg-encoding "^3.1.1"

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://repository.makeblock.com/repository/npm-group/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

end-or-error@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/end-or-error/-/end-or-error-1.0.1.tgz"
  integrity sha1-3HpiEP5403L+4kqLSJnb0VVBTcs=

entities@^1.1.1:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/entities/-/entities-1.1.2.tgz"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/entities/-/entities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@^3.0.1, entities@~3.0.1:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/entities/-/entities-3.0.1.tgz"
  integrity sha1-K4h8piWF6W2zkDSC0zbBAGwwAdQ=

entities@^4.2.0, entities@^4.5.0:
  version "4.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

errno@^0.1.1:
  version "0.1.8"
  resolved "http://repository.makeblock.com/repository/npm-group/errno/-/errno-0.1.8.tgz"
  integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

esbuild-darwin-arm64@0.14.36:
  version "0.14.36"
  resolved "http://repository.makeblock.com/repository/npm-group/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.36.tgz"
  integrity sha512-q8fY4r2Sx6P0Pr3VUm//eFYKVk07C5MHcEinU1BjyFnuYz4IxR/03uBbDwluR6ILIHnZTE7AkTUWIdidRi1Jjw==

esbuild@^0.14.14, esbuild@^0.14.27, esbuild@>=0.13:
  version "0.14.36"
  optionalDependencies:
    esbuild-android-64 "0.14.36"
    esbuild-android-arm64 "0.14.36"
    esbuild-darwin-64 "0.14.36"
    esbuild-darwin-arm64 "0.14.36"
    esbuild-freebsd-64 "0.14.36"
    esbuild-freebsd-arm64 "0.14.36"
    esbuild-linux-32 "0.14.36"
    esbuild-linux-64 "0.14.36"
    esbuild-linux-arm "0.14.36"
    esbuild-linux-arm64 "0.14.36"
    esbuild-linux-mips64le "0.14.36"
    esbuild-linux-ppc64le "0.14.36"
    esbuild-linux-riscv64 "0.14.36"
    esbuild-linux-s390x "0.14.36"
    esbuild-netbsd-64 "0.14.36"
    esbuild-openbsd-64 "0.14.36"
    esbuild-sunos-64 "0.14.36"
    esbuild-windows-32 "0.14.36"
    esbuild-windows-64 "0.14.36"
    esbuild-windows-arm64 "0.14.36"

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/escalade/-/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5, escape-string-regexp@1.0.5:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escodegen@^1.8.1:
  version "1.14.3"
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

escodegen@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/escodegen/-/escodegen-2.0.0.tgz"
  integrity sha1-XjKxKDPoqo+jXhvwvvqJOASEx90=
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-prettier@^8.3.0:
  version "8.5.0"

eslint-plugin-prettier@^4.0.0:
  version "4.0.0"
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-vue@^8.3.0:
  version "8.6.0"
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.0.0:
  version "7.1.1"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@^7.1.1:
  version "7.1.1"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/eslint-utils/-/eslint-utils-3.0.0.tgz"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.0.0, eslint-visitor-keys@^3.1.0, eslint-visitor-keys@^3.3.0:
  version "3.3.0"

eslint@*, "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.2.0 || ^7.0.0 || ^8.0.0", eslint@^8.7.0, eslint@>=5, eslint@>=6.0.0, eslint@>=7.0.0, eslint@>=7.28.0:
  version "8.13.0"
  dependencies:
    "@eslint/eslintrc" "^1.2.1"
    "@humanwhocodes/config-array" "^0.9.2"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.1.1"
    eslint-utils "^3.0.0"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^6.0.1"
    globals "^13.6.0"
    ignore "^5.2.0"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    regexpp "^3.2.0"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^9.0.0, espree@^9.3.1:
  version "9.3.1"
  dependencies:
    acorn "^8.7.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^3.3.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/esprima/-/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.4.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0:
  version "5.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/esutils/-/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@^1.8.1:
  version "1.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/eventemitter3/-/eventemitter3-2.0.3.tgz"
  integrity sha1-teEHm1n7XhuidxwKmTvgYKWMmbo=

eventemitter3@^4.0.7:
  version "4.0.7"
  resolved "http://repository.makeblock.com/repository/npm-group/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/events/-/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^5.0.0, execa@^5.1.1:
  version "5.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/execa/-/execa-5.1.1.tgz"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/exit/-/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/expand-brackets/-/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/expect/-/expect-27.5.1.tgz"
  integrity sha512-E1q5hSUG2AmYQwQJ041nvgpkODHQvB+RKlB4IYdru6uJsyFTRyZAP463M+1lINorwbqAmUggi6+WwkD8lCS/Dw==
  dependencies:
    "@jest/types" "^27.5.1"
    jest-get-type "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/extend-shallow/-/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/extend-shallow/-/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/extend-shallow/-/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.2:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/extend/-/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

extglob@^2.0.2:
  version "2.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/extglob/-/extglob-2.0.4.tgz"
  integrity sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2, fast-diff@1.2.0:
  version "1.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-diff/-/fast-diff-1.2.0.tgz"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-diff@1.1.2:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-diff/-/fast-diff-1.1.2.tgz"
  integrity sha1-S2LEK44D3j+EhGC2OQeZIGldAVQ=

fast-glob@^3.2.11, fast-glob@^3.2.12, fast-glob@^3.2.9:
  version "3.2.12"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-glob/-/fast-glob-3.2.12.tgz"
  integrity sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@2.x:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "http://repository.makeblock.com/repository/npm-group/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

fastq@^1.6.0:
  version "1.13.0"
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==
  dependencies:
    bser "2.1.1"

figures@^3.1.0:
  version "3.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/figures/-/figures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-uri-to-path@2:
  version "2.0.0"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/fill-range/-/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/find-up/-/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/find-up/-/find-up-3.0.0.tgz"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/find-up/-/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/find-up/-/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/find-up/-/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.5"

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "http://repository.makeblock.com/repository/npm-group/follow-redirects/-/follow-redirects-1.15.6.tgz"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://repository.makeblock.com/repository/npm-group/follow-redirects/-/follow-redirects-1.5.10.tgz"
  integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
  dependencies:
    debug "=3.1.0"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/for-in/-/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

form-data@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/form-data/-/form-data-3.0.1.tgz"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/form-data/-/form-data-4.0.0.tgz"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formstream@^1.1.0:
  version "1.1.1"
  dependencies:
    destroy "^1.0.4"
    mime "^2.5.2"
    pause-stream "~0.0.11"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/fragment-cache/-/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fs-extra@^10.0.0:
  version "10.0.1"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.2"

ftp@^0.3.10:
  version "0.3.10"
  dependencies:
    readable-stream "1.1.x"
    xregexp "2.0.0"

function-bind@^1.1.1, function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functional-red-black-tree@^1.0.1:
  version "1.0.1"

functions-have-names@^1.2.2:
  version "1.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

fuse.js@^6.6.2:
  version "6.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/fuse.js/-/fuse.js-6.6.2.tgz"
  integrity sha512-cJaJkxCCxC8qIIcPBF9yGxY0W/tVZS3uEISDxhYIdtk8OL93pe+6Zj7LjCqVV4dzbqcriOZ+kQ/NE4RXZHsIGA==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://repository.makeblock.com/repository/npm-group/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2:
  version "1.1.1"
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-pkg-repo@^4.0.0:
  version "4.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/get-pkg-repo/-/get-pkg-repo-4.2.1.tgz"
  integrity sha1-dZc+HIBQxz9IGQxSBHxM7jrL84U=
  dependencies:
    "@hutson/parse-repository-url" "^3.0.0"
    hosted-git-info "^4.0.0"
    through2 "^2.0.0"
    yargs "^16.2.0"

get-ready@^1.0.0, get-ready@~1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/get-ready/-/get-ready-1.0.0.tgz"
  integrity sha1-+RgX8emt7P6hOlYq38jeiDqzR4I=

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-uri@3:
  version "3.0.2"
  dependencies:
    "@tootallnate/once" "1"
    data-uri-to-buffer "3"
    debug "4"
    file-uri-to-path "2"
    fs-extra "^8.1.0"
    ftp "^0.3.10"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/get-value/-/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

git-raw-commits@^2.0.0, git-raw-commits@^2.0.8:
  version "2.0.11"
  resolved "http://repository.makeblock.com/repository/npm-group/git-raw-commits/-/git-raw-commits-2.0.11.tgz"
  integrity sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A==
  dependencies:
    dargs "^7.0.0"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

git-remote-origin-url@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/git-remote-origin-url/-/git-remote-origin-url-2.0.0.tgz"
  integrity sha1-UoJlna4hBxRaERJhEq0yFuxfpl8=
  dependencies:
    gitconfiglocal "^1.0.0"
    pify "^2.3.0"

git-semver-tags@^4.0.0, git-semver-tags@^4.1.1:
  version "4.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/git-semver-tags/-/git-semver-tags-4.1.1.tgz"
  integrity sha1-YxkbzYCbDsPhUbpHUcFsRE5bV4A=
  dependencies:
    meow "^8.0.0"
    semver "^6.0.0"

gitconfiglocal@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/gitconfiglocal/-/gitconfiglocal-1.0.0.tgz"
  integrity sha1-QdBF84UaXqiPA/JMocYXgRRGS5s=
  dependencies:
    ini "^1.3.2"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.0"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.1:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/global-dirs/-/global-dirs-0.1.1.tgz"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/global-modules/-/global-modules-2.0.0.tgz"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/global-prefix/-/global-prefix-3.0.0.tgz"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://repository.makeblock.com/repository/npm-group/globals/-/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0:
  version "13.13.0"
  dependencies:
    type-fest "^0.20.2"

globals@^13.9.0:
  version "13.13.0"
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.4, globby@^11.1.0:
  version "11.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/globjoin/-/globjoin-0.1.4.tgz"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.9:
  version "4.2.10"

handlebars@^4.7.7:
  version "4.7.8"
  resolved "http://repository.makeblock.com/repository/npm-group/handlebars/-/handlebars-4.7.8.tgz"
  integrity sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.2"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/hard-rejection/-/hard-rejection-2.1.0.tgz"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

harmony-reflect@^1.4.6:
  version "1.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/harmony-reflect/-/harmony-reflect-1.6.2.tgz"
  integrity sha1-Mey9MuZIo00DDYattn1NR1R/5xA=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-ansi/-/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-flag/-/has-flag-1.0.0.tgz"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/has-value/-/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-value/-/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/has-values/-/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/has-values/-/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  dependencies:
    function-bind "^1.1.1"

hasown@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/hasown/-/hasown-2.0.0.tgz"
  integrity sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==
  dependencies:
    function-bind "^1.1.2"

he@^1.1.1:
  version "1.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/he/-/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://repository.makeblock.com/repository/npm-group/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hosted-git-info@^4.0.0, hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
  integrity sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==
  dependencies:
    lru-cache "^6.0.0"

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-tags@^3.2.0:
  version "3.2.0"

htmlparser2@^3.8.3:
  version "3.10.1"
  resolved "http://repository.makeblock.com/repository/npm-group/htmlparser2/-/htmlparser2-3.10.1.tgz"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

htmlparser2@^7.1.2:
  version "7.2.0"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.2"
    domutils "^2.8.0"
    entities "^3.0.1"

htmlparser2@^9.1.0:
  version "9.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/htmlparser2/-/htmlparser2-9.1.0.tgz"
  integrity sha512-5zfg6mHUoaer/97TxnGpxmbR7zJtPwIYFMZ/H5ucTlPZhKvtum05yiPK3Mgai3a0DyVxv7qYqoweaEd2nrYQzQ==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.1.0"
    entities "^4.5.0"

http-errors@2.0.0:
  version "2.0.0"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^4.0.0, http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0, https-proxy-agent@5:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@^7.0.4:
  version "7.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/husky/-/husky-7.0.4.tgz"
  integrity sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=

iconv-lite@^0.4.15, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "http://repository.makeblock.com/repository/npm-group/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://repository.makeblock.com/repository/npm-group/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@0.6.3:
  version "0.6.3"
  resolved "http://repository.makeblock.com/repository/npm-group/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

identity-obj-proxy@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  integrity sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=
  dependencies:
    harmony-reflect "^1.4.6"

ignore@^5.1.8, ignore@^5.2.0, ignore@^5.2.1:
  version "5.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/ignore/-/ignore-5.2.1.tgz"
  integrity sha512-d2qQLzTJ9WxQftPAuEQpSPmKqzxePjzVbpAVv62AQ64NTL+wR4JkrVqR/LqFsFEUsHDAiId52mJteHDFuDkElA==

image-size@^0.5.1, image-size@~0.5.0:
  version "0.5.5"
  resolved "http://repository.makeblock.com/repository/npm-group/image-size/-/image-size-0.5.5.tgz"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immutable@^4.0.0:
  version "4.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/import-lazy/-/import-lazy-4.0.0.tgz"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^3.0.2:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/import-local/-/import-local-3.1.0.tgz"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/inherits/-/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.2, ini@^1.3.4, ini@^1.3.5:
  version "1.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/ini/-/ini-1.3.8.tgz"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

ip@^1.1.5:
  version "1.1.8"
  resolved "http://repository.makeblock.com/repository/npm-group/ip/-/ip-1.1.8.tgz"
  integrity sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==

is-accessor-descriptor@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-accessor-descriptor/-/is-accessor-descriptor-1.0.1.tgz"
  integrity sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA==
  dependencies:
    hasown "^2.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://repository.makeblock.com/repository/npm-group/is-buffer/-/is-buffer-1.1.6.tgz"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/is-class-hotfix/-/is-class-hotfix-0.0.6.tgz"
  integrity sha1-pSfTH7IyeSgd3l84XHe13nCnJDU=

is-core-module@^2.5.0, is-core-module@^2.9.0:
  version "2.11.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-core-module/-/is-core-module-2.11.0.tgz"
  integrity sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==
  dependencies:
    has "^1.0.3"

is-data-descriptor@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-data-descriptor/-/is-data-descriptor-1.0.1.tgz"
  integrity sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw==
  dependencies:
    hasown "^2.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.7"
  resolved "http://repository.makeblock.com/repository/npm-group/is-descriptor/-/is-descriptor-0.1.7.tgz"
  integrity sha512-C3grZTvObeN1xud4cRWl366OMXZTj0+HGyk4hvfpx4ZHt1Pb60ANSXqCK7pdOTeUQpRzECBSTphqvD7U+l22Eg==
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/is-descriptor/-/is-descriptor-1.0.3.tgz"
  integrity sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw==
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-extendable/-/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-extendable/-/is-extendable-1.0.1.tgz"
  integrity sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz"
  integrity sha1-+uMWfHKedGP4RhzlErCApJJoqog=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-number/-/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-number/-/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-obj/-/is-obj-2.0.0.tgz"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-plain-obj@^1.1, is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3:
  version "2.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-plain-object@^5.0.0, is-plain-object@5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-plain-object/-/is-plain-object-5.0.0.tgz"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-plain-object@3.0.1:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-plain-object/-/is-plain-object-3.0.1.tgz"
  integrity sha1-Zi2S0kwKpDAkB7DUXSHyJRyF+Fs=

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.0.4:
  version "1.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/is-regex/-/is-regex-1.1.4.tgz"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-text-path@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-text-path/-/is-text-path-1.0.1.tgz"
  integrity sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=
  dependencies:
    text-extensions "^1.0.0"

is-type-of@^1.0.0:
  version "1.2.1"
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-what@^3.14.1:
  version "3.14.1"
  resolved "http://repository.makeblock.com/repository/npm-group/is-what/-/is-what-3.14.1.tgz"
  integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/is-windows/-/is-windows-1.0.2.tgz"
  integrity sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@0.0.1:
  version "0.0.1"

isarray@1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/isobject/-/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/isobject/-/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/isobject/-/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isobject@^3.0.1:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/isobject/-/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/isstream/-/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz"
  integrity sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=

istanbul-lib-instrument@^5.0.4, istanbul-lib-instrument@^5.1.0:
  version "5.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  integrity sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/istanbul-reports/-/istanbul-reports-3.1.5.tgz"
  integrity sha512-nUsEMa9pBt/NOHqbcbeJEgqIlY/K7rVWUX6Lql2orY5e9roQOthbR3vtY4zzf2orPELg80fnxxk9zUyPlgwD1w==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

javascript-natural-sort@^0.7.1:
  version "0.7.1"
  resolved "http://repository.makeblock.com/repository/npm-group/javascript-natural-sort/-/javascript-natural-sort-0.7.1.tgz"
  integrity sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k=

jest-changed-files@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-changed-files/-/jest-changed-files-27.5.1.tgz"
  integrity sha512-buBLMiByfWGCoMsLLzGUUSpAmIAGnbR2KJoMN10ziLhOLvP4e0SlypHnAel8iqQXTrcbmfEY9sSqae5sgUsTvw==
  dependencies:
    "@jest/types" "^27.5.1"
    execa "^5.0.0"
    throat "^6.0.1"

jest-circus@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-circus/-/jest-circus-27.5.1.tgz"
  integrity sha512-D95R7x5UtlMA5iBYsOHFFbMD/GVA4R/Kdq15f7xYWUfWHBto9NYRsOvnSauTgdF+ogCpJ4tyKOXhUifxS65gdw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    expect "^27.5.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"
    throat "^6.0.1"

jest-cli@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-cli/-/jest-cli-27.5.1.tgz"
  integrity sha512-Hc6HOOwYq4/74/c62dEE3r5elx8wjYqxY0r0G/nFrLDPMFRu6RA/u8qINOIkvhxG7mMQ5EJsOGfRpI8L6eFUVw==
  dependencies:
    "@jest/core" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    import-local "^3.0.2"
    jest-config "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    prompts "^2.0.1"
    yargs "^16.2.0"

jest-config@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-config/-/jest-config-27.5.1.tgz"
  integrity sha512-5sAsjm6tGdsVbW9ahcChPAFCk4IlkQUknH5AvKjuLTSlcO/wCZKyFdn7Rg0EkC+OGgWODEy2hDpWB1PgzH0JNA==
  dependencies:
    "@babel/core" "^7.8.0"
    "@jest/test-sequencer" "^27.5.1"
    "@jest/types" "^27.5.1"
    babel-jest "^27.5.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.9"
    jest-circus "^27.5.1"
    jest-environment-jsdom "^27.5.1"
    jest-environment-node "^27.5.1"
    jest-get-type "^27.5.1"
    jest-jasmine2 "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-runner "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-diff/-/jest-diff-27.5.1.tgz"
  integrity sha512-m0NvkX55LDt9T4mctTEgnZk3fmEg3NRYutvMPWM/0iPnkFj2wIeF45O1718cMSOFO1vINkqmxqD8vE37uTEbqw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-docblock@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-docblock/-/jest-docblock-27.5.1.tgz"
  integrity sha512-rl7hlABeTsRYxKiUfpHrQrG4e2obOiTQWfMEH3PxPjOtdsfLQO4ReWSZaQ7DETm4xu07rl4q/h4zcKXyU0/OzQ==
  dependencies:
    detect-newline "^3.0.0"

jest-each@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-each/-/jest-each-27.5.1.tgz"
  integrity sha512-1Ff6p+FbhT/bXQnEouYy00bkNSY7OUpfIcmdl8vZ31A1UUaurOLPA8a8BbJOF2RDUElwJhmeaV7LnagI+5UwNQ==
  dependencies:
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    jest-get-type "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"

jest-environment-jsdom@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-environment-jsdom/-/jest-environment-jsdom-27.5.1.tgz"
  integrity sha512-TFBvkTC1Hnnnrka/fUb56atfDtJ9VMZ94JkjTbggl1PEpwrYtUBKMezB3inLmWqQsXYLcMwNoDQwoBTAvFfsfw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"
    jsdom "^16.6.0"

jest-environment-node@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-environment-node/-/jest-environment-node-27.5.1.tgz"
  integrity sha512-Jt4ZUnxdOsTGwSRAfKEnE6BcwsSPNOijjwifq5sDFSA2kesnXTvNqKHYgM0hDq3549Uf/KzdXNYn4wMZJPlFLw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"

jest-get-type@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-get-type/-/jest-get-type-27.5.1.tgz"
  integrity sha512-2KY95ksYSaK7DMBWQn6dQz3kqAf3BB64y2udeG+hv4KfSOb9qwcYQstTJc1KCbsix+wLZWZYN8t7nwX3GOBLRw==

jest-haste-map@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-haste-map/-/jest-haste-map-27.5.1.tgz"
  integrity sha512-7GgkZ4Fw4NFbMSDSpZwXeBiIbx+t/46nJ2QitkOjvwPYyZmqttu2TDSimMHP1EkPOi4xUZAN1doE5Vd25H4Jng==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^27.5.1"
    jest-serializer "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    micromatch "^4.0.4"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.3.2"

jest-html-reporters@^3.1.7:
  version "3.1.7"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-html-reporters/-/jest-html-reporters-3.1.7.tgz"
  integrity sha512-GTmjqK6muQ0S0Mnksf9QkL9X9z2FGIpNSxC52E0PHDzjPQ1XDu2+XTI3B3FS43ZiUzD1f354/5FfwbNIBzT7ew==
  dependencies:
    fs-extra "^10.0.0"
    open "^8.0.3"

jest-jasmine2@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-jasmine2/-/jest-jasmine2-27.5.1.tgz"
  integrity sha512-jtq7VVyG8SqAorDpApwiJJImd0V2wv1xzdheGHRGyuT7gZm6gG47QEskOlzsN1PG/6WNaCo5pmwMHDf3AkG2pQ==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/source-map" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^27.5.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"
    throat "^6.0.1"

jest-leak-detector@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-leak-detector/-/jest-leak-detector-27.5.1.tgz"
  integrity sha512-POXfWAMvfU6WMUXftV4HolnJfnPOGEu10fscNCA76KBpRRhcMN2c8d3iT2pxQS3HLbA+5X4sOUPzYO2NUyIlHQ==
  dependencies:
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-matcher-utils@^27.0.0, jest-matcher-utils@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-matcher-utils/-/jest-matcher-utils-27.5.1.tgz"
  integrity sha512-z2uTx/T6LBaCoNWNFWwChLBKYxTMcGBRjAt+2SbP929/Fflb9aa5LGma654Rz8z9HLxsrUaYzxE9T/EFIL/PAw==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-message-util@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-message-util/-/jest-message-util-27.5.1.tgz"
  integrity sha512-rMyFe1+jnyAAf+NHwTclDz0eAaLkVDdKVHHBFWsBWHnnh5YeJMNWWsv7AbFYXfK3oTqvL7VTWkhNLu1jX24D+g==
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^27.5.1"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-mock/-/jest-mock-27.5.1.tgz"
  integrity sha512-K4jKbY1d4ENhbrG2zuPWaQBvDly+iZ2yAW+T1fATN78hc0sInwn7wZB8XtlNnvHug5RMwV897Xm4LqmPM4e2Og==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  integrity sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==

jest-regex-util@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-regex-util/-/jest-regex-util-27.5.1.tgz"
  integrity sha512-4bfKq2zie+x16okqDXjXn9ql2B0dScQu+vcwe4TvFVhkVyuWLqpZrZtXxLLWoXYgn0E87I6r6GRYHF7wFZBUvg==

jest-resolve-dependencies@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-resolve-dependencies/-/jest-resolve-dependencies-27.5.1.tgz"
  integrity sha512-QQOOdY4PE39iawDn5rzbIePNigfe5B9Z91GDD1ae/xNDlu9kaat8QQ5EKnNmVWPV54hUdxCVwwj6YMgR2O7IOg==
  dependencies:
    "@jest/types" "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-snapshot "^27.5.1"

jest-resolve@*, jest-resolve@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-resolve/-/jest-resolve-27.5.1.tgz"
  integrity sha512-FFDy8/9E6CV83IMbDpcjOhumAQPDyETnU2KZ1O98DwTnz8AOBsW/Xv3GySr1mOZdItLR+zDZ7I/UdTFbgSOVCw==
  dependencies:
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-pnp-resolver "^1.2.2"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-runner/-/jest-runner-27.5.1.tgz"
  integrity sha512-g4NPsM4mFCOwFKXO4p/H/kWGdJp9V8kURY2lX8Me2drgXqG7rrZAx5kv+5H7wtt/cdFIjhqYx1HrlqWHaOvDaQ==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/environment" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.8.1"
    graceful-fs "^4.2.9"
    jest-docblock "^27.5.1"
    jest-environment-jsdom "^27.5.1"
    jest-environment-node "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-leak-detector "^27.5.1"
    jest-message-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-runtime "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    source-map-support "^0.5.6"
    throat "^6.0.1"

jest-runtime@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-runtime/-/jest-runtime-27.5.1.tgz"
  integrity sha512-o7gxw3Gf+H2IGt8fv0RiyE1+r83FJBRruoA+FXrlHw6xEyBsU8ugA6IPfTdVyA0w8HClpbK+DGJxH59UrNMx8A==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/globals" "^27.5.1"
    "@jest/source-map" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    execa "^5.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-message-util "^27.5.1"
    jest-mock "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-serializer@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-serializer/-/jest-serializer-27.5.1.tgz"
  integrity sha512-jZCyo6iIxO1aqUxpuBlwTDMkzOAJS4a3eYz3YzgxxVQFwLeSA7Jfq5cbqCY+JLvTDrWirgusI/0KwxKMgrdf7w==
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.9"

jest-snapshot@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-snapshot/-/jest-snapshot-27.5.1.tgz"
  integrity sha512-yYykXI5a0I31xX67mgeLw1DZ0bJB+gpq5IpSuCAoyDi0+BhgU/RIrL+RTzDmkNTchvDFWKP8lp+w/42Z3us5sA==
  dependencies:
    "@babel/core" "^7.7.2"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.0.0"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^27.5.1"
    graceful-fs "^4.2.9"
    jest-diff "^27.5.1"
    jest-get-type "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-util "^27.5.1"
    natural-compare "^1.4.0"
    pretty-format "^27.5.1"
    semver "^7.3.2"

jest-util@^27.0.0:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-util/-/jest-util-27.5.1.tgz"
  integrity sha512-Kv2o/8jNvX1MQ0KGtw480E/w4fBCDOnH6+6DmeKi6LZUIlKA5kwY0YNdlzaWTiVgxqAqik11QyxDOKk543aKXw==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-util@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-util/-/jest-util-27.5.1.tgz"
  integrity sha512-Kv2o/8jNvX1MQ0KGtw480E/w4fBCDOnH6+6DmeKi6LZUIlKA5kwY0YNdlzaWTiVgxqAqik11QyxDOKk543aKXw==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-validate/-/jest-validate-27.5.1.tgz"
  integrity sha512-thkNli0LYTmOI1tDB3FI1S1RTp/Bqyd9pTarJwL87OIBFuqEb5Apv5EaApEudYg4g86e3CT6kM0RowkhtEnCBQ==
  dependencies:
    "@jest/types" "^27.5.1"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^27.5.1"
    leven "^3.1.0"
    pretty-format "^27.5.1"

jest-watcher@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-watcher/-/jest-watcher-27.5.1.tgz"
  integrity sha512-z676SuD6Z8o8qbmEGhoEUFOM1+jfEiL3DXHK/xgEiG2EyNYfFG60jluWcupY6dATjfEsKQuibReS1djInQnoVw==
  dependencies:
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^27.5.1"
    string-length "^4.0.1"

jest-worker@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^27.0.0, jest@^27.4.6, jest@27.x:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jest/-/jest-27.5.1.tgz"
  integrity sha512-Yn0mADZB89zTtjkPJEXwrac3LHudkQMR+Paqa8uxJHCBr9agxztUifWCyiYrjhMPBoUVBjyny0I7XH6ozDr7QQ==
  dependencies:
    "@jest/core" "^27.5.1"
    import-local "^3.0.2"
    jest-cli "^27.5.1"

jmespath@^0.16.0:
  version "0.16.0"
  resolved "http://repository.makeblock.com/repository/npm-group/jmespath/-/jmespath-0.16.0.tgz"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

js-base64@^2.1.9, js-base64@^2.5.2:
  version "2.6.4"
  resolved "http://repository.makeblock.com/repository/npm-group/js-base64/-/js-base64-2.6.4.tgz"
  integrity sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=

js-cookie@^2.2.1:
  version "2.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/js-cookie/-/js-cookie-2.2.1.tgz"
  integrity sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg=

js-cookie@^3.0.1:
  version "3.0.1"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://repository.makeblock.com/repository/npm-group/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsdom@^16.6.0:
  version "16.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/jsdom/-/jsdom-16.7.0.tgz"
  integrity sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://repository.makeblock.com/repository/npm-group/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/jsesc/-/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-editor-vue3@^1.0.6:
  version "1.0.6"
  dependencies:
    jsoneditor "^9.5.6"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-source-map@^0.6.1:
  version "0.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/json-source-map/-/json-source-map-0.6.1.tgz"
  integrity sha512-1QoztHPsMQqhDq0hlXY5ZqcEdUzxQEIxgFkKl4WUp2pgShObl+9ovi4kRh2TfvAfxAoHOJ9vIMEqk3k4iex7tg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@^5.0.1:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^1.0.1:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.2.1, json5@2.x:
  version "2.2.1"

jsoneditor@^9.5.6:
  version "9.7.4"
  dependencies:
    ace-builds "^1.4.14"
    ajv "^6.12.6"
    javascript-natural-sort "^0.7.1"
    jmespath "^0.16.0"
    json-source-map "^0.6.1"
    jsonrepair "^2.2.1"
    mobius1-selectr "^2.4.13"
    picomodal "^3.0.0"
    vanilla-picker "^2.12.1"

jsonfile@^4.0.0:
  version "4.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/jsonparse/-/jsonparse-1.3.1.tgz"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

jsonrepair@^2.2.1:
  version "2.2.1"

JSONStream@^1.0.4:
  version "1.3.5"
  resolved "http://repository.makeblock.com/repository/npm-group/JSONStream/-/JSONStream-1.3.5.tgz"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

jstoxml@^2.0.0:
  version "2.2.9"
  resolved "http://repository.makeblock.com/repository/npm-group/jstoxml/-/jstoxml-2.2.9.tgz"
  integrity sha512-OYWlK0j+roh+eyaMROlNbS5cd5R25Y+IUpdl7cNdB8HNrkgwQzIS7L9MegxOiWNBj9dQhA/yAxiMwCC5mwNoBw==

kind-of@^3.0.2:
  version "3.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^3.0.3:
  version "3.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.2:
  version "5.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-5.1.0.tgz"
  integrity sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==

kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/kleur/-/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

known-css-properties@^0.26.0:
  version "0.26.0"
  resolved "http://repository.makeblock.com/repository/npm-group/known-css-properties/-/known-css-properties-0.26.0.tgz"
  integrity sha512-5FZRzrZzNTBruuurWpvZnvP9pum+fe0HcK8z/ooo+U+Hmp4vtbyp1/QDsqmufirXy4egGzbaH/y2uCZf+6W5Kg==

ko-sleep@^1.0.3:
  version "1.1.4"
  dependencies:
    ms "*"

less@*, less@^4.2.0:
  version "4.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/less/-/less-4.2.1.tgz"
  integrity sha512-CasaJidTIhWmjcqv0Uj5vccMI7pJgfD9lMkKtlnTHAdJdYK/7l8pM9tumLyJ0zhbD4KJLo/YvTj+xznQd5NBhg==
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^3.1.0"
    source-map "~0.6.0"

leven@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/leven/-/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/levn/-/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

levn@~0.3.0:
  version "0.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/levn/-/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lilconfig@2.0.4:
  version "2.0.4"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://repository.makeblock.com/repository/npm-group/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

linkify-it@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/linkify-it/-/linkify-it-4.0.1.tgz"
  integrity sha512-C7bfi1UZmoj8+PQx22XyeXCuBlokoyWQL5pWSP+EI6nzRylyThouddufc2c1NDIcP9k5agmN9fLpA7VNJfIiqw==
  dependencies:
    uc.micro "^1.0.1"

lint-staged@^12.1.7:
  version "12.3.7"
  dependencies:
    cli-truncate "^3.1.0"
    colorette "^2.0.16"
    commander "^8.3.0"
    debug "^4.3.3"
    execa "^5.1.1"
    lilconfig "2.0.4"
    listr2 "^4.0.1"
    micromatch "^4.0.4"
    normalize-path "^3.0.0"
    object-inspect "^1.12.0"
    pidtree "^0.5.0"
    string-argv "^0.3.1"
    supports-color "^9.2.1"
    yaml "^1.10.2"

listr2@^4.0.1:
  version "4.0.5"
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.5"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/load-json-file/-/load-json-file-4.0.0.tgz"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-utils@^1.1.0:
  version "1.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/loader-utils/-/loader-utils-1.4.2.tgz"
  integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

local-pkg@^0.4.1:
  version "0.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/local-pkg/-/local-pkg-0.4.2.tgz"
  integrity sha512-mlERgSPrbxU3BP4qBqAvvwlgW4MTg78iwJdGGnv7kibKjWcJksrG3t6LB5lXI93wXRDvG4NpUgJFmTG4T6rdrg==

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/locate-path/-/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/locate-path/-/locate-path-3.0.0.tgz"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.ismatch@^4.4.0:
  version "4.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.ismatch/-/lodash.ismatch-4.4.0.tgz"
  integrity sha1-dWy1FQyjum8RCFp4hJZF8Yj4Xzc=

lodash.memoize@4.x:
  version "4.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.21, lodash@^4.7.0:
  version "4.17.21"
  resolved "http://repository.makeblock.com/repository/npm-group/lodash/-/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/log-update/-/log-update-4.0.0.tgz"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lru-cache@^8.0.4:
  version "8.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/lru-cache/-/lru-cache-8.0.5.tgz"
  integrity sha512-MhWWlVnuab1RG5/zMRRcVGXZLCXrZTgfwMikgzCegsPnG62yDQo5JnqKkrK4jO5iKqDAZGItAqN5CtKBCBWRUA==

lz-string@^1.4.4:
  version "1.4.4"

magic-string@^0.26.2:
  version "0.26.7"
  resolved "http://repository.makeblock.com/repository/npm-group/magic-string/-/magic-string-0.26.7.tgz"
  integrity sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.30.0:
  version "0.30.0"
  resolved "http://repository.makeblock.com/repository/npm-group/magic-string/-/magic-string-0.30.0.tgz"
  integrity sha512-LA+31JYDJLs82r2ScLrlz1GjSgu66ZV518eyWT+S8VhyQn/JL0u9MeBOvQMGYiPk1DBiSN9DDMOcXvigJZaViQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1, make-error@1.x:
  version "1.3.6"
  resolved "http://repository.makeblock.com/repository/npm-group/make-error/-/make-error-1.3.6.tgz"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "http://repository.makeblock.com/repository/npm-group/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/map-cache/-/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/map-obj/-/map-obj-1.0.1.tgz"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/map-obj/-/map-obj-4.3.0.tgz"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/map-visit/-/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

markdown-it-image-figures@^2.1.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/markdown-it-image-figures/-/markdown-it-image-figures-2.1.1.tgz"
  integrity sha512-mwXSQ2nPeVUzCMIE3HlLvjRioopiqyJLNph0pyx38yf9mpqFDhNGnMpAXF9/A2Xv0oiF2cVyg9xwfF0HNAz05g==

markdown-it-task-lists@^2.1.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/markdown-it-task-lists/-/markdown-it-task-lists-2.1.1.tgz"
  integrity sha512-TxFAc76Jnhb2OUu+n3yz9RMu4CwGfaT788br6HhEDlvWfdeJcLUsxk1Hgw2yJio0OXsxv7pyIPmvECY7bMbluA==

markdown-it@*, markdown-it@^13.0.1:
  version "13.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/markdown-it/-/markdown-it-13.0.1.tgz"
  integrity sha512-lTlxriVoy2criHP0JKRhO2VDG9c2ypWCsT237eDiLqi09rmbKoUetyGHq2uOIRoRS//kfoJckS0eUzzkDR+k2Q==
  dependencies:
    argparse "^2.0.1"
    entities "~3.0.1"
    linkify-it "^4.0.1"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

marked@^4.0.18:
  version "4.0.18"

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://repository.makeblock.com/repository/npm-group/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

md-editor-v3@^4.1.1:
  version "4.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/md-editor-v3/-/md-editor-v3-4.1.1.tgz"
  integrity sha512-8/OmdnaP4ugG1SLCT0n1RJyQ8viuEBVAWKM0TmaJaOtknQ5+5GE8KlkIXbcNKKe8Ew5gZzb5dYWzmdB/vLDocw==
  dependencies:
    "@codemirror/lang-markdown" "^6.1.1"
    "@codemirror/language-data" "^6.3.0"
    "@types/markdown-it" "^12.2.3"
    "@vavt/util" "^1.1.1"
    codemirror "^6.0.1"
    copy-to-clipboard "^3.3.3"
    lru-cache "^8.0.4"
    markdown-it "^13.0.1"
    markdown-it-image-figures "^2.1.1"
    markdown-it-task-lists "^2.1.1"
    medium-zoom "^1.0.8"
    punycode "^2.3.0"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "http://repository.makeblock.com/repository/npm-group/mdn-data/-/mdn-data-2.0.14.tgz"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.30:
  version "2.0.30"
  resolved "http://repository.makeblock.com/repository/npm-group/mdn-data/-/mdn-data-2.0.30.tgz"
  integrity sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==

mdurl@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/mdurl/-/mdurl-1.0.1.tgz"
  integrity sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=

medium-zoom@^1.0.8:
  version "1.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/medium-zoom/-/medium-zoom-1.0.8.tgz"
  integrity sha512-CjFVuFq/IfrdqesAXfg+hzlDKu6A2n80ZIq0Kl9kWjoHh9j1N9Uvk5X0/MmN0hOfm5F9YBswlClhcwnmtwz7gA==

meow@^8.0.0:
  version "8.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/meow/-/meow-8.1.2.tgz"
  integrity sha1-vL5FvaDuFynTUMA8/8g5WjbE6Jc=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

meow@^9.0.0:
  version "9.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/meow/-/meow-9.0.0.tgz"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@^1.0.1:
  version "1.0.1"

merge-options@1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/merge-options/-/merge-options-1.0.1.tgz"
  integrity sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=
  dependencies:
    is-plain-obj "^1.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/merge2/-/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

micromatch@3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/micromatch/-/micromatch-3.1.0.tgz"
  integrity sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g==
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.2.2"
    define-property "^1.0.0"
    extend-shallow "^2.0.1"
    extglob "^2.0.2"
    fragment-cache "^0.2.1"
    kind-of "^5.0.2"
    nanomatch "^1.2.1"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "http://repository.makeblock.com/repository/npm-group/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@^1.4.1:
  version "1.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@^2.4.5, mime@^2.5.2:
  version "2.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mime/-/mime-2.6.0.tgz"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mime@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mime/-/mime-3.0.0.tgz"
  integrity sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/min-indent/-/min-indent-1.0.1.tgz"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimatch@^3.0.4:
  version "3.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.1.0:
  version "5.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/minimatch/-/minimatch-5.1.1.tgz"
  integrity sha512-362NP+zlprccbEt/SkxKfRMHnNY85V74mVnpUpNyr3F35covl09Kec7/sEFLt3RA4oXmewtoaanoIf67SE5Y5g==
  dependencies:
    brace-expansion "^2.0.1"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/minimist-options/-/minimist-options-4.1.0.tgz"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.1.0, minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.6"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/mixin-deep/-/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.1:
  version "0.5.6"
  resolved "http://repository.makeblock.com/repository/npm-group/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

mobius1-selectr@^2.4.13:
  version "2.4.13"
  resolved "http://repository.makeblock.com/repository/npm-group/mobius1-selectr/-/mobius1-selectr-2.4.13.tgz"
  integrity sha512-Mk9qDrvU44UUL0EBhbAA1phfQZ7aMZPjwtL7wkpiBzGh8dETGqfsh50mWoX9EkjDlkONlErWXArHCKfoxVg0Bw==

modify-values@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/modify-values/-/modify-values-1.0.1.tgz"
  integrity sha1-s5OfpgVUZHTj4+PGPWS9Q7TuYCI=

moment@^2.29.2:
  version "2.29.3"

monaco-editor@^0.45.0:
  version "0.45.0"
  resolved "http://repository.makeblock.com/repository/npm-group/monaco-editor/-/monaco-editor-0.45.0.tgz"
  integrity sha512-mjv1G1ZzfEE3k9HZN0dQ2olMdwIfaeAAjFiwNprLfYNRSz7ctv9XuCT7gPtBGrMUeV1/iZzYKj17Khu1hxoHOA==

ms@*, ms@^2.0.0, ms@2.1.2:
  version "2.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/ms/-/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

mz-modules@^2.1.0:
  version "2.1.0"
  dependencies:
    glob "^7.1.2"
    ko-sleep "^1.0.3"
    mkdirp "^0.5.1"
    pump "^3.0.0"
    rimraf "^2.6.1"

mz@^2.7.0:
  version "2.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/mz/-/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.4:
  version "3.3.4"

nanomatch@^1.2.1:
  version "1.2.13"
  resolved "http://repository.makeblock.com/repository/npm-group/nanomatch/-/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

nanopop@^2.1.0:
  version "2.1.0"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^3.1.0:
  version "3.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/needle/-/needle-3.3.1.tgz"
  integrity sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==
  dependencies:
    iconv-lite "^0.6.3"
    sax "^1.2.4"

neo-async@^2.6.2:
  version "2.6.2"
  resolved "http://repository.makeblock.com/repository/npm-group/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

netmask@^2.0.2:
  version "2.0.2"

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.2:
  version "2.0.3"

normalize-package-data@^2.3.2:
  version "2.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nprogress@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/nprogress/-/nprogress-0.2.0.tgz"
  integrity sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA==

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/nth-check/-/nth-check-2.1.1.tgz"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.0:
  version "2.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/nwsapi/-/nwsapi-2.2.2.tgz"
  integrity sha512-90yv+6538zuvUMnN+zCr8LuV6bPFdq50304114vJYJ8RDyK8D5O9Phpbd6SZWgI7PwzmmfN1upeOJlvybDSgCw==

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/object-copy/-/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.12.0, object-inspect@^1.9.0:
  version "1.12.0"

object-is@^1.0.1:
  version "1.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/object-is/-/object-is-1.1.5.tgz"
  integrity sha1-ud7qpfx/GEag+uzc7sE45XePU6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/object-visit/-/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.2"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/object.pick/-/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/onetime/-/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.3:
  version "8.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/open/-/open-8.4.2.tgz"
  integrity sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optionator@^0.8.1:
  version "0.8.3"
  resolved "http://repository.makeblock.com/repository/npm-group/optionator/-/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.1:
  version "0.9.1"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

os-name@~1.0.3:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/os-name/-/os-name-1.0.3.tgz"
  integrity sha1-GzefZINa98Wn9JizV8uVIVwVnt8=
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

osx-release@^1.0.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/osx-release/-/osx-release-1.1.0.tgz"
  integrity sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=
  dependencies:
    minimist "^1.1.0"

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-limit/-/p-limit-1.3.0.tgz"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-locate/-/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-locate/-/p-locate-3.0.0.tgz"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-map/-/p-map-4.0.0.tgz"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-try/-/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/p-try/-/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pac-proxy-agent@^5.0.0:
  version "5.0.0"
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"
    get-uri "3"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "5"
    pac-resolver "^5.0.0"
    raw-body "^2.2.0"
    socks-proxy-agent "5"

pac-resolver@^5.0.0:
  version "5.0.1"
  dependencies:
    degenerator "^3.0.2"
    ip "^1.1.5"
    netmask "^2.0.2"

parchment@^1.1.4:
  version "1.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/parchment/-/parchment-1.1.4.tgz"
  integrity sha1-rt7Xq5OP6SHUw0vDOc4RaLwv/eU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/parse-json/-/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/parse-node-version/-/parse-node-version-1.0.1.tgz"
  integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==

parse5-htmlparser2-tree-adapter@^7.0.0:
  version "7.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-7.1.0.tgz"
  integrity sha512-ruw5xyKs6lrpo9x9rCZqZZnIUntICjQAd0Wsmp396Ul9lN/h+ifgVV1x1gZHi8euej6wTfpqX8j+BFQxF0NS/g==
  dependencies:
    domhandler "^5.0.3"
    parse5 "^7.0.0"

parse5-parser-stream@^7.1.2:
  version "7.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/parse5-parser-stream/-/parse5-parser-stream-7.1.2.tgz"
  integrity sha512-JyeQc9iwFLn5TbvvqACIF/VXG6abODeB3Fwmv/TGdLk2LfbWkaySGY72at4+Ty7EkPZj854u4CrICqNk2qIbow==
  dependencies:
    parse5 "^7.0.0"

parse5@^7.0.0, parse5@^7.1.2:
  version "7.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/parse5/-/parse5-7.2.1.tgz"
  integrity sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==
  dependencies:
    entities "^4.5.0"

parse5@6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/parse5/-/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/pascalcase/-/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/path-exists/-/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/path-key/-/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://repository.makeblock.com/repository/npm-group/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/path-type/-/path-type-3.0.0.tgz"
  integrity sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/path-type/-/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pathe@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pathe/-/pathe-0.2.0.tgz"
  integrity sha1-MP17vgoNkfDmC65iH10Z6eIlwzk=

pause-stream@~0.0.11:
  version "0.0.11"
  resolved "http://repository.makeblock.com/repository/npm-group/pause-stream/-/pause-stream-0.0.11.tgz"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomodal@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/picomodal/-/picomodal-3.0.0.tgz"
  integrity sha512-FoR3TDfuLlqUvcEeK5ifpKSVVns6B4BQvc8SDF6THVMuadya6LLtji0QgUDSStw0ZR2J7I6UGi5V2V23rnPWTw==

pidtree@^0.5.0:
  version "0.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pidtree/-/pidtree-0.5.0.tgz"
  integrity sha1-rV+8HeeLil+Z1vvdT25O7iHRrKE=

pify@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pify/-/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pify/-/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

pirates@^4.0.4:
  version "4.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/pirates/-/pirates-4.0.5.tgz"
  integrity sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

platform@^1.3.1:
  version "1.3.6"
  resolved "http://repository.makeblock.com/repository/npm-group/platform/-/platform-1.3.6.tgz"
  integrity sha1-SLTOmDFksgnC1FoQetsx9HOm56c=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-html@^1.0.0, postcss-html@^1.3.0:
  version "1.3.1"
  dependencies:
    htmlparser2 "^7.1.2"
    postcss "^8.4.0"
    postcss-safe-parser "^6.0.0"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-prefix-selector@^1.6.0:
  version "1.16.0"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-prefix-selector/-/postcss-prefix-selector-1.16.0.tgz"
  integrity sha512-rdVMIi7Q4B0XbXqNUEI+Z4E+pueiu/CS5E6vRCQommzdQ/sgsS4dK42U7GX8oJR+TJOtT+Qv3GkNo6iijUMp3Q==

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz"
  integrity sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.11:
  version "6.0.11"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz"
  integrity sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-sorting@^7.0.1:
  version "7.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-sorting/-/postcss-sorting-7.0.1.tgz"
  integrity sha1-kjtSaEUc8tk+v4g14XplN3VwSaU=

postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^5.2.17:
  version "5.2.18"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss/-/postcss-5.2.18.tgz"
  integrity sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^8.1.10, postcss@^8.2, postcss@^8.3.11, postcss@^8.3.3, postcss@^8.3.9, postcss@^8.4.0, postcss@^8.4.13, postcss@^8.4.19, "postcss@>4 <9":
  version "8.4.19"
  resolved "http://repository.makeblock.com/repository/npm-group/postcss/-/postcss-8.4.19.tgz"
  integrity sha512-h+pbPsyhlYj6N2ozBmHhHrs9DzGmbaarbLvWipMRO7RLS+v4onj26MPFXA5OBYFxyqYhUJK456SwDcY9H2/zsA==
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

posthtml-parser@^0.2.0, posthtml-parser@^0.2.1:
  version "0.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/posthtml-parser/-/posthtml-parser-0.2.1.tgz"
  integrity sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=
  dependencies:
    htmlparser2 "^3.8.3"
    isobject "^2.1.0"

posthtml-rename-id@^1.0:
  version "1.0.12"
  resolved "http://repository.makeblock.com/repository/npm-group/posthtml-rename-id/-/posthtml-rename-id-1.0.12.tgz"
  integrity sha1-z39us3FGvxr6wx5o8YxswZrmFDM=
  dependencies:
    escape-string-regexp "1.0.5"

posthtml-render@^1.0.5, posthtml-render@^1.0.6:
  version "1.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/posthtml-render/-/posthtml-render-1.4.0.tgz"
  integrity sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=

posthtml-svg-mode@^1.0.3:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/posthtml-svg-mode/-/posthtml-svg-mode-1.0.3.tgz"
  integrity sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=
  dependencies:
    merge-options "1.0.1"
    posthtml "^0.9.2"
    posthtml-parser "^0.2.1"
    posthtml-render "^1.0.6"

posthtml@^0.9.2:
  version "0.9.2"
  resolved "http://repository.makeblock.com/repository/npm-group/posthtml/-/posthtml-0.9.2.tgz"
  integrity sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=
  dependencies:
    posthtml-parser "^0.2.0"
    posthtml-render "^1.0.5"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/prelude-ls/-/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.5.1, prettier@>=2.0.0:
  version "2.8.0"
  resolved "http://repository.makeblock.com/repository/npm-group/prettier/-/prettier-2.8.0.tgz"
  integrity sha512-9Lmg8hTFZKG0Asr/kW9Bp8tJjRVluO8EJQVfY2T7FMw9T5jy4I/Uvx0Rca/XWf50QQ1/SS48+6IJWnrb+2yemA==

pretty-format@^27.0.0, pretty-format@^27.0.2, pretty-format@^27.5.1:
  version "27.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/pretty-format/-/pretty-format-27.5.1.tgz"
  integrity sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "http://repository.makeblock.com/repository/npm-group/process/-/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

prompts@^2.0.1:
  version "2.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/prompts/-/prompts-2.4.2.tgz"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

proxy-agent@^5.0.0:
  version "5.0.0"
  dependencies:
    agent-base "^6.0.0"
    debug "4"
    http-proxy-agent "^4.0.0"
    https-proxy-agent "^5.0.0"
    lru-cache "^5.1.1"
    pac-proxy-agent "^5.0.0"
    proxy-from-env "^1.0.0"
    socks-proxy-agent "^5.0.0"

proxy-from-env@^1.0.0, proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

prr@~1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/prr/-/prr-1.0.1.tgz"
  integrity sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==

psl@^1.1.33:
  version "1.8.0"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/pump/-/pump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/punycode/-/punycode-2.3.0.tgz"
  integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==

q@^1.5.1:
  version "1.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/q/-/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@^6.4.0:
  version "6.10.3"
  dependencies:
    side-channel "^1.0.4"

query-string@^4.3.2:
  version "4.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/query-string/-/query-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/quick-lru/-/quick-lru-4.0.1.tgz"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "http://repository.makeblock.com/repository/npm-group/quill-delta/-/quill-delta-3.6.3.tgz"
  integrity sha1-sZ/SuJQSMBxg4f8hPY2GDqwPEDI=
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill-delta@^4.2.2:
  version "4.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/quill-delta/-/quill-delta-4.2.2.tgz"
  integrity sha1-AVOX0Ebgo77Qh82KUfmMEaG481E=
  dependencies:
    fast-diff "1.2.0"
    lodash.clonedeep "^4.5.0"
    lodash.isequal "^4.5.0"

quill@^1.3.7:
  version "1.3.7"
  resolved "http://repository.makeblock.com/repository/npm-group/quill/-/quill-1.3.7.tgz"
  integrity sha1-2lsvOixHDpMjQM2/NmjJ8h+Shug=
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

raw-body@^2.2.0:
  version "2.5.1"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^17.0.1:
  version "17.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/react-is/-/react-is-17.0.2.tgz"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/read-pkg-up/-/read-pkg-up-3.0.0.tgz"
  integrity sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/read-pkg/-/read-pkg-3.0.0.tgz"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/read-pkg/-/read-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.3.6:
  version "2.3.7"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.0, readable-stream@^3.0.2, readable-stream@^3.1.1, readable-stream@3:
  version "3.6.0"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@1.1.x:
  version "1.1.14"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/redent/-/redent-3.0.0.tgz"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

regenerate-unicode-properties@^10.0.1:
  version "10.0.1"
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://repository.makeblock.com/repository/npm-group/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.13.4:
  version "0.13.9"

regenerator-transform@^0.15.0:
  version "0.15.0"
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/regex-not/-/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.4.3"
  resolved "http://repository.makeblock.com/repository/npm-group/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz"
  integrity sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regexpp@^3.2.0:
  version "3.2.0"

regexpu-core@^5.0.1:
  version "5.0.1"
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.0.1"
    regjsgen "^0.6.0"
    regjsparser "^0.8.2"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.6.0:
  version "0.6.0"

regjsparser@^0.8.2:
  version "0.8.4"
  dependencies:
    jsesc "~0.5.0"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "http://repository.makeblock.com/repository/npm-group/repeat-element/-/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/repeat-string/-/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-detector@^0.3.0:
  version "0.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resize-detector/-/resize-detector-0.3.0.tgz"
  integrity sha512-R/tCuvuOHQ8o2boRP6vgx8hXCCy87H1eY9V5imBYeVNyNVpuL9ciReSccLj2gDcax9+2weXy3bc8Vv+NRXeEvQ==

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://repository.makeblock.com/repository/npm-group/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0, resolve-from@5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-global@^1.0.0, resolve-global@1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve-global/-/resolve-global-1.0.0.tgz"
  integrity sha1-oqed9K8so/Sb93753azTItrRklU=
  dependencies:
    global-dirs "^0.1.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve-url/-/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve.exports@^1.1.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve.exports/-/resolve.exports-1.1.0.tgz"
  integrity sha1-XOhCuUsFFGwOAwdphdHQ5+SMkMk=

resolve@^1.10.0, resolve@^1.14.2, resolve@^1.20.0, resolve@^1.22.0:
  version "1.22.1"
  resolved "http://repository.makeblock.com/repository/npm-group/resolve/-/resolve-1.22.1.tgz"
  integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://repository.makeblock.com/repository/npm-group/ret/-/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/reusify/-/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/rfdc/-/rfdc-1.3.0.tgz"
  integrity sha1-0LfEQasnINBdxM8m4ByJYx2doIs=

rimraf@^2.6.1:
  version "2.7.1"
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup@^2.50.0, "rollup@>=2.59.0 <2.78.0":
  version "2.77.3"
  resolved "http://repository.makeblock.com/repository/npm-group/rollup/-/rollup-2.77.3.tgz"
  integrity sha512-/qxNTG7FbmefJWoeeYJFbHehJ2HNWnjkAFRKzWN/45eNBBF/r8lo992CwcJXEzyVxs5FmfId+vTSTQDb+bxA+g==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.5.5:
  version "7.5.5"
  dependencies:
    tslib "^2.1.0"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/safe-regex/-/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass@*, sass@^1.50.0:
  version "1.50.0"
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@^1.2.4, sax@>=0.6.0:
  version "1.2.4"

saxes@^5.0.1:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/saxes/-/saxes-5.0.1.tgz"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

scroll-into-view-if-needed@^2.2.25:
  version "2.2.29"
  dependencies:
    compute-scroll-into-view "^1.0.17"

sdk-base@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/sdk-base/-/sdk-base-2.0.1.tgz"
  integrity sha1-ukAonovfJy7RHdnql+r5jgNtJMY=
  dependencies:
    get-ready "~1.0.0"

semver@^5.0.1:
  version "5.7.1"

semver@^5.6.0:
  version "5.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"

semver@^7.1.1:
  version "7.5.4"
  resolved "http://repository.makeblock.com/repository/npm-group/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.2:
  version "7.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/semver/-/semver-7.3.8.tgz"
  integrity sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.4:
  version "7.3.7"
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.5:
  version "7.3.7"
  dependencies:
    lru-cache "^6.0.0"

"semver@2 || 3 || 4 || 5":
  version "5.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@7.0.0:
  version "7.0.0"

semver@7.3.5:
  version "7.3.5"
  dependencies:
    lru-cache "^6.0.0"

semver@7.x:
  version "7.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/semver/-/semver-7.3.8.tgz"
  integrity sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==
  dependencies:
    lru-cache "^6.0.0"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/set-value/-/set-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.2.0:
  version "1.2.0"

shallow-equal@^1.0.0:
  version "1.2.1"
  resolved "http://repository.makeblock.com/repository/npm-group/shallow-equal/-/shallow-equal-1.2.1.tgz"
  integrity sha1-TBar+lYEOqINBQMk76aJQLDaedo=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shvl@^2.0.3:
  version "2.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/shvl/-/shvl-2.0.3.tgz"
  integrity sha512-V7C6S9Hlol6SzOJPnQ7qzOVEWUQImt3BNmmzh40wObhla3XOYMe4gGiYzLrJd5TFa+cI2f9LKIRJTTKZSTbWgw==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "http://repository.makeblock.com/repository/npm-group/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/slash/-/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/slice-ansi/-/slice-ansi-3.0.0.tgz"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/slice-ansi/-/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/slice-ansi/-/slice-ansi-5.0.0.tgz"
  integrity sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

smart-buffer@^4.2.0:
  version "4.2.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://repository.makeblock.com/repository/npm-group/snapdragon/-/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socks-proxy-agent@^5.0.0, socks-proxy-agent@5:
  version "5.0.1"
  dependencies:
    agent-base "^6.0.2"
    debug "4"
    socks "^2.3.3"

socks@^2.3.3:
  version "2.6.2"
  dependencies:
    ip "^1.1.5"
    smart-buffer "^4.2.0"

source-map-js@^1.0.1, source-map-js@^1.0.2, "source-map-js@>=0.6.2 <2.0.0":
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map-js/-/source-map-js-1.0.2.tgz"
  integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.6:
  version "0.5.21"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map-url/-/source-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"

source-map@^0.6.0:
  version "0.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.6.1:
  version "0.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.7.3:
  version "0.7.4"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.7.4.tgz"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

source-map@~0.6.0:
  version "0.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@~0.6.1:
  version "0.6.1"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@0.5.6:
  version "0.5.6"
  resolved "http://repository.makeblock.com/repository/npm-group/source-map/-/source-map-0.5.6.tgz"
  integrity sha1-dc449SvwczxafwwRjYEzSiu19BI=

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "http://repository.makeblock.com/repository/npm-group/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

spark-md5@^3.0.2:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/spark-md5/-/spark-md5-3.0.2.tgz"
  integrity sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==

spdx-correct@^3.0.0:
  version "3.1.1"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.11"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/split-string/-/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/split/-/split-1.0.1.tgz"
  integrity sha512-mTyOoPbrivtXnwnIxZRFYRrPNtEFKlpB2fvjSnCQUiAA6qAZzqwna5envK4uk6OIeP17CsdF3rSBGYVBsU0Tkg==
  dependencies:
    through "2"

split2@^3.0.0:
  version "3.2.2"
  resolved "http://repository.makeblock.com/repository/npm-group/split2/-/split2-3.2.2.tgz"
  integrity sha1-vyzyo32DgxLCSciSBv16F90SNl8=
  dependencies:
    readable-stream "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stable@^0.1.8:
  version "0.1.8"
  resolved "http://repository.makeblock.com/repository/npm-group/stable/-/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/stack-utils/-/stack-utils-2.0.6.tgz"
  integrity sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==
  dependencies:
    escape-string-regexp "^2.0.0"

standard-version@^9.5.0:
  version "9.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/standard-version/-/standard-version-9.5.0.tgz"
  integrity sha512-3zWJ/mmZQsOaO+fOlsa0+QK90pwhNd042qEcw6hKFNoLFs7peGyvPffpEBbK/DSGPbyOvli0mUIFv5A4qTjh2Q==
  dependencies:
    chalk "^2.4.2"
    conventional-changelog "3.1.25"
    conventional-changelog-config-spec "2.1.0"
    conventional-changelog-conventionalcommits "4.6.3"
    conventional-recommended-bump "6.1.0"
    detect-indent "^6.0.0"
    detect-newline "^3.1.0"
    dotgitignore "^2.1.0"
    figures "^3.1.0"
    find-up "^5.0.0"
    git-semver-tags "^4.0.0"
    semver "^7.1.1"
    stringify-package "^1.0.1"
    yargs "^16.0.0"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/static-extend/-/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@^1.3.1:
  version "1.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/statuses/-/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

statuses@2.0.1:
  version "2.0.1"

stream-http@2.8.2:
  version "2.8.2"
  resolved "http://repository.makeblock.com/repository/npm-group/stream-http/-/stream-http-2.8.2.tgz"
  integrity sha1-QSboxrEHAERlkYqi/DVUnndALIc=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-wormhole@^1.0.4:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stream-wormhole/-/stream-wormhole-1.1.0.tgz"
  integrity sha1-MAr/Rs7VU8/sZCoFJRiFQXaTwz0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string-argv@^0.3.1:
  version "0.3.1"

string-length@^4.0.1:
  version "4.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/string-length/-/string-length-4.0.2.tgz"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0:
  version "4.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.2.0:
  version "4.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.2.3:
  version "4.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0:
  version "5.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

stringify-package@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/stringify-package/-/stringify-package-1.0.1.tgz"
  integrity sha1-5ao2Q+f3TQ8oYoty89rVzs/DuoU=

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-ansi/-/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.0.1"
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-indent/-/strip-indent-3.0.0.tgz"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^2.0.0:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-mod@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/style-mod/-/style-mod-4.0.0.tgz"
  integrity sha512-OPhtyEjyyN9x3nhPsu76f52yUGXiZcgvsrFVtvTkyGRQJ0XK+GPc6ov1z+lRpbeabka+MYEQxOYRnt5nF30aMw==

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/style-search/-/style-search-0.1.0.tgz"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylelint-config-html@^1.0.0, stylelint-config-html@>=1.0.0:
  version "1.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-config-html/-/stylelint-config-html-1.1.0.tgz"
  integrity sha512-IZv4IVESjKLumUGi+HWeb7skgO6/g4VMuAYrJdlqQFndgbj6WJAXPhaysvBiXefX79upBdQVumgYcdd17gCpjQ==

stylelint-config-recess-order@^3.0.0:
  version "3.0.0"
  dependencies:
    stylelint-order "5.x"

stylelint-config-recommended-vue@^1.1.0:
  version "1.4.0"
  dependencies:
    semver "^7.3.5"
    stylelint-config-html ">=1.0.0"
    stylelint-config-recommended ">=6.0.0"

stylelint-config-recommended@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-config-recommended/-/stylelint-config-recommended-6.0.0.tgz"
  integrity sha1-/SUjoyKDYAWtm/Rz0+VTRxnAn50=

stylelint-config-recommended@>=6.0.0:
  version "9.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-config-recommended/-/stylelint-config-recommended-9.0.0.tgz"
  integrity sha512-9YQSrJq4NvvRuTbzDsWX3rrFOzOlYBmZP+o513BJN/yfEmGSr0AxdvrWs0P/ilSpVV/wisamAHu5XSk8Rcf4CQ==

stylelint-config-standard@^24.0.0:
  version "24.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-config-standard/-/stylelint-config-standard-24.0.0.tgz"
  integrity sha1-aCPyB6uZeuC2QfmmNtAHzETXdUE=
  dependencies:
    stylelint-config-recommended "^6.0.0"

stylelint-declaration-block-no-ignored-properties@^2.5.0:
  version "2.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-declaration-block-no-ignored-properties/-/stylelint-declaration-block-no-ignored-properties-2.6.0.tgz"
  integrity sha512-S9EC/tVJL19ppMRC4A4ecxtkENHZ7WNrEAukJVDtFt+iZgNP3SmokOLlYUhe6qZuB2XUvETqUx6r2p3Xfo7Rxw==

stylelint-order@^5.0.0, stylelint-order@5.x:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint-order/-/stylelint-order-5.0.0.tgz"
  integrity sha1-q9IPa4WsZAd0y+QOcNP+nG/fRAA=
  dependencies:
    postcss "^8.3.11"
    postcss-sorting "^7.0.1"

stylelint@^14.0.0, stylelint@^14.10.0, stylelint@^14.2.0, "stylelint@^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0 || ^12.0.0 || ^13.0.0 || ^14.0.0", stylelint@>=14, stylelint@>=14.0.0:
  version "14.16.0"
  resolved "http://repository.makeblock.com/repository/npm-group/stylelint/-/stylelint-14.16.0.tgz"
  integrity sha512-X6uTi9DcxjzLV8ZUAjit1vsRtSwcls0nl07c9rqOPzvpA8IvTX/xWEkBRowS0ffevRrqkHa/ThDEu86u73FQDg==
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^7.1.0"
    css-functions-list "^3.1.0"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^6.0.1"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.2.0"
    ignore "^5.2.1"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.26.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.19"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^6.0.0"
    postcss-selector-parser "^6.0.11"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    style-search "^0.1.0"
    supports-hyperlinks "^2.3.0"
    svg-tags "^1.0.0"
    table "^6.8.1"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^4.0.2"

stylis@^4.1.3:
  version "4.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/stylis/-/stylis-4.3.4.tgz"
  integrity sha512-osIBl6BGUmSfDkyH2mB7EFvCJntXDrLhKjHTRj/rK6xLH0yuPrHULDRQzKokSOD4VoorhtKpfcfW1GAntu8now==

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.2.3:
  version "3.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-3.2.3.tgz"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0:
  version "7.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-color@^9.2.1:
  version "9.2.2"

supports-hyperlinks@^2.0.0, supports-hyperlinks@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz"
  integrity sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-baker@1.7.0:
  version "1.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/svg-baker/-/svg-baker-1.7.0.tgz"
  integrity sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=
  dependencies:
    bluebird "^3.5.0"
    clone "^2.1.1"
    he "^1.1.1"
    image-size "^0.5.1"
    loader-utils "^1.1.0"
    merge-options "1.0.1"
    micromatch "3.1.0"
    postcss "^5.2.17"
    postcss-prefix-selector "^1.6.0"
    posthtml-rename-id "^1.0"
    posthtml-svg-mode "^1.0.3"
    query-string "^4.3.2"
    traverse "^0.6.6"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/svg-tags/-/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^2.8.0:
  version "2.8.0"
  resolved "http://repository.makeblock.com/repository/npm-group/svgo/-/svgo-2.8.0.tgz"
  integrity sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://repository.makeblock.com/repository/npm-group/symbol-tree/-/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

table@^6.8.1:
  version "6.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/table/-/table-6.8.1.tgz"
  integrity sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA==
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/terminal-link/-/terminal-link-2.1.1.tgz"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "http://repository.makeblock.com/repository/npm-group/text-extensions/-/text-extensions-1.9.0.tgz"
  integrity sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/text-table/-/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/thenify/-/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

throat@^6.0.1:
  version "6.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/throat/-/throat-6.0.1.tgz"
  integrity sha1-1RT+2tlXQMEsLX/HDqhj61Gt43U=

throttle-debounce@^5.0.0:
  version "5.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/throttle-debounce/-/throttle-debounce-5.0.2.tgz"
  integrity sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==

through@^2.3.8, "through@>=2.2.7 <3", through@~2.3, through@2:
  version "2.3.8"
  resolved "http://repository.makeblock.com/repository/npm-group/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

through2@^2.0.0:
  version "2.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/through2/-/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through2@^4.0.0:
  version "4.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/through2/-/through2-4.0.2.tgz"
  integrity sha1-p846wqeosLlmyA58SfBITDsjl2Q=
  dependencies:
    readable-stream "3"

tmpl@1.0.5:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/to-object-path/-/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/to-regex-range/-/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/to-regex/-/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toidentifier@1.0.1:
  version "1.0.1"

tough-cookie@^4.0.0:
  version "4.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/tough-cookie/-/tough-cookie-4.1.2.tgz"
  integrity sha512-G9fqXWoYFZgTc2z8Q5zaHy/vJMjm+WV0AkAeHxVCQiEB1b+dGvWzFW6QV07cY5jQ5gRkeid2qIkzkxUnmoQZUQ==
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^2.1.0:
  version "2.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/tr46/-/tr46-2.1.0.tgz"
  integrity sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/tr46/-/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

traverse@^0.6.6:
  version "0.6.8"
  resolved "http://repository.makeblock.com/repository/npm-group/traverse/-/traverse-0.6.8.tgz"
  integrity sha512-aXJDbk6SnumuaZSANd21XAo15ucCDE38H4fkqiGsc3MhCK+wOlZvLP9cB/TvpHT0mOyWgC4Z8EwRlzqYSUzdsA==

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/trim-newlines/-/trim-newlines-3.0.1.tgz"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

ts-jest@^27.1.2, ts-jest@27.x:
  version "27.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/ts-jest/-/ts-jest-27.1.5.tgz"
  integrity sha512-Xv6jBQPoBEvBq/5i2TeSG9tt/nqkbpcurrEG1b+2yfBrcJelOZF9Ml6dmyMh7bcW9JyFbRYpR5rxROSlBLTZHA==
  dependencies:
    bs-logger "0.x"
    fast-json-stable-stringify "2.x"
    jest-util "^27.0.0"
    json5 "2.x"
    lodash.memoize "4.x"
    make-error "1.x"
    semver "7.x"
    yargs-parser "20.x"

ts-node@^10.7.0, ts-node@>=9.0.0:
  version "10.7.0"
  dependencies:
    "@cspotcode/source-map-support" "0.7.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.0"
    yn "3.1.1"

tsconfig@^7.0.0:
  version "7.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/tsconfig/-/tsconfig-7.0.0.tgz"
  integrity sha1-hFOIdaTcIW5cSlQys6Tew9VOkbc=
  dependencies:
    "@types/strip-bom" "^3.0.0"
    "@types/strip-json-comments" "0.0.30"
    strip-bom "^3.0.0"
    strip-json-comments "^2.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "http://repository.makeblock.com/repository/npm-group/tslib/-/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.1, tslib@^2.1.0, tslib@^2.2.0, tslib@^2.3.0:
  version "2.3.1"

tslib@2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/tslib/-/tslib-2.3.0.tgz"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://repository.makeblock.com/repository/npm-group/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel@^0.0.6:
  version "0.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/tunnel/-/tunnel-0.0.6.tgz"
  integrity sha1-cvExSzSlsZLbASMk3yzFh8pH+Sw=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://repository.makeblock.com/repository/npm-group/type-check/-/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/type-check/-/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://repository.makeblock.com/repository/npm-group/type-fest/-/type-fest-0.18.1.tgz"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://repository.makeblock.com/repository/npm-group/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://repository.makeblock.com/repository/npm-group/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://repository.makeblock.com/repository/npm-group/type-fest/-/type-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://repository.makeblock.com/repository/npm-group/type-fest/-/type-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://repository.makeblock.com/repository/npm-group/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@*, typescript@^4.4.3, "typescript@>= 3.x", typescript@>=2.7, "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta", typescript@>=3, "typescript@>=3.8 <5.0", typescript@>=4, typescript@4.9.5:
  version "4.9.5"
  resolved "http://repository.makeblock.com/repository/npm-group/typescript/-/typescript-4.9.5.tgz"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "http://repository.makeblock.com/repository/npm-group/uc.micro/-/uc.micro-1.0.6.tgz"
  integrity sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw=

uglify-js@^3.1.4:
  version "3.17.4"
  resolved "http://repository.makeblock.com/repository/npm-group/uglify-js/-/uglify-js-3.17.4.tgz"
  integrity sha512-T9q82TJI9e/C1TAxYvfb16xO120tMVFZrGA3f9/P4424DNu6ypK103y0GPFVa17yotwSyZW5iYXgjYHkGrJW/g==

undici-types@~5.26.4:
  version "5.26.5"
  resolved "http://repository.makeblock.com/repository/npm-group/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

undici@^6.19.5:
  version "6.21.0"
  resolved "http://repository.makeblock.com/repository/npm-group/undici/-/undici-6.21.0.tgz"
  integrity sha512-BUgJXc752Kou3oOIuU1i+yZZypyZRqNPW0vqoMPl8VaoalSfeR0D8/t4iAS3yirs79SSMTxTag+ZC86uswv+Cw==

unescape@^1.0.1:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/unescape/-/unescape-1.0.1.tgz"
  integrity sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY=
  dependencies:
    extend-shallow "^2.0.1"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/union-value/-/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.0:
  version "0.1.2"

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/universalify/-/universalify-0.2.0.tgz"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

universalify@^2.0.0:
  version "2.0.0"

unpipe@1.0.0:
  version "1.0.0"

unplugin-vue-components@^0.19.9:
  version "0.19.9"
  resolved "http://repository.makeblock.com/repository/npm-group/unplugin-vue-components/-/unplugin-vue-components-0.19.9.tgz"
  integrity sha512-i5mZtg85euPWZrGswFkoa9pf4WjKCP5qOjnwOyg3KOKVzFjnP3osCdrunQMjtoMKehTdz1vV6baZH8bZR4PNgg==
  dependencies:
    "@antfu/utils" "^0.5.2"
    "@rollup/pluginutils" "^4.2.1"
    chokidar "^3.5.3"
    debug "^4.3.4"
    fast-glob "^3.2.11"
    local-pkg "^0.4.1"
    magic-string "^0.26.2"
    minimatch "^5.1.0"
    resolve "^1.22.0"
    unplugin "^0.7.0"

unplugin@^0.7.0:
  version "0.7.2"
  resolved "http://repository.makeblock.com/repository/npm-group/unplugin/-/unplugin-0.7.2.tgz"
  integrity sha512-m7thX4jP8l5sETpLdUASoDOGOcHaOVtgNyrYlToyQUvILUtEzEnngRBrHnAX3IKqooJVmXpoa/CwQ/QqzvGaHQ==
  dependencies:
    acorn "^8.7.1"
    chokidar "^3.5.3"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.4.4"

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/unset-value/-/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://repository.makeblock.com/repository/npm-group/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/urix/-/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://repository.makeblock.com/repository/npm-group/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

urllib@^2.33.1:
  version "2.38.0"
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.9"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    formstream "^1.1.0"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    ip "^1.1.5"
    proxy-agent "^5.0.0"
    pump "^3.0.0"
    qs "^6.4.0"
    statuses "^1.3.1"
    utility "^1.16.1"

use@^3.1.0:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/use/-/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utility@^1.16.1, utility@^1.8.0:
  version "1.17.0"
  dependencies:
    copy-to "^2.0.1"
    escape-html "^1.0.3"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    unescape "^1.0.1"

utility@0.1.11:
  version "0.1.11"
  dependencies:
    address ">=0.0.1"

uuid@^8.3.0:
  version "8.3.2"
  resolved "http://repository.makeblock.com/repository/npm-group/uuid/-/uuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@^9.0.0:
  version "9.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/uuid/-/uuid-9.0.0.tgz"
  integrity sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==

v8-compile-cache-lib@^3.0.0:
  version "3.0.0"

v8-compile-cache@^2.0.3, v8-compile-cache@^2.3.0:
  version "2.3.0"

v8-to-istanbul@^8.1.0:
  version "8.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/v8-to-istanbul/-/v8-to-istanbul-8.1.1.tgz"
  integrity sha512-FGtKtv3xIpR6BYhvgH8MI/y78oT7d8Au3ww4QIxymrCtZEh5b8gCw2siywE+puhEmuWKDtmfrvF5UlB298ut3w==
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://repository.makeblock.com/repository/npm-group/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vanilla-picker@^2.12.1:
  version "2.12.1"
  dependencies:
    "@sphinxxxx/color-conversion" "^2.2.2"

vary@^1:
  version "1.1.2"
  resolved "http://repository.makeblock.com/repository/npm-group/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vite-plugin-qiankun@^1.0.15:
  version "1.0.15"
  resolved "http://repository.makeblock.com/repository/npm-group/vite-plugin-qiankun/-/vite-plugin-qiankun-1.0.15.tgz"
  integrity sha512-0QB0Wr8Eu/LGcuJAfuNXDb7BAFDszo3GCxq4bzgXdSFAlK425u1/UGMxaDEBVA1uPFrLsZPzig83Ufdfl6J45A==
  dependencies:
    cheerio "^1.0.0-rc.10"

vite-plugin-svg-icons@^2.0.1:
  version "2.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/vite-plugin-svg-icons/-/vite-plugin-svg-icons-2.0.1.tgz"
  integrity sha512-6ktD+DhV6Rz3VtedYvBKKVA2eXF+sAQVaKkKLDSqGUfnhqXl3bj5PPkVTl3VexfTuZy66PmINi8Q6eFnVfRUmA==
  dependencies:
    "@types/svgo" "^2.6.1"
    cors "^2.8.5"
    debug "^4.3.3"
    etag "^1.8.1"
    fs-extra "^10.0.0"
    pathe "^0.2.0"
    svg-baker "1.7.0"
    svgo "^2.8.0"

"vite@^2.3.0 || ^3.0.0-0", vite@^2.5.10, vite@^2.9.9, vite@>=2, vite@>=2.0.0:
  version "2.9.15"
  resolved "http://repository.makeblock.com/repository/npm-group/vite/-/vite-2.9.15.tgz"
  integrity sha512-fzMt2jK4vQ3yK56te3Kqpkaeq9DkcZfBbzHwYpobasvgYmP2SoAr6Aic05CsB4CzCZbsDv4sujX3pkEGhLabVQ==
  dependencies:
    esbuild "^0.14.27"
    postcss "^8.4.13"
    resolve "^1.22.0"
    rollup ">=2.59.0 <2.78.0"
  optionalDependencies:
    fsevents "~2.3.2"

vm2@^3.9.8:
  version "3.9.9"
  dependencies:
    acorn "^8.7.0"
    acorn-walk "^8.2.0"

vue-demi@^0.12.1:
  version "0.12.5"

vue-demi@>=0.14.6:
  version "0.14.6"
  resolved "http://repository.makeblock.com/repository/npm-group/vue-demi/-/vue-demi-0.14.6.tgz"
  integrity sha512-8QA7wrYSHKaYgUxDA5ZC24w+eHm3sYCbp0EzcDwKqN3p6HqtTCGR/GVsPyZW92unff4UlcSh++lmqDWN3ZIq4w==

vue-echarts@^6.0.3:
  version "6.0.3"
  dependencies:
    resize-detector "^0.3.0"
    vue-demi "^0.12.1"

vue-eslint-parser@^8.0.1, vue-eslint-parser@^8.2.0:
  version "8.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz"
  integrity sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==
  dependencies:
    debug "^4.3.2"
    eslint-scope "^7.0.0"
    eslint-visitor-keys "^3.1.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

vue-router@^4.0.14:
  version "4.0.14"
  dependencies:
    "@vue/devtools-api" "^6.0.0"

vue-tsc@^0.34.7:
  version "0.34.17"
  resolved "http://repository.makeblock.com/repository/npm-group/vue-tsc/-/vue-tsc-0.34.17.tgz"
  integrity sha512-jzUXky44ZLHC4daaJag7FQr3idlPYN719/K1eObGljz5KaS2UnVGTU/XSYCd7d6ampYYg4OsyalbHyJIxV0aEQ==
  dependencies:
    "@volar/vue-typescript" "0.34.17"

vue-types@^3.0.0:
  version "3.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/vue-types/-/vue-types-3.0.2.tgz"
  integrity sha1-7BbgXUEsA4Ji/B76TOuWR+f7YB0=
  dependencies:
    is-plain-object "3.0.1"

vue-types@^5.1.1:
  version "5.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/vue-types/-/vue-types-5.1.1.tgz"
  integrity sha512-FMY/JCLWePXgGIcMDqYdJsQm1G0CDxEjq6W0+tZMJZlX37q/61eSGSIa/XFRwa9T7kkKXuxxl94/2kgxyWQqKw==
  dependencies:
    is-plain-object "5.0.0"

"vue@^2.0.0 || ^3.0.0", "vue@^2.6.12 || ^3.1.1", vue@^3.0.0, vue@^3.0.0-0, "vue@^3.0.0-0 || ^2.6.0", vue@^3.0.1, vue@^3.0.2, vue@^3.2.0, vue@^3.2.25, vue@^3.2.41, vue@^3.2.47, "vue@>= 3", vue@>=3.0.0, vue@>=3.0.3, vue@>=3.2.0, "vue@2 || 3", vue@3.3.4:
  version "3.3.4"
  resolved "http://repository.makeblock.com/repository/npm-group/vue/-/vue-3.3.4.tgz"
  integrity sha512-VTyEYn3yvIeY1Py0WaYGZsXnz3y5UnGi62GjVEqvEGPl6nxbOrCXbVOTQWBEJUqAyTUk2uJ5JLVnYJ6ZzGbrSw==
  dependencies:
    "@vue/compiler-dom" "3.3.4"
    "@vue/compiler-sfc" "3.3.4"
    "@vue/runtime-dom" "3.3.4"
    "@vue/server-renderer" "3.3.4"
    "@vue/shared" "3.3.4"

vuex-persistedstate@^4.1.0:
  version "4.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/vuex-persistedstate/-/vuex-persistedstate-4.1.0.tgz"
  integrity sha512-3SkEj4NqwM69ikJdFVw6gObeB0NHyspRYMYkR/EbhR0hbvAKyR5gksVhtAfY1UYuWUOCCA0QNGwv9pOwdj+XUQ==
  dependencies:
    deepmerge "^4.2.2"
    shvl "^2.0.3"

"vuex@^3.0 || ^4.0.0-rc", vuex@^4.0.2:
  version "4.0.2"
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-keyname@^2.2.4:
  version "2.2.6"
  resolved "http://repository.makeblock.com/repository/npm-group/w3c-keyname/-/w3c-keyname-2.2.6.tgz"
  integrity sha512-f+fciywl1SJEniZHD6H+kUO8gOnwIr7f4ijKA6+ZvJFjeGi1r4PDLl53Ayud9O/rk64RqgoQine0feoeOU0kXg==

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7:
  version "1.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/walker/-/walker-1.0.8.tgz"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

warning@^4.0.0:
  version "4.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/warning/-/warning-4.0.3.tgz"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "http://repository.makeblock.com/repository/npm-group/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-virtual-modules@^0.4.4:
  version "0.4.6"
  resolved "http://repository.makeblock.com/repository/npm-group/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz"
  integrity sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-encoding@^3.1.1:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz"
  integrity sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==
  dependencies:
    iconv-lite "0.6.3"

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz"
  integrity sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "http://repository.makeblock.com/repository/npm-group/whatwg-url/-/whatwg-url-8.7.0.tgz"
  integrity sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which@^1.3.1:
  version "1.3.1"
  resolved "http://repository.makeblock.com/repository/npm-group/which/-/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/which/-/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

win-release@^1.0.0:
  version "1.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/win-release/-/win-release-1.1.1.tgz"
  integrity sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=
  dependencies:
    semver "^5.0.1"

word-wrap@^1.2.3, word-wrap@~1.2.3:
  version "1.2.3"

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/wordwrap/-/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "http://repository.makeblock.com/repository/npm-group/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/write-file-atomic/-/write-file-atomic-4.0.2.tgz"
  integrity sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@^7.4.6:
  version "7.5.9"
  resolved "http://repository.makeblock.com/repository/npm-group/ws/-/ws-7.5.9.tgz"
  integrity sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml2js@^0.4.16:
  version "0.4.23"
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2js@^0.5.0:
  version "0.5.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xml2js/-/xml2js-0.5.0.tgz"
  integrity sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://repository.makeblock.com/repository/npm-group/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xregexp@2.0.0:
  version "2.0.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://repository.makeblock.com/repository/npm-group/xtend/-/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

xterm-addon-attach@^0.9.0:
  version "0.9.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xterm-addon-attach/-/xterm-addon-attach-0.9.0.tgz"
  integrity sha512-NykWWOsobVZPPK3P9eFkItrnBK9Lw0f94uey5zhqIVB1bhswdVBfl+uziEzSOhe2h0rT9wD0wOeAYsdSXeavPw==

xterm-addon-fit@^0.8.0:
  version "0.8.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xterm-addon-fit/-/xterm-addon-fit-0.8.0.tgz"
  integrity sha512-yj3Np7XlvxxhYF/EJ7p3KHaMt6OdwQ+HDu573Vx1lRXsVxOcnVJs51RgjZOouIZOczTsskaS+CpXspK81/DLqw==

xterm@^5.0.0, xterm@^5.3.0:
  version "5.3.0"
  resolved "http://repository.makeblock.com/repository/npm-group/xterm/-/xterm-5.3.0.tgz"
  integrity sha512-8QqjlekLUFTrU6x7xck1MsPzPA571K5zNqWm0M0oroYEWVOptZ0+ubQSkQ3uxIEhcIHRujJy6emDWX4A7qyFzg==

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://repository.makeblock.com/repository/npm-group/y18n/-/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/yallist/-/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://repository.makeblock.com/repository/npm-group/yallist/-/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0, yaml@^1.10.2:
  version "1.10.2"
  resolved "http://repository.makeblock.com/repository/npm-group/yaml/-/yaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^20.2.2, yargs-parser@^20.2.3, yargs-parser@20.x:
  version "20.2.9"
  resolved "http://repository.makeblock.com/repository/npm-group/yargs-parser/-/yargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^21.0.0:
  version "21.0.1"

yargs@^16.0.0:
  version "16.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/yargs/-/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^16.2.0:
  version "16.2.0"
  resolved "http://repository.makeblock.com/repository/npm-group/yargs/-/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.0.0:
  version "17.4.1"
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.0.0"

yn@3.1.1:
  version "3.1.1"
  resolved "http://repository.makeblock.com/repository/npm-group/yn/-/yn-3.1.1.tgz"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://repository.makeblock.com/repository/npm-group/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zrender@5.3.1:
  version "5.3.1"
  dependencies:
    tslib "2.3.0"
